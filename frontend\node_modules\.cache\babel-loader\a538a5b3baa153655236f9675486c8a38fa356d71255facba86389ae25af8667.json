{"ast": null, "code": "function initial(arr) {\n  return arr.slice(0, -1);\n}\nexport { initial };", "map": {"version": 3, "names": ["initial", "arr", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/initial.mjs"], "sourcesContent": ["function initial(arr) {\n    return arr.slice(0, -1);\n}\n\nexport { initial };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAClB,OAAOA,GAAG,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3B;AAEA,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}