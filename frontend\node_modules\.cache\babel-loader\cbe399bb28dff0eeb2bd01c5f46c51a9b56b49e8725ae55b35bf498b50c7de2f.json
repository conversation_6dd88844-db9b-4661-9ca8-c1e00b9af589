{"ast": null, "code": "function trimStart(str, chars) {\n  if (chars === undefined) {\n    return str.trimStart();\n  }\n  let startIndex = 0;\n  switch (typeof chars) {\n    case 'string':\n      {\n        while (startIndex < str.length && str[startIndex] === chars) {\n          startIndex++;\n        }\n        break;\n      }\n    case 'object':\n      {\n        while (startIndex < str.length && chars.includes(str[startIndex])) {\n          startIndex++;\n        }\n      }\n  }\n  return str.substring(startIndex);\n}\nexport { trimStart };", "map": {"version": 3, "names": ["trimStart", "str", "chars", "undefined", "startIndex", "length", "includes", "substring"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/string/trimStart.mjs"], "sourcesContent": ["function trimStart(str, chars) {\n    if (chars === undefined) {\n        return str.trimStart();\n    }\n    let startIndex = 0;\n    switch (typeof chars) {\n        case 'string': {\n            while (startIndex < str.length && str[startIndex] === chars) {\n                startIndex++;\n            }\n            break;\n        }\n        case 'object': {\n            while (startIndex < str.length && chars.includes(str[startIndex])) {\n                startIndex++;\n            }\n        }\n    }\n    return str.substring(startIndex);\n}\n\nexport { trimStart };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC3B,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACrB,OAAOF,GAAG,CAACD,SAAS,CAAC,CAAC;EAC1B;EACA,IAAII,UAAU,GAAG,CAAC;EAClB,QAAQ,OAAOF,KAAK;IAChB,KAAK,QAAQ;MAAE;QACX,OAAOE,UAAU,GAAGH,GAAG,CAACI,MAAM,IAAIJ,GAAG,CAACG,UAAU,CAAC,KAAKF,KAAK,EAAE;UACzDE,UAAU,EAAE;QAChB;QACA;MACJ;IACA,KAAK,QAAQ;MAAE;QACX,OAAOA,UAAU,GAAGH,GAAG,CAACI,MAAM,IAAIH,KAAK,CAACI,QAAQ,CAACL,GAAG,CAACG,UAAU,CAAC,CAAC,EAAE;UAC/DA,UAAU,EAAE;QAChB;MACJ;EACJ;EACA,OAAOH,GAAG,CAACM,SAAS,CAACH,UAAU,CAAC;AACpC;AAEA,SAASJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}