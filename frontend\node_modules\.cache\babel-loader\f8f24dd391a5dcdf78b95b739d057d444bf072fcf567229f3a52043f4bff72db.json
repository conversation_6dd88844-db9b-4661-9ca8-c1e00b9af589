{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { gridFilterModelSelector, gridFilterActiveItemsSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridDensityFactorSelector } from \"../density/index.js\";\nimport { useGridLogger, useGridSelector, useGridApiMethod, useGridEvent } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPageCountSelector, gridPaginationModelSelector } from \"./gridPaginationSelector.js\";\nimport { getPageCount, defaultPageSize, throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel, getValidPage } from \"./gridPaginationUtils.js\";\nexport const getDerivedPaginationModel = (paginationState, signature, paginationModelProp) => {\n  let paginationModel = paginationState.paginationModel;\n  const rowCount = paginationState.rowCount;\n  const pageSize = paginationModelProp?.pageSize ?? paginationModel.pageSize;\n  const page = paginationModelProp?.page ?? paginationModel.page;\n  const pageCount = getPageCount(rowCount, pageSize, page);\n  if (paginationModelProp && (paginationModelProp?.page !== paginationModel.page || paginationModelProp?.pageSize !== paginationModel.pageSize)) {\n    paginationModel = paginationModelProp;\n  }\n  const validPage = pageSize === -1 ? 0 : getValidPage(paginationModel.page, pageCount);\n  if (validPage !== paginationModel.page) {\n    paginationModel = _extends({}, paginationModel, {\n      page: validPage\n    });\n  }\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, signature);\n  return paginationModel;\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPaginationModel = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPaginationModel');\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const previousFilterModel = React.useRef(gridFilterModelSelector(apiRef));\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  apiRef.current.registerControlState({\n    stateId: 'paginationModel',\n    propModel: props.paginationModel,\n    propOnChange: props.onPaginationModelChange,\n    stateSelector: gridPaginationModelSelector,\n    changeEvent: 'paginationModelChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setPage = React.useCallback(page => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (page === currentModel.page) {\n      return;\n    }\n    logger.debug(`Setting page to ${page}`);\n    apiRef.current.setPaginationModel({\n      page,\n      pageSize: currentModel.pageSize\n    });\n  }, [apiRef, logger]);\n  const setPageSize = React.useCallback(pageSize => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (pageSize === currentModel.pageSize) {\n      return;\n    }\n    logger.debug(`Setting page size to ${pageSize}`);\n    apiRef.current.setPaginationModel({\n      pageSize,\n      page: currentModel.page\n    });\n  }, [apiRef, logger]);\n  const setPaginationModel = React.useCallback(paginationModel => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel === currentModel) {\n      return;\n    }\n    logger.debug(\"Setting 'paginationModel' to\", paginationModel);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }), 'setPaginationModel');\n  }, [apiRef, logger, props.signature]);\n  const paginationModelApi = {\n    setPage,\n    setPageSize,\n    setPaginationModel\n  };\n  useGridApiMethod(apiRef, paginationModelApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    const shouldExportPaginationModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `paginationModel` is controlled\n    props.paginationModel != null ||\n    // Always export if the `paginationModel` has been initialized\n    props.initialState?.pagination?.paginationModel != null ||\n    // Export if `page` or `pageSize` is not equal to the default value\n    paginationModel.page !== 0 && paginationModel.pageSize !== defaultPageSize(props.autoPageSize);\n    if (!shouldExportPaginationModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        paginationModel\n      })\n    });\n  }, [apiRef, props.paginationModel, props.initialState?.pagination?.paginationModel, props.autoPageSize]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const paginationModel = context.stateToRestore.pagination?.paginationModel ? _extends({}, getDefaultGridPaginationModel(props.autoPageSize), context.stateToRestore.pagination?.paginationModel) : gridPaginationModelSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }), 'stateRestorePreProcessing');\n    return params;\n  }, [apiRef, props.autoPageSize, props.signature]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const handlePaginationModelChange = () => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (apiRef.current.virtualScrollerRef?.current) {\n      apiRef.current.scrollToIndexes({\n        rowIndex: paginationModel.page * paginationModel.pageSize\n      });\n    }\n  };\n  const handleUpdateAutoPageSize = React.useCallback(() => {\n    if (!props.autoPageSize) {\n      return;\n    }\n    const dimensions = apiRef.current.getRootDimensions();\n    const maximumPageSizeWithoutScrollBar = Math.max(1, Math.floor(dimensions.viewportInnerSize.height / rowHeight));\n    apiRef.current.setPageSize(maximumPageSizeWithoutScrollBar);\n  }, [apiRef, props.autoPageSize, rowHeight]);\n  const handleRowCountChange = React.useCallback(newRowCount => {\n    if (newRowCount == null) {\n      return;\n    }\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel.page === 0) {\n      return;\n    }\n    const pageCount = gridPageCountSelector(apiRef);\n    if (paginationModel.page > pageCount - 1) {\n      apiRef.current.setPage(Math.max(0, pageCount - 1));\n    }\n  }, [apiRef]);\n\n  /**\n   * Goes to the first row of the grid\n   */\n  const navigateToStart = React.useCallback(() => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel.page !== 0) {\n      apiRef.current.setPage(0);\n    }\n\n    // If the page was not changed it might be needed to scroll to the top\n    const scrollPosition = apiRef.current.getScrollPosition();\n    if (scrollPosition.top !== 0) {\n      apiRef.current.scroll({\n        top: 0\n      });\n    }\n  }, [apiRef]);\n\n  /**\n   * Resets the page only if the active items or quick filter has changed from the last time.\n   * This is to avoid resetting the page when the filter model is changed\n   * because of and update of the operator from an item that does not have the value\n   * or reseting when the filter panel is just opened\n   */\n  const handleFilterModelChange = React.useCallback(filterModel => {\n    const currentActiveFilters = _extends({}, filterModel, {\n      // replace items with the active items\n      items: gridFilterActiveItemsSelector(apiRef)\n    });\n    if (isDeepEqual(currentActiveFilters, previousFilterModel.current)) {\n      return;\n    }\n    previousFilterModel.current = currentActiveFilters;\n    navigateToStart();\n  }, [apiRef, navigateToStart]);\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleUpdateAutoPageSize);\n  useGridEvent(apiRef, 'paginationModelChange', handlePaginationModelChange);\n  useGridEvent(apiRef, 'rowCountChange', handleRowCountChange);\n  useGridEvent(apiRef, 'sortModelChange', navigateToStart);\n  useGridEvent(apiRef, 'filterModelChange', handleFilterModelChange);\n\n  /**\n   * EFFECTS\n   */\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    if (!props.pagination) {\n      return;\n    }\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, props.paginationModel)\n      })\n    }));\n  }, [apiRef, props.paginationModel, props.signature, props.pagination]);\n  React.useEffect(() => {\n    apiRef.current.setState(state => {\n      const isEnabled = props.pagination === true;\n      if (state.pagination.paginationMode === props.paginationMode && state.pagination.enabled === isEnabled) {\n        return state;\n      }\n      return _extends({}, state, {\n        pagination: _extends({}, state.pagination, {\n          paginationMode: props.paginationMode,\n          enabled: isEnabled\n        })\n      });\n    });\n  }, [apiRef, props.paginationMode, props.pagination]);\n  React.useEffect(handleUpdateAutoPageSize, [handleUpdateAutoPageSize]);\n};", "map": {"version": 3, "names": ["_extends", "React", "isDeepEqual", "gridFilterModelSelector", "gridFilterActiveItemsSelector", "gridDensityFactorSelector", "useGridLogger", "useGridSelector", "useGridApiMethod", "useGridEvent", "useGridRegisterPipeProcessor", "gridPageCountSelector", "gridPaginationModelSelector", "getPageCount", "defaultPageSize", "throwIfPageSizeExceedsTheLimit", "getDefaultGridPaginationModel", "getValidPage", "getDerivedPaginationModel", "paginationState", "signature", "paginationModelProp", "paginationModel", "rowCount", "pageSize", "page", "pageCount", "validPage", "useGridPaginationModel", "apiRef", "props", "logger", "densityFactor", "previousFilterModel", "useRef", "rowHeight", "Math", "floor", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onPaginationModelChange", "stateSelector", "changeEvent", "setPage", "useCallback", "currentModel", "debug", "setPaginationModel", "setPageSize", "setState", "state", "pagination", "paginationModelApi", "stateExportPreProcessing", "prevState", "context", "shouldExportPaginationModel", "exportOnlyDirtyModels", "initialState", "autoPageSize", "stateRestorePreProcessing", "params", "stateToRestore", "handlePaginationModelChange", "virtualScrollerRef", "scrollToIndexes", "rowIndex", "handleUpdateAutoPageSize", "dimensions", "getRootDimensions", "maximumPageSizeWithoutScrollBar", "max", "viewportInnerSize", "height", "handleRowCountChange", "newRowCount", "navigateToStart", "scrollPosition", "getScrollPosition", "top", "scroll", "handleFilterModelChange", "filterModel", "currentActiveFilters", "items", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "isEnabled", "paginationMode", "enabled"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/pagination/useGridPaginationModel.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { gridFilterModelSelector, gridFilterActiveItemsSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridDensityFactorSelector } from \"../density/index.js\";\nimport { useGridLogger, useGridSelector, useGridApiMethod, useGridEvent } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPageCountSelector, gridPaginationModelSelector } from \"./gridPaginationSelector.js\";\nimport { getPageCount, defaultPageSize, throwIfPageSizeExceedsTheLimit, getDefaultGridPaginationModel, getValidPage } from \"./gridPaginationUtils.js\";\nexport const getDerivedPaginationModel = (paginationState, signature, paginationModelProp) => {\n  let paginationModel = paginationState.paginationModel;\n  const rowCount = paginationState.rowCount;\n  const pageSize = paginationModelProp?.pageSize ?? paginationModel.pageSize;\n  const page = paginationModelProp?.page ?? paginationModel.page;\n  const pageCount = getPageCount(rowCount, pageSize, page);\n  if (paginationModelProp && (paginationModelProp?.page !== paginationModel.page || paginationModelProp?.pageSize !== paginationModel.pageSize)) {\n    paginationModel = paginationModelProp;\n  }\n  const validPage = pageSize === -1 ? 0 : getValidPage(paginationModel.page, pageCount);\n  if (validPage !== paginationModel.page) {\n    paginationModel = _extends({}, paginationModel, {\n      page: validPage\n    });\n  }\n  throwIfPageSizeExceedsTheLimit(paginationModel.pageSize, signature);\n  return paginationModel;\n};\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridPaginationModel = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPaginationModel');\n  const densityFactor = useGridSelector(apiRef, gridDensityFactorSelector);\n  const previousFilterModel = React.useRef(gridFilterModelSelector(apiRef));\n  const rowHeight = Math.floor(props.rowHeight * densityFactor);\n  apiRef.current.registerControlState({\n    stateId: 'paginationModel',\n    propModel: props.paginationModel,\n    propOnChange: props.onPaginationModelChange,\n    stateSelector: gridPaginationModelSelector,\n    changeEvent: 'paginationModelChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setPage = React.useCallback(page => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (page === currentModel.page) {\n      return;\n    }\n    logger.debug(`Setting page to ${page}`);\n    apiRef.current.setPaginationModel({\n      page,\n      pageSize: currentModel.pageSize\n    });\n  }, [apiRef, logger]);\n  const setPageSize = React.useCallback(pageSize => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (pageSize === currentModel.pageSize) {\n      return;\n    }\n    logger.debug(`Setting page size to ${pageSize}`);\n    apiRef.current.setPaginationModel({\n      pageSize,\n      page: currentModel.page\n    });\n  }, [apiRef, logger]);\n  const setPaginationModel = React.useCallback(paginationModel => {\n    const currentModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel === currentModel) {\n      return;\n    }\n    logger.debug(\"Setting 'paginationModel' to\", paginationModel);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }), 'setPaginationModel');\n  }, [apiRef, logger, props.signature]);\n  const paginationModelApi = {\n    setPage,\n    setPageSize,\n    setPaginationModel\n  };\n  useGridApiMethod(apiRef, paginationModelApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    const shouldExportPaginationModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `paginationModel` is controlled\n    props.paginationModel != null ||\n    // Always export if the `paginationModel` has been initialized\n    props.initialState?.pagination?.paginationModel != null ||\n    // Export if `page` or `pageSize` is not equal to the default value\n    paginationModel.page !== 0 && paginationModel.pageSize !== defaultPageSize(props.autoPageSize);\n    if (!shouldExportPaginationModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        paginationModel\n      })\n    });\n  }, [apiRef, props.paginationModel, props.initialState?.pagination?.paginationModel, props.autoPageSize]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const paginationModel = context.stateToRestore.pagination?.paginationModel ? _extends({}, getDefaultGridPaginationModel(props.autoPageSize), context.stateToRestore.pagination?.paginationModel) : gridPaginationModelSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, paginationModel)\n      })\n    }), 'stateRestorePreProcessing');\n    return params;\n  }, [apiRef, props.autoPageSize, props.signature]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EVENTS\n   */\n  const handlePaginationModelChange = () => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (apiRef.current.virtualScrollerRef?.current) {\n      apiRef.current.scrollToIndexes({\n        rowIndex: paginationModel.page * paginationModel.pageSize\n      });\n    }\n  };\n  const handleUpdateAutoPageSize = React.useCallback(() => {\n    if (!props.autoPageSize) {\n      return;\n    }\n    const dimensions = apiRef.current.getRootDimensions();\n    const maximumPageSizeWithoutScrollBar = Math.max(1, Math.floor(dimensions.viewportInnerSize.height / rowHeight));\n    apiRef.current.setPageSize(maximumPageSizeWithoutScrollBar);\n  }, [apiRef, props.autoPageSize, rowHeight]);\n  const handleRowCountChange = React.useCallback(newRowCount => {\n    if (newRowCount == null) {\n      return;\n    }\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel.page === 0) {\n      return;\n    }\n    const pageCount = gridPageCountSelector(apiRef);\n    if (paginationModel.page > pageCount - 1) {\n      apiRef.current.setPage(Math.max(0, pageCount - 1));\n    }\n  }, [apiRef]);\n\n  /**\n   * Goes to the first row of the grid\n   */\n  const navigateToStart = React.useCallback(() => {\n    const paginationModel = gridPaginationModelSelector(apiRef);\n    if (paginationModel.page !== 0) {\n      apiRef.current.setPage(0);\n    }\n\n    // If the page was not changed it might be needed to scroll to the top\n    const scrollPosition = apiRef.current.getScrollPosition();\n    if (scrollPosition.top !== 0) {\n      apiRef.current.scroll({\n        top: 0\n      });\n    }\n  }, [apiRef]);\n\n  /**\n   * Resets the page only if the active items or quick filter has changed from the last time.\n   * This is to avoid resetting the page when the filter model is changed\n   * because of and update of the operator from an item that does not have the value\n   * or reseting when the filter panel is just opened\n   */\n  const handleFilterModelChange = React.useCallback(filterModel => {\n    const currentActiveFilters = _extends({}, filterModel, {\n      // replace items with the active items\n      items: gridFilterActiveItemsSelector(apiRef)\n    });\n    if (isDeepEqual(currentActiveFilters, previousFilterModel.current)) {\n      return;\n    }\n    previousFilterModel.current = currentActiveFilters;\n    navigateToStart();\n  }, [apiRef, navigateToStart]);\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleUpdateAutoPageSize);\n  useGridEvent(apiRef, 'paginationModelChange', handlePaginationModelChange);\n  useGridEvent(apiRef, 'rowCountChange', handleRowCountChange);\n  useGridEvent(apiRef, 'sortModelChange', navigateToStart);\n  useGridEvent(apiRef, 'filterModelChange', handleFilterModelChange);\n\n  /**\n   * EFFECTS\n   */\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    if (!props.pagination) {\n      return;\n    }\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        paginationModel: getDerivedPaginationModel(state.pagination, props.signature, props.paginationModel)\n      })\n    }));\n  }, [apiRef, props.paginationModel, props.signature, props.pagination]);\n  React.useEffect(() => {\n    apiRef.current.setState(state => {\n      const isEnabled = props.pagination === true;\n      if (state.pagination.paginationMode === props.paginationMode && state.pagination.enabled === isEnabled) {\n        return state;\n      }\n      return _extends({}, state, {\n        pagination: _extends({}, state.pagination, {\n          paginationMode: props.paginationMode,\n          enabled: isEnabled\n        })\n      });\n    });\n  }, [apiRef, props.paginationMode, props.pagination]);\n  React.useEffect(handleUpdateAutoPageSize, [handleUpdateAutoPageSize]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,uBAAuB,EAAEC,6BAA6B,QAAQ,iCAAiC;AACxG,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,sBAAsB;AACrG,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,qBAAqB,EAAEC,2BAA2B,QAAQ,6BAA6B;AAChG,SAASC,YAAY,EAAEC,eAAe,EAAEC,8BAA8B,EAAEC,6BAA6B,EAAEC,YAAY,QAAQ,0BAA0B;AACrJ,OAAO,MAAMC,yBAAyB,GAAGA,CAACC,eAAe,EAAEC,SAAS,EAAEC,mBAAmB,KAAK;EAC5F,IAAIC,eAAe,GAAGH,eAAe,CAACG,eAAe;EACrD,MAAMC,QAAQ,GAAGJ,eAAe,CAACI,QAAQ;EACzC,MAAMC,QAAQ,GAAGH,mBAAmB,EAAEG,QAAQ,IAAIF,eAAe,CAACE,QAAQ;EAC1E,MAAMC,IAAI,GAAGJ,mBAAmB,EAAEI,IAAI,IAAIH,eAAe,CAACG,IAAI;EAC9D,MAAMC,SAAS,GAAGb,YAAY,CAACU,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EACxD,IAAIJ,mBAAmB,KAAKA,mBAAmB,EAAEI,IAAI,KAAKH,eAAe,CAACG,IAAI,IAAIJ,mBAAmB,EAAEG,QAAQ,KAAKF,eAAe,CAACE,QAAQ,CAAC,EAAE;IAC7IF,eAAe,GAAGD,mBAAmB;EACvC;EACA,MAAMM,SAAS,GAAGH,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGP,YAAY,CAACK,eAAe,CAACG,IAAI,EAAEC,SAAS,CAAC;EACrF,IAAIC,SAAS,KAAKL,eAAe,CAACG,IAAI,EAAE;IACtCH,eAAe,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEsB,eAAe,EAAE;MAC9CG,IAAI,EAAEE;IACR,CAAC,CAAC;EACJ;EACAZ,8BAA8B,CAACO,eAAe,CAACE,QAAQ,EAAEJ,SAAS,CAAC;EACnE,OAAOE,eAAe;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMM,sBAAsB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACvD,MAAMC,MAAM,GAAGzB,aAAa,CAACuB,MAAM,EAAE,wBAAwB,CAAC;EAC9D,MAAMG,aAAa,GAAGzB,eAAe,CAACsB,MAAM,EAAExB,yBAAyB,CAAC;EACxE,MAAM4B,mBAAmB,GAAGhC,KAAK,CAACiC,MAAM,CAAC/B,uBAAuB,CAAC0B,MAAM,CAAC,CAAC;EACzE,MAAMM,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACP,KAAK,CAACK,SAAS,GAAGH,aAAa,CAAC;EAC7DH,MAAM,CAACS,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,iBAAiB;IAC1BC,SAAS,EAAEX,KAAK,CAACR,eAAe;IAChCoB,YAAY,EAAEZ,KAAK,CAACa,uBAAuB;IAC3CC,aAAa,EAAEhC,2BAA2B;IAC1CiC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAMC,OAAO,GAAG7C,KAAK,CAAC8C,WAAW,CAACtB,IAAI,IAAI;IACxC,MAAMuB,YAAY,GAAGpC,2BAA2B,CAACiB,MAAM,CAAC;IACxD,IAAIJ,IAAI,KAAKuB,YAAY,CAACvB,IAAI,EAAE;MAC9B;IACF;IACAM,MAAM,CAACkB,KAAK,CAAC,mBAAmBxB,IAAI,EAAE,CAAC;IACvCI,MAAM,CAACS,OAAO,CAACY,kBAAkB,CAAC;MAChCzB,IAAI;MACJD,QAAQ,EAAEwB,YAAY,CAACxB;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACK,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMoB,WAAW,GAAGlD,KAAK,CAAC8C,WAAW,CAACvB,QAAQ,IAAI;IAChD,MAAMwB,YAAY,GAAGpC,2BAA2B,CAACiB,MAAM,CAAC;IACxD,IAAIL,QAAQ,KAAKwB,YAAY,CAACxB,QAAQ,EAAE;MACtC;IACF;IACAO,MAAM,CAACkB,KAAK,CAAC,wBAAwBzB,QAAQ,EAAE,CAAC;IAChDK,MAAM,CAACS,OAAO,CAACY,kBAAkB,CAAC;MAChC1B,QAAQ;MACRC,IAAI,EAAEuB,YAAY,CAACvB;IACrB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACI,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMmB,kBAAkB,GAAGjD,KAAK,CAAC8C,WAAW,CAACzB,eAAe,IAAI;IAC9D,MAAM0B,YAAY,GAAGpC,2BAA2B,CAACiB,MAAM,CAAC;IACxD,IAAIP,eAAe,KAAK0B,YAAY,EAAE;MACpC;IACF;IACAjB,MAAM,CAACkB,KAAK,CAAC,8BAA8B,EAAE3B,eAAe,CAAC;IAC7DO,MAAM,CAACS,OAAO,CAACc,QAAQ,CAACC,KAAK,IAAIrD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,EAAE;MACnDC,UAAU,EAAEtD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,CAACC,UAAU,EAAE;QACzChC,eAAe,EAAEJ,yBAAyB,CAACmC,KAAK,CAACC,UAAU,EAAExB,KAAK,CAACV,SAAS,EAAEE,eAAe;MAC/F,CAAC;IACH,CAAC,CAAC,EAAE,oBAAoB,CAAC;EAC3B,CAAC,EAAE,CAACO,MAAM,EAAEE,MAAM,EAAED,KAAK,CAACV,SAAS,CAAC,CAAC;EACrC,MAAMmC,kBAAkB,GAAG;IACzBT,OAAO;IACPK,WAAW;IACXD;EACF,CAAC;EACD1C,gBAAgB,CAACqB,MAAM,EAAE0B,kBAAkB,EAAE,QAAQ,CAAC;;EAEtD;AACF;AACA;EACE,MAAMC,wBAAwB,GAAGvD,KAAK,CAAC8C,WAAW,CAAC,CAACU,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMpC,eAAe,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IAC3D,MAAM8B,2BAA2B;IACjC;IACA,CAACD,OAAO,CAACE,qBAAqB;IAC9B;IACA9B,KAAK,CAACR,eAAe,IAAI,IAAI;IAC7B;IACAQ,KAAK,CAAC+B,YAAY,EAAEP,UAAU,EAAEhC,eAAe,IAAI,IAAI;IACvD;IACAA,eAAe,CAACG,IAAI,KAAK,CAAC,IAAIH,eAAe,CAACE,QAAQ,KAAKV,eAAe,CAACgB,KAAK,CAACgC,YAAY,CAAC;IAC9F,IAAI,CAACH,2BAA2B,EAAE;MAChC,OAAOF,SAAS;IAClB;IACA,OAAOzD,QAAQ,CAAC,CAAC,CAAC,EAAEyD,SAAS,EAAE;MAC7BH,UAAU,EAAEtD,QAAQ,CAAC,CAAC,CAAC,EAAEyD,SAAS,CAACH,UAAU,EAAE;QAC7ChC;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACO,MAAM,EAAEC,KAAK,CAACR,eAAe,EAAEQ,KAAK,CAAC+B,YAAY,EAAEP,UAAU,EAAEhC,eAAe,EAAEQ,KAAK,CAACgC,YAAY,CAAC,CAAC;EACxG,MAAMC,yBAAyB,GAAG9D,KAAK,CAAC8C,WAAW,CAAC,CAACiB,MAAM,EAAEN,OAAO,KAAK;IACvE,MAAMpC,eAAe,GAAGoC,OAAO,CAACO,cAAc,CAACX,UAAU,EAAEhC,eAAe,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,6BAA6B,CAACc,KAAK,CAACgC,YAAY,CAAC,EAAEJ,OAAO,CAACO,cAAc,CAACX,UAAU,EAAEhC,eAAe,CAAC,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IACtOA,MAAM,CAACS,OAAO,CAACc,QAAQ,CAACC,KAAK,IAAIrD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,EAAE;MACnDC,UAAU,EAAEtD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,CAACC,UAAU,EAAE;QACzChC,eAAe,EAAEJ,yBAAyB,CAACmC,KAAK,CAACC,UAAU,EAAExB,KAAK,CAACV,SAAS,EAAEE,eAAe;MAC/F,CAAC;IACH,CAAC,CAAC,EAAE,2BAA2B,CAAC;IAChC,OAAO0C,MAAM;EACf,CAAC,EAAE,CAACnC,MAAM,EAAEC,KAAK,CAACgC,YAAY,EAAEhC,KAAK,CAACV,SAAS,CAAC,CAAC;EACjDV,4BAA4B,CAACmB,MAAM,EAAE,aAAa,EAAE2B,wBAAwB,CAAC;EAC7E9C,4BAA4B,CAACmB,MAAM,EAAE,cAAc,EAAEkC,yBAAyB,CAAC;;EAE/E;AACF;AACA;EACE,MAAMG,2BAA2B,GAAGA,CAAA,KAAM;IACxC,MAAM5C,eAAe,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IAC3D,IAAIA,MAAM,CAACS,OAAO,CAAC6B,kBAAkB,EAAE7B,OAAO,EAAE;MAC9CT,MAAM,CAACS,OAAO,CAAC8B,eAAe,CAAC;QAC7BC,QAAQ,EAAE/C,eAAe,CAACG,IAAI,GAAGH,eAAe,CAACE;MACnD,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM8C,wBAAwB,GAAGrE,KAAK,CAAC8C,WAAW,CAAC,MAAM;IACvD,IAAI,CAACjB,KAAK,CAACgC,YAAY,EAAE;MACvB;IACF;IACA,MAAMS,UAAU,GAAG1C,MAAM,CAACS,OAAO,CAACkC,iBAAiB,CAAC,CAAC;IACrD,MAAMC,+BAA+B,GAAGrC,IAAI,CAACsC,GAAG,CAAC,CAAC,EAAEtC,IAAI,CAACC,KAAK,CAACkC,UAAU,CAACI,iBAAiB,CAACC,MAAM,GAAGzC,SAAS,CAAC,CAAC;IAChHN,MAAM,CAACS,OAAO,CAACa,WAAW,CAACsB,+BAA+B,CAAC;EAC7D,CAAC,EAAE,CAAC5C,MAAM,EAAEC,KAAK,CAACgC,YAAY,EAAE3B,SAAS,CAAC,CAAC;EAC3C,MAAM0C,oBAAoB,GAAG5E,KAAK,CAAC8C,WAAW,CAAC+B,WAAW,IAAI;IAC5D,IAAIA,WAAW,IAAI,IAAI,EAAE;MACvB;IACF;IACA,MAAMxD,eAAe,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IAC3D,IAAIP,eAAe,CAACG,IAAI,KAAK,CAAC,EAAE;MAC9B;IACF;IACA,MAAMC,SAAS,GAAGf,qBAAqB,CAACkB,MAAM,CAAC;IAC/C,IAAIP,eAAe,CAACG,IAAI,GAAGC,SAAS,GAAG,CAAC,EAAE;MACxCG,MAAM,CAACS,OAAO,CAACQ,OAAO,CAACV,IAAI,CAACsC,GAAG,CAAC,CAAC,EAAEhD,SAAS,GAAG,CAAC,CAAC,CAAC;IACpD;EACF,CAAC,EAAE,CAACG,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMkD,eAAe,GAAG9E,KAAK,CAAC8C,WAAW,CAAC,MAAM;IAC9C,MAAMzB,eAAe,GAAGV,2BAA2B,CAACiB,MAAM,CAAC;IAC3D,IAAIP,eAAe,CAACG,IAAI,KAAK,CAAC,EAAE;MAC9BI,MAAM,CAACS,OAAO,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC3B;;IAEA;IACA,MAAMkC,cAAc,GAAGnD,MAAM,CAACS,OAAO,CAAC2C,iBAAiB,CAAC,CAAC;IACzD,IAAID,cAAc,CAACE,GAAG,KAAK,CAAC,EAAE;MAC5BrD,MAAM,CAACS,OAAO,CAAC6C,MAAM,CAAC;QACpBD,GAAG,EAAE;MACP,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrD,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;AACA;AACA;AACA;EACE,MAAMuD,uBAAuB,GAAGnF,KAAK,CAAC8C,WAAW,CAACsC,WAAW,IAAI;IAC/D,MAAMC,oBAAoB,GAAGtF,QAAQ,CAAC,CAAC,CAAC,EAAEqF,WAAW,EAAE;MACrD;MACAE,KAAK,EAAEnF,6BAA6B,CAACyB,MAAM;IAC7C,CAAC,CAAC;IACF,IAAI3B,WAAW,CAACoF,oBAAoB,EAAErD,mBAAmB,CAACK,OAAO,CAAC,EAAE;MAClE;IACF;IACAL,mBAAmB,CAACK,OAAO,GAAGgD,oBAAoB;IAClDP,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAClD,MAAM,EAAEkD,eAAe,CAAC,CAAC;EAC7BtE,YAAY,CAACoB,MAAM,EAAE,yBAAyB,EAAEyC,wBAAwB,CAAC;EACzE7D,YAAY,CAACoB,MAAM,EAAE,uBAAuB,EAAEqC,2BAA2B,CAAC;EAC1EzD,YAAY,CAACoB,MAAM,EAAE,gBAAgB,EAAEgD,oBAAoB,CAAC;EAC5DpE,YAAY,CAACoB,MAAM,EAAE,iBAAiB,EAAEkD,eAAe,CAAC;EACxDtE,YAAY,CAACoB,MAAM,EAAE,mBAAmB,EAAEuD,uBAAuB,CAAC;;EAElE;AACF;AACA;EACE,MAAMI,aAAa,GAAGvF,KAAK,CAACiC,MAAM,CAAC,IAAI,CAAC;EACxCjC,KAAK,CAACwF,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,CAAClD,OAAO,EAAE;MACzBkD,aAAa,CAAClD,OAAO,GAAG,KAAK;MAC7B;IACF;IACA,IAAI,CAACR,KAAK,CAACwB,UAAU,EAAE;MACrB;IACF;IACAzB,MAAM,CAACS,OAAO,CAACc,QAAQ,CAACC,KAAK,IAAIrD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,EAAE;MACnDC,UAAU,EAAEtD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,CAACC,UAAU,EAAE;QACzChC,eAAe,EAAEJ,yBAAyB,CAACmC,KAAK,CAACC,UAAU,EAAExB,KAAK,CAACV,SAAS,EAAEU,KAAK,CAACR,eAAe;MACrG,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACO,MAAM,EAAEC,KAAK,CAACR,eAAe,EAAEQ,KAAK,CAACV,SAAS,EAAEU,KAAK,CAACwB,UAAU,CAAC,CAAC;EACtErD,KAAK,CAACwF,SAAS,CAAC,MAAM;IACpB5D,MAAM,CAACS,OAAO,CAACc,QAAQ,CAACC,KAAK,IAAI;MAC/B,MAAMqC,SAAS,GAAG5D,KAAK,CAACwB,UAAU,KAAK,IAAI;MAC3C,IAAID,KAAK,CAACC,UAAU,CAACqC,cAAc,KAAK7D,KAAK,CAAC6D,cAAc,IAAItC,KAAK,CAACC,UAAU,CAACsC,OAAO,KAAKF,SAAS,EAAE;QACtG,OAAOrC,KAAK;MACd;MACA,OAAOrD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,EAAE;QACzBC,UAAU,EAAEtD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,CAACC,UAAU,EAAE;UACzCqC,cAAc,EAAE7D,KAAK,CAAC6D,cAAc;UACpCC,OAAO,EAAEF;QACX,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7D,MAAM,EAAEC,KAAK,CAAC6D,cAAc,EAAE7D,KAAK,CAACwB,UAAU,CAAC,CAAC;EACpDrD,KAAK,CAACwF,SAAS,CAACnB,wBAAwB,EAAE,CAACA,wBAAwB,CAAC,CAAC;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}