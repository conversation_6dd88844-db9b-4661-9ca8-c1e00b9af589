{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n/* eslint-disable no-cond-assign */\n\nexport class Store {\n  // HACK: `any` fixes adding listeners that accept partial state.\n\n  // Internal state to handle recursive `setState()` calls\n\n  static create(state) {\n    return new Store(state);\n  }\n  constructor(state) {\n    this.state = state;\n    this.listeners = new Set();\n    this.updateTick = 0;\n  }\n  subscribe = fn => {\n    this.listeners.add(fn);\n    return () => {\n      this.listeners.delete(fn);\n    };\n  };\n  getSnapshot = () => {\n    return this.state;\n  };\n  setState(newState) {\n    this.state = newState;\n    this.updateTick += 1;\n    const currentTick = this.updateTick;\n    const it = this.listeners.values();\n    let result;\n    while (result = it.next(), !result.done) {\n      if (currentTick !== this.updateTick) {\n        // If the tick has changed, a recursive `setState` call has been made,\n        // and it has already notified all listeners.\n        return;\n      }\n      const listener = result.value;\n      listener(newState);\n    }\n  }\n  update(changes) {\n    for (const key in changes) {\n      if (!Object.is(this.state[key], changes[key])) {\n        this.setState(_extends({}, this.state, changes));\n        return;\n      }\n    }\n  }\n  set(key, value) {\n    if (!Object.is(this.state[key], value)) {\n      this.setState(_extends({}, this.state, {\n        [key]: value\n      }));\n    }\n  }\n}", "map": {"version": 3, "names": ["_extends", "Store", "create", "state", "constructor", "listeners", "Set", "updateTick", "subscribe", "fn", "add", "delete", "getSnapshot", "setState", "newState", "currentTick", "it", "values", "result", "next", "done", "listener", "value", "update", "changes", "key", "Object", "is", "set"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/store/Store.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n/* eslint-disable no-cond-assign */\n\nexport class Store {\n  // HACK: `any` fixes adding listeners that accept partial state.\n\n  // Internal state to handle recursive `setState()` calls\n\n  static create(state) {\n    return new Store(state);\n  }\n  constructor(state) {\n    this.state = state;\n    this.listeners = new Set();\n    this.updateTick = 0;\n  }\n  subscribe = fn => {\n    this.listeners.add(fn);\n    return () => {\n      this.listeners.delete(fn);\n    };\n  };\n  getSnapshot = () => {\n    return this.state;\n  };\n  setState(newState) {\n    this.state = newState;\n    this.updateTick += 1;\n    const currentTick = this.updateTick;\n    const it = this.listeners.values();\n    let result;\n    while (result = it.next(), !result.done) {\n      if (currentTick !== this.updateTick) {\n        // If the tick has changed, a recursive `setState` call has been made,\n        // and it has already notified all listeners.\n        return;\n      }\n      const listener = result.value;\n      listener(newState);\n    }\n  }\n  update(changes) {\n    for (const key in changes) {\n      if (!Object.is(this.state[key], changes[key])) {\n        this.setState(_extends({}, this.state, changes));\n        return;\n      }\n    }\n  }\n  set(key, value) {\n    if (!Object.is(this.state[key], value)) {\n      this.setState(_extends({}, this.state, {\n        [key]: value\n      }));\n    }\n  }\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;;AAEA,OAAO,MAAMC,KAAK,CAAC;EACjB;;EAEA;;EAEA,OAAOC,MAAMA,CAACC,KAAK,EAAE;IACnB,OAAO,IAAIF,KAAK,CAACE,KAAK,CAAC;EACzB;EACAC,WAAWA,CAACD,KAAK,EAAE;IACjB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,UAAU,GAAG,CAAC;EACrB;EACAC,SAAS,GAAGC,EAAE,IAAI;IAChB,IAAI,CAACJ,SAAS,CAACK,GAAG,CAACD,EAAE,CAAC;IACtB,OAAO,MAAM;MACX,IAAI,CAACJ,SAAS,CAACM,MAAM,CAACF,EAAE,CAAC;IAC3B,CAAC;EACH,CAAC;EACDG,WAAW,GAAGA,CAAA,KAAM;IAClB,OAAO,IAAI,CAACT,KAAK;EACnB,CAAC;EACDU,QAAQA,CAACC,QAAQ,EAAE;IACjB,IAAI,CAACX,KAAK,GAAGW,QAAQ;IACrB,IAAI,CAACP,UAAU,IAAI,CAAC;IACpB,MAAMQ,WAAW,GAAG,IAAI,CAACR,UAAU;IACnC,MAAMS,EAAE,GAAG,IAAI,CAACX,SAAS,CAACY,MAAM,CAAC,CAAC;IAClC,IAAIC,MAAM;IACV,OAAOA,MAAM,GAAGF,EAAE,CAACG,IAAI,CAAC,CAAC,EAAE,CAACD,MAAM,CAACE,IAAI,EAAE;MACvC,IAAIL,WAAW,KAAK,IAAI,CAACR,UAAU,EAAE;QACnC;QACA;QACA;MACF;MACA,MAAMc,QAAQ,GAAGH,MAAM,CAACI,KAAK;MAC7BD,QAAQ,CAACP,QAAQ,CAAC;IACpB;EACF;EACAS,MAAMA,CAACC,OAAO,EAAE;IACd,KAAK,MAAMC,GAAG,IAAID,OAAO,EAAE;MACzB,IAAI,CAACE,MAAM,CAACC,EAAE,CAAC,IAAI,CAACxB,KAAK,CAACsB,GAAG,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC,CAAC,EAAE;QAC7C,IAAI,CAACZ,QAAQ,CAACb,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,KAAK,EAAEqB,OAAO,CAAC,CAAC;QAChD;MACF;IACF;EACF;EACAI,GAAGA,CAACH,GAAG,EAAEH,KAAK,EAAE;IACd,IAAI,CAACI,MAAM,CAACC,EAAE,CAAC,IAAI,CAACxB,KAAK,CAACsB,GAAG,CAAC,EAAEH,KAAK,CAAC,EAAE;MACtC,IAAI,CAACT,QAAQ,CAACb,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACG,KAAK,EAAE;QACrC,CAACsB,GAAG,GAAGH;MACT,CAAC,CAAC,CAAC;IACL;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}