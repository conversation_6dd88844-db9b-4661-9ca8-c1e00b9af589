{"ast": null, "code": "export class GridGetRowsError extends Error {\n  /**\n   * The parameters used in the failed request\n   */\n\n  /**\n   * The original error that caused this error\n   */\n\n  constructor(options) {\n    super(options.message);\n    this.name = 'GridGetRowsError';\n    this.params = options.params;\n    this.cause = options.cause;\n  }\n}\nexport class GridUpdateRowError extends Error {\n  /**\n   * The parameters used in the failed request\n   */\n\n  /**\n   * The original error that caused this error\n   */\n\n  constructor(options) {\n    super(options.message);\n    this.name = 'GridUpdateRowError';\n    this.params = options.params;\n    this.cause = options.cause;\n  }\n}", "map": {"version": 3, "names": ["GridGetRowsError", "Error", "constructor", "options", "message", "name", "params", "cause", "GridUpdateRowError"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/dataSource/gridDataSourceError.js"], "sourcesContent": ["export class GridGetRowsError extends Error {\n  /**\n   * The parameters used in the failed request\n   */\n\n  /**\n   * The original error that caused this error\n   */\n\n  constructor(options) {\n    super(options.message);\n    this.name = 'GridGetRowsError';\n    this.params = options.params;\n    this.cause = options.cause;\n  }\n}\nexport class GridUpdateRowError extends Error {\n  /**\n   * The parameters used in the failed request\n   */\n\n  /**\n   * The original error that caused this error\n   */\n\n  constructor(options) {\n    super(options.message);\n    this.name = 'GridUpdateRowError';\n    this.params = options.params;\n    this.cause = options.cause;\n  }\n}"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,SAASC,KAAK,CAAC;EAC1C;AACF;AACA;;EAEE;AACF;AACA;;EAEEC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAACC,OAAO,CAAC;IACtB,IAAI,CAACC,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACC,MAAM,GAAGH,OAAO,CAACG,MAAM;IAC5B,IAAI,CAACC,KAAK,GAAGJ,OAAO,CAACI,KAAK;EAC5B;AACF;AACA,OAAO,MAAMC,kBAAkB,SAASP,KAAK,CAAC;EAC5C;AACF;AACA;;EAEE;AACF;AACA;;EAEEC,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAACA,OAAO,CAACC,OAAO,CAAC;IACtB,IAAI,CAACC,IAAI,GAAG,oBAAoB;IAChC,IAAI,CAACC,MAAM,GAAGH,OAAO,CAACG,MAAM;IAC5B,IAAI,CAACC,KAAK,GAAGJ,OAAO,CAACI,KAAK;EAC5B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}