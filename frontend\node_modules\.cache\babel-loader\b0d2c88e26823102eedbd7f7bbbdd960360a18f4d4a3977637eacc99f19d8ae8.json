{"ast": null, "code": "import * as React from 'react';\nimport { getGridCellElement, getGridColumnHeaderElement, getGridRowElement } from \"../../../utils/domUtils.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { gridListColumnSelector } from \"../listView/gridListViewSelectors.js\";\nimport { gridRowNodeSelector } from \"./gridRowsSelector.js\";\nimport { getRowValue as getRowValueFn } from \"./gridRowsUtils.js\";\nexport class MissingRowIdError extends Error {}\n\n/**\n * @requires useGridColumns (method)\n * @requires useGridRows (method)\n * @requires useGridFocus (state)\n * @requires useGridEditing (method)\n * TODO: Impossible priority - useGridEditing also needs to be after useGridParamsApi\n * TODO: Impossible priority - useGridFocus also needs to be after useGridParamsApi\n */\nexport function useGridParamsApi(apiRef, props) {\n  const getColumnHeaderParams = React.useCallback(field => ({\n    field,\n    colDef: apiRef.current.getColumn(field)\n  }), [apiRef]);\n  const getRowParams = React.useCallback(id => {\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const params = {\n      id,\n      columns: apiRef.current.getAllColumns(),\n      row\n    };\n    return params;\n  }, [apiRef]);\n  const getCellParamsForRow = React.useCallback((id, field, row, {\n    cellMode,\n    colDef,\n    hasFocus,\n    rowNode,\n    tabIndex\n  }) => {\n    const rawValue = row[field];\n    const value = colDef?.valueGetter ? colDef.valueGetter(rawValue, row, colDef, apiRef) : rawValue;\n    const params = {\n      id,\n      field,\n      row,\n      rowNode,\n      colDef,\n      cellMode,\n      hasFocus,\n      tabIndex,\n      value,\n      formattedValue: value,\n      isEditable: false,\n      api: apiRef.current\n    };\n    if (colDef && colDef.valueFormatter) {\n      params.formattedValue = colDef.valueFormatter(value, row, colDef, apiRef);\n    }\n    params.isEditable = colDef && apiRef.current.isCellEditable(params);\n    return params;\n  }, [apiRef]);\n  const getCellParams = React.useCallback((id, field) => {\n    const row = apiRef.current.getRow(id);\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (!row || !rowNode) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const cellFocus = gridFocusCellSelector(apiRef);\n    const cellTabIndex = gridTabIndexCellSelector(apiRef);\n    const cellMode = apiRef.current.getCellMode(id, field);\n    return apiRef.current.getCellParamsForRow(id, field, row, {\n      colDef: props.listView && props.listViewColumn?.field === field ? gridListColumnSelector(apiRef) : apiRef.current.getColumn(field),\n      rowNode,\n      hasFocus: cellFocus !== null && cellFocus.field === field && cellFocus.id === id,\n      tabIndex: cellTabIndex && cellTabIndex.field === field && cellTabIndex.id === id ? 0 : -1,\n      cellMode\n    });\n  }, [apiRef, props.listView, props.listViewColumn?.field]);\n  const getCellValue = React.useCallback((id, field) => {\n    const colDef = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    if (!colDef || !colDef.valueGetter) {\n      return row[field];\n    }\n    return colDef.valueGetter(row[colDef.field], row, colDef, apiRef);\n  }, [apiRef]);\n  const getRowValue = React.useCallback((row, colDef) => getRowValueFn(row, colDef, apiRef), [apiRef]);\n  const getRowFormattedValue = React.useCallback((row, colDef) => {\n    const value = getRowValue(row, colDef);\n    if (!colDef || !colDef.valueFormatter) {\n      return value;\n    }\n    return colDef.valueFormatter(value, row, colDef, apiRef);\n  }, [apiRef, getRowValue]);\n  const getColumnHeaderElement = React.useCallback(field => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridColumnHeaderElement(apiRef.current.rootElementRef.current, field);\n  }, [apiRef]);\n  const getRowElement = React.useCallback(id => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridRowElement(apiRef.current.rootElementRef.current, id);\n  }, [apiRef]);\n  const getCellElement = React.useCallback((id, field) => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridCellElement(apiRef.current.rootElementRef.current, {\n      id,\n      field\n    });\n  }, [apiRef]);\n  const paramsApi = {\n    getCellValue,\n    getCellParams,\n    getCellElement,\n    getRowValue,\n    getRowFormattedValue,\n    getRowParams,\n    getRowElement,\n    getColumnHeaderParams,\n    getColumnHeaderElement\n  };\n  const paramsPrivateApi = {\n    getCellParamsForRow\n  };\n  useGridApiMethod(apiRef, paramsApi, 'public');\n  useGridApiMethod(apiRef, paramsPrivateApi, 'private');\n}", "map": {"version": 3, "names": ["React", "getGridCellElement", "getGridColumnHeaderElement", "getGridRowElement", "useGridApiMethod", "gridFocusCellSelector", "gridTabIndexCellSelector", "gridListColumnSelector", "gridRowNodeSelector", "getRowValue", "getRowValueFn", "MissingRowIdError", "Error", "useGridParamsApi", "apiRef", "props", "getColumnHeaderParams", "useCallback", "field", "colDef", "current", "getColumn", "getRowParams", "id", "row", "getRow", "params", "columns", "getAllColumns", "getCellParamsForRow", "cellMode", "hasFocus", "rowNode", "tabIndex", "rawValue", "value", "valueGetter", "formattedValue", "isEditable", "api", "valueFormatter", "isCellEditable", "getCellParams", "cellFocus", "cellTabIndex", "getCellMode", "listView", "listViewColumn", "getCellValue", "getRowFormattedValue", "getColumnHeaderElement", "rootElementRef", "getRowElement", "getCellElement", "params<PERSON><PERSON>", "paramsPrivateApi"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rows/useGridParamsApi.js"], "sourcesContent": ["import * as React from 'react';\nimport { getGridCellElement, getGridColumnHeaderElement, getGridRowElement } from \"../../../utils/domUtils.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridFocusCellSelector, gridTabIndexCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { gridListColumnSelector } from \"../listView/gridListViewSelectors.js\";\nimport { gridRowNodeSelector } from \"./gridRowsSelector.js\";\nimport { getRowValue as getRowValueFn } from \"./gridRowsUtils.js\";\nexport class MissingRowIdError extends Error {}\n\n/**\n * @requires useGridColumns (method)\n * @requires useGridRows (method)\n * @requires useGridFocus (state)\n * @requires useGridEditing (method)\n * TODO: Impossible priority - useGridEditing also needs to be after useGridParamsApi\n * TODO: Impossible priority - useGridFocus also needs to be after useGridParamsApi\n */\nexport function useGridParamsApi(apiRef, props) {\n  const getColumnHeaderParams = React.useCallback(field => ({\n    field,\n    colDef: apiRef.current.getColumn(field)\n  }), [apiRef]);\n  const getRowParams = React.useCallback(id => {\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const params = {\n      id,\n      columns: apiRef.current.getAllColumns(),\n      row\n    };\n    return params;\n  }, [apiRef]);\n  const getCellParamsForRow = React.useCallback((id, field, row, {\n    cellMode,\n    colDef,\n    hasFocus,\n    rowNode,\n    tabIndex\n  }) => {\n    const rawValue = row[field];\n    const value = colDef?.valueGetter ? colDef.valueGetter(rawValue, row, colDef, apiRef) : rawValue;\n    const params = {\n      id,\n      field,\n      row,\n      rowNode,\n      colDef,\n      cellMode,\n      hasFocus,\n      tabIndex,\n      value,\n      formattedValue: value,\n      isEditable: false,\n      api: apiRef.current\n    };\n    if (colDef && colDef.valueFormatter) {\n      params.formattedValue = colDef.valueFormatter(value, row, colDef, apiRef);\n    }\n    params.isEditable = colDef && apiRef.current.isCellEditable(params);\n    return params;\n  }, [apiRef]);\n  const getCellParams = React.useCallback((id, field) => {\n    const row = apiRef.current.getRow(id);\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (!row || !rowNode) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    const cellFocus = gridFocusCellSelector(apiRef);\n    const cellTabIndex = gridTabIndexCellSelector(apiRef);\n    const cellMode = apiRef.current.getCellMode(id, field);\n    return apiRef.current.getCellParamsForRow(id, field, row, {\n      colDef: props.listView && props.listViewColumn?.field === field ? gridListColumnSelector(apiRef) : apiRef.current.getColumn(field),\n      rowNode,\n      hasFocus: cellFocus !== null && cellFocus.field === field && cellFocus.id === id,\n      tabIndex: cellTabIndex && cellTabIndex.field === field && cellTabIndex.id === id ? 0 : -1,\n      cellMode\n    });\n  }, [apiRef, props.listView, props.listViewColumn?.field]);\n  const getCellValue = React.useCallback((id, field) => {\n    const colDef = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    if (!row) {\n      throw new MissingRowIdError(`No row with id #${id} found`);\n    }\n    if (!colDef || !colDef.valueGetter) {\n      return row[field];\n    }\n    return colDef.valueGetter(row[colDef.field], row, colDef, apiRef);\n  }, [apiRef]);\n  const getRowValue = React.useCallback((row, colDef) => getRowValueFn(row, colDef, apiRef), [apiRef]);\n  const getRowFormattedValue = React.useCallback((row, colDef) => {\n    const value = getRowValue(row, colDef);\n    if (!colDef || !colDef.valueFormatter) {\n      return value;\n    }\n    return colDef.valueFormatter(value, row, colDef, apiRef);\n  }, [apiRef, getRowValue]);\n  const getColumnHeaderElement = React.useCallback(field => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridColumnHeaderElement(apiRef.current.rootElementRef.current, field);\n  }, [apiRef]);\n  const getRowElement = React.useCallback(id => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridRowElement(apiRef.current.rootElementRef.current, id);\n  }, [apiRef]);\n  const getCellElement = React.useCallback((id, field) => {\n    if (!apiRef.current.rootElementRef.current) {\n      return null;\n    }\n    return getGridCellElement(apiRef.current.rootElementRef.current, {\n      id,\n      field\n    });\n  }, [apiRef]);\n  const paramsApi = {\n    getCellValue,\n    getCellParams,\n    getCellElement,\n    getRowValue,\n    getRowFormattedValue,\n    getRowParams,\n    getRowElement,\n    getColumnHeaderParams,\n    getColumnHeaderElement\n  };\n  const paramsPrivateApi = {\n    getCellParamsForRow\n  };\n  useGridApiMethod(apiRef, paramsApi, 'public');\n  useGridApiMethod(apiRef, paramsPrivateApi, 'private');\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,0BAA0B,EAAEC,iBAAiB,QAAQ,4BAA4B;AAC9G,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,qBAAqB,EAAEC,wBAAwB,QAAQ,oCAAoC;AACpG,SAASC,sBAAsB,QAAQ,sCAAsC;AAC7E,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,WAAW,IAAIC,aAAa,QAAQ,oBAAoB;AACjE,OAAO,MAAMC,iBAAiB,SAASC,KAAK,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC9C,MAAMC,qBAAqB,GAAGhB,KAAK,CAACiB,WAAW,CAACC,KAAK,KAAK;IACxDA,KAAK;IACLC,MAAM,EAAEL,MAAM,CAACM,OAAO,CAACC,SAAS,CAACH,KAAK;EACxC,CAAC,CAAC,EAAE,CAACJ,MAAM,CAAC,CAAC;EACb,MAAMQ,YAAY,GAAGtB,KAAK,CAACiB,WAAW,CAACM,EAAE,IAAI;IAC3C,MAAMC,GAAG,GAAGV,MAAM,CAACM,OAAO,CAACK,MAAM,CAACF,EAAE,CAAC;IACrC,IAAI,CAACC,GAAG,EAAE;MACR,MAAM,IAAIb,iBAAiB,CAAC,mBAAmBY,EAAE,QAAQ,CAAC;IAC5D;IACA,MAAMG,MAAM,GAAG;MACbH,EAAE;MACFI,OAAO,EAAEb,MAAM,CAACM,OAAO,CAACQ,aAAa,CAAC,CAAC;MACvCJ;IACF,CAAC;IACD,OAAOE,MAAM;EACf,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EACZ,MAAMe,mBAAmB,GAAG7B,KAAK,CAACiB,WAAW,CAAC,CAACM,EAAE,EAAEL,KAAK,EAAEM,GAAG,EAAE;IAC7DM,QAAQ;IACRX,MAAM;IACNY,QAAQ;IACRC,OAAO;IACPC;EACF,CAAC,KAAK;IACJ,MAAMC,QAAQ,GAAGV,GAAG,CAACN,KAAK,CAAC;IAC3B,MAAMiB,KAAK,GAAGhB,MAAM,EAAEiB,WAAW,GAAGjB,MAAM,CAACiB,WAAW,CAACF,QAAQ,EAAEV,GAAG,EAAEL,MAAM,EAAEL,MAAM,CAAC,GAAGoB,QAAQ;IAChG,MAAMR,MAAM,GAAG;MACbH,EAAE;MACFL,KAAK;MACLM,GAAG;MACHQ,OAAO;MACPb,MAAM;MACNW,QAAQ;MACRC,QAAQ;MACRE,QAAQ;MACRE,KAAK;MACLE,cAAc,EAAEF,KAAK;MACrBG,UAAU,EAAE,KAAK;MACjBC,GAAG,EAAEzB,MAAM,CAACM;IACd,CAAC;IACD,IAAID,MAAM,IAAIA,MAAM,CAACqB,cAAc,EAAE;MACnCd,MAAM,CAACW,cAAc,GAAGlB,MAAM,CAACqB,cAAc,CAACL,KAAK,EAAEX,GAAG,EAAEL,MAAM,EAAEL,MAAM,CAAC;IAC3E;IACAY,MAAM,CAACY,UAAU,GAAGnB,MAAM,IAAIL,MAAM,CAACM,OAAO,CAACqB,cAAc,CAACf,MAAM,CAAC;IACnE,OAAOA,MAAM;EACf,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EACZ,MAAM4B,aAAa,GAAG1C,KAAK,CAACiB,WAAW,CAAC,CAACM,EAAE,EAAEL,KAAK,KAAK;IACrD,MAAMM,GAAG,GAAGV,MAAM,CAACM,OAAO,CAACK,MAAM,CAACF,EAAE,CAAC;IACrC,MAAMS,OAAO,GAAGxB,mBAAmB,CAACM,MAAM,EAAES,EAAE,CAAC;IAC/C,IAAI,CAACC,GAAG,IAAI,CAACQ,OAAO,EAAE;MACpB,MAAM,IAAIrB,iBAAiB,CAAC,mBAAmBY,EAAE,QAAQ,CAAC;IAC5D;IACA,MAAMoB,SAAS,GAAGtC,qBAAqB,CAACS,MAAM,CAAC;IAC/C,MAAM8B,YAAY,GAAGtC,wBAAwB,CAACQ,MAAM,CAAC;IACrD,MAAMgB,QAAQ,GAAGhB,MAAM,CAACM,OAAO,CAACyB,WAAW,CAACtB,EAAE,EAAEL,KAAK,CAAC;IACtD,OAAOJ,MAAM,CAACM,OAAO,CAACS,mBAAmB,CAACN,EAAE,EAAEL,KAAK,EAAEM,GAAG,EAAE;MACxDL,MAAM,EAAEJ,KAAK,CAAC+B,QAAQ,IAAI/B,KAAK,CAACgC,cAAc,EAAE7B,KAAK,KAAKA,KAAK,GAAGX,sBAAsB,CAACO,MAAM,CAAC,GAAGA,MAAM,CAACM,OAAO,CAACC,SAAS,CAACH,KAAK,CAAC;MAClIc,OAAO;MACPD,QAAQ,EAAEY,SAAS,KAAK,IAAI,IAAIA,SAAS,CAACzB,KAAK,KAAKA,KAAK,IAAIyB,SAAS,CAACpB,EAAE,KAAKA,EAAE;MAChFU,QAAQ,EAAEW,YAAY,IAAIA,YAAY,CAAC1B,KAAK,KAAKA,KAAK,IAAI0B,YAAY,CAACrB,EAAE,KAAKA,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;MACzFO;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChB,MAAM,EAAEC,KAAK,CAAC+B,QAAQ,EAAE/B,KAAK,CAACgC,cAAc,EAAE7B,KAAK,CAAC,CAAC;EACzD,MAAM8B,YAAY,GAAGhD,KAAK,CAACiB,WAAW,CAAC,CAACM,EAAE,EAAEL,KAAK,KAAK;IACpD,MAAMC,MAAM,GAAGL,MAAM,CAACM,OAAO,CAACC,SAAS,CAACH,KAAK,CAAC;IAC9C,MAAMM,GAAG,GAAGV,MAAM,CAACM,OAAO,CAACK,MAAM,CAACF,EAAE,CAAC;IACrC,IAAI,CAACC,GAAG,EAAE;MACR,MAAM,IAAIb,iBAAiB,CAAC,mBAAmBY,EAAE,QAAQ,CAAC;IAC5D;IACA,IAAI,CAACJ,MAAM,IAAI,CAACA,MAAM,CAACiB,WAAW,EAAE;MAClC,OAAOZ,GAAG,CAACN,KAAK,CAAC;IACnB;IACA,OAAOC,MAAM,CAACiB,WAAW,CAACZ,GAAG,CAACL,MAAM,CAACD,KAAK,CAAC,EAAEM,GAAG,EAAEL,MAAM,EAAEL,MAAM,CAAC;EACnE,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAML,WAAW,GAAGT,KAAK,CAACiB,WAAW,CAAC,CAACO,GAAG,EAAEL,MAAM,KAAKT,aAAa,CAACc,GAAG,EAAEL,MAAM,EAAEL,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACpG,MAAMmC,oBAAoB,GAAGjD,KAAK,CAACiB,WAAW,CAAC,CAACO,GAAG,EAAEL,MAAM,KAAK;IAC9D,MAAMgB,KAAK,GAAG1B,WAAW,CAACe,GAAG,EAAEL,MAAM,CAAC;IACtC,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACqB,cAAc,EAAE;MACrC,OAAOL,KAAK;IACd;IACA,OAAOhB,MAAM,CAACqB,cAAc,CAACL,KAAK,EAAEX,GAAG,EAAEL,MAAM,EAAEL,MAAM,CAAC;EAC1D,CAAC,EAAE,CAACA,MAAM,EAAEL,WAAW,CAAC,CAAC;EACzB,MAAMyC,sBAAsB,GAAGlD,KAAK,CAACiB,WAAW,CAACC,KAAK,IAAI;IACxD,IAAI,CAACJ,MAAM,CAACM,OAAO,CAAC+B,cAAc,CAAC/B,OAAO,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,OAAOlB,0BAA0B,CAACY,MAAM,CAACM,OAAO,CAAC+B,cAAc,CAAC/B,OAAO,EAAEF,KAAK,CAAC;EACjF,CAAC,EAAE,CAACJ,MAAM,CAAC,CAAC;EACZ,MAAMsC,aAAa,GAAGpD,KAAK,CAACiB,WAAW,CAACM,EAAE,IAAI;IAC5C,IAAI,CAACT,MAAM,CAACM,OAAO,CAAC+B,cAAc,CAAC/B,OAAO,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,OAAOjB,iBAAiB,CAACW,MAAM,CAACM,OAAO,CAAC+B,cAAc,CAAC/B,OAAO,EAAEG,EAAE,CAAC;EACrE,CAAC,EAAE,CAACT,MAAM,CAAC,CAAC;EACZ,MAAMuC,cAAc,GAAGrD,KAAK,CAACiB,WAAW,CAAC,CAACM,EAAE,EAAEL,KAAK,KAAK;IACtD,IAAI,CAACJ,MAAM,CAACM,OAAO,CAAC+B,cAAc,CAAC/B,OAAO,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,OAAOnB,kBAAkB,CAACa,MAAM,CAACM,OAAO,CAAC+B,cAAc,CAAC/B,OAAO,EAAE;MAC/DG,EAAE;MACFL;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,MAAM,CAAC,CAAC;EACZ,MAAMwC,SAAS,GAAG;IAChBN,YAAY;IACZN,aAAa;IACbW,cAAc;IACd5C,WAAW;IACXwC,oBAAoB;IACpB3B,YAAY;IACZ8B,aAAa;IACbpC,qBAAqB;IACrBkC;EACF,CAAC;EACD,MAAMK,gBAAgB,GAAG;IACvB1B;EACF,CAAC;EACDzB,gBAAgB,CAACU,MAAM,EAAEwC,SAAS,EAAE,QAAQ,CAAC;EAC7ClD,gBAAgB,CAACU,MAAM,EAAEyC,gBAAgB,EAAE,SAAS,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}