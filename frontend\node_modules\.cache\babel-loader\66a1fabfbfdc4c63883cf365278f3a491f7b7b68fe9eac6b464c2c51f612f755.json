{"ast": null, "code": "function isSymbol(value) {\n  return typeof value === 'symbol';\n}\nexport { isSymbol };", "map": {"version": 3, "names": ["isSymbol", "value"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isSymbol.mjs"], "sourcesContent": ["function isSymbol(value) {\n    return typeof value === 'symbol';\n}\n\nexport { isSymbol };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ;AACpC;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}