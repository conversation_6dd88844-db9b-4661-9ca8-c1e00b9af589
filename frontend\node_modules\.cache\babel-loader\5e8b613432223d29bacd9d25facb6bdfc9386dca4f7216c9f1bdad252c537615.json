{"ast": null, "code": "// If no effect ran after this amount of time, we assume that the render was not committed by React\nconst CLEANUP_TIMER_LOOP_MILLIS = 1000;\nexport class TimerBasedCleanupTracking {\n  timeouts = (() => new Map())();\n  cleanupTimeout = (() => CLEANUP_TIMER_LOOP_MILLIS)();\n  constructor(timeout = CLEANUP_TIMER_LOOP_MILLIS) {\n    this.cleanupTimeout = timeout;\n  }\n  register(object, unsubscribe, unregisterToken) {\n    if (!this.timeouts) {\n      this.timeouts = new Map();\n    }\n    const timeout = setTimeout(() => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n      this.timeouts.delete(unregisterToken.cleanupToken);\n    }, this.cleanupTimeout);\n    this.timeouts.set(unregisterToken.cleanupToken, timeout);\n  }\n  unregister(unregisterToken) {\n    const timeout = this.timeouts.get(unregisterToken.cleanupToken);\n    if (timeout) {\n      this.timeouts.delete(unregisterToken.cleanupToken);\n      clearTimeout(timeout);\n    }\n  }\n  reset() {\n    if (this.timeouts) {\n      this.timeouts.forEach((value, key) => {\n        this.unregister({\n          cleanupToken: key\n        });\n      });\n      this.timeouts = undefined;\n    }\n  }\n}", "map": {"version": 3, "names": ["CLEANUP_TIMER_LOOP_MILLIS", "TimerBasedCleanupTracking", "timeouts", "Map", "cleanupTimeout", "constructor", "timeout", "register", "object", "unsubscribe", "unregisterToken", "setTimeout", "delete", "cleanupToken", "set", "unregister", "get", "clearTimeout", "reset", "for<PERSON>ach", "value", "key", "undefined"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/cleanupTracking/TimerBasedCleanupTracking.js"], "sourcesContent": ["// If no effect ran after this amount of time, we assume that the render was not committed by React\nconst CLEANUP_TIMER_LOOP_MILLIS = 1000;\nexport class TimerBasedCleanupTracking {\n  timeouts = (() => new Map())();\n  cleanupTimeout = (() => CLEANUP_TIMER_LOOP_MILLIS)();\n  constructor(timeout = CLEANUP_TIMER_LOOP_MILLIS) {\n    this.cleanupTimeout = timeout;\n  }\n  register(object, unsubscribe, unregisterToken) {\n    if (!this.timeouts) {\n      this.timeouts = new Map();\n    }\n    const timeout = setTimeout(() => {\n      if (typeof unsubscribe === 'function') {\n        unsubscribe();\n      }\n      this.timeouts.delete(unregisterToken.cleanupToken);\n    }, this.cleanupTimeout);\n    this.timeouts.set(unregisterToken.cleanupToken, timeout);\n  }\n  unregister(unregisterToken) {\n    const timeout = this.timeouts.get(unregisterToken.cleanupToken);\n    if (timeout) {\n      this.timeouts.delete(unregisterToken.cleanupToken);\n      clearTimeout(timeout);\n    }\n  }\n  reset() {\n    if (this.timeouts) {\n      this.timeouts.forEach((value, key) => {\n        this.unregister({\n          cleanupToken: key\n        });\n      });\n      this.timeouts = undefined;\n    }\n  }\n}"], "mappings": "AAAA;AACA,MAAMA,yBAAyB,GAAG,IAAI;AACtC,OAAO,MAAMC,yBAAyB,CAAC;EACrCC,QAAQ,GAAG,CAAC,MAAM,IAAIC,GAAG,CAAC,CAAC,EAAE,CAAC;EAC9BC,cAAc,GAAG,CAAC,MAAMJ,yBAAyB,EAAE,CAAC;EACpDK,WAAWA,CAACC,OAAO,GAAGN,yBAAyB,EAAE;IAC/C,IAAI,CAACI,cAAc,GAAGE,OAAO;EAC/B;EACAC,QAAQA,CAACC,MAAM,EAAEC,WAAW,EAAEC,eAAe,EAAE;IAC7C,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC3B;IACA,MAAMG,OAAO,GAAGK,UAAU,CAAC,MAAM;MAC/B,IAAI,OAAOF,WAAW,KAAK,UAAU,EAAE;QACrCA,WAAW,CAAC,CAAC;MACf;MACA,IAAI,CAACP,QAAQ,CAACU,MAAM,CAACF,eAAe,CAACG,YAAY,CAAC;IACpD,CAAC,EAAE,IAAI,CAACT,cAAc,CAAC;IACvB,IAAI,CAACF,QAAQ,CAACY,GAAG,CAACJ,eAAe,CAACG,YAAY,EAAEP,OAAO,CAAC;EAC1D;EACAS,UAAUA,CAACL,eAAe,EAAE;IAC1B,MAAMJ,OAAO,GAAG,IAAI,CAACJ,QAAQ,CAACc,GAAG,CAACN,eAAe,CAACG,YAAY,CAAC;IAC/D,IAAIP,OAAO,EAAE;MACX,IAAI,CAACJ,QAAQ,CAACU,MAAM,CAACF,eAAe,CAACG,YAAY,CAAC;MAClDI,YAAY,CAACX,OAAO,CAAC;IACvB;EACF;EACAY,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAAChB,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACiB,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QACpC,IAAI,CAACN,UAAU,CAAC;UACdF,YAAY,EAAEQ;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACnB,QAAQ,GAAGoB,SAAS;IAC3B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}