{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridDensitySelector } from \"./densitySelector.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nexport const densityStateInitializer = (state, props) => _extends({}, state, {\n  density: props.initialState?.density ?? props.density ?? 'standard'\n});\nexport const useGridDensity = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useDensity');\n  apiRef.current.registerControlState({\n    stateId: 'density',\n    propModel: props.density,\n    propOnChange: props.onDensityChange,\n    stateSelector: gridDensitySelector,\n    changeEvent: 'densityChange'\n  });\n  const setDensity = useEventCallback(newDensity => {\n    const currentDensity = gridDensitySelector(apiRef);\n    if (currentDensity === newDensity) {\n      return;\n    }\n    logger.debug(`Set grid density to ${newDensity}`);\n    apiRef.current.setState(state => _extends({}, state, {\n      density: newDensity\n    }));\n  });\n  const densityApi = {\n    setDensity\n  };\n  useGridApiMethod(apiRef, densityApi, 'public');\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedDensity = gridDensitySelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `density` is controlled\n    props.density != null ||\n    // Always export if the `density` has been initialized\n    props.initialState?.density != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      density: exportedDensity\n    });\n  }, [apiRef, props.density, props.initialState?.density]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredDensity = context.stateToRestore?.density ? context.stateToRestore.density : gridDensitySelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      density: restoredDensity\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  React.useEffect(() => {\n    if (props.density) {\n      apiRef.current.setDensity(props.density);\n    }\n  }, [apiRef, props.density]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useGridLogger", "useGridApiMethod", "gridDensitySelector", "useGridRegisterPipeProcessor", "densityStateInitializer", "state", "props", "density", "initialState", "useGridDensity", "apiRef", "logger", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onDensityChange", "stateSelector", "changeEvent", "setDensity", "newDensity", "currentDensity", "debug", "setState", "densityApi", "stateExportPreProcessing", "useCallback", "prevState", "context", "exportedDensity", "shouldExportRowCount", "exportOnlyDirtyModels", "stateRestorePreProcessing", "params", "restoredDensity", "stateToRestore", "useEffect"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/density/useGridDensity.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridDensitySelector } from \"./densitySelector.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nexport const densityStateInitializer = (state, props) => _extends({}, state, {\n  density: props.initialState?.density ?? props.density ?? 'standard'\n});\nexport const useGridDensity = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useDensity');\n  apiRef.current.registerControlState({\n    stateId: 'density',\n    propModel: props.density,\n    propOnChange: props.onDensityChange,\n    stateSelector: gridDensitySelector,\n    changeEvent: 'densityChange'\n  });\n  const setDensity = useEventCallback(newDensity => {\n    const currentDensity = gridDensitySelector(apiRef);\n    if (currentDensity === newDensity) {\n      return;\n    }\n    logger.debug(`Set grid density to ${newDensity}`);\n    apiRef.current.setState(state => _extends({}, state, {\n      density: newDensity\n    }));\n  });\n  const densityApi = {\n    setDensity\n  };\n  useGridApiMethod(apiRef, densityApi, 'public');\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedDensity = gridDensitySelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `density` is controlled\n    props.density != null ||\n    // Always export if the `density` has been initialized\n    props.initialState?.density != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      density: exportedDensity\n    });\n  }, [apiRef, props.density, props.initialState?.density]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredDensity = context.stateToRestore?.density ? context.stateToRestore.density : gridDensitySelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      density: restoredDensity\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  React.useEffect(() => {\n    if (props.density) {\n      apiRef.current.setDensity(props.density);\n    }\n  }, [apiRef, props.density]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAKT,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;EAC3EE,OAAO,EAAED,KAAK,CAACE,YAAY,EAAED,OAAO,IAAID,KAAK,CAACC,OAAO,IAAI;AAC3D,CAAC,CAAC;AACF,OAAO,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEJ,KAAK,KAAK;EAC/C,MAAMK,MAAM,GAAGX,aAAa,CAACU,MAAM,EAAE,YAAY,CAAC;EAClDA,MAAM,CAACE,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAET,KAAK,CAACC,OAAO;IACxBS,YAAY,EAAEV,KAAK,CAACW,eAAe;IACnCC,aAAa,EAAEhB,mBAAmB;IAClCiB,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGrB,gBAAgB,CAACsB,UAAU,IAAI;IAChD,MAAMC,cAAc,GAAGpB,mBAAmB,CAACQ,MAAM,CAAC;IAClD,IAAIY,cAAc,KAAKD,UAAU,EAAE;MACjC;IACF;IACAV,MAAM,CAACY,KAAK,CAAC,uBAAuBF,UAAU,EAAE,CAAC;IACjDX,MAAM,CAACE,OAAO,CAACY,QAAQ,CAACnB,KAAK,IAAIR,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;MACnDE,OAAO,EAAEc;IACX,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,MAAMI,UAAU,GAAG;IACjBL;EACF,CAAC;EACDnB,gBAAgB,CAACS,MAAM,EAAEe,UAAU,EAAE,QAAQ,CAAC;EAC9C,MAAMC,wBAAwB,GAAG5B,KAAK,CAAC6B,WAAW,CAAC,CAACC,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,eAAe,GAAG5B,mBAAmB,CAACQ,MAAM,CAAC;IACnD,MAAMqB,oBAAoB;IAC1B;IACA,CAACF,OAAO,CAACG,qBAAqB;IAC9B;IACA1B,KAAK,CAACC,OAAO,IAAI,IAAI;IACrB;IACAD,KAAK,CAACE,YAAY,EAAED,OAAO,IAAI,IAAI;IACnC,IAAI,CAACwB,oBAAoB,EAAE;MACzB,OAAOH,SAAS;IAClB;IACA,OAAO/B,QAAQ,CAAC,CAAC,CAAC,EAAE+B,SAAS,EAAE;MAC7BrB,OAAO,EAAEuB;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpB,MAAM,EAAEJ,KAAK,CAACC,OAAO,EAAED,KAAK,CAACE,YAAY,EAAED,OAAO,CAAC,CAAC;EACxD,MAAM0B,yBAAyB,GAAGnC,KAAK,CAAC6B,WAAW,CAAC,CAACO,MAAM,EAAEL,OAAO,KAAK;IACvE,MAAMM,eAAe,GAAGN,OAAO,CAACO,cAAc,EAAE7B,OAAO,GAAGsB,OAAO,CAACO,cAAc,CAAC7B,OAAO,GAAGL,mBAAmB,CAACQ,MAAM,CAAC;IACtHA,MAAM,CAACE,OAAO,CAACY,QAAQ,CAACnB,KAAK,IAAIR,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;MACnDE,OAAO,EAAE4B;IACX,CAAC,CAAC,CAAC;IACH,OAAOD,MAAM;EACf,CAAC,EAAE,CAACxB,MAAM,CAAC,CAAC;EACZP,4BAA4B,CAACO,MAAM,EAAE,aAAa,EAAEgB,wBAAwB,CAAC;EAC7EvB,4BAA4B,CAACO,MAAM,EAAE,cAAc,EAAEuB,yBAAyB,CAAC;EAC/EnC,KAAK,CAACuC,SAAS,CAAC,MAAM;IACpB,IAAI/B,KAAK,CAACC,OAAO,EAAE;MACjBG,MAAM,CAACE,OAAO,CAACQ,UAAU,CAACd,KAAK,CAACC,OAAO,CAAC;IAC1C;EACF,CAAC,EAAE,CAACG,MAAM,EAAEJ,KAAK,CAACC,OAAO,CAAC,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}