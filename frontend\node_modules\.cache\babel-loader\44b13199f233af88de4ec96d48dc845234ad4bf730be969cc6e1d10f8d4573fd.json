{"ast": null, "code": "import { flattenDeep } from './flattenDeep.mjs';\nfunction flatMapDeep(arr, iteratee) {\n  return flattenDeep(arr.map(item => iteratee(item)));\n}\nexport { flatMapDeep };", "map": {"version": 3, "names": ["flattenDeep", "flatMapDeep", "arr", "iteratee", "map", "item"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/flatMapDeep.mjs"], "sourcesContent": ["import { flattenDeep } from './flattenDeep.mjs';\n\nfunction flatMapDeep(arr, iteratee) {\n    return flattenDeep(arr.map((item) => iteratee(item)));\n}\n\nexport { flatMapDeep };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAE/C,SAASC,WAAWA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EAChC,OAAOH,WAAW,CAACE,GAAG,CAACE,GAAG,CAAEC,IAAI,IAAKF,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;AACzD;AAEA,SAASJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}