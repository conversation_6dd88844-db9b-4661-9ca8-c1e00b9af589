{"ast": null, "code": "import { createRootSelector } from \"../../utils/createSelector.js\";\n/**\n * Get the theme state\n * @category Core\n */\nexport const gridIsRtlSelector = createRootSelector(state => state.isRtl);", "map": {"version": 3, "names": ["createRootSelector", "gridIsRtlSelector", "state", "isRtl"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/core/gridCoreSelector.js"], "sourcesContent": ["import { createRootSelector } from \"../../utils/createSelector.js\";\n/**\n * Get the theme state\n * @category Core\n */\nexport const gridIsRtlSelector = createRootSelector(state => state.isRtl);"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,+BAA+B;AAClE;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGD,kBAAkB,CAACE,KAAK,IAAIA,KAAK,CAACC,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}