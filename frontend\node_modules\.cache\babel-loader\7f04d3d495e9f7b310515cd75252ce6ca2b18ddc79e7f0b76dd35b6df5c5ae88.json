{"ast": null, "code": "import useLazyRef from '@mui/utils/useLazyRef';\nimport useOnMount from '@mui/utils/useOnMount';\nconst noop = () => {};\n\n/**\n * An Effect implementation for the Store. This should be used for side-effects only. To\n * compute and store derived state, use `createSelectorMemoized` instead.\n */\nexport function useStoreEffect(store, selector, effect) {\n  const instance = useLazyRef(initialize, {\n    store,\n    selector\n  }).current;\n  instance.effect = effect;\n  useOnMount(instance.onMount);\n}\n\n// `useLazyRef` typings are incorrect, `params` should not be optional\nfunction initialize(params) {\n  const {\n    store,\n    selector\n  } = params;\n  let previousState = selector(store.state);\n  const instance = {\n    effect: noop,\n    dispose: null,\n    // We want a single subscription done right away and cleared on unmount only,\n    // but React triggers `useOnMount` multiple times in dev, so we need to manage\n    // the subscription anyway.\n    subscribe: () => {\n      instance.dispose ??= store.subscribe(state => {\n        const nextState = selector(state);\n        instance.effect(previousState, nextState);\n        previousState = nextState;\n      });\n    },\n    onMount: () => {\n      instance.subscribe();\n      return () => {\n        instance.dispose?.();\n        instance.dispose = null;\n      };\n    }\n  };\n  instance.subscribe();\n  return instance;\n}", "map": {"version": 3, "names": ["useLazyRef", "useOnMount", "noop", "useStoreEffect", "store", "selector", "effect", "instance", "initialize", "current", "onMount", "params", "previousState", "state", "dispose", "subscribe", "nextState"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/store/useStoreEffect.js"], "sourcesContent": ["import useLazyRef from '@mui/utils/useLazyRef';\nimport useOnMount from '@mui/utils/useOnMount';\nconst noop = () => {};\n\n/**\n * An Effect implementation for the Store. This should be used for side-effects only. To\n * compute and store derived state, use `createSelectorMemoized` instead.\n */\nexport function useStoreEffect(store, selector, effect) {\n  const instance = useLazyRef(initialize, {\n    store,\n    selector\n  }).current;\n  instance.effect = effect;\n  useOnMount(instance.onMount);\n}\n\n// `useLazyRef` typings are incorrect, `params` should not be optional\nfunction initialize(params) {\n  const {\n    store,\n    selector\n  } = params;\n  let previousState = selector(store.state);\n  const instance = {\n    effect: noop,\n    dispose: null,\n    // We want a single subscription done right away and cleared on unmount only,\n    // but React triggers `useOnMount` multiple times in dev, so we need to manage\n    // the subscription anyway.\n    subscribe: () => {\n      instance.dispose ??= store.subscribe(state => {\n        const nextState = selector(state);\n        instance.effect(previousState, nextState);\n        previousState = nextState;\n      });\n    },\n    onMount: () => {\n      instance.subscribe();\n      return () => {\n        instance.dispose?.();\n        instance.dispose = null;\n      };\n    }\n  };\n  instance.subscribe();\n  return instance;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;;AAErB;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACtD,MAAMC,QAAQ,GAAGP,UAAU,CAACQ,UAAU,EAAE;IACtCJ,KAAK;IACLC;EACF,CAAC,CAAC,CAACI,OAAO;EACVF,QAAQ,CAACD,MAAM,GAAGA,MAAM;EACxBL,UAAU,CAACM,QAAQ,CAACG,OAAO,CAAC;AAC9B;;AAEA;AACA,SAASF,UAAUA,CAACG,MAAM,EAAE;EAC1B,MAAM;IACJP,KAAK;IACLC;EACF,CAAC,GAAGM,MAAM;EACV,IAAIC,aAAa,GAAGP,QAAQ,CAACD,KAAK,CAACS,KAAK,CAAC;EACzC,MAAMN,QAAQ,GAAG;IACfD,MAAM,EAAEJ,IAAI;IACZY,OAAO,EAAE,IAAI;IACb;IACA;IACA;IACAC,SAAS,EAAEA,CAAA,KAAM;MACfR,QAAQ,CAACO,OAAO,KAAKV,KAAK,CAACW,SAAS,CAACF,KAAK,IAAI;QAC5C,MAAMG,SAAS,GAAGX,QAAQ,CAACQ,KAAK,CAAC;QACjCN,QAAQ,CAACD,MAAM,CAACM,aAAa,EAAEI,SAAS,CAAC;QACzCJ,aAAa,GAAGI,SAAS;MAC3B,CAAC,CAAC;IACJ,CAAC;IACDN,OAAO,EAAEA,CAAA,KAAM;MACbH,QAAQ,CAACQ,SAAS,CAAC,CAAC;MACpB,OAAO,MAAM;QACXR,QAAQ,CAACO,OAAO,GAAG,CAAC;QACpBP,QAAQ,CAACO,OAAO,GAAG,IAAI;MACzB,CAAC;IACH;EACF,CAAC;EACDP,QAAQ,CAACQ,SAAS,CAAC,CAAC;EACpB,OAAOR,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}