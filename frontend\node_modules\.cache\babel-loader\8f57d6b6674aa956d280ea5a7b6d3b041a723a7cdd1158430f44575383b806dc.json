{"ast": null, "code": "import { lruMemoize, createSelectorCreator } from 'reselect';\n/* eslint-disable no-underscore-dangle */ // __cacheKey__\n\nconst reselectCreateSelector = createSelectorCreator({\n  memoize: lruMemoize,\n  memoizeOptions: {\n    maxSize: 1,\n    equalityCheck: Object.is\n  }\n});\n/* eslint-disable id-denylist */\nexport const createSelector = (a, b, c, d, e, f, ...other) => {\n  if (other.length > 0) {\n    throw new Error('Unsupported number of selectors');\n  }\n  let selector;\n  if (a && b && c && d && e && f) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      const vd = d(state, a1, a2, a3);\n      const ve = e(state, a1, a2, a3);\n      return f(va, vb, vc, vd, ve, a1, a2, a3);\n    };\n  } else if (a && b && c && d && e) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      const vd = d(state, a1, a2, a3);\n      return e(va, vb, vc, vd, a1, a2, a3);\n    };\n  } else if (a && b && c && d) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      return d(va, vb, vc, a1, a2, a3);\n    };\n  } else if (a && b && c) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      return c(va, vb, a1, a2, a3);\n    };\n  } else if (a && b) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      return b(va, a1, a2, a3);\n    };\n  } else if (a) {\n    selector = a;\n  } else {\n    throw new Error('Missing arguments');\n  }\n  return selector;\n};\n/* eslint-enable id-denylist */\n\nexport const createSelectorMemoized = (...inputs) => {\n  const cache = new WeakMap();\n  let nextCacheId = 1;\n  const combiner = inputs[inputs.length - 1];\n  const nSelectors = inputs.length - 1 || 1;\n  // (s1, s2, ..., sN, a1, a2, a3) => { ... }\n  const argsLength = Math.max(combiner.length - nSelectors, 0);\n  if (argsLength > 3) {\n    throw new Error('Unsupported number of arguments');\n  }\n\n  // prettier-ignore\n  const selector = (state, a1, a2, a3) => {\n    let cacheKey = state.__cacheKey__;\n    if (!cacheKey) {\n      cacheKey = {\n        id: nextCacheId\n      };\n      state.__cacheKey__ = cacheKey;\n      nextCacheId += 1;\n    }\n    let fn = cache.get(cacheKey);\n    if (!fn) {\n      const selectors = inputs.length === 1 ? [x => x, combiner] : inputs;\n      let reselectArgs = inputs;\n      const selectorArgs = [undefined, undefined, undefined];\n      switch (argsLength) {\n        case 0:\n          break;\n        case 1:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], combiner];\n            break;\n          }\n        case 2:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], () => selectorArgs[1], combiner];\n            break;\n          }\n        case 3:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], () => selectorArgs[1], () => selectorArgs[2], combiner];\n            break;\n          }\n        default:\n          throw new Error('Unsupported number of arguments');\n      }\n      fn = reselectCreateSelector(...reselectArgs);\n      fn.selectorArgs = selectorArgs;\n      cache.set(cacheKey, fn);\n    }\n\n    /* eslint-disable no-fallthrough */\n\n    switch (argsLength) {\n      case 3:\n        fn.selectorArgs[2] = a3;\n      case 2:\n        fn.selectorArgs[1] = a2;\n      case 1:\n        fn.selectorArgs[0] = a1;\n      case 0:\n      default:\n    }\n    switch (argsLength) {\n      case 0:\n        return fn(state);\n      case 1:\n        return fn(state, a1);\n      case 2:\n        return fn(state, a1, a2);\n      case 3:\n        return fn(state, a1, a2, a3);\n      default:\n        throw new Error('unreachable');\n    }\n  };\n  return selector;\n};", "map": {"version": 3, "names": ["lruMemoize", "createSelectorCreator", "reselectCreateSelector", "memoize", "memoizeOptions", "maxSize", "equalityCheck", "Object", "is", "createSelector", "a", "b", "c", "d", "e", "f", "other", "length", "Error", "selector", "state", "a1", "a2", "a3", "va", "vb", "vc", "vd", "ve", "createSelectorMemoized", "inputs", "cache", "WeakMap", "nextCacheId", "combiner", "nSelectors", "arg<PERSON><PERSON><PERSON><PERSON>", "Math", "max", "cache<PERSON>ey", "__cacheKey__", "id", "fn", "get", "selectors", "x", "reselectArgs", "selectorArgs", "undefined", "slice", "set"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/store/createSelector.js"], "sourcesContent": ["import { lruMemoize, createSelectorCreator } from 'reselect';\n/* eslint-disable no-underscore-dangle */ // __cacheKey__\n\nconst reselectCreateSelector = createSelectorCreator({\n  memoize: lruMemoize,\n  memoizeOptions: {\n    maxSize: 1,\n    equalityCheck: Object.is\n  }\n});\n/* eslint-disable id-denylist */\nexport const createSelector = (a, b, c, d, e, f, ...other) => {\n  if (other.length > 0) {\n    throw new Error('Unsupported number of selectors');\n  }\n  let selector;\n  if (a && b && c && d && e && f) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      const vd = d(state, a1, a2, a3);\n      const ve = e(state, a1, a2, a3);\n      return f(va, vb, vc, vd, ve, a1, a2, a3);\n    };\n  } else if (a && b && c && d && e) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      const vd = d(state, a1, a2, a3);\n      return e(va, vb, vc, vd, a1, a2, a3);\n    };\n  } else if (a && b && c && d) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      const vc = c(state, a1, a2, a3);\n      return d(va, vb, vc, a1, a2, a3);\n    };\n  } else if (a && b && c) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      const vb = b(state, a1, a2, a3);\n      return c(va, vb, a1, a2, a3);\n    };\n  } else if (a && b) {\n    selector = (state, a1, a2, a3) => {\n      const va = a(state, a1, a2, a3);\n      return b(va, a1, a2, a3);\n    };\n  } else if (a) {\n    selector = a;\n  } else {\n    throw new Error('Missing arguments');\n  }\n  return selector;\n};\n/* eslint-enable id-denylist */\n\nexport const createSelectorMemoized = (...inputs) => {\n  const cache = new WeakMap();\n  let nextCacheId = 1;\n  const combiner = inputs[inputs.length - 1];\n  const nSelectors = inputs.length - 1 || 1;\n  // (s1, s2, ..., sN, a1, a2, a3) => { ... }\n  const argsLength = Math.max(combiner.length - nSelectors, 0);\n  if (argsLength > 3) {\n    throw new Error('Unsupported number of arguments');\n  }\n\n  // prettier-ignore\n  const selector = (state, a1, a2, a3) => {\n    let cacheKey = state.__cacheKey__;\n    if (!cacheKey) {\n      cacheKey = {\n        id: nextCacheId\n      };\n      state.__cacheKey__ = cacheKey;\n      nextCacheId += 1;\n    }\n    let fn = cache.get(cacheKey);\n    if (!fn) {\n      const selectors = inputs.length === 1 ? [x => x, combiner] : inputs;\n      let reselectArgs = inputs;\n      const selectorArgs = [undefined, undefined, undefined];\n      switch (argsLength) {\n        case 0:\n          break;\n        case 1:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], combiner];\n            break;\n          }\n        case 2:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], () => selectorArgs[1], combiner];\n            break;\n          }\n        case 3:\n          {\n            reselectArgs = [...selectors.slice(0, -1), () => selectorArgs[0], () => selectorArgs[1], () => selectorArgs[2], combiner];\n            break;\n          }\n        default:\n          throw new Error('Unsupported number of arguments');\n      }\n      fn = reselectCreateSelector(...reselectArgs);\n      fn.selectorArgs = selectorArgs;\n      cache.set(cacheKey, fn);\n    }\n\n    /* eslint-disable no-fallthrough */\n\n    switch (argsLength) {\n      case 3:\n        fn.selectorArgs[2] = a3;\n      case 2:\n        fn.selectorArgs[1] = a2;\n      case 1:\n        fn.selectorArgs[0] = a1;\n      case 0:\n      default:\n    }\n    switch (argsLength) {\n      case 0:\n        return fn(state);\n      case 1:\n        return fn(state, a1);\n      case 2:\n        return fn(state, a1, a2);\n      case 3:\n        return fn(state, a1, a2, a3);\n      default:\n        throw new Error('unreachable');\n    }\n  };\n  return selector;\n};"], "mappings": "AAAA,SAASA,UAAU,EAAEC,qBAAqB,QAAQ,UAAU;AAC5D,0CAA0C;;AAE1C,MAAMC,sBAAsB,GAAGD,qBAAqB,CAAC;EACnDE,OAAO,EAAEH,UAAU;EACnBI,cAAc,EAAE;IACdC,OAAO,EAAE,CAAC;IACVC,aAAa,EAAEC,MAAM,CAACC;EACxB;AACF,CAAC,CAAC;AACF;AACA,OAAO,MAAMC,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGC,KAAK,KAAK;EAC5D,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,IAAIC,QAAQ;EACZ,IAAIT,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAAC,EAAE;IAC9BI,QAAQ,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;MAChC,MAAMC,EAAE,GAAGd,CAAC,CAACU,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAME,EAAE,GAAGd,CAAC,CAACS,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAMG,EAAE,GAAGd,CAAC,CAACQ,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAMI,EAAE,GAAGd,CAAC,CAACO,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAMK,EAAE,GAAGd,CAAC,CAACM,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,OAAOR,CAAC,CAACS,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEP,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC1C,CAAC;EACH,CAAC,MAAM,IAAIb,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAAC,EAAE;IAChCK,QAAQ,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;MAChC,MAAMC,EAAE,GAAGd,CAAC,CAACU,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAME,EAAE,GAAGd,CAAC,CAACS,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAMG,EAAE,GAAGd,CAAC,CAACQ,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAMI,EAAE,GAAGd,CAAC,CAACO,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,OAAOT,CAAC,CAACU,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEN,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IACtC,CAAC;EACH,CAAC,MAAM,IAAIb,CAAC,IAAIC,CAAC,IAAIC,CAAC,IAAIC,CAAC,EAAE;IAC3BM,QAAQ,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;MAChC,MAAMC,EAAE,GAAGd,CAAC,CAACU,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAME,EAAE,GAAGd,CAAC,CAACS,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAMG,EAAE,GAAGd,CAAC,CAACQ,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,OAAOV,CAAC,CAACW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEL,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAClC,CAAC;EACH,CAAC,MAAM,IAAIb,CAAC,IAAIC,CAAC,IAAIC,CAAC,EAAE;IACtBO,QAAQ,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;MAChC,MAAMC,EAAE,GAAGd,CAAC,CAACU,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,MAAME,EAAE,GAAGd,CAAC,CAACS,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,OAAOX,CAAC,CAACY,EAAE,EAAEC,EAAE,EAAEJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC9B,CAAC;EACH,CAAC,MAAM,IAAIb,CAAC,IAAIC,CAAC,EAAE;IACjBQ,QAAQ,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;MAChC,MAAMC,EAAE,GAAGd,CAAC,CAACU,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC/B,OAAOZ,CAAC,CAACa,EAAE,EAAEH,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC1B,CAAC;EACH,CAAC,MAAM,IAAIb,CAAC,EAAE;IACZS,QAAQ,GAAGT,CAAC;EACd,CAAC,MAAM;IACL,MAAM,IAAIQ,KAAK,CAAC,mBAAmB,CAAC;EACtC;EACA,OAAOC,QAAQ;AACjB,CAAC;AACD;;AAEA,OAAO,MAAMU,sBAAsB,GAAGA,CAAC,GAAGC,MAAM,KAAK;EACnD,MAAMC,KAAK,GAAG,IAAIC,OAAO,CAAC,CAAC;EAC3B,IAAIC,WAAW,GAAG,CAAC;EACnB,MAAMC,QAAQ,GAAGJ,MAAM,CAACA,MAAM,CAACb,MAAM,GAAG,CAAC,CAAC;EAC1C,MAAMkB,UAAU,GAAGL,MAAM,CAACb,MAAM,GAAG,CAAC,IAAI,CAAC;EACzC;EACA,MAAMmB,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAACjB,MAAM,GAAGkB,UAAU,EAAE,CAAC,CAAC;EAC5D,IAAIC,UAAU,GAAG,CAAC,EAAE;IAClB,MAAM,IAAIlB,KAAK,CAAC,iCAAiC,CAAC;EACpD;;EAEA;EACA,MAAMC,QAAQ,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;IACtC,IAAIgB,QAAQ,GAAGnB,KAAK,CAACoB,YAAY;IACjC,IAAI,CAACD,QAAQ,EAAE;MACbA,QAAQ,GAAG;QACTE,EAAE,EAAER;MACN,CAAC;MACDb,KAAK,CAACoB,YAAY,GAAGD,QAAQ;MAC7BN,WAAW,IAAI,CAAC;IAClB;IACA,IAAIS,EAAE,GAAGX,KAAK,CAACY,GAAG,CAACJ,QAAQ,CAAC;IAC5B,IAAI,CAACG,EAAE,EAAE;MACP,MAAME,SAAS,GAAGd,MAAM,CAACb,MAAM,KAAK,CAAC,GAAG,CAAC4B,CAAC,IAAIA,CAAC,EAAEX,QAAQ,CAAC,GAAGJ,MAAM;MACnE,IAAIgB,YAAY,GAAGhB,MAAM;MACzB,MAAMiB,YAAY,GAAG,CAACC,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC;MACtD,QAAQZ,UAAU;QAChB,KAAK,CAAC;UACJ;QACF,KAAK,CAAC;UACJ;YACEU,YAAY,GAAG,CAAC,GAAGF,SAAS,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,MAAMF,YAAY,CAAC,CAAC,CAAC,EAAEb,QAAQ,CAAC;YAC3E;UACF;QACF,KAAK,CAAC;UACJ;YACEY,YAAY,GAAG,CAAC,GAAGF,SAAS,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,MAAMF,YAAY,CAAC,CAAC,CAAC,EAAE,MAAMA,YAAY,CAAC,CAAC,CAAC,EAAEb,QAAQ,CAAC;YAClG;UACF;QACF,KAAK,CAAC;UACJ;YACEY,YAAY,GAAG,CAAC,GAAGF,SAAS,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,MAAMF,YAAY,CAAC,CAAC,CAAC,EAAE,MAAMA,YAAY,CAAC,CAAC,CAAC,EAAE,MAAMA,YAAY,CAAC,CAAC,CAAC,EAAEb,QAAQ,CAAC;YACzH;UACF;QACF;UACE,MAAM,IAAIhB,KAAK,CAAC,iCAAiC,CAAC;MACtD;MACAwB,EAAE,GAAGxC,sBAAsB,CAAC,GAAG4C,YAAY,CAAC;MAC5CJ,EAAE,CAACK,YAAY,GAAGA,YAAY;MAC9BhB,KAAK,CAACmB,GAAG,CAACX,QAAQ,EAAEG,EAAE,CAAC;IACzB;;IAEA;;IAEA,QAAQN,UAAU;MAChB,KAAK,CAAC;QACJM,EAAE,CAACK,YAAY,CAAC,CAAC,CAAC,GAAGxB,EAAE;MACzB,KAAK,CAAC;QACJmB,EAAE,CAACK,YAAY,CAAC,CAAC,CAAC,GAAGzB,EAAE;MACzB,KAAK,CAAC;QACJoB,EAAE,CAACK,YAAY,CAAC,CAAC,CAAC,GAAG1B,EAAE;MACzB,KAAK,CAAC;MACN;IACF;IACA,QAAQe,UAAU;MAChB,KAAK,CAAC;QACJ,OAAOM,EAAE,CAACtB,KAAK,CAAC;MAClB,KAAK,CAAC;QACJ,OAAOsB,EAAE,CAACtB,KAAK,EAAEC,EAAE,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOqB,EAAE,CAACtB,KAAK,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC1B,KAAK,CAAC;QACJ,OAAOoB,EAAE,CAACtB,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MAC9B;QACE,MAAM,IAAIL,KAAK,CAAC,aAAa,CAAC;IAClC;EACF,CAAC;EACD,OAAOC,QAAQ;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}