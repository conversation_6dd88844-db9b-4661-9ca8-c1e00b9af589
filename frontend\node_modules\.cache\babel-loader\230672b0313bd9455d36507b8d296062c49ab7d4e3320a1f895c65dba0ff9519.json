{"ast": null, "code": "function getTag(value) {\n  if (value == null) {\n    return value === undefined ? '[object Undefined]' : '[object Null]';\n  }\n  return Object.prototype.toString.call(value);\n}\nexport { getTag };", "map": {"version": 3, "names": ["getTag", "value", "undefined", "Object", "prototype", "toString", "call"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/compat/_internal/getTag.mjs"], "sourcesContent": ["function getTag(value) {\n    if (value == null) {\n        return value === undefined ? '[object Undefined]' : '[object Null]';\n    }\n    return Object.prototype.toString.call(value);\n}\n\nexport { getTag };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,KAAK,EAAE;EACnB,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOA,KAAK,KAAKC,SAAS,GAAG,oBAAoB,GAAG,eAAe;EACvE;EACA,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC;AAChD;AAEA,SAASD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}