{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { lruMemoize } from '@mui/x-internals/lruMemoize';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useLazyRef } from \"../../utils/useLazyRef.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/gridPreferencePanelsValue.js\";\nimport { defaultGridFilterLookup, getDefaultGridFilterModel } from \"./gridFilterState.js\";\nimport { gridFilterModelSelector } from \"./gridFilterSelector.js\";\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nimport { gridRowsLookupSelector } from \"../rows/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/index.js\";\nimport { buildAggregatedFilterApplier, sanitizeFilterModel, mergeStateWithFilterModel, cleanFilterItem, passFilterLogic, shouldQuickFilterExcludeHiddenColumns } from \"./gridFilterUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const filterStateInitializer = (state, props, apiRef) => {\n  const filterModel = props.filterModel ?? props.initialState?.filter?.filterModel ?? getDefaultGridFilterModel();\n  return _extends({}, state, {\n    filter: _extends({\n      filterModel: sanitizeFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef)\n    }, defaultGridFilterLookup),\n    visibleRowsLookup: {}\n  });\n};\nconst getVisibleRowsLookup = params => {\n  // For flat tree, the `visibleRowsLookup` and the `filteredRowsLookup` are equals since no row is collapsed.\n  return params.filteredRowsLookup;\n};\nfunction getVisibleRowsLookupState(apiRef, state) {\n  return apiRef.current.applyStrategyProcessor('visibleRowsLookupCreation', {\n    tree: state.rows.tree,\n    filteredRowsLookup: state.filter.filteredRowsLookup\n  });\n}\nfunction createMemoizedValues() {\n  return lruMemoize(Object.values);\n}\n\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n * @requires useGridRows (event)\n */\nexport const useGridFilter = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFilter');\n  apiRef.current.registerControlState({\n    stateId: 'filter',\n    propModel: props.filterModel,\n    propOnChange: props.onFilterModelChange,\n    stateSelector: gridFilterModelSelector,\n    changeEvent: 'filterModelChange'\n  });\n  const updateFilteredRows = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterState = apiRef.current.getFilterState(filterModel);\n      const newState = _extends({}, state, {\n        filter: _extends({}, state.filter, filterState)\n      });\n      const visibleRowsLookupState = getVisibleRowsLookupState(apiRef, newState);\n      return _extends({}, newState, {\n        visibleRowsLookup: visibleRowsLookupState\n      });\n    });\n    apiRef.current.publishEvent('filteredRowsSet');\n  }, [apiRef]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.filterable === false || props.disableColumnFilter) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuFilterItem'];\n  }, [props.disableColumnFilter]);\n\n  /**\n   * API METHODS\n   */\n  const upsertFilterItem = React.useCallback(item => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = [...filterModel.items];\n    const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n    if (itemIndex === -1) {\n      items.push(item);\n    } else {\n      items[itemIndex] = item;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItem');\n  }, [apiRef]);\n  const upsertFilterItems = React.useCallback(items => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const existingItems = [...filterModel.items];\n    items.forEach(item => {\n      const itemIndex = existingItems.findIndex(filterItem => filterItem.id === item.id);\n      if (itemIndex === -1) {\n        existingItems.push(item);\n      } else {\n        existingItems[itemIndex] = item;\n      }\n    });\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items: existingItems\n    }), 'upsertFilterItems');\n  }, [apiRef]);\n  const deleteFilterItem = React.useCallback(itemToDelete => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = filterModel.items.filter(item => item.id !== itemToDelete.id);\n    if (items.length === filterModel.items.length) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'deleteFilterItem');\n  }, [apiRef]);\n  const showFilterPanel = React.useCallback((targetColumnField, panelId, labelId) => {\n    logger.debug('Displaying filter panel');\n    if (targetColumnField) {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterItemsWithValue = filterModel.items.filter(item => {\n        if (item.value !== undefined) {\n          // Some filters like `isAnyOf` support array as `item.value`.\n          // If array is empty, we want to remove it from the filter model.\n          if (Array.isArray(item.value) && item.value.length === 0) {\n            return false;\n          }\n          return true;\n        }\n        const column = apiRef.current.getColumn(item.field);\n        const filterOperator = column.filterOperators?.find(operator => operator.value === item.operator);\n        const requiresFilterValue = typeof filterOperator?.requiresFilterValue === 'undefined' ? true : filterOperator?.requiresFilterValue;\n\n        // Operators like `isEmpty` don't have and don't require `item.value`.\n        // So we don't want to remove them from the filter model if `item.value === undefined`.\n        // See https://github.com/mui/mui-x/issues/5402\n        if (requiresFilterValue) {\n          return false;\n        }\n        return true;\n      });\n      let newFilterItems;\n      const filterItemOnTarget = filterItemsWithValue.find(item => item.field === targetColumnField);\n      const targetColumn = apiRef.current.getColumn(targetColumnField);\n      if (filterItemOnTarget) {\n        newFilterItems = filterItemsWithValue;\n      } else if (props.disableMultipleColumnsFiltering) {\n        newFilterItems = [cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      } else {\n        newFilterItems = [...filterItemsWithValue, cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      }\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n    apiRef.current.showPreferences(GridPreferencePanelsValue.filters, panelId, labelId);\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const hideFilterPanel = React.useCallback(() => {\n    logger.debug('Hiding filter panel');\n    apiRef.current.hidePreferences();\n  }, [apiRef, logger]);\n  const setFilterLogicOperator = React.useCallback(logicOperator => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.logicOperator === logicOperator) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      logicOperator\n    }), 'changeLogicOperator');\n  }, [apiRef]);\n  const setQuickFilterValues = React.useCallback(values => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (isDeepEqual(filterModel.quickFilterValues, values)) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      quickFilterValues: [...values]\n    }));\n  }, [apiRef]);\n  const setFilterModel = React.useCallback((model, reason) => {\n    const currentModel = gridFilterModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug('Setting filter model');\n      apiRef.current.updateControlState('filter', mergeStateWithFilterModel(model, props.disableMultipleColumnsFiltering, apiRef), reason);\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const getFilterState = React.useCallback(inputFilterModel => {\n    const filterModel = sanitizeFilterModel(inputFilterModel, props.disableMultipleColumnsFiltering, apiRef);\n    const isRowMatchingFilters = props.filterMode === 'client' ? buildAggregatedFilterApplier(filterModel, apiRef, props.disableEval) : null;\n    const filterResult = apiRef.current.applyStrategyProcessor('filtering', {\n      isRowMatchingFilters,\n      filterModel: filterModel ?? getDefaultGridFilterModel()\n    });\n    return _extends({}, filterResult, {\n      filterModel\n    });\n  }, [props.disableMultipleColumnsFiltering, props.filterMode, props.disableEval, apiRef]);\n  const filterApi = {\n    setFilterLogicOperator,\n    unstable_applyFilters: updateFilteredRows,\n    deleteFilterItem,\n    upsertFilterItem,\n    upsertFilterItems,\n    setFilterModel,\n    showFilterPanel,\n    hideFilterPanel,\n    setQuickFilterValues,\n    ignoreDiacritics: props.ignoreDiacritics,\n    getFilterState\n  };\n  useGridApiMethod(apiRef, filterApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const filterModelToExport = gridFilterModelSelector(apiRef);\n\n    // Remove the additional `fromInput` property from the filter model\n    filterModelToExport.items.forEach(item => {\n      delete item.fromInput;\n    });\n    const shouldExportFilterModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.filterModel != null ||\n    // Always export if the model has been initialized\n    props.initialState?.filter?.filterModel != null ||\n    // Export if the model is not equal to the default value\n    !isDeepEqual(filterModelToExport, getDefaultGridFilterModel());\n    if (!shouldExportFilterModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      filter: {\n        filterModel: filterModelToExport\n      }\n    });\n  }, [apiRef, props.filterModel, props.initialState?.filter?.filterModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const filterModel = context.stateToRestore.filter?.filterModel;\n    if (filterModel == null) {\n      return params;\n    }\n    apiRef.current.updateControlState('filter', mergeStateWithFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef), 'restoreState');\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.unstable_applyFilters]\n    });\n  }, [apiRef, props.disableMultipleColumnsFiltering]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.filters) {\n      const FilterPanel = props.slots.filterPanel;\n      return /*#__PURE__*/_jsx(FilterPanel, _extends({}, props.slotProps?.filterPanel));\n    }\n    return initialValue;\n  }, [props.slots.filterPanel, props.slotProps?.filterPanel]);\n  const {\n    getRowId\n  } = props;\n  const getRowsRef = useLazyRef(createMemoizedValues);\n  const flatFilteringMethod = React.useCallback(params => {\n    if (props.filterMode !== 'client' || !params.isRowMatchingFilters || !params.filterModel.items.length && !params.filterModel.quickFilterValues?.length) {\n      return defaultGridFilterLookup;\n    }\n    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);\n    const filteredRowsLookup = {};\n    const {\n      isRowMatchingFilters\n    } = params;\n    const filterCache = {};\n    const result = {\n      passingFilterItems: null,\n      passingQuickFilterValues: null\n    };\n    const rows = getRowsRef.current(apiRef.current.state.rows.dataRowIdToModelLookup);\n    for (let i = 0; i < rows.length; i += 1) {\n      const row = rows[i];\n      const id = getRowId ? getRowId(row) : row.id;\n      isRowMatchingFilters(row, undefined, result);\n      const isRowPassing = passFilterLogic([result.passingFilterItems], [result.passingQuickFilterValues], params.filterModel, apiRef, filterCache);\n      if (!isRowPassing) {\n        filteredRowsLookup[id] = isRowPassing;\n      }\n    }\n    const footerId = 'auto-generated-group-footer-root';\n    const footer = dataRowIdToModelLookup[footerId];\n    if (footer) {\n      filteredRowsLookup[footerId] = true;\n    }\n    return {\n      filteredRowsLookup,\n      filteredChildrenCountLookup: {},\n      filteredDescendantCountLookup: {}\n    };\n  }, [apiRef, props.filterMode, getRowId, getRowsRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'filtering', flatFilteringMethod);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnsChange = React.useCallback(() => {\n    logger.debug('onColUpdated - GridColumns changed, applying filters');\n    const filterModel = gridFilterModelSelector(apiRef);\n    const columnsLookup = gridColumnLookupSelector(apiRef);\n    const newFilterItems = filterModel.items.filter(item => item.field && columnsLookup[item.field]);\n    if (newFilterItems.length < filterModel.items.length) {\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n  }, [apiRef, logger]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'filtering') {\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef]);\n  const updateVisibleRowsLookupState = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        visibleRowsLookup: getVisibleRowsLookupState(apiRef, state)\n      });\n    });\n  }, [apiRef]);\n  useGridEvent(apiRef, 'rowsSet', updateFilteredRows);\n  useGridEvent(apiRef, 'columnsChange', handleColumnsChange);\n  useGridEvent(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridEvent(apiRef, 'rowExpansionChange', updateVisibleRowsLookupState);\n  useGridEvent(apiRef, 'columnVisibilityModelChange', () => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.quickFilterValues?.length && shouldQuickFilterExcludeHiddenColumns(filterModel)) {\n      // re-apply filters because the quick filter results may have changed\n      updateFilteredRows();\n    }\n  });\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    updateFilteredRows();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.filterModel !== undefined) {\n      apiRef.current.setFilterModel(props.filterModel);\n    }\n  }, [apiRef, logger, props.filterModel]);\n};", "map": {"version": 3, "names": ["_extends", "React", "lruMemoize", "useEnhancedEffect", "isDeepEqual", "useLazyRef", "useGridEvent", "useGridApiMethod", "useGridLogger", "gridColumnLookupSelector", "GridPreferencePanelsValue", "defaultGridFilterLookup", "getDefaultGridFilterModel", "gridFilterModelSelector", "useFirstRender", "gridRowsLookupSelector", "useGridRegisterPipeProcessor", "GRID_DEFAULT_STRATEGY", "useGridRegisterStrategyProcessor", "buildAggregatedFilterApplier", "sanitizeFilterModel", "mergeStateWithFilterModel", "cleanFilterItem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shouldQuickFilterExcludeHiddenColumns", "jsx", "_jsx", "filterStateInitializer", "state", "props", "apiRef", "filterModel", "initialState", "filter", "disableMultipleColumnsFiltering", "visibleRowsLookup", "getVisibleRowsLookup", "params", "filteredRowsLookup", "getVisibleRowsLookupState", "current", "applyStrategyProcessor", "tree", "rows", "createMemoizedValues", "Object", "values", "useGridFilter", "logger", "registerControlState", "stateId", "propModel", "propOnChange", "onFilterModelChange", "stateSelector", "changeEvent", "updateFilteredRows", "useCallback", "setState", "filterState", "getFilterState", "newState", "visibleRowsLookupState", "publishEvent", "addColumnMenuItem", "columnMenuItems", "colDef", "filterable", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "upsertFilterItem", "item", "items", "itemIndex", "findIndex", "filterItem", "id", "push", "setFilterModel", "upsertFilterItems", "existingItems", "for<PERSON>ach", "deleteFilterItem", "itemToDelete", "length", "showFilterPanel", "targetColumnField", "panelId", "labelId", "debug", "filterItemsWithValue", "value", "undefined", "Array", "isArray", "column", "getColumn", "field", "filterOperator", "filterOperators", "find", "operator", "requiresFilterValue", "newFilterItems", "filterItemOnTarget", "targetColumn", "showPreferences", "filters", "hideFilterPanel", "hidePreferences", "setFilterLogicOperator", "logicOperator", "setQuickFilter<PERSON><PERSON><PERSON>", "quickFilterV<PERSON>ues", "model", "reason", "currentModel", "updateControlState", "unstable_applyFilters", "inputFilterModel", "isRowMatchingFilters", "filterMode", "disableEval", "filterResult", "filterApi", "ignoreDiacritics", "stateExportPreProcessing", "prevState", "context", "filterModelToExport", "fromInput", "shouldExportFilterModel", "exportOnlyDirtyModels", "stateRestorePreProcessing", "stateToRestore", "callbacks", "preferencePanelPreProcessing", "initialValue", "FilterPanel", "slots", "filterPanel", "slotProps", "getRowId", "getRowsRef", "flatFilteringMethod", "dataRowIdToModelLookup", "filterCache", "result", "passingFilterItems", "passingQuickF<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "row", "isRowPassing", "footerId", "footer", "filteredChildrenCountLookup", "filteredDescendantCountLookup", "handleColumnsChange", "columnsLookup", "handleStrategyProcessorChange", "methodName", "updateVisibleRowsLookupState"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/filter/useGridFilter.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { lruMemoize } from '@mui/x-internals/lruMemoize';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useLazyRef } from \"../../utils/useLazyRef.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnLookupSelector } from \"../columns/gridColumnsSelector.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/gridPreferencePanelsValue.js\";\nimport { defaultGridFilterLookup, getDefaultGridFilterModel } from \"./gridFilterState.js\";\nimport { gridFilterModelSelector } from \"./gridFilterSelector.js\";\nimport { useFirstRender } from \"../../utils/useFirstRender.js\";\nimport { gridRowsLookupSelector } from \"../rows/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GRID_DEFAULT_STRATEGY, useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/index.js\";\nimport { buildAggregatedFilterApplier, sanitizeFilterModel, mergeStateWithFilterModel, cleanFilterItem, passFilterLogic, shouldQuickFilterExcludeHiddenColumns } from \"./gridFilterUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const filterStateInitializer = (state, props, apiRef) => {\n  const filterModel = props.filterModel ?? props.initialState?.filter?.filterModel ?? getDefaultGridFilterModel();\n  return _extends({}, state, {\n    filter: _extends({\n      filterModel: sanitizeFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef)\n    }, defaultGridFilterLookup),\n    visibleRowsLookup: {}\n  });\n};\nconst getVisibleRowsLookup = params => {\n  // For flat tree, the `visibleRowsLookup` and the `filteredRowsLookup` are equals since no row is collapsed.\n  return params.filteredRowsLookup;\n};\nfunction getVisibleRowsLookupState(apiRef, state) {\n  return apiRef.current.applyStrategyProcessor('visibleRowsLookupCreation', {\n    tree: state.rows.tree,\n    filteredRowsLookup: state.filter.filteredRowsLookup\n  });\n}\nfunction createMemoizedValues() {\n  return lruMemoize(Object.values);\n}\n\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n * @requires useGridRows (event)\n */\nexport const useGridFilter = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFilter');\n  apiRef.current.registerControlState({\n    stateId: 'filter',\n    propModel: props.filterModel,\n    propOnChange: props.onFilterModelChange,\n    stateSelector: gridFilterModelSelector,\n    changeEvent: 'filterModelChange'\n  });\n  const updateFilteredRows = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterState = apiRef.current.getFilterState(filterModel);\n      const newState = _extends({}, state, {\n        filter: _extends({}, state.filter, filterState)\n      });\n      const visibleRowsLookupState = getVisibleRowsLookupState(apiRef, newState);\n      return _extends({}, newState, {\n        visibleRowsLookup: visibleRowsLookupState\n      });\n    });\n    apiRef.current.publishEvent('filteredRowsSet');\n  }, [apiRef]);\n  const addColumnMenuItem = React.useCallback((columnMenuItems, colDef) => {\n    if (colDef == null || colDef.filterable === false || props.disableColumnFilter) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuFilterItem'];\n  }, [props.disableColumnFilter]);\n\n  /**\n   * API METHODS\n   */\n  const upsertFilterItem = React.useCallback(item => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = [...filterModel.items];\n    const itemIndex = items.findIndex(filterItem => filterItem.id === item.id);\n    if (itemIndex === -1) {\n      items.push(item);\n    } else {\n      items[itemIndex] = item;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'upsertFilterItem');\n  }, [apiRef]);\n  const upsertFilterItems = React.useCallback(items => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const existingItems = [...filterModel.items];\n    items.forEach(item => {\n      const itemIndex = existingItems.findIndex(filterItem => filterItem.id === item.id);\n      if (itemIndex === -1) {\n        existingItems.push(item);\n      } else {\n        existingItems[itemIndex] = item;\n      }\n    });\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items: existingItems\n    }), 'upsertFilterItems');\n  }, [apiRef]);\n  const deleteFilterItem = React.useCallback(itemToDelete => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const items = filterModel.items.filter(item => item.id !== itemToDelete.id);\n    if (items.length === filterModel.items.length) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      items\n    }), 'deleteFilterItem');\n  }, [apiRef]);\n  const showFilterPanel = React.useCallback((targetColumnField, panelId, labelId) => {\n    logger.debug('Displaying filter panel');\n    if (targetColumnField) {\n      const filterModel = gridFilterModelSelector(apiRef);\n      const filterItemsWithValue = filterModel.items.filter(item => {\n        if (item.value !== undefined) {\n          // Some filters like `isAnyOf` support array as `item.value`.\n          // If array is empty, we want to remove it from the filter model.\n          if (Array.isArray(item.value) && item.value.length === 0) {\n            return false;\n          }\n          return true;\n        }\n        const column = apiRef.current.getColumn(item.field);\n        const filterOperator = column.filterOperators?.find(operator => operator.value === item.operator);\n        const requiresFilterValue = typeof filterOperator?.requiresFilterValue === 'undefined' ? true : filterOperator?.requiresFilterValue;\n\n        // Operators like `isEmpty` don't have and don't require `item.value`.\n        // So we don't want to remove them from the filter model if `item.value === undefined`.\n        // See https://github.com/mui/mui-x/issues/5402\n        if (requiresFilterValue) {\n          return false;\n        }\n        return true;\n      });\n      let newFilterItems;\n      const filterItemOnTarget = filterItemsWithValue.find(item => item.field === targetColumnField);\n      const targetColumn = apiRef.current.getColumn(targetColumnField);\n      if (filterItemOnTarget) {\n        newFilterItems = filterItemsWithValue;\n      } else if (props.disableMultipleColumnsFiltering) {\n        newFilterItems = [cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      } else {\n        newFilterItems = [...filterItemsWithValue, cleanFilterItem({\n          field: targetColumnField,\n          operator: targetColumn.filterOperators[0].value\n        }, apiRef)];\n      }\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n    apiRef.current.showPreferences(GridPreferencePanelsValue.filters, panelId, labelId);\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const hideFilterPanel = React.useCallback(() => {\n    logger.debug('Hiding filter panel');\n    apiRef.current.hidePreferences();\n  }, [apiRef, logger]);\n  const setFilterLogicOperator = React.useCallback(logicOperator => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.logicOperator === logicOperator) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      logicOperator\n    }), 'changeLogicOperator');\n  }, [apiRef]);\n  const setQuickFilterValues = React.useCallback(values => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (isDeepEqual(filterModel.quickFilterValues, values)) {\n      return;\n    }\n    apiRef.current.setFilterModel(_extends({}, filterModel, {\n      quickFilterValues: [...values]\n    }));\n  }, [apiRef]);\n  const setFilterModel = React.useCallback((model, reason) => {\n    const currentModel = gridFilterModelSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug('Setting filter model');\n      apiRef.current.updateControlState('filter', mergeStateWithFilterModel(model, props.disableMultipleColumnsFiltering, apiRef), reason);\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef, logger, props.disableMultipleColumnsFiltering]);\n  const getFilterState = React.useCallback(inputFilterModel => {\n    const filterModel = sanitizeFilterModel(inputFilterModel, props.disableMultipleColumnsFiltering, apiRef);\n    const isRowMatchingFilters = props.filterMode === 'client' ? buildAggregatedFilterApplier(filterModel, apiRef, props.disableEval) : null;\n    const filterResult = apiRef.current.applyStrategyProcessor('filtering', {\n      isRowMatchingFilters,\n      filterModel: filterModel ?? getDefaultGridFilterModel()\n    });\n    return _extends({}, filterResult, {\n      filterModel\n    });\n  }, [props.disableMultipleColumnsFiltering, props.filterMode, props.disableEval, apiRef]);\n  const filterApi = {\n    setFilterLogicOperator,\n    unstable_applyFilters: updateFilteredRows,\n    deleteFilterItem,\n    upsertFilterItem,\n    upsertFilterItems,\n    setFilterModel,\n    showFilterPanel,\n    hideFilterPanel,\n    setQuickFilterValues,\n    ignoreDiacritics: props.ignoreDiacritics,\n    getFilterState\n  };\n  useGridApiMethod(apiRef, filterApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const filterModelToExport = gridFilterModelSelector(apiRef);\n\n    // Remove the additional `fromInput` property from the filter model\n    filterModelToExport.items.forEach(item => {\n      delete item.fromInput;\n    });\n    const shouldExportFilterModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.filterModel != null ||\n    // Always export if the model has been initialized\n    props.initialState?.filter?.filterModel != null ||\n    // Export if the model is not equal to the default value\n    !isDeepEqual(filterModelToExport, getDefaultGridFilterModel());\n    if (!shouldExportFilterModel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      filter: {\n        filterModel: filterModelToExport\n      }\n    });\n  }, [apiRef, props.filterModel, props.initialState?.filter?.filterModel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const filterModel = context.stateToRestore.filter?.filterModel;\n    if (filterModel == null) {\n      return params;\n    }\n    apiRef.current.updateControlState('filter', mergeStateWithFilterModel(filterModel, props.disableMultipleColumnsFiltering, apiRef), 'restoreState');\n    return _extends({}, params, {\n      callbacks: [...params.callbacks, apiRef.current.unstable_applyFilters]\n    });\n  }, [apiRef, props.disableMultipleColumnsFiltering]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.filters) {\n      const FilterPanel = props.slots.filterPanel;\n      return /*#__PURE__*/_jsx(FilterPanel, _extends({}, props.slotProps?.filterPanel));\n    }\n    return initialValue;\n  }, [props.slots.filterPanel, props.slotProps?.filterPanel]);\n  const {\n    getRowId\n  } = props;\n  const getRowsRef = useLazyRef(createMemoizedValues);\n  const flatFilteringMethod = React.useCallback(params => {\n    if (props.filterMode !== 'client' || !params.isRowMatchingFilters || !params.filterModel.items.length && !params.filterModel.quickFilterValues?.length) {\n      return defaultGridFilterLookup;\n    }\n    const dataRowIdToModelLookup = gridRowsLookupSelector(apiRef);\n    const filteredRowsLookup = {};\n    const {\n      isRowMatchingFilters\n    } = params;\n    const filterCache = {};\n    const result = {\n      passingFilterItems: null,\n      passingQuickFilterValues: null\n    };\n    const rows = getRowsRef.current(apiRef.current.state.rows.dataRowIdToModelLookup);\n    for (let i = 0; i < rows.length; i += 1) {\n      const row = rows[i];\n      const id = getRowId ? getRowId(row) : row.id;\n      isRowMatchingFilters(row, undefined, result);\n      const isRowPassing = passFilterLogic([result.passingFilterItems], [result.passingQuickFilterValues], params.filterModel, apiRef, filterCache);\n      if (!isRowPassing) {\n        filteredRowsLookup[id] = isRowPassing;\n      }\n    }\n    const footerId = 'auto-generated-group-footer-root';\n    const footer = dataRowIdToModelLookup[footerId];\n    if (footer) {\n      filteredRowsLookup[footerId] = true;\n    }\n    return {\n      filteredRowsLookup,\n      filteredChildrenCountLookup: {},\n      filteredDescendantCountLookup: {}\n    };\n  }, [apiRef, props.filterMode, getRowId, getRowsRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItem);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'filtering', flatFilteringMethod);\n  useGridRegisterStrategyProcessor(apiRef, GRID_DEFAULT_STRATEGY, 'visibleRowsLookupCreation', getVisibleRowsLookup);\n\n  /**\n   * EVENTS\n   */\n  const handleColumnsChange = React.useCallback(() => {\n    logger.debug('onColUpdated - GridColumns changed, applying filters');\n    const filterModel = gridFilterModelSelector(apiRef);\n    const columnsLookup = gridColumnLookupSelector(apiRef);\n    const newFilterItems = filterModel.items.filter(item => item.field && columnsLookup[item.field]);\n    if (newFilterItems.length < filterModel.items.length) {\n      apiRef.current.setFilterModel(_extends({}, filterModel, {\n        items: newFilterItems\n      }));\n    }\n  }, [apiRef, logger]);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (methodName === 'filtering') {\n      apiRef.current.unstable_applyFilters();\n    }\n  }, [apiRef]);\n  const updateVisibleRowsLookupState = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        visibleRowsLookup: getVisibleRowsLookupState(apiRef, state)\n      });\n    });\n  }, [apiRef]);\n  useGridEvent(apiRef, 'rowsSet', updateFilteredRows);\n  useGridEvent(apiRef, 'columnsChange', handleColumnsChange);\n  useGridEvent(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridEvent(apiRef, 'rowExpansionChange', updateVisibleRowsLookupState);\n  useGridEvent(apiRef, 'columnVisibilityModelChange', () => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    if (filterModel.quickFilterValues?.length && shouldQuickFilterExcludeHiddenColumns(filterModel)) {\n      // re-apply filters because the quick filter results may have changed\n      updateFilteredRows();\n    }\n  });\n\n  /**\n   * 1ST RENDER\n   */\n  useFirstRender(() => {\n    updateFilteredRows();\n  });\n\n  /**\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    if (props.filterModel !== undefined) {\n      apiRef.current.setFilterModel(props.filterModel);\n    }\n  }, [apiRef, logger, props.filterModel]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,6BAA6B;AACxD,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,sBAAsB;AACzF,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,sBAAsB,QAAQ,kBAAkB;AACzD,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,qBAAqB,EAAEC,gCAAgC,QAAQ,wCAAwC;AAChH,SAASC,4BAA4B,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,qCAAqC,QAAQ,sBAAsB;AAC5L,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC9D,MAAMC,WAAW,GAAGF,KAAK,CAACE,WAAW,IAAIF,KAAK,CAACG,YAAY,EAAEC,MAAM,EAAEF,WAAW,IAAInB,yBAAyB,CAAC,CAAC;EAC/G,OAAOZ,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACzBK,MAAM,EAAEjC,QAAQ,CAAC;MACf+B,WAAW,EAAEX,mBAAmB,CAACW,WAAW,EAAEF,KAAK,CAACK,+BAA+B,EAAEJ,MAAM;IAC7F,CAAC,EAAEnB,uBAAuB,CAAC;IAC3BwB,iBAAiB,EAAE,CAAC;EACtB,CAAC,CAAC;AACJ,CAAC;AACD,MAAMC,oBAAoB,GAAGC,MAAM,IAAI;EACrC;EACA,OAAOA,MAAM,CAACC,kBAAkB;AAClC,CAAC;AACD,SAASC,yBAAyBA,CAACT,MAAM,EAAEF,KAAK,EAAE;EAChD,OAAOE,MAAM,CAACU,OAAO,CAACC,sBAAsB,CAAC,2BAA2B,EAAE;IACxEC,IAAI,EAAEd,KAAK,CAACe,IAAI,CAACD,IAAI;IACrBJ,kBAAkB,EAAEV,KAAK,CAACK,MAAM,CAACK;EACnC,CAAC,CAAC;AACJ;AACA,SAASM,oBAAoBA,CAAA,EAAG;EAC9B,OAAO1C,UAAU,CAAC2C,MAAM,CAACC,MAAM,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACjB,MAAM,EAAED,KAAK,KAAK;EAC9C,MAAMmB,MAAM,GAAGxC,aAAa,CAACsB,MAAM,EAAE,eAAe,CAAC;EACrDA,MAAM,CAACU,OAAO,CAACS,oBAAoB,CAAC;IAClCC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAEtB,KAAK,CAACE,WAAW;IAC5BqB,YAAY,EAAEvB,KAAK,CAACwB,mBAAmB;IACvCC,aAAa,EAAEzC,uBAAuB;IACtC0C,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGvD,KAAK,CAACwD,WAAW,CAAC,MAAM;IACjD3B,MAAM,CAACU,OAAO,CAACkB,QAAQ,CAAC9B,KAAK,IAAI;MAC/B,MAAMG,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;MACnD,MAAM6B,WAAW,GAAG7B,MAAM,CAACU,OAAO,CAACoB,cAAc,CAAC7B,WAAW,CAAC;MAC9D,MAAM8B,QAAQ,GAAG7D,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;QACnCK,MAAM,EAAEjC,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,CAACK,MAAM,EAAE0B,WAAW;MAChD,CAAC,CAAC;MACF,MAAMG,sBAAsB,GAAGvB,yBAAyB,CAACT,MAAM,EAAE+B,QAAQ,CAAC;MAC1E,OAAO7D,QAAQ,CAAC,CAAC,CAAC,EAAE6D,QAAQ,EAAE;QAC5B1B,iBAAiB,EAAE2B;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFhC,MAAM,CAACU,OAAO,CAACuB,YAAY,CAAC,iBAAiB,CAAC;EAChD,CAAC,EAAE,CAACjC,MAAM,CAAC,CAAC;EACZ,MAAMkC,iBAAiB,GAAG/D,KAAK,CAACwD,WAAW,CAAC,CAACQ,eAAe,EAAEC,MAAM,KAAK;IACvE,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACC,UAAU,KAAK,KAAK,IAAItC,KAAK,CAACuC,mBAAmB,EAAE;MAC9E,OAAOH,eAAe;IACxB;IACA,OAAO,CAAC,GAAGA,eAAe,EAAE,sBAAsB,CAAC;EACrD,CAAC,EAAE,CAACpC,KAAK,CAACuC,mBAAmB,CAAC,CAAC;;EAE/B;AACF;AACA;EACE,MAAMC,gBAAgB,GAAGpE,KAAK,CAACwD,WAAW,CAACa,IAAI,IAAI;IACjD,MAAMvC,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,MAAMyC,KAAK,GAAG,CAAC,GAAGxC,WAAW,CAACwC,KAAK,CAAC;IACpC,MAAMC,SAAS,GAAGD,KAAK,CAACE,SAAS,CAACC,UAAU,IAAIA,UAAU,CAACC,EAAE,KAAKL,IAAI,CAACK,EAAE,CAAC;IAC1E,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;MACpBD,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;IAClB,CAAC,MAAM;MACLC,KAAK,CAACC,SAAS,CAAC,GAAGF,IAAI;IACzB;IACAxC,MAAM,CAACU,OAAO,CAACqC,cAAc,CAAC7E,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtDwC;IACF,CAAC,CAAC,EAAE,kBAAkB,CAAC;EACzB,CAAC,EAAE,CAACzC,MAAM,CAAC,CAAC;EACZ,MAAMgD,iBAAiB,GAAG7E,KAAK,CAACwD,WAAW,CAACc,KAAK,IAAI;IACnD,MAAMxC,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,MAAMiD,aAAa,GAAG,CAAC,GAAGhD,WAAW,CAACwC,KAAK,CAAC;IAC5CA,KAAK,CAACS,OAAO,CAACV,IAAI,IAAI;MACpB,MAAME,SAAS,GAAGO,aAAa,CAACN,SAAS,CAACC,UAAU,IAAIA,UAAU,CAACC,EAAE,KAAKL,IAAI,CAACK,EAAE,CAAC;MAClF,IAAIH,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBO,aAAa,CAACH,IAAI,CAACN,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLS,aAAa,CAACP,SAAS,CAAC,GAAGF,IAAI;MACjC;IACF,CAAC,CAAC;IACFxC,MAAM,CAACU,OAAO,CAACqC,cAAc,CAAC7E,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtDwC,KAAK,EAAEQ;IACT,CAAC,CAAC,EAAE,mBAAmB,CAAC;EAC1B,CAAC,EAAE,CAACjD,MAAM,CAAC,CAAC;EACZ,MAAMmD,gBAAgB,GAAGhF,KAAK,CAACwD,WAAW,CAACyB,YAAY,IAAI;IACzD,MAAMnD,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,MAAMyC,KAAK,GAAGxC,WAAW,CAACwC,KAAK,CAACtC,MAAM,CAACqC,IAAI,IAAIA,IAAI,CAACK,EAAE,KAAKO,YAAY,CAACP,EAAE,CAAC;IAC3E,IAAIJ,KAAK,CAACY,MAAM,KAAKpD,WAAW,CAACwC,KAAK,CAACY,MAAM,EAAE;MAC7C;IACF;IACArD,MAAM,CAACU,OAAO,CAACqC,cAAc,CAAC7E,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtDwC;IACF,CAAC,CAAC,EAAE,kBAAkB,CAAC;EACzB,CAAC,EAAE,CAACzC,MAAM,CAAC,CAAC;EACZ,MAAMsD,eAAe,GAAGnF,KAAK,CAACwD,WAAW,CAAC,CAAC4B,iBAAiB,EAAEC,OAAO,EAAEC,OAAO,KAAK;IACjFvC,MAAM,CAACwC,KAAK,CAAC,yBAAyB,CAAC;IACvC,IAAIH,iBAAiB,EAAE;MACrB,MAAMtD,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;MACnD,MAAM2D,oBAAoB,GAAG1D,WAAW,CAACwC,KAAK,CAACtC,MAAM,CAACqC,IAAI,IAAI;QAC5D,IAAIA,IAAI,CAACoB,KAAK,KAAKC,SAAS,EAAE;UAC5B;UACA;UACA,IAAIC,KAAK,CAACC,OAAO,CAACvB,IAAI,CAACoB,KAAK,CAAC,IAAIpB,IAAI,CAACoB,KAAK,CAACP,MAAM,KAAK,CAAC,EAAE;YACxD,OAAO,KAAK;UACd;UACA,OAAO,IAAI;QACb;QACA,MAAMW,MAAM,GAAGhE,MAAM,CAACU,OAAO,CAACuD,SAAS,CAACzB,IAAI,CAAC0B,KAAK,CAAC;QACnD,MAAMC,cAAc,GAAGH,MAAM,CAACI,eAAe,EAAEC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACV,KAAK,KAAKpB,IAAI,CAAC8B,QAAQ,CAAC;QACjG,MAAMC,mBAAmB,GAAG,OAAOJ,cAAc,EAAEI,mBAAmB,KAAK,WAAW,GAAG,IAAI,GAAGJ,cAAc,EAAEI,mBAAmB;;QAEnI;QACA;QACA;QACA,IAAIA,mBAAmB,EAAE;UACvB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAIC,cAAc;MAClB,MAAMC,kBAAkB,GAAGd,oBAAoB,CAACU,IAAI,CAAC7B,IAAI,IAAIA,IAAI,CAAC0B,KAAK,KAAKX,iBAAiB,CAAC;MAC9F,MAAMmB,YAAY,GAAG1E,MAAM,CAACU,OAAO,CAACuD,SAAS,CAACV,iBAAiB,CAAC;MAChE,IAAIkB,kBAAkB,EAAE;QACtBD,cAAc,GAAGb,oBAAoB;MACvC,CAAC,MAAM,IAAI5D,KAAK,CAACK,+BAA+B,EAAE;QAChDoE,cAAc,GAAG,CAAChF,eAAe,CAAC;UAChC0E,KAAK,EAAEX,iBAAiB;UACxBe,QAAQ,EAAEI,YAAY,CAACN,eAAe,CAAC,CAAC,CAAC,CAACR;QAC5C,CAAC,EAAE5D,MAAM,CAAC,CAAC;MACb,CAAC,MAAM;QACLwE,cAAc,GAAG,CAAC,GAAGb,oBAAoB,EAAEnE,eAAe,CAAC;UACzD0E,KAAK,EAAEX,iBAAiB;UACxBe,QAAQ,EAAEI,YAAY,CAACN,eAAe,CAAC,CAAC,CAAC,CAACR;QAC5C,CAAC,EAAE5D,MAAM,CAAC,CAAC;MACb;MACAA,MAAM,CAACU,OAAO,CAACqC,cAAc,CAAC7E,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;QACtDwC,KAAK,EAAE+B;MACT,CAAC,CAAC,CAAC;IACL;IACAxE,MAAM,CAACU,OAAO,CAACiE,eAAe,CAAC/F,yBAAyB,CAACgG,OAAO,EAAEpB,OAAO,EAAEC,OAAO,CAAC;EACrF,CAAC,EAAE,CAACzD,MAAM,EAAEkB,MAAM,EAAEnB,KAAK,CAACK,+BAA+B,CAAC,CAAC;EAC3D,MAAMyE,eAAe,GAAG1G,KAAK,CAACwD,WAAW,CAAC,MAAM;IAC9CT,MAAM,CAACwC,KAAK,CAAC,qBAAqB,CAAC;IACnC1D,MAAM,CAACU,OAAO,CAACoE,eAAe,CAAC,CAAC;EAClC,CAAC,EAAE,CAAC9E,MAAM,EAAEkB,MAAM,CAAC,CAAC;EACpB,MAAM6D,sBAAsB,GAAG5G,KAAK,CAACwD,WAAW,CAACqD,aAAa,IAAI;IAChE,MAAM/E,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,IAAIC,WAAW,CAAC+E,aAAa,KAAKA,aAAa,EAAE;MAC/C;IACF;IACAhF,MAAM,CAACU,OAAO,CAACqC,cAAc,CAAC7E,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtD+E;IACF,CAAC,CAAC,EAAE,qBAAqB,CAAC;EAC5B,CAAC,EAAE,CAAChF,MAAM,CAAC,CAAC;EACZ,MAAMiF,oBAAoB,GAAG9G,KAAK,CAACwD,WAAW,CAACX,MAAM,IAAI;IACvD,MAAMf,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,IAAI1B,WAAW,CAAC2B,WAAW,CAACiF,iBAAiB,EAAElE,MAAM,CAAC,EAAE;MACtD;IACF;IACAhB,MAAM,CAACU,OAAO,CAACqC,cAAc,CAAC7E,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;MACtDiF,iBAAiB,EAAE,CAAC,GAAGlE,MAAM;IAC/B,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAChB,MAAM,CAAC,CAAC;EACZ,MAAM+C,cAAc,GAAG5E,KAAK,CAACwD,WAAW,CAAC,CAACwD,KAAK,EAAEC,MAAM,KAAK;IAC1D,MAAMC,YAAY,GAAGtG,uBAAuB,CAACiB,MAAM,CAAC;IACpD,IAAIqF,YAAY,KAAKF,KAAK,EAAE;MAC1BjE,MAAM,CAACwC,KAAK,CAAC,sBAAsB,CAAC;MACpC1D,MAAM,CAACU,OAAO,CAAC4E,kBAAkB,CAAC,QAAQ,EAAE/F,yBAAyB,CAAC4F,KAAK,EAAEpF,KAAK,CAACK,+BAA+B,EAAEJ,MAAM,CAAC,EAAEoF,MAAM,CAAC;MACpIpF,MAAM,CAACU,OAAO,CAAC6E,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,EAAE,CAACvF,MAAM,EAAEkB,MAAM,EAAEnB,KAAK,CAACK,+BAA+B,CAAC,CAAC;EAC3D,MAAM0B,cAAc,GAAG3D,KAAK,CAACwD,WAAW,CAAC6D,gBAAgB,IAAI;IAC3D,MAAMvF,WAAW,GAAGX,mBAAmB,CAACkG,gBAAgB,EAAEzF,KAAK,CAACK,+BAA+B,EAAEJ,MAAM,CAAC;IACxG,MAAMyF,oBAAoB,GAAG1F,KAAK,CAAC2F,UAAU,KAAK,QAAQ,GAAGrG,4BAA4B,CAACY,WAAW,EAAED,MAAM,EAAED,KAAK,CAAC4F,WAAW,CAAC,GAAG,IAAI;IACxI,MAAMC,YAAY,GAAG5F,MAAM,CAACU,OAAO,CAACC,sBAAsB,CAAC,WAAW,EAAE;MACtE8E,oBAAoB;MACpBxF,WAAW,EAAEA,WAAW,IAAInB,yBAAyB,CAAC;IACxD,CAAC,CAAC;IACF,OAAOZ,QAAQ,CAAC,CAAC,CAAC,EAAE0H,YAAY,EAAE;MAChC3F;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACF,KAAK,CAACK,+BAA+B,EAAEL,KAAK,CAAC2F,UAAU,EAAE3F,KAAK,CAAC4F,WAAW,EAAE3F,MAAM,CAAC,CAAC;EACxF,MAAM6F,SAAS,GAAG;IAChBd,sBAAsB;IACtBQ,qBAAqB,EAAE7D,kBAAkB;IACzCyB,gBAAgB;IAChBZ,gBAAgB;IAChBS,iBAAiB;IACjBD,cAAc;IACdO,eAAe;IACfuB,eAAe;IACfI,oBAAoB;IACpBa,gBAAgB,EAAE/F,KAAK,CAAC+F,gBAAgB;IACxChE;EACF,CAAC;EACDrD,gBAAgB,CAACuB,MAAM,EAAE6F,SAAS,EAAE,QAAQ,CAAC;;EAE7C;AACF;AACA;EACE,MAAME,wBAAwB,GAAG5H,KAAK,CAACwD,WAAW,CAAC,CAACqE,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,mBAAmB,GAAGnH,uBAAuB,CAACiB,MAAM,CAAC;;IAE3D;IACAkG,mBAAmB,CAACzD,KAAK,CAACS,OAAO,CAACV,IAAI,IAAI;MACxC,OAAOA,IAAI,CAAC2D,SAAS;IACvB,CAAC,CAAC;IACF,MAAMC,uBAAuB;IAC7B;IACA,CAACH,OAAO,CAACI,qBAAqB;IAC9B;IACAtG,KAAK,CAACE,WAAW,IAAI,IAAI;IACzB;IACAF,KAAK,CAACG,YAAY,EAAEC,MAAM,EAAEF,WAAW,IAAI,IAAI;IAC/C;IACA,CAAC3B,WAAW,CAAC4H,mBAAmB,EAAEpH,yBAAyB,CAAC,CAAC,CAAC;IAC9D,IAAI,CAACsH,uBAAuB,EAAE;MAC5B,OAAOJ,SAAS;IAClB;IACA,OAAO9H,QAAQ,CAAC,CAAC,CAAC,EAAE8H,SAAS,EAAE;MAC7B7F,MAAM,EAAE;QACNF,WAAW,EAAEiG;MACf;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClG,MAAM,EAAED,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACG,YAAY,EAAEC,MAAM,EAAEF,WAAW,CAAC,CAAC;EACxE,MAAMqG,yBAAyB,GAAGnI,KAAK,CAACwD,WAAW,CAAC,CAACpB,MAAM,EAAE0F,OAAO,KAAK;IACvE,MAAMhG,WAAW,GAAGgG,OAAO,CAACM,cAAc,CAACpG,MAAM,EAAEF,WAAW;IAC9D,IAAIA,WAAW,IAAI,IAAI,EAAE;MACvB,OAAOM,MAAM;IACf;IACAP,MAAM,CAACU,OAAO,CAAC4E,kBAAkB,CAAC,QAAQ,EAAE/F,yBAAyB,CAACU,WAAW,EAAEF,KAAK,CAACK,+BAA+B,EAAEJ,MAAM,CAAC,EAAE,cAAc,CAAC;IAClJ,OAAO9B,QAAQ,CAAC,CAAC,CAAC,EAAEqC,MAAM,EAAE;MAC1BiG,SAAS,EAAE,CAAC,GAAGjG,MAAM,CAACiG,SAAS,EAAExG,MAAM,CAACU,OAAO,CAAC6E,qBAAqB;IACvE,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvF,MAAM,EAAED,KAAK,CAACK,+BAA+B,CAAC,CAAC;EACnD,MAAMqG,4BAA4B,GAAGtI,KAAK,CAACwD,WAAW,CAAC,CAAC+E,YAAY,EAAE9C,KAAK,KAAK;IAC9E,IAAIA,KAAK,KAAKhF,yBAAyB,CAACgG,OAAO,EAAE;MAC/C,MAAM+B,WAAW,GAAG5G,KAAK,CAAC6G,KAAK,CAACC,WAAW;MAC3C,OAAO,aAAajH,IAAI,CAAC+G,WAAW,EAAEzI,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAAC+G,SAAS,EAAED,WAAW,CAAC,CAAC;IACnF;IACA,OAAOH,YAAY;EACrB,CAAC,EAAE,CAAC3G,KAAK,CAAC6G,KAAK,CAACC,WAAW,EAAE9G,KAAK,CAAC+G,SAAS,EAAED,WAAW,CAAC,CAAC;EAC3D,MAAM;IACJE;EACF,CAAC,GAAGhH,KAAK;EACT,MAAMiH,UAAU,GAAGzI,UAAU,CAACuC,oBAAoB,CAAC;EACnD,MAAMmG,mBAAmB,GAAG9I,KAAK,CAACwD,WAAW,CAACpB,MAAM,IAAI;IACtD,IAAIR,KAAK,CAAC2F,UAAU,KAAK,QAAQ,IAAI,CAACnF,MAAM,CAACkF,oBAAoB,IAAI,CAAClF,MAAM,CAACN,WAAW,CAACwC,KAAK,CAACY,MAAM,IAAI,CAAC9C,MAAM,CAACN,WAAW,CAACiF,iBAAiB,EAAE7B,MAAM,EAAE;MACtJ,OAAOxE,uBAAuB;IAChC;IACA,MAAMqI,sBAAsB,GAAGjI,sBAAsB,CAACe,MAAM,CAAC;IAC7D,MAAMQ,kBAAkB,GAAG,CAAC,CAAC;IAC7B,MAAM;MACJiF;IACF,CAAC,GAAGlF,MAAM;IACV,MAAM4G,WAAW,GAAG,CAAC,CAAC;IACtB,MAAMC,MAAM,GAAG;MACbC,kBAAkB,EAAE,IAAI;MACxBC,wBAAwB,EAAE;IAC5B,CAAC;IACD,MAAMzG,IAAI,GAAGmG,UAAU,CAACtG,OAAO,CAACV,MAAM,CAACU,OAAO,CAACZ,KAAK,CAACe,IAAI,CAACqG,sBAAsB,CAAC;IACjF,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1G,IAAI,CAACwC,MAAM,EAAEkE,CAAC,IAAI,CAAC,EAAE;MACvC,MAAMC,GAAG,GAAG3G,IAAI,CAAC0G,CAAC,CAAC;MACnB,MAAM1E,EAAE,GAAGkE,QAAQ,GAAGA,QAAQ,CAACS,GAAG,CAAC,GAAGA,GAAG,CAAC3E,EAAE;MAC5C4C,oBAAoB,CAAC+B,GAAG,EAAE3D,SAAS,EAAEuD,MAAM,CAAC;MAC5C,MAAMK,YAAY,GAAGhI,eAAe,CAAC,CAAC2H,MAAM,CAACC,kBAAkB,CAAC,EAAE,CAACD,MAAM,CAACE,wBAAwB,CAAC,EAAE/G,MAAM,CAACN,WAAW,EAAED,MAAM,EAAEmH,WAAW,CAAC;MAC7I,IAAI,CAACM,YAAY,EAAE;QACjBjH,kBAAkB,CAACqC,EAAE,CAAC,GAAG4E,YAAY;MACvC;IACF;IACA,MAAMC,QAAQ,GAAG,kCAAkC;IACnD,MAAMC,MAAM,GAAGT,sBAAsB,CAACQ,QAAQ,CAAC;IAC/C,IAAIC,MAAM,EAAE;MACVnH,kBAAkB,CAACkH,QAAQ,CAAC,GAAG,IAAI;IACrC;IACA,OAAO;MACLlH,kBAAkB;MAClBoH,2BAA2B,EAAE,CAAC,CAAC;MAC/BC,6BAA6B,EAAE,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAAC7H,MAAM,EAAED,KAAK,CAAC2F,UAAU,EAAEqB,QAAQ,EAAEC,UAAU,CAAC,CAAC;EACpD9H,4BAA4B,CAACc,MAAM,EAAE,YAAY,EAAEkC,iBAAiB,CAAC;EACrEhD,4BAA4B,CAACc,MAAM,EAAE,aAAa,EAAE+F,wBAAwB,CAAC;EAC7E7G,4BAA4B,CAACc,MAAM,EAAE,cAAc,EAAEsG,yBAAyB,CAAC;EAC/EpH,4BAA4B,CAACc,MAAM,EAAE,iBAAiB,EAAEyG,4BAA4B,CAAC;EACrFrH,gCAAgC,CAACY,MAAM,EAAEb,qBAAqB,EAAE,WAAW,EAAE8H,mBAAmB,CAAC;EACjG7H,gCAAgC,CAACY,MAAM,EAAEb,qBAAqB,EAAE,2BAA2B,EAAEmB,oBAAoB,CAAC;;EAElH;AACF;AACA;EACE,MAAMwH,mBAAmB,GAAG3J,KAAK,CAACwD,WAAW,CAAC,MAAM;IAClDT,MAAM,CAACwC,KAAK,CAAC,sDAAsD,CAAC;IACpE,MAAMzD,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,MAAM+H,aAAa,GAAGpJ,wBAAwB,CAACqB,MAAM,CAAC;IACtD,MAAMwE,cAAc,GAAGvE,WAAW,CAACwC,KAAK,CAACtC,MAAM,CAACqC,IAAI,IAAIA,IAAI,CAAC0B,KAAK,IAAI6D,aAAa,CAACvF,IAAI,CAAC0B,KAAK,CAAC,CAAC;IAChG,IAAIM,cAAc,CAACnB,MAAM,GAAGpD,WAAW,CAACwC,KAAK,CAACY,MAAM,EAAE;MACpDrD,MAAM,CAACU,OAAO,CAACqC,cAAc,CAAC7E,QAAQ,CAAC,CAAC,CAAC,EAAE+B,WAAW,EAAE;QACtDwC,KAAK,EAAE+B;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACxE,MAAM,EAAEkB,MAAM,CAAC,CAAC;EACpB,MAAM8G,6BAA6B,GAAG7J,KAAK,CAACwD,WAAW,CAACsG,UAAU,IAAI;IACpE,IAAIA,UAAU,KAAK,WAAW,EAAE;MAC9BjI,MAAM,CAACU,OAAO,CAAC6E,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,EAAE,CAACvF,MAAM,CAAC,CAAC;EACZ,MAAMkI,4BAA4B,GAAG/J,KAAK,CAACwD,WAAW,CAAC,MAAM;IAC3D3B,MAAM,CAACU,OAAO,CAACkB,QAAQ,CAAC9B,KAAK,IAAI;MAC/B,OAAO5B,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;QACzBO,iBAAiB,EAAEI,yBAAyB,CAACT,MAAM,EAAEF,KAAK;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACE,MAAM,CAAC,CAAC;EACZxB,YAAY,CAACwB,MAAM,EAAE,SAAS,EAAE0B,kBAAkB,CAAC;EACnDlD,YAAY,CAACwB,MAAM,EAAE,eAAe,EAAE8H,mBAAmB,CAAC;EAC1DtJ,YAAY,CAACwB,MAAM,EAAE,+BAA+B,EAAEgI,6BAA6B,CAAC;EACpFxJ,YAAY,CAACwB,MAAM,EAAE,oBAAoB,EAAEkI,4BAA4B,CAAC;EACxE1J,YAAY,CAACwB,MAAM,EAAE,6BAA6B,EAAE,MAAM;IACxD,MAAMC,WAAW,GAAGlB,uBAAuB,CAACiB,MAAM,CAAC;IACnD,IAAIC,WAAW,CAACiF,iBAAiB,EAAE7B,MAAM,IAAI3D,qCAAqC,CAACO,WAAW,CAAC,EAAE;MAC/F;MACAyB,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,CAAC;;EAEF;AACF;AACA;EACE1C,cAAc,CAAC,MAAM;IACnB0C,kBAAkB,CAAC,CAAC;EACtB,CAAC,CAAC;;EAEF;AACF;AACA;EACErD,iBAAiB,CAAC,MAAM;IACtB,IAAI0B,KAAK,CAACE,WAAW,KAAK4D,SAAS,EAAE;MACnC7D,MAAM,CAACU,OAAO,CAACqC,cAAc,CAAChD,KAAK,CAACE,WAAW,CAAC;IAClD;EACF,CAAC,EAAE,CAACD,MAAM,EAAEkB,MAAM,EAAEnB,KAAK,CAACE,WAAW,CAAC,CAAC;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}