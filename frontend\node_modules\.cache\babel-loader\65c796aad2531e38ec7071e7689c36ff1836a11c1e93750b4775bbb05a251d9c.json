{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { gridFocusCellSelector, gridFocusColumnGroupHeaderSelector } from \"./gridFocusStateSelector.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nexport const focusStateInitializer = state => _extends({}, state, {\n  focus: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  },\n  tabIndex: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  }\n});\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridRows (method)\n * @requires useGridEditing (event)\n */\nexport const useGridFocus = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFocus');\n  const lastClickedCell = React.useRef(null);\n  const hasRootReference = apiRef.current.rootElementRef.current !== null;\n  const publishCellFocusOut = React.useCallback((cell, event) => {\n    if (cell) {\n      // The row might have been deleted\n      if (apiRef.current.getRow(cell.id)) {\n        apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n      }\n    }\n  }, [apiRef]);\n  const setCellFocus = React.useCallback((id, field) => {\n    const focusedCell = gridFocusCellSelector(apiRef);\n    if (focusedCell?.id === id && focusedCell?.field === field) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on cell with id=${id} and field=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n\n    // The row might have been deleted\n    if (!apiRef.current.getRow(id)) {\n      return;\n    }\n    if (focusedCell) {\n      // There's a focused cell but another cell was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, {});\n    }\n    apiRef.current.publishEvent('cellFocusIn', apiRef.current.getCellParams(id, field));\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFocus = React.useCallback((field, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFilterFocus = React.useCallback((field, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header filter with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnGroupHeaderFocus = React.useCallback((field, depth, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell) {\n      apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        },\n        focus: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        }\n      });\n    });\n  }, [apiRef]);\n  const getColumnGroupHeaderFocus = React.useCallback(() => gridFocusColumnGroupHeaderSelector(apiRef), [apiRef]);\n  const moveFocusToRelativeCell = React.useCallback((id, field, direction) => {\n    let columnIndexToFocus = apiRef.current.getColumnIndex(field);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const pinnedRows = gridPinnedRowsSelector(apiRef);\n\n    // Include pinned rows as well\n    const currentPageRows = [].concat(pinnedRows.top || [], currentPage.rows, pinnedRows.bottom || []);\n    let rowIndexToFocus = currentPageRows.findIndex(row => row.id === id);\n    if (direction === 'right') {\n      columnIndexToFocus += 1;\n    } else if (direction === 'left') {\n      columnIndexToFocus -= 1;\n    } else {\n      rowIndexToFocus += 1;\n    }\n    if (columnIndexToFocus >= visibleColumns.length) {\n      // Go to next row if we are after the last column\n      rowIndexToFocus += 1;\n      if (rowIndexToFocus < currentPageRows.length) {\n        // Go to first column of the next row if there's one more row\n        columnIndexToFocus = 0;\n      }\n    } else if (columnIndexToFocus < 0) {\n      // Go to previous row if we are before the first column\n      rowIndexToFocus -= 1;\n      if (rowIndexToFocus >= 0) {\n        // Go to last column of the previous if there's one more row\n        columnIndexToFocus = visibleColumns.length - 1;\n      }\n    }\n    rowIndexToFocus = clamp(rowIndexToFocus, 0, currentPageRows.length - 1);\n    const rowToFocus = currentPageRows[rowIndexToFocus];\n    if (!rowToFocus) {\n      return;\n    }\n    const colSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowToFocus.id, columnIndexToFocus);\n    if (colSpanInfo && colSpanInfo.spannedByColSpan) {\n      if (direction === 'left' || direction === 'below') {\n        columnIndexToFocus = colSpanInfo.leftVisibleCellIndex;\n      } else if (direction === 'right') {\n        columnIndexToFocus = colSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    columnIndexToFocus = clamp(columnIndexToFocus, 0, visibleColumns.length - 1);\n    const columnToFocus = visibleColumns[columnIndexToFocus];\n    apiRef.current.setCellFocus(rowToFocus.id, columnToFocus.field);\n  }, [apiRef, props.pagination, props.paginationMode]);\n  const handleCellDoubleClick = React.useCallback(({\n    id,\n    field\n  }) => {\n    apiRef.current.setCellFocus(id, field);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // GRID_CELL_NAVIGATION_KEY_DOWN handles the focus on Enter, Tab and navigation keys\n    if (event.key === 'Enter' || event.key === 'Tab' || event.key === 'Shift' || isNavigationKey(event.key)) {\n      return;\n    }\n    apiRef.current.setCellFocus(params.id, params.field);\n  }, [apiRef]);\n  const handleColumnHeaderFocus = React.useCallback(({\n    field\n  }, event) => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef]);\n  const handleColumnGroupHeaderFocus = React.useCallback(({\n    fields,\n    depth\n  }, event) => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup !== null && focusedColumnGroup.depth === depth && fields.includes(focusedColumnGroup.field)) {\n      // This group cell has already been focused\n      return;\n    }\n    apiRef.current.setColumnGroupHeaderFocus(fields[0], depth, event);\n  }, [apiRef]);\n  const handleBlur = React.useCallback((_, event) => {\n    if (event.relatedTarget?.getAttribute('class')?.includes(gridClasses.columnHeader)) {\n      return;\n    }\n    logger.debug(`Clearing focus`);\n    apiRef.current.setState(state => _extends({}, state, {\n      focus: {\n        cell: null,\n        columnHeader: null,\n        columnHeaderFilter: null,\n        columnGroupHeader: null\n      }\n    }));\n  }, [logger, apiRef]);\n  const handleCellMouseDown = React.useCallback(params => {\n    lastClickedCell.current = params;\n  }, []);\n  const handleDocumentClick = React.useCallback(event => {\n    const cellParams = lastClickedCell.current;\n    lastClickedCell.current = null;\n    const focusedCell = gridFocusCellSelector(apiRef);\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    if (!focusedCell) {\n      if (cellParams) {\n        apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n      }\n      return;\n    }\n    if (cellParams?.id === focusedCell.id && cellParams?.field === focusedCell.field) {\n      return;\n    }\n    const cellElement = apiRef.current.getCellElement(focusedCell.id, focusedCell.field);\n    if (cellElement?.contains(event.target)) {\n      return;\n    }\n    if (cellParams) {\n      apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n    } else {\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: null,\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n\n      // There's a focused cell but another element (not a cell) was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, event);\n    }\n  }, [apiRef, publishCellFocusOut]);\n  const handleCellModeChange = React.useCallback(params => {\n    if (params.cellMode === 'view') {\n      return;\n    }\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell?.id !== params.id || cell?.field !== params.field) {\n      apiRef.current.setCellFocus(params.id, params.field);\n    }\n  }, [apiRef]);\n  const handleRowSet = React.useCallback(() => {\n    const cell = gridFocusCellSelector(apiRef);\n\n    // If the focused cell is in a row which does not exist anymore,\n    // focus previous row or remove the focus\n    if (cell && !apiRef.current.getRow(cell.id)) {\n      const lastFocusedRowId = cell.id;\n      let nextRowId = null;\n      if (typeof lastFocusedRowId !== 'undefined') {\n        const rowEl = apiRef.current.getRowElement(lastFocusedRowId);\n        const lastFocusedRowIndex = rowEl?.dataset.rowindex ? Number(rowEl?.dataset.rowindex) : 0;\n        const currentPage = getVisibleRows(apiRef, {\n          pagination: props.pagination,\n          paginationMode: props.paginationMode\n        });\n        const nextRow = currentPage.rows[clamp(lastFocusedRowIndex, 0, currentPage.rows.length - 1)];\n        nextRowId = nextRow?.id ?? null;\n      }\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: nextRowId === null ? null : {\n            id: nextRowId,\n            field: cell.field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n    }\n  }, [apiRef, props.pagination, props.paginationMode]);\n  const handlePaginationModelChange = useEventCallback(() => {\n    const currentFocusedCell = gridFocusCellSelector(apiRef);\n    if (!currentFocusedCell) {\n      return;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const rowIsInCurrentPage = currentPage.rows.find(row => row.id === currentFocusedCell.id);\n    if (rowIsInCurrentPage || currentPage.rows.length === 0) {\n      return;\n    }\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id: currentPage.rows[0].id,\n            field: visibleColumns[0].field\n          },\n          columnGroupHeader: null,\n          columnHeader: null,\n          columnHeaderFilter: null\n        }\n      });\n    });\n  });\n  const focusApi = {\n    setCellFocus,\n    setColumnHeaderFocus,\n    setColumnHeaderFilterFocus\n  };\n  const focusPrivateApi = {\n    moveFocusToRelativeCell,\n    setColumnGroupHeaderFocus,\n    getColumnGroupHeaderFocus\n  };\n  useGridApiMethod(apiRef, focusApi, 'public');\n  useGridApiMethod(apiRef, focusPrivateApi, 'private');\n  React.useEffect(() => {\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.addEventListener('mouseup', handleDocumentClick);\n    return () => {\n      doc.removeEventListener('mouseup', handleDocumentClick);\n    };\n  }, [apiRef, hasRootReference, handleDocumentClick]);\n  useGridEvent(apiRef, 'columnHeaderBlur', handleBlur);\n  useGridEvent(apiRef, 'cellDoubleClick', handleCellDoubleClick);\n  useGridEvent(apiRef, 'cellMouseDown', handleCellMouseDown);\n  useGridEvent(apiRef, 'cellKeyDown', handleCellKeyDown);\n  useGridEvent(apiRef, 'cellModeChange', handleCellModeChange);\n  useGridEvent(apiRef, 'columnHeaderFocus', handleColumnHeaderFocus);\n  useGridEvent(apiRef, 'columnGroupHeaderFocus', handleColumnGroupHeaderFocus);\n  useGridEvent(apiRef, 'rowsSet', handleRowSet);\n  useGridEvent(apiRef, 'paginationModelChange', handlePaginationModelChange);\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "ownerDocument", "gridClasses", "useGridApiMethod", "useGridLogger", "useGridEvent", "isNavigationKey", "gridFocusCellSelector", "gridFocusColumnGroupHeaderSelector", "gridVisibleColumnDefinitionsSelector", "getVisibleRows", "clamp", "gridPinnedRowsSelector", "focusStateInitializer", "state", "focus", "cell", "columnHeader", "columnHeaderFilter", "columnGroupHeader", "tabIndex", "useGridFocus", "apiRef", "props", "logger", "lastClickedCell", "useRef", "hasRootReference", "current", "rootElementRef", "publishCellFocusOut", "useCallback", "event", "getRow", "id", "publishEvent", "getCellParams", "field", "setCellFocus", "focusedCell", "setState", "debug", "setColumnHeaderFocus", "setColumnHeaderFilterFocus", "setColumnGroupHeaderFocus", "depth", "getColumnGroupHeaderFocus", "moveFocusToRelativeCell", "direction", "columnIndexToFocus", "getColumnIndex", "visibleColumns", "currentPage", "pagination", "paginationMode", "pinnedRows", "currentPageRows", "concat", "top", "rows", "bottom", "rowIndexToFocus", "findIndex", "row", "length", "rowToFocus", "colSpanInfo", "unstable_getCellColSpanInfo", "spannedByColSpan", "leftVisibleCellIndex", "rightVisibleCellIndex", "columnToFocus", "handleCellDoubleClick", "handleCellKeyDown", "params", "key", "handleColumnHeaderFocus", "target", "currentTarget", "handleColumnGroupHeaderFocus", "fields", "focusedColumnGroup", "includes", "handleBlur", "_", "relatedTarget", "getAttribute", "handleCellMouseDown", "handleDocumentClick", "cellParams", "canUpdateFocus", "unstable_applyPipeProcessors", "cellElement", "getCellElement", "contains", "handleCellModeChange", "cellMode", "handleRowSet", "lastFocusedRowId", "nextRowId", "rowEl", "getRowElement", "lastFocusedRowIndex", "dataset", "rowindex", "Number", "nextRow", "handlePaginationModelChange", "currentFocusedCell", "rowIsInCurrentPage", "find", "focusApi", "focusPrivateApi", "useEffect", "doc", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/focus/useGridFocus.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { gridFocusCellSelector, gridFocusColumnGroupHeaderSelector } from \"./gridFocusStateSelector.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nexport const focusStateInitializer = state => _extends({}, state, {\n  focus: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  },\n  tabIndex: {\n    cell: null,\n    columnHeader: null,\n    columnHeaderFilter: null,\n    columnGroupHeader: null\n  }\n});\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridRows (method)\n * @requires useGridEditing (event)\n */\nexport const useGridFocus = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridFocus');\n  const lastClickedCell = React.useRef(null);\n  const hasRootReference = apiRef.current.rootElementRef.current !== null;\n  const publishCellFocusOut = React.useCallback((cell, event) => {\n    if (cell) {\n      // The row might have been deleted\n      if (apiRef.current.getRow(cell.id)) {\n        apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n      }\n    }\n  }, [apiRef]);\n  const setCellFocus = React.useCallback((id, field) => {\n    const focusedCell = gridFocusCellSelector(apiRef);\n    if (focusedCell?.id === id && focusedCell?.field === field) {\n      return;\n    }\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on cell with id=${id} and field=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          cell: {\n            id,\n            field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n\n    // The row might have been deleted\n    if (!apiRef.current.getRow(id)) {\n      return;\n    }\n    if (focusedCell) {\n      // There's a focused cell but another cell was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, {});\n    }\n    apiRef.current.publishEvent('cellFocusIn', apiRef.current.getCellParams(id, field));\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFocus = React.useCallback((field, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: {\n            field\n          },\n          columnHeaderFilter: null,\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnHeaderFilterFocus = React.useCallback((field, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    publishCellFocusOut(cell, event);\n    apiRef.current.setState(state => {\n      logger.debug(`Focusing on column header filter with colIndex=${field}`);\n      return _extends({}, state, {\n        tabIndex: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        },\n        focus: {\n          columnHeader: null,\n          columnHeaderFilter: {\n            field\n          },\n          cell: null,\n          columnGroupHeader: null\n        }\n      });\n    });\n  }, [apiRef, logger, publishCellFocusOut]);\n  const setColumnGroupHeaderFocus = React.useCallback((field, depth, event = {}) => {\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell) {\n      apiRef.current.publishEvent('cellFocusOut', apiRef.current.getCellParams(cell.id, cell.field), event);\n    }\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        },\n        focus: {\n          columnGroupHeader: {\n            field,\n            depth\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          cell: null\n        }\n      });\n    });\n  }, [apiRef]);\n  const getColumnGroupHeaderFocus = React.useCallback(() => gridFocusColumnGroupHeaderSelector(apiRef), [apiRef]);\n  const moveFocusToRelativeCell = React.useCallback((id, field, direction) => {\n    let columnIndexToFocus = apiRef.current.getColumnIndex(field);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const pinnedRows = gridPinnedRowsSelector(apiRef);\n\n    // Include pinned rows as well\n    const currentPageRows = [].concat(pinnedRows.top || [], currentPage.rows, pinnedRows.bottom || []);\n    let rowIndexToFocus = currentPageRows.findIndex(row => row.id === id);\n    if (direction === 'right') {\n      columnIndexToFocus += 1;\n    } else if (direction === 'left') {\n      columnIndexToFocus -= 1;\n    } else {\n      rowIndexToFocus += 1;\n    }\n    if (columnIndexToFocus >= visibleColumns.length) {\n      // Go to next row if we are after the last column\n      rowIndexToFocus += 1;\n      if (rowIndexToFocus < currentPageRows.length) {\n        // Go to first column of the next row if there's one more row\n        columnIndexToFocus = 0;\n      }\n    } else if (columnIndexToFocus < 0) {\n      // Go to previous row if we are before the first column\n      rowIndexToFocus -= 1;\n      if (rowIndexToFocus >= 0) {\n        // Go to last column of the previous if there's one more row\n        columnIndexToFocus = visibleColumns.length - 1;\n      }\n    }\n    rowIndexToFocus = clamp(rowIndexToFocus, 0, currentPageRows.length - 1);\n    const rowToFocus = currentPageRows[rowIndexToFocus];\n    if (!rowToFocus) {\n      return;\n    }\n    const colSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowToFocus.id, columnIndexToFocus);\n    if (colSpanInfo && colSpanInfo.spannedByColSpan) {\n      if (direction === 'left' || direction === 'below') {\n        columnIndexToFocus = colSpanInfo.leftVisibleCellIndex;\n      } else if (direction === 'right') {\n        columnIndexToFocus = colSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    columnIndexToFocus = clamp(columnIndexToFocus, 0, visibleColumns.length - 1);\n    const columnToFocus = visibleColumns[columnIndexToFocus];\n    apiRef.current.setCellFocus(rowToFocus.id, columnToFocus.field);\n  }, [apiRef, props.pagination, props.paginationMode]);\n  const handleCellDoubleClick = React.useCallback(({\n    id,\n    field\n  }) => {\n    apiRef.current.setCellFocus(id, field);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // GRID_CELL_NAVIGATION_KEY_DOWN handles the focus on Enter, Tab and navigation keys\n    if (event.key === 'Enter' || event.key === 'Tab' || event.key === 'Shift' || isNavigationKey(event.key)) {\n      return;\n    }\n    apiRef.current.setCellFocus(params.id, params.field);\n  }, [apiRef]);\n  const handleColumnHeaderFocus = React.useCallback(({\n    field\n  }, event) => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef]);\n  const handleColumnGroupHeaderFocus = React.useCallback(({\n    fields,\n    depth\n  }, event) => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup !== null && focusedColumnGroup.depth === depth && fields.includes(focusedColumnGroup.field)) {\n      // This group cell has already been focused\n      return;\n    }\n    apiRef.current.setColumnGroupHeaderFocus(fields[0], depth, event);\n  }, [apiRef]);\n  const handleBlur = React.useCallback((_, event) => {\n    if (event.relatedTarget?.getAttribute('class')?.includes(gridClasses.columnHeader)) {\n      return;\n    }\n    logger.debug(`Clearing focus`);\n    apiRef.current.setState(state => _extends({}, state, {\n      focus: {\n        cell: null,\n        columnHeader: null,\n        columnHeaderFilter: null,\n        columnGroupHeader: null\n      }\n    }));\n  }, [logger, apiRef]);\n  const handleCellMouseDown = React.useCallback(params => {\n    lastClickedCell.current = params;\n  }, []);\n  const handleDocumentClick = React.useCallback(event => {\n    const cellParams = lastClickedCell.current;\n    lastClickedCell.current = null;\n    const focusedCell = gridFocusCellSelector(apiRef);\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    if (!focusedCell) {\n      if (cellParams) {\n        apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n      }\n      return;\n    }\n    if (cellParams?.id === focusedCell.id && cellParams?.field === focusedCell.field) {\n      return;\n    }\n    const cellElement = apiRef.current.getCellElement(focusedCell.id, focusedCell.field);\n    if (cellElement?.contains(event.target)) {\n      return;\n    }\n    if (cellParams) {\n      apiRef.current.setCellFocus(cellParams.id, cellParams.field);\n    } else {\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: null,\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n\n      // There's a focused cell but another element (not a cell) was clicked\n      // Publishes an event to notify that the focus was lost\n      publishCellFocusOut(focusedCell, event);\n    }\n  }, [apiRef, publishCellFocusOut]);\n  const handleCellModeChange = React.useCallback(params => {\n    if (params.cellMode === 'view') {\n      return;\n    }\n    const cell = gridFocusCellSelector(apiRef);\n    if (cell?.id !== params.id || cell?.field !== params.field) {\n      apiRef.current.setCellFocus(params.id, params.field);\n    }\n  }, [apiRef]);\n  const handleRowSet = React.useCallback(() => {\n    const cell = gridFocusCellSelector(apiRef);\n\n    // If the focused cell is in a row which does not exist anymore,\n    // focus previous row or remove the focus\n    if (cell && !apiRef.current.getRow(cell.id)) {\n      const lastFocusedRowId = cell.id;\n      let nextRowId = null;\n      if (typeof lastFocusedRowId !== 'undefined') {\n        const rowEl = apiRef.current.getRowElement(lastFocusedRowId);\n        const lastFocusedRowIndex = rowEl?.dataset.rowindex ? Number(rowEl?.dataset.rowindex) : 0;\n        const currentPage = getVisibleRows(apiRef, {\n          pagination: props.pagination,\n          paginationMode: props.paginationMode\n        });\n        const nextRow = currentPage.rows[clamp(lastFocusedRowIndex, 0, currentPage.rows.length - 1)];\n        nextRowId = nextRow?.id ?? null;\n      }\n      apiRef.current.setState(state => _extends({}, state, {\n        focus: {\n          cell: nextRowId === null ? null : {\n            id: nextRowId,\n            field: cell.field\n          },\n          columnHeader: null,\n          columnHeaderFilter: null,\n          columnGroupHeader: null\n        }\n      }));\n    }\n  }, [apiRef, props.pagination, props.paginationMode]);\n  const handlePaginationModelChange = useEventCallback(() => {\n    const currentFocusedCell = gridFocusCellSelector(apiRef);\n    if (!currentFocusedCell) {\n      return;\n    }\n    const currentPage = getVisibleRows(apiRef, {\n      pagination: props.pagination,\n      paginationMode: props.paginationMode\n    });\n    const rowIsInCurrentPage = currentPage.rows.find(row => row.id === currentFocusedCell.id);\n    if (rowIsInCurrentPage || currentPage.rows.length === 0) {\n      return;\n    }\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        tabIndex: {\n          cell: {\n            id: currentPage.rows[0].id,\n            field: visibleColumns[0].field\n          },\n          columnGroupHeader: null,\n          columnHeader: null,\n          columnHeaderFilter: null\n        }\n      });\n    });\n  });\n  const focusApi = {\n    setCellFocus,\n    setColumnHeaderFocus,\n    setColumnHeaderFilterFocus\n  };\n  const focusPrivateApi = {\n    moveFocusToRelativeCell,\n    setColumnGroupHeaderFocus,\n    getColumnGroupHeaderFocus\n  };\n  useGridApiMethod(apiRef, focusApi, 'public');\n  useGridApiMethod(apiRef, focusPrivateApi, 'private');\n  React.useEffect(() => {\n    const doc = ownerDocument(apiRef.current.rootElementRef.current);\n    doc.addEventListener('mouseup', handleDocumentClick);\n    return () => {\n      doc.removeEventListener('mouseup', handleDocumentClick);\n    };\n  }, [apiRef, hasRootReference, handleDocumentClick]);\n  useGridEvent(apiRef, 'columnHeaderBlur', handleBlur);\n  useGridEvent(apiRef, 'cellDoubleClick', handleCellDoubleClick);\n  useGridEvent(apiRef, 'cellMouseDown', handleCellMouseDown);\n  useGridEvent(apiRef, 'cellKeyDown', handleCellKeyDown);\n  useGridEvent(apiRef, 'cellModeChange', handleCellModeChange);\n  useGridEvent(apiRef, 'columnHeaderFocus', handleColumnHeaderFocus);\n  useGridEvent(apiRef, 'columnGroupHeaderFocus', handleColumnGroupHeaderFocus);\n  useGridEvent(apiRef, 'rowsSet', handleRowSet);\n  useGridEvent(apiRef, 'paginationModelChange', handlePaginationModelChange);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,qBAAqB,EAAEC,kCAAkC,QAAQ,6BAA6B;AACvG,SAASC,oCAAoC,QAAQ,mCAAmC;AACxF,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAO,MAAMC,qBAAqB,GAAGC,KAAK,IAAIhB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;EAChEC,KAAK,EAAE;IACLC,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,iBAAiB,EAAE;EACrB,CAAC;EACDC,QAAQ,EAAE;IACRJ,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,IAAI;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,YAAY,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC7C,MAAMC,MAAM,GAAGpB,aAAa,CAACkB,MAAM,EAAE,cAAc,CAAC;EACpD,MAAMG,eAAe,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAMC,gBAAgB,GAAGL,MAAM,CAACM,OAAO,CAACC,cAAc,CAACD,OAAO,KAAK,IAAI;EACvE,MAAME,mBAAmB,GAAG/B,KAAK,CAACgC,WAAW,CAAC,CAACf,IAAI,EAAEgB,KAAK,KAAK;IAC7D,IAAIhB,IAAI,EAAE;MACR;MACA,IAAIM,MAAM,CAACM,OAAO,CAACK,MAAM,CAACjB,IAAI,CAACkB,EAAE,CAAC,EAAE;QAClCZ,MAAM,CAACM,OAAO,CAACO,YAAY,CAAC,cAAc,EAAEb,MAAM,CAACM,OAAO,CAACQ,aAAa,CAACpB,IAAI,CAACkB,EAAE,EAAElB,IAAI,CAACqB,KAAK,CAAC,EAAEL,KAAK,CAAC;MACvG;IACF;EACF,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,MAAMgB,YAAY,GAAGvC,KAAK,CAACgC,WAAW,CAAC,CAACG,EAAE,EAAEG,KAAK,KAAK;IACpD,MAAME,WAAW,GAAGhC,qBAAqB,CAACe,MAAM,CAAC;IACjD,IAAIiB,WAAW,EAAEL,EAAE,KAAKA,EAAE,IAAIK,WAAW,EAAEF,KAAK,KAAKA,KAAK,EAAE;MAC1D;IACF;IACAf,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/BU,MAAM,CAACiB,KAAK,CAAC,4BAA4BP,EAAE,cAAcG,KAAK,EAAE,CAAC;MACjE,OAAOvC,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRJ,IAAI,EAAE;YACJkB,EAAE;YACFG;UACF,CAAC;UACDpB,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBC,iBAAiB,EAAE;QACrB,CAAC;QACDJ,KAAK,EAAE;UACLC,IAAI,EAAE;YACJkB,EAAE;YACFG;UACF,CAAC;UACDpB,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBC,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,IAAI,CAACG,MAAM,CAACM,OAAO,CAACK,MAAM,CAACC,EAAE,CAAC,EAAE;MAC9B;IACF;IACA,IAAIK,WAAW,EAAE;MACf;MACA;MACAT,mBAAmB,CAACS,WAAW,EAAE,CAAC,CAAC,CAAC;IACtC;IACAjB,MAAM,CAACM,OAAO,CAACO,YAAY,CAAC,aAAa,EAAEb,MAAM,CAACM,OAAO,CAACQ,aAAa,CAACF,EAAE,EAAEG,KAAK,CAAC,CAAC;EACrF,CAAC,EAAE,CAACf,MAAM,EAAEE,MAAM,EAAEM,mBAAmB,CAAC,CAAC;EACzC,MAAMY,oBAAoB,GAAG3C,KAAK,CAACgC,WAAW,CAAC,CAACM,KAAK,EAAEL,KAAK,GAAG,CAAC,CAAC,KAAK;IACpE,MAAMhB,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;IAC1CQ,mBAAmB,CAACd,IAAI,EAAEgB,KAAK,CAAC;IAChCV,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/BU,MAAM,CAACiB,KAAK,CAAC,2CAA2CJ,KAAK,EAAE,CAAC;MAChE,OAAOvC,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRH,YAAY,EAAE;YACZoB;UACF,CAAC;UACDnB,kBAAkB,EAAE,IAAI;UACxBF,IAAI,EAAE,IAAI;UACVG,iBAAiB,EAAE;QACrB,CAAC;QACDJ,KAAK,EAAE;UACLE,YAAY,EAAE;YACZoB;UACF,CAAC;UACDnB,kBAAkB,EAAE,IAAI;UACxBF,IAAI,EAAE,IAAI;UACVG,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACG,MAAM,EAAEE,MAAM,EAAEM,mBAAmB,CAAC,CAAC;EACzC,MAAMa,0BAA0B,GAAG5C,KAAK,CAACgC,WAAW,CAAC,CAACM,KAAK,EAAEL,KAAK,GAAG,CAAC,CAAC,KAAK;IAC1E,MAAMhB,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;IAC1CQ,mBAAmB,CAACd,IAAI,EAAEgB,KAAK,CAAC;IAChCV,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/BU,MAAM,CAACiB,KAAK,CAAC,kDAAkDJ,KAAK,EAAE,CAAC;MACvE,OAAOvC,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRH,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE;YAClBmB;UACF,CAAC;UACDrB,IAAI,EAAE,IAAI;UACVG,iBAAiB,EAAE;QACrB,CAAC;QACDJ,KAAK,EAAE;UACLE,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE;YAClBmB;UACF,CAAC;UACDrB,IAAI,EAAE,IAAI;UACVG,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACG,MAAM,EAAEE,MAAM,EAAEM,mBAAmB,CAAC,CAAC;EACzC,MAAMc,yBAAyB,GAAG7C,KAAK,CAACgC,WAAW,CAAC,CAACM,KAAK,EAAEQ,KAAK,EAAEb,KAAK,GAAG,CAAC,CAAC,KAAK;IAChF,MAAMhB,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;IAC1C,IAAIN,IAAI,EAAE;MACRM,MAAM,CAACM,OAAO,CAACO,YAAY,CAAC,cAAc,EAAEb,MAAM,CAACM,OAAO,CAACQ,aAAa,CAACpB,IAAI,CAACkB,EAAE,EAAElB,IAAI,CAACqB,KAAK,CAAC,EAAEL,KAAK,CAAC;IACvG;IACAV,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/B,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRD,iBAAiB,EAAE;YACjBkB,KAAK;YACLQ;UACF,CAAC;UACD5B,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBF,IAAI,EAAE;QACR,CAAC;QACDD,KAAK,EAAE;UACLI,iBAAiB,EAAE;YACjBkB,KAAK;YACLQ;UACF,CAAC;UACD5B,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBF,IAAI,EAAE;QACR;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACM,MAAM,CAAC,CAAC;EACZ,MAAMwB,yBAAyB,GAAG/C,KAAK,CAACgC,WAAW,CAAC,MAAMvB,kCAAkC,CAACc,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC/G,MAAMyB,uBAAuB,GAAGhD,KAAK,CAACgC,WAAW,CAAC,CAACG,EAAE,EAAEG,KAAK,EAAEW,SAAS,KAAK;IAC1E,IAAIC,kBAAkB,GAAG3B,MAAM,CAACM,OAAO,CAACsB,cAAc,CAACb,KAAK,CAAC;IAC7D,MAAMc,cAAc,GAAG1C,oCAAoC,CAACa,MAAM,CAAC;IACnE,MAAM8B,WAAW,GAAG1C,cAAc,CAACY,MAAM,EAAE;MACzC+B,UAAU,EAAE9B,KAAK,CAAC8B,UAAU;MAC5BC,cAAc,EAAE/B,KAAK,CAAC+B;IACxB,CAAC,CAAC;IACF,MAAMC,UAAU,GAAG3C,sBAAsB,CAACU,MAAM,CAAC;;IAEjD;IACA,MAAMkC,eAAe,GAAG,EAAE,CAACC,MAAM,CAACF,UAAU,CAACG,GAAG,IAAI,EAAE,EAAEN,WAAW,CAACO,IAAI,EAAEJ,UAAU,CAACK,MAAM,IAAI,EAAE,CAAC;IAClG,IAAIC,eAAe,GAAGL,eAAe,CAACM,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAKA,EAAE,CAAC;IACrE,IAAIc,SAAS,KAAK,OAAO,EAAE;MACzBC,kBAAkB,IAAI,CAAC;IACzB,CAAC,MAAM,IAAID,SAAS,KAAK,MAAM,EAAE;MAC/BC,kBAAkB,IAAI,CAAC;IACzB,CAAC,MAAM;MACLY,eAAe,IAAI,CAAC;IACtB;IACA,IAAIZ,kBAAkB,IAAIE,cAAc,CAACa,MAAM,EAAE;MAC/C;MACAH,eAAe,IAAI,CAAC;MACpB,IAAIA,eAAe,GAAGL,eAAe,CAACQ,MAAM,EAAE;QAC5C;QACAf,kBAAkB,GAAG,CAAC;MACxB;IACF,CAAC,MAAM,IAAIA,kBAAkB,GAAG,CAAC,EAAE;MACjC;MACAY,eAAe,IAAI,CAAC;MACpB,IAAIA,eAAe,IAAI,CAAC,EAAE;QACxB;QACAZ,kBAAkB,GAAGE,cAAc,CAACa,MAAM,GAAG,CAAC;MAChD;IACF;IACAH,eAAe,GAAGlD,KAAK,CAACkD,eAAe,EAAE,CAAC,EAAEL,eAAe,CAACQ,MAAM,GAAG,CAAC,CAAC;IACvE,MAAMC,UAAU,GAAGT,eAAe,CAACK,eAAe,CAAC;IACnD,IAAI,CAACI,UAAU,EAAE;MACf;IACF;IACA,MAAMC,WAAW,GAAG5C,MAAM,CAACM,OAAO,CAACuC,2BAA2B,CAACF,UAAU,CAAC/B,EAAE,EAAEe,kBAAkB,CAAC;IACjG,IAAIiB,WAAW,IAAIA,WAAW,CAACE,gBAAgB,EAAE;MAC/C,IAAIpB,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;QACjDC,kBAAkB,GAAGiB,WAAW,CAACG,oBAAoB;MACvD,CAAC,MAAM,IAAIrB,SAAS,KAAK,OAAO,EAAE;QAChCC,kBAAkB,GAAGiB,WAAW,CAACI,qBAAqB;MACxD;IACF;IACArB,kBAAkB,GAAGtC,KAAK,CAACsC,kBAAkB,EAAE,CAAC,EAAEE,cAAc,CAACa,MAAM,GAAG,CAAC,CAAC;IAC5E,MAAMO,aAAa,GAAGpB,cAAc,CAACF,kBAAkB,CAAC;IACxD3B,MAAM,CAACM,OAAO,CAACU,YAAY,CAAC2B,UAAU,CAAC/B,EAAE,EAAEqC,aAAa,CAAClC,KAAK,CAAC;EACjE,CAAC,EAAE,CAACf,MAAM,EAAEC,KAAK,CAAC8B,UAAU,EAAE9B,KAAK,CAAC+B,cAAc,CAAC,CAAC;EACpD,MAAMkB,qBAAqB,GAAGzE,KAAK,CAACgC,WAAW,CAAC,CAAC;IAC/CG,EAAE;IACFG;EACF,CAAC,KAAK;IACJf,MAAM,CAACM,OAAO,CAACU,YAAY,CAACJ,EAAE,EAAEG,KAAK,CAAC;EACxC,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EACZ,MAAMmD,iBAAiB,GAAG1E,KAAK,CAACgC,WAAW,CAAC,CAAC2C,MAAM,EAAE1C,KAAK,KAAK;IAC7D;IACA,IAAIA,KAAK,CAAC2C,GAAG,KAAK,OAAO,IAAI3C,KAAK,CAAC2C,GAAG,KAAK,KAAK,IAAI3C,KAAK,CAAC2C,GAAG,KAAK,OAAO,IAAIrE,eAAe,CAAC0B,KAAK,CAAC2C,GAAG,CAAC,EAAE;MACvG;IACF;IACArD,MAAM,CAACM,OAAO,CAACU,YAAY,CAACoC,MAAM,CAACxC,EAAE,EAAEwC,MAAM,CAACrC,KAAK,CAAC;EACtD,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EACZ,MAAMsD,uBAAuB,GAAG7E,KAAK,CAACgC,WAAW,CAAC,CAAC;IACjDM;EACF,CAAC,EAAEL,KAAK,KAAK;IACX,IAAIA,KAAK,CAAC6C,MAAM,KAAK7C,KAAK,CAAC8C,aAAa,EAAE;MACxC;IACF;IACAxD,MAAM,CAACM,OAAO,CAACc,oBAAoB,CAACL,KAAK,EAAEL,KAAK,CAAC;EACnD,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,MAAMyD,4BAA4B,GAAGhF,KAAK,CAACgC,WAAW,CAAC,CAAC;IACtDiD,MAAM;IACNnC;EACF,CAAC,EAAEb,KAAK,KAAK;IACX,IAAIA,KAAK,CAAC6C,MAAM,KAAK7C,KAAK,CAAC8C,aAAa,EAAE;MACxC;IACF;IACA,MAAMG,kBAAkB,GAAGzE,kCAAkC,CAACc,MAAM,CAAC;IACrE,IAAI2D,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,CAACpC,KAAK,KAAKA,KAAK,IAAImC,MAAM,CAACE,QAAQ,CAACD,kBAAkB,CAAC5C,KAAK,CAAC,EAAE;MAClH;MACA;IACF;IACAf,MAAM,CAACM,OAAO,CAACgB,yBAAyB,CAACoC,MAAM,CAAC,CAAC,CAAC,EAAEnC,KAAK,EAAEb,KAAK,CAAC;EACnE,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,MAAM6D,UAAU,GAAGpF,KAAK,CAACgC,WAAW,CAAC,CAACqD,CAAC,EAAEpD,KAAK,KAAK;IACjD,IAAIA,KAAK,CAACqD,aAAa,EAAEC,YAAY,CAAC,OAAO,CAAC,EAAEJ,QAAQ,CAAChF,WAAW,CAACe,YAAY,CAAC,EAAE;MAClF;IACF;IACAO,MAAM,CAACiB,KAAK,CAAC,gBAAgB,CAAC;IAC9BnB,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAIhB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;MACnDC,KAAK,EAAE;QACLC,IAAI,EAAE,IAAI;QACVC,YAAY,EAAE,IAAI;QAClBC,kBAAkB,EAAE,IAAI;QACxBC,iBAAiB,EAAE;MACrB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACK,MAAM,EAAEF,MAAM,CAAC,CAAC;EACpB,MAAMiE,mBAAmB,GAAGxF,KAAK,CAACgC,WAAW,CAAC2C,MAAM,IAAI;IACtDjD,eAAe,CAACG,OAAO,GAAG8C,MAAM;EAClC,CAAC,EAAE,EAAE,CAAC;EACN,MAAMc,mBAAmB,GAAGzF,KAAK,CAACgC,WAAW,CAACC,KAAK,IAAI;IACrD,MAAMyD,UAAU,GAAGhE,eAAe,CAACG,OAAO;IAC1CH,eAAe,CAACG,OAAO,GAAG,IAAI;IAC9B,MAAMW,WAAW,GAAGhC,qBAAqB,CAACe,MAAM,CAAC;IACjD,MAAMoE,cAAc,GAAGpE,MAAM,CAACM,OAAO,CAAC+D,4BAA4B,CAAC,gBAAgB,EAAE,IAAI,EAAE;MACzF3D,KAAK;MACLhB,IAAI,EAAEyE;IACR,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,EAAE;MACnB;IACF;IACA,IAAI,CAACnD,WAAW,EAAE;MAChB,IAAIkD,UAAU,EAAE;QACdnE,MAAM,CAACM,OAAO,CAACU,YAAY,CAACmD,UAAU,CAACvD,EAAE,EAAEuD,UAAU,CAACpD,KAAK,CAAC;MAC9D;MACA;IACF;IACA,IAAIoD,UAAU,EAAEvD,EAAE,KAAKK,WAAW,CAACL,EAAE,IAAIuD,UAAU,EAAEpD,KAAK,KAAKE,WAAW,CAACF,KAAK,EAAE;MAChF;IACF;IACA,MAAMuD,WAAW,GAAGtE,MAAM,CAACM,OAAO,CAACiE,cAAc,CAACtD,WAAW,CAACL,EAAE,EAAEK,WAAW,CAACF,KAAK,CAAC;IACpF,IAAIuD,WAAW,EAAEE,QAAQ,CAAC9D,KAAK,CAAC6C,MAAM,CAAC,EAAE;MACvC;IACF;IACA,IAAIY,UAAU,EAAE;MACdnE,MAAM,CAACM,OAAO,CAACU,YAAY,CAACmD,UAAU,CAACvD,EAAE,EAAEuD,UAAU,CAACpD,KAAK,CAAC;IAC9D,CAAC,MAAM;MACLf,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAIhB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;QACnDC,KAAK,EAAE;UACLC,IAAI,EAAE,IAAI;UACVC,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBC,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC,CAAC;;MAEH;MACA;MACAW,mBAAmB,CAACS,WAAW,EAAEP,KAAK,CAAC;IACzC;EACF,CAAC,EAAE,CAACV,MAAM,EAAEQ,mBAAmB,CAAC,CAAC;EACjC,MAAMiE,oBAAoB,GAAGhG,KAAK,CAACgC,WAAW,CAAC2C,MAAM,IAAI;IACvD,IAAIA,MAAM,CAACsB,QAAQ,KAAK,MAAM,EAAE;MAC9B;IACF;IACA,MAAMhF,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;IAC1C,IAAIN,IAAI,EAAEkB,EAAE,KAAKwC,MAAM,CAACxC,EAAE,IAAIlB,IAAI,EAAEqB,KAAK,KAAKqC,MAAM,CAACrC,KAAK,EAAE;MAC1Df,MAAM,CAACM,OAAO,CAACU,YAAY,CAACoC,MAAM,CAACxC,EAAE,EAAEwC,MAAM,CAACrC,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAACf,MAAM,CAAC,CAAC;EACZ,MAAM2E,YAAY,GAAGlG,KAAK,CAACgC,WAAW,CAAC,MAAM;IAC3C,MAAMf,IAAI,GAAGT,qBAAqB,CAACe,MAAM,CAAC;;IAE1C;IACA;IACA,IAAIN,IAAI,IAAI,CAACM,MAAM,CAACM,OAAO,CAACK,MAAM,CAACjB,IAAI,CAACkB,EAAE,CAAC,EAAE;MAC3C,MAAMgE,gBAAgB,GAAGlF,IAAI,CAACkB,EAAE;MAChC,IAAIiE,SAAS,GAAG,IAAI;MACpB,IAAI,OAAOD,gBAAgB,KAAK,WAAW,EAAE;QAC3C,MAAME,KAAK,GAAG9E,MAAM,CAACM,OAAO,CAACyE,aAAa,CAACH,gBAAgB,CAAC;QAC5D,MAAMI,mBAAmB,GAAGF,KAAK,EAAEG,OAAO,CAACC,QAAQ,GAAGC,MAAM,CAACL,KAAK,EAAEG,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC;QACzF,MAAMpD,WAAW,GAAG1C,cAAc,CAACY,MAAM,EAAE;UACzC+B,UAAU,EAAE9B,KAAK,CAAC8B,UAAU;UAC5BC,cAAc,EAAE/B,KAAK,CAAC+B;QACxB,CAAC,CAAC;QACF,MAAMoD,OAAO,GAAGtD,WAAW,CAACO,IAAI,CAAChD,KAAK,CAAC2F,mBAAmB,EAAE,CAAC,EAAElD,WAAW,CAACO,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5FmC,SAAS,GAAGO,OAAO,EAAExE,EAAE,IAAI,IAAI;MACjC;MACAZ,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAIhB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;QACnDC,KAAK,EAAE;UACLC,IAAI,EAAEmF,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG;YAChCjE,EAAE,EAAEiE,SAAS;YACb9D,KAAK,EAAErB,IAAI,CAACqB;UACd,CAAC;UACDpB,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE,IAAI;UACxBC,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACG,MAAM,EAAEC,KAAK,CAAC8B,UAAU,EAAE9B,KAAK,CAAC+B,cAAc,CAAC,CAAC;EACpD,MAAMqD,2BAA2B,GAAG3G,gBAAgB,CAAC,MAAM;IACzD,MAAM4G,kBAAkB,GAAGrG,qBAAqB,CAACe,MAAM,CAAC;IACxD,IAAI,CAACsF,kBAAkB,EAAE;MACvB;IACF;IACA,MAAMxD,WAAW,GAAG1C,cAAc,CAACY,MAAM,EAAE;MACzC+B,UAAU,EAAE9B,KAAK,CAAC8B,UAAU;MAC5BC,cAAc,EAAE/B,KAAK,CAAC+B;IACxB,CAAC,CAAC;IACF,MAAMuD,kBAAkB,GAAGzD,WAAW,CAACO,IAAI,CAACmD,IAAI,CAAC/C,GAAG,IAAIA,GAAG,CAAC7B,EAAE,KAAK0E,kBAAkB,CAAC1E,EAAE,CAAC;IACzF,IAAI2E,kBAAkB,IAAIzD,WAAW,CAACO,IAAI,CAACK,MAAM,KAAK,CAAC,EAAE;MACvD;IACF;IACA,MAAMb,cAAc,GAAG1C,oCAAoC,CAACa,MAAM,CAAC;IACnEA,MAAM,CAACM,OAAO,CAACY,QAAQ,CAAC1B,KAAK,IAAI;MAC/B,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;QACzBM,QAAQ,EAAE;UACRJ,IAAI,EAAE;YACJkB,EAAE,EAAEkB,WAAW,CAACO,IAAI,CAAC,CAAC,CAAC,CAACzB,EAAE;YAC1BG,KAAK,EAAEc,cAAc,CAAC,CAAC,CAAC,CAACd;UAC3B,CAAC;UACDlB,iBAAiB,EAAE,IAAI;UACvBF,YAAY,EAAE,IAAI;UAClBC,kBAAkB,EAAE;QACtB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM6F,QAAQ,GAAG;IACfzE,YAAY;IACZI,oBAAoB;IACpBC;EACF,CAAC;EACD,MAAMqE,eAAe,GAAG;IACtBjE,uBAAuB;IACvBH,yBAAyB;IACzBE;EACF,CAAC;EACD3C,gBAAgB,CAACmB,MAAM,EAAEyF,QAAQ,EAAE,QAAQ,CAAC;EAC5C5G,gBAAgB,CAACmB,MAAM,EAAE0F,eAAe,EAAE,SAAS,CAAC;EACpDjH,KAAK,CAACkH,SAAS,CAAC,MAAM;IACpB,MAAMC,GAAG,GAAGjH,aAAa,CAACqB,MAAM,CAACM,OAAO,CAACC,cAAc,CAACD,OAAO,CAAC;IAChEsF,GAAG,CAACC,gBAAgB,CAAC,SAAS,EAAE3B,mBAAmB,CAAC;IACpD,OAAO,MAAM;MACX0B,GAAG,CAACE,mBAAmB,CAAC,SAAS,EAAE5B,mBAAmB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,CAAClE,MAAM,EAAEK,gBAAgB,EAAE6D,mBAAmB,CAAC,CAAC;EACnDnF,YAAY,CAACiB,MAAM,EAAE,kBAAkB,EAAE6D,UAAU,CAAC;EACpD9E,YAAY,CAACiB,MAAM,EAAE,iBAAiB,EAAEkD,qBAAqB,CAAC;EAC9DnE,YAAY,CAACiB,MAAM,EAAE,eAAe,EAAEiE,mBAAmB,CAAC;EAC1DlF,YAAY,CAACiB,MAAM,EAAE,aAAa,EAAEmD,iBAAiB,CAAC;EACtDpE,YAAY,CAACiB,MAAM,EAAE,gBAAgB,EAAEyE,oBAAoB,CAAC;EAC5D1F,YAAY,CAACiB,MAAM,EAAE,mBAAmB,EAAEsD,uBAAuB,CAAC;EAClEvE,YAAY,CAACiB,MAAM,EAAE,wBAAwB,EAAEyD,4BAA4B,CAAC;EAC5E1E,YAAY,CAACiB,MAAM,EAAE,SAAS,EAAE2E,YAAY,CAAC;EAC7C5F,YAAY,CAACiB,MAAM,EAAE,uBAAuB,EAAEqF,2BAA2B,CAAC;AAC5E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}