{"ast": null, "code": "import { toInteger } from '../compat/util/toInteger.mjs';\nfunction takeRight(arr, count = 1, guard) {\n  count = guard || count === undefined ? 1 : toInteger(count);\n  if (count <= 0 || arr == null || arr.length === 0) {\n    return [];\n  }\n  return arr.slice(-count);\n}\nexport { takeRight };", "map": {"version": 3, "names": ["toInteger", "takeRight", "arr", "count", "guard", "undefined", "length", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/takeRight.mjs"], "sourcesContent": ["import { toInteger } from '../compat/util/toInteger.mjs';\n\nfunction takeRight(arr, count = 1, guard) {\n    count = guard || count === undefined ? 1 : toInteger(count);\n    if (count <= 0 || arr == null || arr.length === 0) {\n        return [];\n    }\n    return arr.slice(-count);\n}\n\nexport { takeRight };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,8BAA8B;AAExD,SAASC,SAASA,CAACC,GAAG,EAAEC,KAAK,GAAG,CAAC,EAAEC,KAAK,EAAE;EACtCD,KAAK,GAAGC,KAAK,IAAID,KAAK,KAAKE,SAAS,GAAG,CAAC,GAAGL,SAAS,CAACG,KAAK,CAAC;EAC3D,IAAIA,KAAK,IAAI,CAAC,IAAID,GAAG,IAAI,IAAI,IAAIA,GAAG,CAACI,MAAM,KAAK,CAAC,EAAE;IAC/C,OAAO,EAAE;EACb;EACA,OAAOJ,GAAG,CAACK,KAAK,CAAC,CAACJ,KAAK,CAAC;AAC5B;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}