{"ast": null, "code": "const hasSymbol = typeof Symbol === 'function' && Symbol.for;\nexport default hasSymbol ? Symbol.for('mui.nested') : '__THEME_NESTED__';", "map": {"version": 3, "names": ["hasSymbol", "Symbol", "for"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/private-theming/esm/ThemeProvider/nested.js"], "sourcesContent": ["const hasSymbol = typeof Symbol === 'function' && Symbol.for;\nexport default hasSymbol ? Symbol.for('mui.nested') : '__THEME_NESTED__';"], "mappings": "AAAA,MAAMA,SAAS,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG;AAC5D,eAAeF,SAAS,GAAGC,MAAM,CAACC,GAAG,CAAC,YAAY,CAAC,GAAG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}