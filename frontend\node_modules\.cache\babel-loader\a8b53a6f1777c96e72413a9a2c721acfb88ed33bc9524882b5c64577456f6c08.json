{"ast": null, "code": "import { GridLogicOperator } from \"../../../models/gridFilterItem.js\";\nexport const defaultGridFilterLookup = {\n  filteredRowsLookup: {},\n  filteredChildrenCountLookup: {},\n  filteredDescendantCountLookup: {}\n};\nexport const getDefaultGridFilterModel = () => ({\n  items: [],\n  logicOperator: GridLogicOperator.And,\n  quickFilterValues: [],\n  quickFilterLogicOperator: GridLogicOperator.And\n});\n\n/**\n * @param {GridValidRowModel} row The model of the row we want to filter.\n * @param {(filterItem: GridFilterItem) => boolean} shouldApplyItem An optional callback to allow the filtering engine to only apply some items.\n * @param {GridAggregatedFilterItemApplierResult} result The previous result of the filtering engine.\n */\n\n/**\n * Visibility status for each row.\n * A row is visible if it is passing the filters AND if its parents are expanded.\n * If a row is not registered in this lookup, it is visible.\n */", "map": {"version": 3, "names": ["GridLogicOperator", "defaultGridFilterLookup", "filteredRowsLookup", "filteredChildrenCountLookup", "filteredDescendantCountLookup", "getDefaultGridFilterModel", "items", "logicOperator", "And", "quickFilterV<PERSON>ues", "quickFilterLogicOperator"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/filter/gridFilterState.js"], "sourcesContent": ["import { GridLogicOperator } from \"../../../models/gridFilterItem.js\";\nexport const defaultGridFilterLookup = {\n  filteredRowsLookup: {},\n  filteredChildrenCountLookup: {},\n  filteredDescendantCountLookup: {}\n};\nexport const getDefaultGridFilterModel = () => ({\n  items: [],\n  logicOperator: GridLogicOperator.And,\n  quickFilterValues: [],\n  quickFilterLogicOperator: GridLogicOperator.And\n});\n\n/**\n * @param {GridValidRowModel} row The model of the row we want to filter.\n * @param {(filterItem: GridFilterItem) => boolean} shouldApplyItem An optional callback to allow the filtering engine to only apply some items.\n * @param {GridAggregatedFilterItemApplierResult} result The previous result of the filtering engine.\n */\n\n/**\n * Visibility status for each row.\n * A row is visible if it is passing the filters AND if its parents are expanded.\n * If a row is not registered in this lookup, it is visible.\n */"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,mCAAmC;AACrE,OAAO,MAAMC,uBAAuB,GAAG;EACrCC,kBAAkB,EAAE,CAAC,CAAC;EACtBC,2BAA2B,EAAE,CAAC,CAAC;EAC/BC,6BAA6B,EAAE,CAAC;AAClC,CAAC;AACD,OAAO,MAAMC,yBAAyB,GAAGA,CAAA,MAAO;EAC9CC,KAAK,EAAE,EAAE;EACTC,aAAa,EAAEP,iBAAiB,CAACQ,GAAG;EACpCC,iBAAiB,EAAE,EAAE;EACrBC,wBAAwB,EAAEV,iBAAiB,CAACQ;AAC9C,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}