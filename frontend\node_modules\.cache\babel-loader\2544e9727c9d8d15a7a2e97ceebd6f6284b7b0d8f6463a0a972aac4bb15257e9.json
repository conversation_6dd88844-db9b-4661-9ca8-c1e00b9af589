{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridDimensionsSelector = createRootSelector(state => state.dimensions);\n\n/**\n * Get the summed width of all the visible columns.\n * @category Visible Columns\n */\nexport const gridColumnsTotalWidthSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.columnsTotalWidth);\nexport const gridRowHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.rowHeight);\nexport const gridContentHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.contentSize.height);\nexport const gridHasScrollXSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollX);\nexport const gridHasScrollYSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollY);\nexport const gridHasFillerSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.columnsTotalWidth < dimensions.viewportOuterSize.width);\nexport const gridHeaderHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.headerHeight);\nexport const gridGroupHeaderHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.groupHeaderHeight);\nexport const gridHeaderFilterHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.headerFilterHeight);\nexport const gridHorizontalScrollbarHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollX ? dimensions.scrollbarSize : 0);\nexport const gridVerticalScrollbarWidthSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollY ? dimensions.scrollbarSize : 0);\nexport const gridHasBottomFillerSelector = createSelector(gridDimensionsSelector, gridHorizontalScrollbarHeightSelector, (dimensions, height) => {\n  const needsLastRowBorder = dimensions.viewportOuterSize.height - dimensions.minimumSize.height > 0;\n  if (height === 0 && !needsLastRowBorder) {\n    return false;\n  }\n  return true;\n});", "map": {"version": 3, "names": ["createSelector", "createRootSelector", "gridDimensionsSelector", "state", "dimensions", "gridColumnsTotalWidthSelector", "columnsTotalWidth", "gridRowHeightSelector", "rowHeight", "gridContentHeightSelector", "contentSize", "height", "gridHasScrollXSelector", "hasScrollX", "gridHasScrollYSelector", "hasScrollY", "gridHasFillerSelector", "viewportOuterSize", "width", "gridHeaderHeightSelector", "headerHeight", "gridGroupHeaderHeightSelector", "groupHeaderHeight", "gridHeaderFilterHeightSelector", "headerFilterHeight", "gridHorizontalScrollbarHeightSelector", "scrollbarSize", "gridVerticalScrollbarWidthSelector", "gridHasBottomFillerSelector", "needsLastRowBorder", "minimumSize"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/dimensions/gridDimensionsSelectors.js"], "sourcesContent": ["import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridDimensionsSelector = createRootSelector(state => state.dimensions);\n\n/**\n * Get the summed width of all the visible columns.\n * @category Visible Columns\n */\nexport const gridColumnsTotalWidthSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.columnsTotalWidth);\nexport const gridRowHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.rowHeight);\nexport const gridContentHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.contentSize.height);\nexport const gridHasScrollXSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollX);\nexport const gridHasScrollYSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollY);\nexport const gridHasFillerSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.columnsTotalWidth < dimensions.viewportOuterSize.width);\nexport const gridHeaderHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.headerHeight);\nexport const gridGroupHeaderHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.groupHeaderHeight);\nexport const gridHeaderFilterHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.headerFilterHeight);\nexport const gridHorizontalScrollbarHeightSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollX ? dimensions.scrollbarSize : 0);\nexport const gridVerticalScrollbarWidthSelector = createSelector(gridDimensionsSelector, dimensions => dimensions.hasScrollY ? dimensions.scrollbarSize : 0);\nexport const gridHasBottomFillerSelector = createSelector(gridDimensionsSelector, gridHorizontalScrollbarHeightSelector, (dimensions, height) => {\n  const needsLastRowBorder = dimensions.viewportOuterSize.height - dimensions.minimumSize.height > 0;\n  if (height === 0 && !needsLastRowBorder) {\n    return false;\n  }\n  return true;\n});"], "mappings": "AAAA,SAASA,cAAc,EAAEC,kBAAkB,QAAQ,kCAAkC;AACrF,OAAO,MAAMC,sBAAsB,GAAGD,kBAAkB,CAACE,KAAK,IAAIA,KAAK,CAACC,UAAU,CAAC;;AAEnF;AACA;AACA;AACA;AACA,OAAO,MAAMC,6BAA6B,GAAGL,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACE,iBAAiB,CAAC;AAC/H,OAAO,MAAMC,qBAAqB,GAAGP,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACI,SAAS,CAAC;AAC/G,OAAO,MAAMC,yBAAyB,GAAGT,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACM,WAAW,CAACC,MAAM,CAAC;AAC5H,OAAO,MAAMC,sBAAsB,GAAGZ,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACS,UAAU,CAAC;AACjH,OAAO,MAAMC,sBAAsB,GAAGd,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACW,UAAU,CAAC;AACjH,OAAO,MAAMC,qBAAqB,GAAGhB,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACE,iBAAiB,GAAGF,UAAU,CAACa,iBAAiB,CAACC,KAAK,CAAC;AAC5J,OAAO,MAAMC,wBAAwB,GAAGnB,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACgB,YAAY,CAAC;AACrH,OAAO,MAAMC,6BAA6B,GAAGrB,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACkB,iBAAiB,CAAC;AAC/H,OAAO,MAAMC,8BAA8B,GAAGvB,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACoB,kBAAkB,CAAC;AACjI,OAAO,MAAMC,qCAAqC,GAAGzB,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACS,UAAU,GAAGT,UAAU,CAACsB,aAAa,GAAG,CAAC,CAAC;AAC/J,OAAO,MAAMC,kCAAkC,GAAG3B,cAAc,CAACE,sBAAsB,EAAEE,UAAU,IAAIA,UAAU,CAACW,UAAU,GAAGX,UAAU,CAACsB,aAAa,GAAG,CAAC,CAAC;AAC5J,OAAO,MAAME,2BAA2B,GAAG5B,cAAc,CAACE,sBAAsB,EAAEuB,qCAAqC,EAAE,CAACrB,UAAU,EAAEO,MAAM,KAAK;EAC/I,MAAMkB,kBAAkB,GAAGzB,UAAU,CAACa,iBAAiB,CAACN,MAAM,GAAGP,UAAU,CAAC0B,WAAW,CAACnB,MAAM,GAAG,CAAC;EAClG,IAAIA,MAAM,KAAK,CAAC,IAAI,CAACkB,kBAAkB,EAAE;IACvC,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}