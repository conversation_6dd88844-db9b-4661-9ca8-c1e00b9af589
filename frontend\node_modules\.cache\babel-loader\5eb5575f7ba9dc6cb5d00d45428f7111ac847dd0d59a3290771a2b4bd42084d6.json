{"ast": null, "code": "export function GridDetailPanels(_) {\n  return null;\n}", "map": {"version": 3, "names": ["GridDetailPanels", "_"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/GridDetailPanels.js"], "sourcesContent": ["export function GridDetailPanels(_) {\n  return null;\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,CAAC,EAAE;EAClC,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}