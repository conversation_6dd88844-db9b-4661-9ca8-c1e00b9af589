{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nexport const QuickFilterContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") QuickFilterContext.displayName = \"QuickFilterContext\";\nexport function useQuickFilterContext() {\n  const context = React.useContext(QuickFilterContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context. Quick Filter subcomponents must be placed within a <QuickFilter /> component.');\n  }\n  return context;\n}", "map": {"version": 3, "names": ["React", "QuickFilterContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName", "useQuickFilterContext", "context", "useContext", "Error"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/quickFilter/QuickFilterContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nexport const QuickFilterContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") QuickFilterContext.displayName = \"QuickFilterContext\";\nexport function useQuickFilterContext() {\n  const context = React.useContext(QuickFilterContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context. Quick Filter subcomponents must be placed within a <QuickFilter /> component.');\n  }\n  return context;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,kBAAkB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAC7E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEL,kBAAkB,CAACM,WAAW,GAAG,oBAAoB;AAChG,OAAO,SAASC,qBAAqBA,CAAA,EAAG;EACtC,MAAMC,OAAO,GAAGT,KAAK,CAACU,UAAU,CAACT,kBAAkB,CAAC;EACpD,IAAIQ,OAAO,KAAKN,SAAS,EAAE;IACzB,MAAM,IAAIQ,KAAK,CAAC,uGAAuG,CAAC;EAC1H;EACA,OAAOF,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}