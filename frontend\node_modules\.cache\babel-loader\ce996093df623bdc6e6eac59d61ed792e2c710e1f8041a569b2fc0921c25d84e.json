{"ast": null, "code": "function last(arr) {\n  return arr[arr.length - 1];\n}\nexport { last };", "map": {"version": 3, "names": ["last", "arr", "length"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/last.mjs"], "sourcesContent": ["function last(arr) {\n    return arr[arr.length - 1];\n}\n\nexport { last };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,GAAG,EAAE;EACf,OAAOA,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;AAC9B;AAEA,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}