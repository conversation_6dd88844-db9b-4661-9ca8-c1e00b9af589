{"ast": null, "code": "import { sum } from './sum.mjs';\nfunction mean(nums) {\n  return sum(nums) / nums.length;\n}\nexport { mean };", "map": {"version": 3, "names": ["sum", "mean", "nums", "length"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/math/mean.mjs"], "sourcesContent": ["import { sum } from './sum.mjs';\n\nfunction mean(nums) {\n    return sum(nums) / nums.length;\n}\n\nexport { mean };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,WAAW;AAE/B,SAASC,IAAIA,CAACC,IAAI,EAAE;EAChB,OAAOF,GAAG,CAACE,IAAI,CAAC,GAAGA,IAAI,CAACC,MAAM;AAClC;AAEA,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}