{"ast": null, "code": "import { delay } from './delay.mjs';\nimport { TimeoutError } from '../error/TimeoutError.mjs';\nasync function timeout(ms) {\n  await delay(ms);\n  throw new TimeoutError();\n}\nexport { timeout };", "map": {"version": 3, "names": ["delay", "TimeoutError", "timeout", "ms"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/promise/timeout.mjs"], "sourcesContent": ["import { delay } from './delay.mjs';\nimport { TimeoutError } from '../error/TimeoutError.mjs';\n\nasync function timeout(ms) {\n    await delay(ms);\n    throw new TimeoutError();\n}\n\nexport { timeout };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,YAAY,QAAQ,2BAA2B;AAExD,eAAeC,OAAOA,CAACC,EAAE,EAAE;EACvB,MAAMH,KAAK,CAACG,EAAE,CAAC;EACf,MAAM,IAAIF,YAAY,CAAC,CAAC;AAC5B;AAEA,SAASC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}