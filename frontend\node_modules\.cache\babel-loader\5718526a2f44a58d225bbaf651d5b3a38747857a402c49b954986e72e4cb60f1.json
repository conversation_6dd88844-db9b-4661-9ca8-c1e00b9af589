{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from \"./gridColumnsSelector.js\";\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"./gridColumnsInterfaces.js\";\nimport { hydrateColumnsWidth, createColumnsState, COLUMNS_DIMENSION_PROPERTIES } from \"./gridColumnsUtils.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/index.js\";\nimport { gridPivotActiveSelector } from \"../pivoting/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  apiRef.current.caches.columns = {\n    lastColumnsProp: props.columns\n  };\n  const columnsState = createColumnsState({\n    apiRef,\n    columnsToUpsert: props.columns,\n    initialState: props.initialState?.columns,\n    columnVisibilityModel: props.columnVisibilityModel ?? props.initialState?.columns?.columnVisibilityModel ?? {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState,\n    // In pro/premium, this part of the state is defined. We give it an empty but defined value\n    // for the community version.\n    pinnedColumns: state.pinnedColumns ?? EMPTY_PINNED_COLUMN_FIELDS\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback((field, useVisibleColumns = true) => {\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.updateRenderContext?.();\n    }\n  }, [apiRef]);\n  const updateColumns = React.useCallback(columns => {\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotColumns(columns);\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false,\n      updateInitialVisibilityModel: true\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = columnVisibilityModel[field] ?? true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(`Moving column ${field} to index ${targetIndexPosition}`);\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    logger.debug(`Updating column ${field} width to ${width}`);\n    const columnsState = gridColumnsStateSelector(apiRef);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), apiRef.current.getRootDimensions()));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys(props.initialState?.columns?.columnVisibilityModel ?? {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, props.initialState?.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const initialState = context.stateToRestore.columns;\n    const columnVisibilityModelToImport = initialState?.columnVisibilityModel;\n    if (initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    if (initialState != null) {\n      apiRef.current.setState(prevState => _extends({}, prevState, {\n        columns: _extends({}, prevState.columns, {\n          lookup: columnsState.lookup,\n          orderedFields: columnsState.orderedFields,\n          initialColumnVisibilityModel: columnsState.initialColumnVisibilityModel\n        })\n      }));\n    }\n\n    // separate column visibility model state update as it can be controlled\n    // https://github.com/mui/mui-x/issues/17681#issuecomment-3012528602\n    if (columnVisibilityModelToImport != null) {\n      apiRef.current.setState(prevState => _extends({}, prevState, {\n        columns: _extends({}, prevState.columns, {\n          columnVisibilityModel: columnVisibilityModelToImport\n        })\n      }));\n    }\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, props.slotProps?.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, props.slotProps?.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    const isPivotActive = gridPivotActiveSelector(apiRef);\n    if (props.disableColumnSelector || isPivotActive) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector, apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /*\n   * EVENTS\n   */\n\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = size => {\n    if (prevInnerWidth.current !== size.width) {\n      prevInnerWidth.current = size.width;\n      const hasFlexColumns = gridVisibleColumnDefinitionsSelector(apiRef).some(col => col.flex && col.flex > 0);\n      if (!hasFlexColumns) {\n        return;\n      }\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef), apiRef.current.getRootDimensions()));\n    }\n  };\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(`Columns pipe processing have changed, regenerating the columns`);\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /*\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  React.useEffect(() => {\n    if (apiRef.current.caches.columns.lastColumnsProp === props.columns) {\n      return;\n    }\n    apiRef.current.caches.columns.lastColumnsProp = props.columns;\n    logger.info(`GridColumns have changed, new length ${props.columns.length}`);\n    const columnsState = createColumnsState({\n      apiRef,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true,\n      updateInitialVisibilityModel: true,\n      columnVisibilityModel: props.columnVisibilityModel\n    });\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns, props.columnVisibilityModel]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}\nfunction mergeColumnsState(columnsState) {\n  return state => _extends({}, state, {\n    columns: columnsState\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "useGridLogger", "gridColumnFieldsSelector", "gridColumnDefinitionsSelector", "gridColumnLookupSelector", "gridColumnsStateSelector", "gridColumnVisibilityModelSelector", "gridVisibleColumnDefinitionsSelector", "gridColumnPositionsSelector", "GridSignature", "useGridEvent", "useGridRegisterPipeProcessor", "useGridRegisterPipeApplier", "EMPTY_PINNED_COLUMN_FIELDS", "hydrateColumnsWidth", "createColumnsState", "COLUMNS_DIMENSION_PROPERTIES", "GridPreferencePanelsValue", "gridPivotActiveSelector", "jsx", "_jsx", "columnsStateInitializer", "state", "props", "apiRef", "current", "caches", "columns", "lastColumnsProp", "columnsState", "columnsToUpsert", "initialState", "columnVisibilityModel", "keepOnlyColumnsToUpsert", "pinnedColumns", "useGridColumns", "logger", "registerControlState", "stateId", "propModel", "propOnChange", "onColumnVisibilityModelChange", "stateSelector", "changeEvent", "setGridColumnsState", "useCallback", "debug", "setState", "mergeColumnsState", "publishEvent", "orderedFields", "getColumn", "field", "getAllColumns", "getVisibleColumns", "getColumnIndex", "useVisibleColumns", "findIndex", "col", "getColumnPosition", "index", "setColumnVisibilityModel", "model", "currentModel", "undefined", "updateRenderContext", "updateColumns", "updateNonPivotColumns", "updateInitialVisibilityModel", "setColumnVisibility", "isVisible", "isCurrentlyVisible", "newModel", "getColumnIndexRelativeToVisibleColumns", "allColumns", "setColumnIndex", "targetIndexPosition", "oldIndexPosition", "updatedColumns", "fieldRemoved", "splice", "params", "column", "targetIndex", "oldIndex", "setColumn<PERSON><PERSON><PERSON>", "width", "lookup", "newColumn", "hasBeenResized", "getRootDimensions", "element", "getColumnHeaderElement", "colDef", "columnApi", "columnReorderApi", "signature", "DataGrid", "stateExportPreProcessing", "prevState", "context", "columnsStateToExport", "columnVisibilityModelToExport", "shouldExportColumnVisibilityModel", "exportOnlyDirtyModels", "Object", "keys", "length", "dimensions", "for<PERSON>ach", "colDefDimensions", "propertyName", "propertyValue", "Infinity", "stateRestorePreProcessing", "stateToRestore", "columnVisibilityModelToImport", "initialColumnVisibilityModel", "preferencePanelPreProcessing", "initialValue", "value", "ColumnsPanel", "slots", "columnsPanel", "slotProps", "addColumnMenuItems", "columnMenuItems", "isPivotActive", "disableColumnSelector", "prevInnerWidth", "useRef", "handleGridSizeChange", "size", "hasFlexColumns", "some", "flex", "hydrateColumns", "info", "useEffect"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columns/useGridColumns.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnFieldsSelector, gridColumnDefinitionsSelector, gridColumnLookupSelector, gridColumnsStateSelector, gridColumnVisibilityModelSelector, gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector } from \"./gridColumnsSelector.js\";\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridRegisterPipeProcessor, useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"./gridColumnsInterfaces.js\";\nimport { hydrateColumnsWidth, createColumnsState, COLUMNS_DIMENSION_PROPERTIES } from \"./gridColumnsUtils.js\";\nimport { GridPreferencePanelsValue } from \"../preferencesPanel/index.js\";\nimport { gridPivotActiveSelector } from \"../pivoting/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const columnsStateInitializer = (state, props, apiRef) => {\n  apiRef.current.caches.columns = {\n    lastColumnsProp: props.columns\n  };\n  const columnsState = createColumnsState({\n    apiRef,\n    columnsToUpsert: props.columns,\n    initialState: props.initialState?.columns,\n    columnVisibilityModel: props.columnVisibilityModel ?? props.initialState?.columns?.columnVisibilityModel ?? {},\n    keepOnlyColumnsToUpsert: true\n  });\n  return _extends({}, state, {\n    columns: columnsState,\n    // In pro/premium, this part of the state is defined. We give it an empty but defined value\n    // for the community version.\n    pinnedColumns: state.pinnedColumns ?? EMPTY_PINNED_COLUMN_FIELDS\n  });\n};\n\n/**\n * @requires useGridParamsApi (method)\n * @requires useGridDimensions (method, event) - can be after\n * TODO: Impossible priority - useGridParamsApi also needs to be after useGridColumns\n */\nexport function useGridColumns(apiRef, props) {\n  const logger = useGridLogger(apiRef, 'useGridColumns');\n  apiRef.current.registerControlState({\n    stateId: 'visibleColumns',\n    propModel: props.columnVisibilityModel,\n    propOnChange: props.onColumnVisibilityModelChange,\n    stateSelector: gridColumnVisibilityModelSelector,\n    changeEvent: 'columnVisibilityModelChange'\n  });\n  const setGridColumnsState = React.useCallback(columnsState => {\n    logger.debug('Updating columns state.');\n    apiRef.current.setState(mergeColumnsState(columnsState));\n    apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n  }, [logger, apiRef]);\n\n  /**\n   * API METHODS\n   */\n  const getColumn = React.useCallback(field => gridColumnLookupSelector(apiRef)[field], [apiRef]);\n  const getAllColumns = React.useCallback(() => gridColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getVisibleColumns = React.useCallback(() => gridVisibleColumnDefinitionsSelector(apiRef), [apiRef]);\n  const getColumnIndex = React.useCallback((field, useVisibleColumns = true) => {\n    const columns = useVisibleColumns ? gridVisibleColumnDefinitionsSelector(apiRef) : gridColumnDefinitionsSelector(apiRef);\n    return columns.findIndex(col => col.field === field);\n  }, [apiRef]);\n  const getColumnPosition = React.useCallback(field => {\n    const index = getColumnIndex(field);\n    return gridColumnPositionsSelector(apiRef)[index];\n  }, [apiRef, getColumnIndex]);\n  const setColumnVisibilityModel = React.useCallback(model => {\n    const currentModel = gridColumnVisibilityModelSelector(apiRef);\n    if (currentModel !== model) {\n      apiRef.current.setState(state => _extends({}, state, {\n        columns: createColumnsState({\n          apiRef,\n          columnsToUpsert: [],\n          initialState: undefined,\n          columnVisibilityModel: model,\n          keepOnlyColumnsToUpsert: false\n        })\n      }));\n      apiRef.current.updateRenderContext?.();\n    }\n  }, [apiRef]);\n  const updateColumns = React.useCallback(columns => {\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotColumns(columns);\n      return;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: columns,\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false,\n      updateInitialVisibilityModel: true\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, setGridColumnsState]);\n  const setColumnVisibility = React.useCallback((field, isVisible) => {\n    const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n    const isCurrentlyVisible = columnVisibilityModel[field] ?? true;\n    if (isVisible !== isCurrentlyVisible) {\n      const newModel = _extends({}, columnVisibilityModel, {\n        [field]: isVisible\n      });\n      apiRef.current.setColumnVisibilityModel(newModel);\n    }\n  }, [apiRef]);\n  const getColumnIndexRelativeToVisibleColumns = React.useCallback(field => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    return allColumns.findIndex(col => col === field);\n  }, [apiRef]);\n  const setColumnIndex = React.useCallback((field, targetIndexPosition) => {\n    const allColumns = gridColumnFieldsSelector(apiRef);\n    const oldIndexPosition = getColumnIndexRelativeToVisibleColumns(field);\n    if (oldIndexPosition === targetIndexPosition) {\n      return;\n    }\n    logger.debug(`Moving column ${field} to index ${targetIndexPosition}`);\n    const updatedColumns = [...allColumns];\n    const fieldRemoved = updatedColumns.splice(oldIndexPosition, 1)[0];\n    updatedColumns.splice(targetIndexPosition, 0, fieldRemoved);\n    setGridColumnsState(_extends({}, gridColumnsStateSelector(apiRef), {\n      orderedFields: updatedColumns\n    }));\n    const params = {\n      column: apiRef.current.getColumn(field),\n      targetIndex: apiRef.current.getColumnIndexRelativeToVisibleColumns(field),\n      oldIndex: oldIndexPosition\n    };\n    apiRef.current.publishEvent('columnIndexChange', params);\n  }, [apiRef, logger, setGridColumnsState, getColumnIndexRelativeToVisibleColumns]);\n  const setColumnWidth = React.useCallback((field, width) => {\n    logger.debug(`Updating column ${field} width to ${width}`);\n    const columnsState = gridColumnsStateSelector(apiRef);\n    const column = columnsState.lookup[field];\n    const newColumn = _extends({}, column, {\n      width,\n      hasBeenResized: true\n    });\n    setGridColumnsState(hydrateColumnsWidth(_extends({}, columnsState, {\n      lookup: _extends({}, columnsState.lookup, {\n        [field]: newColumn\n      })\n    }), apiRef.current.getRootDimensions()));\n    apiRef.current.publishEvent('columnWidthChange', {\n      element: apiRef.current.getColumnHeaderElement(field),\n      colDef: newColumn,\n      width\n    });\n  }, [apiRef, logger, setGridColumnsState]);\n  const columnApi = {\n    getColumn,\n    getAllColumns,\n    getColumnIndex,\n    getColumnPosition,\n    getVisibleColumns,\n    getColumnIndexRelativeToVisibleColumns,\n    updateColumns,\n    setColumnVisibilityModel,\n    setColumnVisibility,\n    setColumnWidth\n  };\n  const columnReorderApi = {\n    setColumnIndex\n  };\n  useGridApiMethod(apiRef, columnApi, 'public');\n  useGridApiMethod(apiRef, columnReorderApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const columnsStateToExport = {};\n    const columnVisibilityModelToExport = gridColumnVisibilityModelSelector(apiRef);\n    const shouldExportColumnVisibilityModel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the model is controlled\n    props.columnVisibilityModel != null ||\n    // Always export if the model has been initialized\n    // TODO v6 Do a nullish check instead to export even if the initial model equals \"{}\"\n    Object.keys(props.initialState?.columns?.columnVisibilityModel ?? {}).length > 0 ||\n    // Always export if the model is not empty\n    Object.keys(columnVisibilityModelToExport).length > 0;\n    if (shouldExportColumnVisibilityModel) {\n      columnsStateToExport.columnVisibilityModel = columnVisibilityModelToExport;\n    }\n    columnsStateToExport.orderedFields = gridColumnFieldsSelector(apiRef);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const dimensions = {};\n    columns.forEach(colDef => {\n      if (colDef.hasBeenResized) {\n        const colDefDimensions = {};\n        COLUMNS_DIMENSION_PROPERTIES.forEach(propertyName => {\n          let propertyValue = colDef[propertyName];\n          if (propertyValue === Infinity) {\n            propertyValue = -1;\n          }\n          colDefDimensions[propertyName] = propertyValue;\n        });\n        dimensions[colDef.field] = colDefDimensions;\n      }\n    });\n    if (Object.keys(dimensions).length > 0) {\n      columnsStateToExport.dimensions = dimensions;\n    }\n    return _extends({}, prevState, {\n      columns: columnsStateToExport\n    });\n  }, [apiRef, props.columnVisibilityModel, props.initialState?.columns]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const initialState = context.stateToRestore.columns;\n    const columnVisibilityModelToImport = initialState?.columnVisibilityModel;\n    if (initialState == null) {\n      return params;\n    }\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState,\n      columnVisibilityModel: columnVisibilityModelToImport,\n      keepOnlyColumnsToUpsert: false\n    });\n    if (initialState != null) {\n      apiRef.current.setState(prevState => _extends({}, prevState, {\n        columns: _extends({}, prevState.columns, {\n          lookup: columnsState.lookup,\n          orderedFields: columnsState.orderedFields,\n          initialColumnVisibilityModel: columnsState.initialColumnVisibilityModel\n        })\n      }));\n    }\n\n    // separate column visibility model state update as it can be controlled\n    // https://github.com/mui/mui-x/issues/17681#issuecomment-3012528602\n    if (columnVisibilityModelToImport != null) {\n      apiRef.current.setState(prevState => _extends({}, prevState, {\n        columns: _extends({}, prevState.columns, {\n          columnVisibilityModel: columnVisibilityModelToImport\n        })\n      }));\n    }\n    if (initialState != null) {\n      apiRef.current.publishEvent('columnsChange', columnsState.orderedFields);\n    }\n    return params;\n  }, [apiRef]);\n  const preferencePanelPreProcessing = React.useCallback((initialValue, value) => {\n    if (value === GridPreferencePanelsValue.columns) {\n      const ColumnsPanel = props.slots.columnsPanel;\n      return /*#__PURE__*/_jsx(ColumnsPanel, _extends({}, props.slotProps?.columnsPanel));\n    }\n    return initialValue;\n  }, [props.slots.columnsPanel, props.slotProps?.columnsPanel]);\n  const addColumnMenuItems = React.useCallback(columnMenuItems => {\n    const isPivotActive = gridPivotActiveSelector(apiRef);\n    if (props.disableColumnSelector || isPivotActive) {\n      return columnMenuItems;\n    }\n    return [...columnMenuItems, 'columnMenuColumnsItem'];\n  }, [props.disableColumnSelector, apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'columnMenu', addColumnMenuItems);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'preferencePanel', preferencePanelPreProcessing);\n\n  /*\n   * EVENTS\n   */\n\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = size => {\n    if (prevInnerWidth.current !== size.width) {\n      prevInnerWidth.current = size.width;\n      const hasFlexColumns = gridVisibleColumnDefinitionsSelector(apiRef).some(col => col.flex && col.flex > 0);\n      if (!hasFlexColumns) {\n        return;\n      }\n      setGridColumnsState(hydrateColumnsWidth(gridColumnsStateSelector(apiRef), apiRef.current.getRootDimensions()));\n    }\n  };\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n\n  /**\n   * APPLIERS\n   */\n  const hydrateColumns = React.useCallback(() => {\n    logger.info(`Columns pipe processing have changed, regenerating the columns`);\n    const columnsState = createColumnsState({\n      apiRef,\n      columnsToUpsert: [],\n      initialState: undefined,\n      keepOnlyColumnsToUpsert: false\n    });\n    setGridColumnsState(columnsState);\n  }, [apiRef, logger, setGridColumnsState]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateColumns', hydrateColumns);\n\n  /*\n   * EFFECTS\n   */\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridColumns`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  React.useEffect(() => {\n    if (apiRef.current.caches.columns.lastColumnsProp === props.columns) {\n      return;\n    }\n    apiRef.current.caches.columns.lastColumnsProp = props.columns;\n    logger.info(`GridColumns have changed, new length ${props.columns.length}`);\n    const columnsState = createColumnsState({\n      apiRef,\n      initialState: undefined,\n      // If the user provides a model, we don't want to set it in the state here because it has it's dedicated `useEffect` which calls `setColumnVisibilityModel`\n      columnsToUpsert: props.columns,\n      keepOnlyColumnsToUpsert: true,\n      updateInitialVisibilityModel: true,\n      columnVisibilityModel: props.columnVisibilityModel\n    });\n    setGridColumnsState(columnsState);\n  }, [logger, apiRef, setGridColumnsState, props.columns, props.columnVisibilityModel]);\n  React.useEffect(() => {\n    if (props.columnVisibilityModel !== undefined) {\n      apiRef.current.setColumnVisibilityModel(props.columnVisibilityModel);\n    }\n  }, [apiRef, logger, props.columnVisibilityModel]);\n}\nfunction mergeColumnsState(columnsState) {\n  return state => _extends({}, state, {\n    columns: columnsState\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,wBAAwB,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAEC,wBAAwB,EAAEC,iCAAiC,EAAEC,oCAAoC,EAAEC,2BAA2B,QAAQ,0BAA0B;AAC5P,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,4BAA4B,EAAEC,0BAA0B,QAAQ,oCAAoC;AAC7G,SAASC,0BAA0B,QAAQ,4BAA4B;AACvE,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,4BAA4B,QAAQ,uBAAuB;AAC7G,SAASC,yBAAyB,QAAQ,8BAA8B;AACxE,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC/DA,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,OAAO,GAAG;IAC9BC,eAAe,EAAEL,KAAK,CAACI;EACzB,CAAC;EACD,MAAME,YAAY,GAAGd,kBAAkB,CAAC;IACtCS,MAAM;IACNM,eAAe,EAAEP,KAAK,CAACI,OAAO;IAC9BI,YAAY,EAAER,KAAK,CAACQ,YAAY,EAAEJ,OAAO;IACzCK,qBAAqB,EAAET,KAAK,CAACS,qBAAqB,IAAIT,KAAK,CAACQ,YAAY,EAAEJ,OAAO,EAAEK,qBAAqB,IAAI,CAAC,CAAC;IAC9GC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;EACF,OAAOnC,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;IACzBK,OAAO,EAAEE,YAAY;IACrB;IACA;IACAK,aAAa,EAAEZ,KAAK,CAACY,aAAa,IAAIrB;EACxC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsB,cAAcA,CAACX,MAAM,EAAED,KAAK,EAAE;EAC5C,MAAMa,MAAM,GAAGnC,aAAa,CAACuB,MAAM,EAAE,gBAAgB,CAAC;EACtDA,MAAM,CAACC,OAAO,CAACY,oBAAoB,CAAC;IAClCC,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAEhB,KAAK,CAACS,qBAAqB;IACtCQ,YAAY,EAAEjB,KAAK,CAACkB,6BAA6B;IACjDC,aAAa,EAAEpC,iCAAiC;IAChDqC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAMC,mBAAmB,GAAG7C,KAAK,CAAC8C,WAAW,CAAChB,YAAY,IAAI;IAC5DO,MAAM,CAACU,KAAK,CAAC,yBAAyB,CAAC;IACvCtB,MAAM,CAACC,OAAO,CAACsB,QAAQ,CAACC,iBAAiB,CAACnB,YAAY,CAAC,CAAC;IACxDL,MAAM,CAACC,OAAO,CAACwB,YAAY,CAAC,eAAe,EAAEpB,YAAY,CAACqB,aAAa,CAAC;EAC1E,CAAC,EAAE,CAACd,MAAM,EAAEZ,MAAM,CAAC,CAAC;;EAEpB;AACF;AACA;EACE,MAAM2B,SAAS,GAAGpD,KAAK,CAAC8C,WAAW,CAACO,KAAK,IAAIhD,wBAAwB,CAACoB,MAAM,CAAC,CAAC4B,KAAK,CAAC,EAAE,CAAC5B,MAAM,CAAC,CAAC;EAC/F,MAAM6B,aAAa,GAAGtD,KAAK,CAAC8C,WAAW,CAAC,MAAM1C,6BAA6B,CAACqB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC9F,MAAM8B,iBAAiB,GAAGvD,KAAK,CAAC8C,WAAW,CAAC,MAAMtC,oCAAoC,CAACiB,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACzG,MAAM+B,cAAc,GAAGxD,KAAK,CAAC8C,WAAW,CAAC,CAACO,KAAK,EAAEI,iBAAiB,GAAG,IAAI,KAAK;IAC5E,MAAM7B,OAAO,GAAG6B,iBAAiB,GAAGjD,oCAAoC,CAACiB,MAAM,CAAC,GAAGrB,6BAA6B,CAACqB,MAAM,CAAC;IACxH,OAAOG,OAAO,CAAC8B,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACN,KAAK,KAAKA,KAAK,CAAC;EACtD,CAAC,EAAE,CAAC5B,MAAM,CAAC,CAAC;EACZ,MAAMmC,iBAAiB,GAAG5D,KAAK,CAAC8C,WAAW,CAACO,KAAK,IAAI;IACnD,MAAMQ,KAAK,GAAGL,cAAc,CAACH,KAAK,CAAC;IACnC,OAAO5C,2BAA2B,CAACgB,MAAM,CAAC,CAACoC,KAAK,CAAC;EACnD,CAAC,EAAE,CAACpC,MAAM,EAAE+B,cAAc,CAAC,CAAC;EAC5B,MAAMM,wBAAwB,GAAG9D,KAAK,CAAC8C,WAAW,CAACiB,KAAK,IAAI;IAC1D,MAAMC,YAAY,GAAGzD,iCAAiC,CAACkB,MAAM,CAAC;IAC9D,IAAIuC,YAAY,KAAKD,KAAK,EAAE;MAC1BtC,MAAM,CAACC,OAAO,CAACsB,QAAQ,CAACzB,KAAK,IAAIxB,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;QACnDK,OAAO,EAAEZ,kBAAkB,CAAC;UAC1BS,MAAM;UACNM,eAAe,EAAE,EAAE;UACnBC,YAAY,EAAEiC,SAAS;UACvBhC,qBAAqB,EAAE8B,KAAK;UAC5B7B,uBAAuB,EAAE;QAC3B,CAAC;MACH,CAAC,CAAC,CAAC;MACHT,MAAM,CAACC,OAAO,CAACwC,mBAAmB,GAAG,CAAC;IACxC;EACF,CAAC,EAAE,CAACzC,MAAM,CAAC,CAAC;EACZ,MAAM0C,aAAa,GAAGnE,KAAK,CAAC8C,WAAW,CAAClB,OAAO,IAAI;IACjD,IAAIT,uBAAuB,CAACM,MAAM,CAAC,EAAE;MACnCA,MAAM,CAACC,OAAO,CAAC0C,qBAAqB,CAACxC,OAAO,CAAC;MAC7C;IACF;IACA,MAAME,YAAY,GAAGd,kBAAkB,CAAC;MACtCS,MAAM;MACNM,eAAe,EAAEH,OAAO;MACxBI,YAAY,EAAEiC,SAAS;MACvB/B,uBAAuB,EAAE,KAAK;MAC9BmC,4BAA4B,EAAE;IAChC,CAAC,CAAC;IACFxB,mBAAmB,CAACf,YAAY,CAAC;EACnC,CAAC,EAAE,CAACL,MAAM,EAAEoB,mBAAmB,CAAC,CAAC;EACjC,MAAMyB,mBAAmB,GAAGtE,KAAK,CAAC8C,WAAW,CAAC,CAACO,KAAK,EAAEkB,SAAS,KAAK;IAClE,MAAMtC,qBAAqB,GAAG1B,iCAAiC,CAACkB,MAAM,CAAC;IACvE,MAAM+C,kBAAkB,GAAGvC,qBAAqB,CAACoB,KAAK,CAAC,IAAI,IAAI;IAC/D,IAAIkB,SAAS,KAAKC,kBAAkB,EAAE;MACpC,MAAMC,QAAQ,GAAG1E,QAAQ,CAAC,CAAC,CAAC,EAAEkC,qBAAqB,EAAE;QACnD,CAACoB,KAAK,GAAGkB;MACX,CAAC,CAAC;MACF9C,MAAM,CAACC,OAAO,CAACoC,wBAAwB,CAACW,QAAQ,CAAC;IACnD;EACF,CAAC,EAAE,CAAChD,MAAM,CAAC,CAAC;EACZ,MAAMiD,sCAAsC,GAAG1E,KAAK,CAAC8C,WAAW,CAACO,KAAK,IAAI;IACxE,MAAMsB,UAAU,GAAGxE,wBAAwB,CAACsB,MAAM,CAAC;IACnD,OAAOkD,UAAU,CAACjB,SAAS,CAACC,GAAG,IAAIA,GAAG,KAAKN,KAAK,CAAC;EACnD,CAAC,EAAE,CAAC5B,MAAM,CAAC,CAAC;EACZ,MAAMmD,cAAc,GAAG5E,KAAK,CAAC8C,WAAW,CAAC,CAACO,KAAK,EAAEwB,mBAAmB,KAAK;IACvE,MAAMF,UAAU,GAAGxE,wBAAwB,CAACsB,MAAM,CAAC;IACnD,MAAMqD,gBAAgB,GAAGJ,sCAAsC,CAACrB,KAAK,CAAC;IACtE,IAAIyB,gBAAgB,KAAKD,mBAAmB,EAAE;MAC5C;IACF;IACAxC,MAAM,CAACU,KAAK,CAAC,iBAAiBM,KAAK,aAAawB,mBAAmB,EAAE,CAAC;IACtE,MAAME,cAAc,GAAG,CAAC,GAAGJ,UAAU,CAAC;IACtC,MAAMK,YAAY,GAAGD,cAAc,CAACE,MAAM,CAACH,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAClEC,cAAc,CAACE,MAAM,CAACJ,mBAAmB,EAAE,CAAC,EAAEG,YAAY,CAAC;IAC3DnC,mBAAmB,CAAC9C,QAAQ,CAAC,CAAC,CAAC,EAAEO,wBAAwB,CAACmB,MAAM,CAAC,EAAE;MACjE0B,aAAa,EAAE4B;IACjB,CAAC,CAAC,CAAC;IACH,MAAMG,MAAM,GAAG;MACbC,MAAM,EAAE1D,MAAM,CAACC,OAAO,CAAC0B,SAAS,CAACC,KAAK,CAAC;MACvC+B,WAAW,EAAE3D,MAAM,CAACC,OAAO,CAACgD,sCAAsC,CAACrB,KAAK,CAAC;MACzEgC,QAAQ,EAAEP;IACZ,CAAC;IACDrD,MAAM,CAACC,OAAO,CAACwB,YAAY,CAAC,mBAAmB,EAAEgC,MAAM,CAAC;EAC1D,CAAC,EAAE,CAACzD,MAAM,EAAEY,MAAM,EAAEQ,mBAAmB,EAAE6B,sCAAsC,CAAC,CAAC;EACjF,MAAMY,cAAc,GAAGtF,KAAK,CAAC8C,WAAW,CAAC,CAACO,KAAK,EAAEkC,KAAK,KAAK;IACzDlD,MAAM,CAACU,KAAK,CAAC,mBAAmBM,KAAK,aAAakC,KAAK,EAAE,CAAC;IAC1D,MAAMzD,YAAY,GAAGxB,wBAAwB,CAACmB,MAAM,CAAC;IACrD,MAAM0D,MAAM,GAAGrD,YAAY,CAAC0D,MAAM,CAACnC,KAAK,CAAC;IACzC,MAAMoC,SAAS,GAAG1F,QAAQ,CAAC,CAAC,CAAC,EAAEoF,MAAM,EAAE;MACrCI,KAAK;MACLG,cAAc,EAAE;IAClB,CAAC,CAAC;IACF7C,mBAAmB,CAAC9B,mBAAmB,CAAChB,QAAQ,CAAC,CAAC,CAAC,EAAE+B,YAAY,EAAE;MACjE0D,MAAM,EAAEzF,QAAQ,CAAC,CAAC,CAAC,EAAE+B,YAAY,CAAC0D,MAAM,EAAE;QACxC,CAACnC,KAAK,GAAGoC;MACX,CAAC;IACH,CAAC,CAAC,EAAEhE,MAAM,CAACC,OAAO,CAACiE,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACxClE,MAAM,CAACC,OAAO,CAACwB,YAAY,CAAC,mBAAmB,EAAE;MAC/C0C,OAAO,EAAEnE,MAAM,CAACC,OAAO,CAACmE,sBAAsB,CAACxC,KAAK,CAAC;MACrDyC,MAAM,EAAEL,SAAS;MACjBF;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9D,MAAM,EAAEY,MAAM,EAAEQ,mBAAmB,CAAC,CAAC;EACzC,MAAMkD,SAAS,GAAG;IAChB3C,SAAS;IACTE,aAAa;IACbE,cAAc;IACdI,iBAAiB;IACjBL,iBAAiB;IACjBmB,sCAAsC;IACtCP,aAAa;IACbL,wBAAwB;IACxBQ,mBAAmB;IACnBgB;EACF,CAAC;EACD,MAAMU,gBAAgB,GAAG;IACvBpB;EACF,CAAC;EACD3E,gBAAgB,CAACwB,MAAM,EAAEsE,SAAS,EAAE,QAAQ,CAAC;EAC7C9F,gBAAgB,CAACwB,MAAM,EAAEuE,gBAAgB,EAAExE,KAAK,CAACyE,SAAS,KAAKvF,aAAa,CAACwF,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;;EAE7G;AACF;AACA;EACE,MAAMC,wBAAwB,GAAGnG,KAAK,CAAC8C,WAAW,CAAC,CAACsD,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,oBAAoB,GAAG,CAAC,CAAC;IAC/B,MAAMC,6BAA6B,GAAGhG,iCAAiC,CAACkB,MAAM,CAAC;IAC/E,MAAM+E,iCAAiC;IACvC;IACA,CAACH,OAAO,CAACI,qBAAqB;IAC9B;IACAjF,KAAK,CAACS,qBAAqB,IAAI,IAAI;IACnC;IACA;IACAyE,MAAM,CAACC,IAAI,CAACnF,KAAK,CAACQ,YAAY,EAAEJ,OAAO,EAAEK,qBAAqB,IAAI,CAAC,CAAC,CAAC,CAAC2E,MAAM,GAAG,CAAC;IAChF;IACAF,MAAM,CAACC,IAAI,CAACJ,6BAA6B,CAAC,CAACK,MAAM,GAAG,CAAC;IACrD,IAAIJ,iCAAiC,EAAE;MACrCF,oBAAoB,CAACrE,qBAAqB,GAAGsE,6BAA6B;IAC5E;IACAD,oBAAoB,CAACnD,aAAa,GAAGhD,wBAAwB,CAACsB,MAAM,CAAC;IACrE,MAAMG,OAAO,GAAGxB,6BAA6B,CAACqB,MAAM,CAAC;IACrD,MAAMoF,UAAU,GAAG,CAAC,CAAC;IACrBjF,OAAO,CAACkF,OAAO,CAAChB,MAAM,IAAI;MACxB,IAAIA,MAAM,CAACJ,cAAc,EAAE;QACzB,MAAMqB,gBAAgB,GAAG,CAAC,CAAC;QAC3B9F,4BAA4B,CAAC6F,OAAO,CAACE,YAAY,IAAI;UACnD,IAAIC,aAAa,GAAGnB,MAAM,CAACkB,YAAY,CAAC;UACxC,IAAIC,aAAa,KAAKC,QAAQ,EAAE;YAC9BD,aAAa,GAAG,CAAC,CAAC;UACpB;UACAF,gBAAgB,CAACC,YAAY,CAAC,GAAGC,aAAa;QAChD,CAAC,CAAC;QACFJ,UAAU,CAACf,MAAM,CAACzC,KAAK,CAAC,GAAG0D,gBAAgB;MAC7C;IACF,CAAC,CAAC;IACF,IAAIL,MAAM,CAACC,IAAI,CAACE,UAAU,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;MACtCN,oBAAoB,CAACO,UAAU,GAAGA,UAAU;IAC9C;IACA,OAAO9G,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,EAAE;MAC7BxE,OAAO,EAAE0E;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7E,MAAM,EAAED,KAAK,CAACS,qBAAqB,EAAET,KAAK,CAACQ,YAAY,EAAEJ,OAAO,CAAC,CAAC;EACtE,MAAMuF,yBAAyB,GAAGnH,KAAK,CAAC8C,WAAW,CAAC,CAACoC,MAAM,EAAEmB,OAAO,KAAK;IACvE,MAAMrE,YAAY,GAAGqE,OAAO,CAACe,cAAc,CAACxF,OAAO;IACnD,MAAMyF,6BAA6B,GAAGrF,YAAY,EAAEC,qBAAqB;IACzE,IAAID,YAAY,IAAI,IAAI,EAAE;MACxB,OAAOkD,MAAM;IACf;IACA,MAAMpD,YAAY,GAAGd,kBAAkB,CAAC;MACtCS,MAAM;MACNM,eAAe,EAAE,EAAE;MACnBC,YAAY;MACZC,qBAAqB,EAAEoF,6BAA6B;MACpDnF,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACF,IAAIF,YAAY,IAAI,IAAI,EAAE;MACxBP,MAAM,CAACC,OAAO,CAACsB,QAAQ,CAACoD,SAAS,IAAIrG,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,EAAE;QAC3DxE,OAAO,EAAE7B,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,CAACxE,OAAO,EAAE;UACvC4D,MAAM,EAAE1D,YAAY,CAAC0D,MAAM;UAC3BrC,aAAa,EAAErB,YAAY,CAACqB,aAAa;UACzCmE,4BAA4B,EAAExF,YAAY,CAACwF;QAC7C,CAAC;MACH,CAAC,CAAC,CAAC;IACL;;IAEA;IACA;IACA,IAAID,6BAA6B,IAAI,IAAI,EAAE;MACzC5F,MAAM,CAACC,OAAO,CAACsB,QAAQ,CAACoD,SAAS,IAAIrG,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,EAAE;QAC3DxE,OAAO,EAAE7B,QAAQ,CAAC,CAAC,CAAC,EAAEqG,SAAS,CAACxE,OAAO,EAAE;UACvCK,qBAAqB,EAAEoF;QACzB,CAAC;MACH,CAAC,CAAC,CAAC;IACL;IACA,IAAIrF,YAAY,IAAI,IAAI,EAAE;MACxBP,MAAM,CAACC,OAAO,CAACwB,YAAY,CAAC,eAAe,EAAEpB,YAAY,CAACqB,aAAa,CAAC;IAC1E;IACA,OAAO+B,MAAM;EACf,CAAC,EAAE,CAACzD,MAAM,CAAC,CAAC;EACZ,MAAM8F,4BAA4B,GAAGvH,KAAK,CAAC8C,WAAW,CAAC,CAAC0E,YAAY,EAAEC,KAAK,KAAK;IAC9E,IAAIA,KAAK,KAAKvG,yBAAyB,CAACU,OAAO,EAAE;MAC/C,MAAM8F,YAAY,GAAGlG,KAAK,CAACmG,KAAK,CAACC,YAAY;MAC7C,OAAO,aAAavG,IAAI,CAACqG,YAAY,EAAE3H,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,CAACqG,SAAS,EAAED,YAAY,CAAC,CAAC;IACrF;IACA,OAAOJ,YAAY;EACrB,CAAC,EAAE,CAAChG,KAAK,CAACmG,KAAK,CAACC,YAAY,EAAEpG,KAAK,CAACqG,SAAS,EAAED,YAAY,CAAC,CAAC;EAC7D,MAAME,kBAAkB,GAAG9H,KAAK,CAAC8C,WAAW,CAACiF,eAAe,IAAI;IAC9D,MAAMC,aAAa,GAAG7G,uBAAuB,CAACM,MAAM,CAAC;IACrD,IAAID,KAAK,CAACyG,qBAAqB,IAAID,aAAa,EAAE;MAChD,OAAOD,eAAe;IACxB;IACA,OAAO,CAAC,GAAGA,eAAe,EAAE,uBAAuB,CAAC;EACtD,CAAC,EAAE,CAACvG,KAAK,CAACyG,qBAAqB,EAAExG,MAAM,CAAC,CAAC;EACzCb,4BAA4B,CAACa,MAAM,EAAE,YAAY,EAAEqG,kBAAkB,CAAC;EACtElH,4BAA4B,CAACa,MAAM,EAAE,aAAa,EAAE0E,wBAAwB,CAAC;EAC7EvF,4BAA4B,CAACa,MAAM,EAAE,cAAc,EAAE0F,yBAAyB,CAAC;EAC/EvG,4BAA4B,CAACa,MAAM,EAAE,iBAAiB,EAAE8F,4BAA4B,CAAC;;EAErF;AACF;AACA;;EAEE,MAAMW,cAAc,GAAGlI,KAAK,CAACmI,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMC,oBAAoB,GAAGC,IAAI,IAAI;IACnC,IAAIH,cAAc,CAACxG,OAAO,KAAK2G,IAAI,CAAC9C,KAAK,EAAE;MACzC2C,cAAc,CAACxG,OAAO,GAAG2G,IAAI,CAAC9C,KAAK;MACnC,MAAM+C,cAAc,GAAG9H,oCAAoC,CAACiB,MAAM,CAAC,CAAC8G,IAAI,CAAC5E,GAAG,IAAIA,GAAG,CAAC6E,IAAI,IAAI7E,GAAG,CAAC6E,IAAI,GAAG,CAAC,CAAC;MACzG,IAAI,CAACF,cAAc,EAAE;QACnB;MACF;MACAzF,mBAAmB,CAAC9B,mBAAmB,CAACT,wBAAwB,CAACmB,MAAM,CAAC,EAAEA,MAAM,CAACC,OAAO,CAACiE,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAChH;EACF,CAAC;EACDhF,YAAY,CAACc,MAAM,EAAE,yBAAyB,EAAE2G,oBAAoB,CAAC;;EAErE;AACF;AACA;EACE,MAAMK,cAAc,GAAGzI,KAAK,CAAC8C,WAAW,CAAC,MAAM;IAC7CT,MAAM,CAACqG,IAAI,CAAC,gEAAgE,CAAC;IAC7E,MAAM5G,YAAY,GAAGd,kBAAkB,CAAC;MACtCS,MAAM;MACNM,eAAe,EAAE,EAAE;MACnBC,YAAY,EAAEiC,SAAS;MACvB/B,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFW,mBAAmB,CAACf,YAAY,CAAC;EACnC,CAAC,EAAE,CAACL,MAAM,EAAEY,MAAM,EAAEQ,mBAAmB,CAAC,CAAC;EACzChC,0BAA0B,CAACY,MAAM,EAAE,gBAAgB,EAAEgH,cAAc,CAAC;;EAEpE;AACF;AACA;EACE;EACA;EACAzI,KAAK,CAAC2I,SAAS,CAAC,MAAM;IACpB,IAAIlH,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,OAAO,CAACC,eAAe,KAAKL,KAAK,CAACI,OAAO,EAAE;MACnE;IACF;IACAH,MAAM,CAACC,OAAO,CAACC,MAAM,CAACC,OAAO,CAACC,eAAe,GAAGL,KAAK,CAACI,OAAO;IAC7DS,MAAM,CAACqG,IAAI,CAAC,wCAAwClH,KAAK,CAACI,OAAO,CAACgF,MAAM,EAAE,CAAC;IAC3E,MAAM9E,YAAY,GAAGd,kBAAkB,CAAC;MACtCS,MAAM;MACNO,YAAY,EAAEiC,SAAS;MACvB;MACAlC,eAAe,EAAEP,KAAK,CAACI,OAAO;MAC9BM,uBAAuB,EAAE,IAAI;MAC7BmC,4BAA4B,EAAE,IAAI;MAClCpC,qBAAqB,EAAET,KAAK,CAACS;IAC/B,CAAC,CAAC;IACFY,mBAAmB,CAACf,YAAY,CAAC;EACnC,CAAC,EAAE,CAACO,MAAM,EAAEZ,MAAM,EAAEoB,mBAAmB,EAAErB,KAAK,CAACI,OAAO,EAAEJ,KAAK,CAACS,qBAAqB,CAAC,CAAC;EACrFjC,KAAK,CAAC2I,SAAS,CAAC,MAAM;IACpB,IAAInH,KAAK,CAACS,qBAAqB,KAAKgC,SAAS,EAAE;MAC7CxC,MAAM,CAACC,OAAO,CAACoC,wBAAwB,CAACtC,KAAK,CAACS,qBAAqB,CAAC;IACtE;EACF,CAAC,EAAE,CAACR,MAAM,EAAEY,MAAM,EAAEb,KAAK,CAACS,qBAAqB,CAAC,CAAC;AACnD;AACA,SAASgB,iBAAiBA,CAACnB,YAAY,EAAE;EACvC,OAAOP,KAAK,IAAIxB,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;IAClCK,OAAO,EAAEE;EACX,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}