{"ast": null, "code": "export function NotRendered(_props) {\n  throw new Error('Failed assertion: should not be rendered');\n}", "map": {"version": 3, "names": ["NotRendered", "_props", "Error"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/assert.js"], "sourcesContent": ["export function NotRendered(_props) {\n  throw new Error('Failed assertion: should not be rendered');\n}"], "mappings": "AAAA,OAAO,SAASA,WAAWA,CAACC,MAAM,EAAE;EAClC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}