{"ast": null, "code": "function isBrowser() {\n  return typeof window !== 'undefined' && window?.document != null;\n}\nexport { isBrowser };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isBrowser.mjs"], "sourcesContent": ["function isBrowser() {\n    return typeof window !== 'undefined' && window?.document != null;\n}\n\nexport { isBrowser };\n"], "mappings": "AAAA,SAASA,SAASA,CAAA,EAAG;EACjB,OAAO,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,EAAEC,QAAQ,IAAI,IAAI;AACpE;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}