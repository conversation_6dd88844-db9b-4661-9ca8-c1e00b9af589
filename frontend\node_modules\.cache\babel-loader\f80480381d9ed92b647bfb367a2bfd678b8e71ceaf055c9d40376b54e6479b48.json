{"ast": null, "code": "import { gridVisibleRowsSelector } from \"../features/pagination/gridPaginationSelector.js\";\nimport { useGridSelector } from \"./index.js\";\nexport const getVisibleRows = (apiRef, props) => {\n  return gridVisibleRowsSelector(apiRef);\n};\n\n/**\n * Computes the list of rows that are reachable by scroll.\n * Depending on whether pagination is enabled, it will return the rows in the current page.\n * - If the pagination is disabled or in server mode, it equals all the visible rows.\n * - If the row tree has several layers, it contains up to `state.pageSize` top level rows and all their descendants.\n * - If the row tree is flat, it only contains up to `state.pageSize` rows.\n */\n\nexport const useGridVisibleRows = (apiRef, props) => {\n  return useGridSelector(apiRef, gridVisibleRowsSelector);\n};", "map": {"version": 3, "names": ["gridVisibleRowsSelector", "useGridSelector", "getVisibleRows", "apiRef", "props", "useGridVisibleRows"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridVisibleRows.js"], "sourcesContent": ["import { gridVisibleRowsSelector } from \"../features/pagination/gridPaginationSelector.js\";\nimport { useGridSelector } from \"./index.js\";\nexport const getVisibleRows = (apiRef, props) => {\n  return gridVisibleRowsSelector(apiRef);\n};\n\n/**\n * Computes the list of rows that are reachable by scroll.\n * Depending on whether pagination is enabled, it will return the rows in the current page.\n * - If the pagination is disabled or in server mode, it equals all the visible rows.\n * - If the row tree has several layers, it contains up to `state.pageSize` top level rows and all their descendants.\n * - If the row tree is flat, it only contains up to `state.pageSize` rows.\n */\n\nexport const useGridVisibleRows = (apiRef, props) => {\n  return useGridSelector(apiRef, gridVisibleRowsSelector);\n};"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,kDAAkD;AAC1F,SAASC,eAAe,QAAQ,YAAY;AAC5C,OAAO,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC/C,OAAOJ,uBAAuB,CAACG,MAAM,CAAC;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAME,kBAAkB,GAAGA,CAACF,MAAM,EAAEC,KAAK,KAAK;EACnD,OAAOH,eAAe,CAACE,MAAM,EAAEH,uBAAuB,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}