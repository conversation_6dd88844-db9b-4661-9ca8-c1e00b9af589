{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\", \"field\"],\n  _excluded2 = [\"id\", \"field\"];\nimport * as React from 'react';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useGridEvent, useGridEventPriority } from \"../../utils/useGridEvent.js\";\nimport { GridEditModes, GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridCellEditStartReasons, GridCellEditStopReasons } from \"../../../models/params/gridEditCellParams.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridCellEditing = (apiRef, props) => {\n  const [cellModesModel, setCellModesModel] = React.useState({});\n  const cellModesModelRef = React.useRef(cellModesModel);\n  const prevCellModesModel = React.useRef({});\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    cellModesModel: cellModesModelProp,\n    onCellModesModelChange\n  } = props;\n  const runIfEditModeIsCell = callback => (...args) => {\n    if (props.editMode === GridEditModes.Cell) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, field, mode) => {\n    if (apiRef.current.getCellMode(id, field) !== mode) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (params.cellMode === GridCellModes.Edit) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('cellEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.View) {\n      return;\n    }\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.View) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStopReasons.cellFocusOut\n    });\n    apiRef.current.publishEvent('cellEditStop', newParams, event);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridCellEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        reason = event.shiftKey ? GridCellEditStopReasons.shiftTabKeyDown : GridCellEditStopReasons.tabKeyDown;\n        event.preventDefault(); // Prevent going to the next element in the tab sequence\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason\n        });\n        apiRef.current.publishEvent('cellEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'cell'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridCellEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridCellEditStartReasons.pasteKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridCellEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason,\n          key: event.key\n        });\n        apiRef.current.publishEvent('cellEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleCellEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startCellEditModeParams = {\n      id,\n      field\n    };\n    if (reason === GridCellEditStartReasons.printableKeyDown || reason === GridCellEditStartReasons.deleteKeyDown || reason === GridCellEditStartReasons.pasteKeyDown) {\n      startCellEditModeParams.deleteValue = true;\n    }\n    apiRef.current.startCellEditMode(startCellEditModeParams);\n  }, [apiRef]);\n  const handleCellEditStop = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    let cellToFocusAfter;\n    if (reason === GridCellEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridCellEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridCellEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopCellEditMode({\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  const runIfNoFieldErrors = callback => async (...args) => {\n    if (callback) {\n      const {\n        id,\n        field\n      } = args[0];\n      const editRowsState = apiRef.current.state.editRows;\n      const hasFieldErrors = editRowsState[id][field]?.error;\n      if (!hasFieldErrors) {\n        callback(...args);\n      }\n    }\n  };\n  useGridEvent(apiRef, 'cellDoubleClick', runIfEditModeIsCell(handleCellDoubleClick));\n  useGridEvent(apiRef, 'cellFocusOut', runIfEditModeIsCell(handleCellFocusOut));\n  useGridEvent(apiRef, 'cellKeyDown', runIfEditModeIsCell(handleCellKeyDown));\n  useGridEvent(apiRef, 'cellEditStart', runIfEditModeIsCell(handleCellEditStart));\n  useGridEvent(apiRef, 'cellEditStop', runIfEditModeIsCell(handleCellEditStop));\n  useGridEventPriority(apiRef, 'cellEditStart', props.onCellEditStart);\n  useGridEventPriority(apiRef, 'cellEditStop', runIfNoFieldErrors(props.onCellEditStop));\n  const getCellMode = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const isEditing = editingState[id] && editingState[id][field];\n    return isEditing ? GridCellModes.Edit : GridCellModes.View;\n  }, [apiRef]);\n  const updateCellModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.cellModesModel;\n    if (onCellModesModelChange && isNewModelDifferentFromProp) {\n      onCellModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.cellModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setCellModesModel(newModel);\n    cellModesModelRef.current = newModel;\n    apiRef.current.publishEvent('cellModesModelChange', newModel);\n  });\n  const updateFieldInCellModesModel = React.useCallback((id, field, newProps) => {\n    // We use the ref because it always contain the up-to-date value, different from the state\n    // that needs a rerender to reflect the new value\n    const newModel = _extends({}, cellModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newModel[id], {\n        [field]: _extends({}, newProps)\n      });\n    } else {\n      const _newModel$id = newModel[id],\n        otherFields = _objectWithoutPropertiesLoose(_newModel$id, [field].map(_toPropertyKey)); // Ensure that we have a new object, not a reference\n      newModel[id] = otherFields;\n      if (Object.keys(newModel[id]).length === 0) {\n        delete newModel[id];\n      }\n    }\n    updateCellModesModel(newModel);\n  }, [updateCellModesModel]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const startCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.View);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.Edit\n    }, other));\n  }, [throwIfNotEditable, throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStartCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      deleteValue,\n      initialValue\n    } = params;\n    const value = apiRef.current.getCellValue(id, field);\n    let newValue = value;\n    if (deleteValue) {\n      newValue = getDefaultCellValue(apiRef.current.getColumn(field));\n    } else if (initialValue) {\n      newValue = initialValue;\n    }\n    const column = apiRef.current.getColumn(field);\n    const shouldProcessEditCellProps = !!column.preProcessEditCellProps && deleteValue;\n    let newProps = {\n      value: newValue,\n      error: false,\n      isProcessingProps: shouldProcessEditCellProps\n    };\n    updateOrDeleteFieldState(id, field, newProps);\n    apiRef.current.setCellFocus(id, field);\n    if (shouldProcessEditCellProps) {\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row: apiRef.current.getRow(id),\n        props: newProps,\n        hasChanged: newValue !== value\n      }));\n      // Check if still in edit mode before updating\n      if (apiRef.current.getCellMode(id, field) === GridCellModes.Edit) {\n        const editingState = gridEditRowsStateSelector(apiRef);\n        updateOrDeleteFieldState(id, field, _extends({}, newProps, {\n          value: editingState[id][field].value,\n          isProcessingProps: false\n        }));\n      }\n    }\n  });\n  const stopCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.View\n    }, other));\n  }, [throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStopCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter = 'none'\n    } = params;\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    const finishCellEditMode = () => {\n      updateOrDeleteFieldState(id, field, null);\n      updateFieldInCellModesModel(id, field, null);\n      if (cellToFocusAfter !== 'none') {\n        apiRef.current.moveFocusToRelativeCell(id, field, cellToFocusAfter);\n      }\n    };\n    if (ignoreModifications) {\n      finishCellEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const {\n      error,\n      isProcessingProps\n    } = editingState[id][field];\n    const row = apiRef.current.getRow(id);\n    if (error || isProcessingProps) {\n      // Attempt to change cell mode to \"view\" was not successful\n      // Update previous mode to allow another attempt\n      prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n      // Revert the mode in the cellModesModel prop back to \"edit\"\n      updateFieldInCellModesModel(id, field, {\n        mode: GridCellModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field);\n    if (props.dataSource?.updateRow) {\n      if (isDeepEqual(row, rowUpdate)) {\n        finishCellEditMode();\n        return;\n      }\n      const handleError = () => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n      };\n      const updateRowParams = {\n        rowId: id,\n        updatedRow: rowUpdate,\n        previousRow: row\n      };\n      try {\n        await apiRef.current.dataSource.editRow(updateRowParams);\n        finishCellEditMode();\n      } catch {\n        handleError();\n      }\n    } else if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate()` threw an error which was not handled because `onProcessRowUpdateError()` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError()` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/persistence/.'], 'error');\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishCellEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishCellEditMode();\n    }\n  });\n  const setCellEditingEditCellValue = React.useCallback(async params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (column.preProcessEditCellProps) {\n      const hasChanged = value !== editingState[id][field].value;\n      newProps = _extends({}, newProps, {\n        isProcessingProps: true\n      });\n      updateOrDeleteFieldState(id, field, newProps);\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps,\n        hasChanged\n      }));\n    }\n\n    // Check again if the cell is in edit mode because the user may have\n    // discarded the changes while the props were being processed.\n    if (apiRef.current.getCellMode(id, field) === GridCellModes.View) {\n      return false;\n    }\n    editingState = gridEditRowsStateSelector(apiRef);\n    newProps = _extends({}, newProps, {\n      isProcessingProps: false\n    });\n    // We don't update the value with the one coming from the props pre-processing\n    // because when the promise resolves it may be already outdated. The only\n    // exception to this rule is when there's no pre-processing.\n    newProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n    updateOrDeleteFieldState(id, field, newProps);\n    editingState = gridEditRowsStateSelector(apiRef);\n    return !editingState[id]?.[field]?.error;\n  }, [apiRef, throwIfNotEditable, throwIfNotInMode, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromCellEditing = React.useCallback((id, field) => {\n    const column = apiRef.current.getColumn(field);\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id] || !editingState[id][field]) {\n      return apiRef.current.getRow(id);\n    }\n    const {\n      value\n    } = editingState[id][field];\n    return column.valueSetter ? column.valueSetter(value, row, column, apiRef) : _extends({}, row, {\n      [field]: value\n    });\n  }, [apiRef]);\n  const editingApi = {\n    getCellMode,\n    startCellEditMode,\n    stopCellEditMode\n  };\n  const editingPrivateApi = {\n    setCellEditingEditCellValue,\n    getRowWithUpdatedValuesFromCellEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (cellModesModelProp) {\n      updateCellModesModel(cellModesModelProp);\n    }\n  }, [cellModesModelProp, updateCellModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopCellEditMode may change it later\n    const copyOfPrevCellModes = prevCellModesModel.current;\n    prevCellModesModel.current = deepClone(cellModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(cellModesModel).forEach(([id, fields]) => {\n      Object.entries(fields).forEach(([field, params]) => {\n        const prevMode = copyOfPrevCellModes[id]?.[field]?.mode || GridCellModes.View;\n        const originalId = rowsLookup[id] ? apiRef.current.getRowId(rowsLookup[id]) : id;\n        if (params.mode === GridCellModes.Edit && prevMode === GridCellModes.View) {\n          updateStateToStartCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        } else if (params.mode === GridCellModes.View && prevMode === GridCellModes.Edit) {\n          updateStateToStopCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        }\n      });\n    });\n  }, [apiRef, cellModesModel, updateStateToStartCellEditMode, updateStateToStopCellEditMode]);\n};", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_extends", "_excluded", "_excluded2", "React", "warnOnce", "useEventCallback", "useEnhancedEffect", "isDeepEqual", "useGridEvent", "useGridEventPriority", "GridEditModes", "GridCellModes", "useGridApiMethod", "gridEditRowsStateSelector", "isPrintableKey", "isPasteShortcut", "gridRowsLookupSelector", "deepClone", "GridCellEditStartReasons", "GridCellEditStopReasons", "getDefaultCellValue", "useGridCellEditing", "apiRef", "props", "cellModesModel", "setCellModesModel", "useState", "cellModesModelRef", "useRef", "prevCellModesModel", "processRowUpdate", "onProcessRowUpdateError", "cellModesModelProp", "onCellModesModelChange", "runIfEditModeIsCell", "callback", "args", "editMode", "Cell", "throwIfNotEditable", "useCallback", "id", "field", "params", "current", "getCellParams", "isCellEditable", "Error", "throwIfNotInMode", "mode", "getCellMode", "handleCellDoubleClick", "event", "isEditable", "cellMode", "Edit", "newParams", "reason", "cellDoubleClick", "publishEvent", "handleCellFocusOut", "View", "cellFocusOut", "handleCellKeyDown", "which", "key", "escapeKeyDown", "enterKeyDown", "shift<PERSON>ey", "shiftTabKeyDown", "tabKeyDown", "preventDefault", "canStartEditing", "unstable_applyPipeProcessors", "cellParams", "printableKeyDown", "pasteKeyDown", "deleteKeyDown", "handleCellEditStart", "startCellEditModeParams", "deleteValue", "startCellEditMode", "handleCellEditStop", "runPendingEditCellValueMutation", "cellToFocusAfter", "ignoreModifications", "stopCellEditMode", "runIfNoFieldErrors", "editRowsState", "state", "editRows", "hasFieldErrors", "error", "onCellEditStart", "onCellEditStop", "editingState", "isEditing", "updateCellModesModel", "newModel", "isNewModelDifferentFromProp", "api", "updateFieldInCellModesModel", "newProps", "_newModel$id", "otherFields", "map", "Object", "keys", "length", "updateOrDeleteFieldState", "setState", "newEditingState", "other", "updateStateToStartCellEditMode", "initialValue", "value", "getCellValue", "newValue", "getColumn", "column", "shouldProcessEditCellProps", "preProcessEditCellProps", "isProcessingProps", "setCellFocus", "Promise", "resolve", "row", "getRow", "has<PERSON><PERSON>ed", "updateStateToStopCellEditMode", "finishCellEditMode", "moveFocusToRelativeCell", "rowUpdate", "getRowWithUpdatedValuesFromCellEditing", "dataSource", "updateRow", "handleError", "updateRowParams", "rowId", "updatedRow", "previousRow", "editRow", "errorThrown", "process", "env", "NODE_ENV", "then", "finalRowUpdate", "updateRows", "catch", "setCellEditingEditCellValue", "debounceMs", "unstable_skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsedValue", "valueParser", "changeReason", "valueSetter", "editingApi", "editingPrivateApi", "useEffect", "rowsLookup", "copyOfPrevCellModes", "entries", "for<PERSON>ach", "fields", "prevMode", "originalId", "getRowId"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/editing/useGridCellEditing.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\", \"field\"],\n  _excluded2 = [\"id\", \"field\"];\nimport * as React from 'react';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useGridEvent, useGridEventPriority } from \"../../utils/useGridEvent.js\";\nimport { GridEditModes, GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridCellEditStartReasons, GridCellEditStopReasons } from \"../../../models/params/gridEditCellParams.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridCellEditing = (apiRef, props) => {\n  const [cellModesModel, setCellModesModel] = React.useState({});\n  const cellModesModelRef = React.useRef(cellModesModel);\n  const prevCellModesModel = React.useRef({});\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    cellModesModel: cellModesModelProp,\n    onCellModesModelChange\n  } = props;\n  const runIfEditModeIsCell = callback => (...args) => {\n    if (props.editMode === GridEditModes.Cell) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, field, mode) => {\n    if (apiRef.current.getCellMode(id, field) !== mode) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (params.cellMode === GridCellModes.Edit) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('cellEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.View) {\n      return;\n    }\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.View) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStopReasons.cellFocusOut\n    });\n    apiRef.current.publishEvent('cellEditStop', newParams, event);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridCellEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        reason = event.shiftKey ? GridCellEditStopReasons.shiftTabKeyDown : GridCellEditStopReasons.tabKeyDown;\n        event.preventDefault(); // Prevent going to the next element in the tab sequence\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason\n        });\n        apiRef.current.publishEvent('cellEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'cell'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridCellEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridCellEditStartReasons.pasteKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridCellEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason,\n          key: event.key\n        });\n        apiRef.current.publishEvent('cellEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleCellEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startCellEditModeParams = {\n      id,\n      field\n    };\n    if (reason === GridCellEditStartReasons.printableKeyDown || reason === GridCellEditStartReasons.deleteKeyDown || reason === GridCellEditStartReasons.pasteKeyDown) {\n      startCellEditModeParams.deleteValue = true;\n    }\n    apiRef.current.startCellEditMode(startCellEditModeParams);\n  }, [apiRef]);\n  const handleCellEditStop = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    let cellToFocusAfter;\n    if (reason === GridCellEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridCellEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridCellEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopCellEditMode({\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  const runIfNoFieldErrors = callback => async (...args) => {\n    if (callback) {\n      const {\n        id,\n        field\n      } = args[0];\n      const editRowsState = apiRef.current.state.editRows;\n      const hasFieldErrors = editRowsState[id][field]?.error;\n      if (!hasFieldErrors) {\n        callback(...args);\n      }\n    }\n  };\n  useGridEvent(apiRef, 'cellDoubleClick', runIfEditModeIsCell(handleCellDoubleClick));\n  useGridEvent(apiRef, 'cellFocusOut', runIfEditModeIsCell(handleCellFocusOut));\n  useGridEvent(apiRef, 'cellKeyDown', runIfEditModeIsCell(handleCellKeyDown));\n  useGridEvent(apiRef, 'cellEditStart', runIfEditModeIsCell(handleCellEditStart));\n  useGridEvent(apiRef, 'cellEditStop', runIfEditModeIsCell(handleCellEditStop));\n  useGridEventPriority(apiRef, 'cellEditStart', props.onCellEditStart);\n  useGridEventPriority(apiRef, 'cellEditStop', runIfNoFieldErrors(props.onCellEditStop));\n  const getCellMode = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const isEditing = editingState[id] && editingState[id][field];\n    return isEditing ? GridCellModes.Edit : GridCellModes.View;\n  }, [apiRef]);\n  const updateCellModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.cellModesModel;\n    if (onCellModesModelChange && isNewModelDifferentFromProp) {\n      onCellModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.cellModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setCellModesModel(newModel);\n    cellModesModelRef.current = newModel;\n    apiRef.current.publishEvent('cellModesModelChange', newModel);\n  });\n  const updateFieldInCellModesModel = React.useCallback((id, field, newProps) => {\n    // We use the ref because it always contain the up-to-date value, different from the state\n    // that needs a rerender to reflect the new value\n    const newModel = _extends({}, cellModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newModel[id], {\n        [field]: _extends({}, newProps)\n      });\n    } else {\n      const _newModel$id = newModel[id],\n        otherFields = _objectWithoutPropertiesLoose(_newModel$id, [field].map(_toPropertyKey)); // Ensure that we have a new object, not a reference\n      newModel[id] = otherFields;\n      if (Object.keys(newModel[id]).length === 0) {\n        delete newModel[id];\n      }\n    }\n    updateCellModesModel(newModel);\n  }, [updateCellModesModel]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const startCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.View);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.Edit\n    }, other));\n  }, [throwIfNotEditable, throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStartCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      deleteValue,\n      initialValue\n    } = params;\n    const value = apiRef.current.getCellValue(id, field);\n    let newValue = value;\n    if (deleteValue) {\n      newValue = getDefaultCellValue(apiRef.current.getColumn(field));\n    } else if (initialValue) {\n      newValue = initialValue;\n    }\n    const column = apiRef.current.getColumn(field);\n    const shouldProcessEditCellProps = !!column.preProcessEditCellProps && deleteValue;\n    let newProps = {\n      value: newValue,\n      error: false,\n      isProcessingProps: shouldProcessEditCellProps\n    };\n    updateOrDeleteFieldState(id, field, newProps);\n    apiRef.current.setCellFocus(id, field);\n    if (shouldProcessEditCellProps) {\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row: apiRef.current.getRow(id),\n        props: newProps,\n        hasChanged: newValue !== value\n      }));\n      // Check if still in edit mode before updating\n      if (apiRef.current.getCellMode(id, field) === GridCellModes.Edit) {\n        const editingState = gridEditRowsStateSelector(apiRef);\n        updateOrDeleteFieldState(id, field, _extends({}, newProps, {\n          value: editingState[id][field].value,\n          isProcessingProps: false\n        }));\n      }\n    }\n  });\n  const stopCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.View\n    }, other));\n  }, [throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStopCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter = 'none'\n    } = params;\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    const finishCellEditMode = () => {\n      updateOrDeleteFieldState(id, field, null);\n      updateFieldInCellModesModel(id, field, null);\n      if (cellToFocusAfter !== 'none') {\n        apiRef.current.moveFocusToRelativeCell(id, field, cellToFocusAfter);\n      }\n    };\n    if (ignoreModifications) {\n      finishCellEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const {\n      error,\n      isProcessingProps\n    } = editingState[id][field];\n    const row = apiRef.current.getRow(id);\n    if (error || isProcessingProps) {\n      // Attempt to change cell mode to \"view\" was not successful\n      // Update previous mode to allow another attempt\n      prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n      // Revert the mode in the cellModesModel prop back to \"edit\"\n      updateFieldInCellModesModel(id, field, {\n        mode: GridCellModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field);\n    if (props.dataSource?.updateRow) {\n      if (isDeepEqual(row, rowUpdate)) {\n        finishCellEditMode();\n        return;\n      }\n      const handleError = () => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n      };\n      const updateRowParams = {\n        rowId: id,\n        updatedRow: rowUpdate,\n        previousRow: row\n      };\n      try {\n        await apiRef.current.dataSource.editRow(updateRowParams);\n        finishCellEditMode();\n      } catch {\n        handleError();\n      }\n    } else if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate()` threw an error which was not handled because `onProcessRowUpdateError()` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError()` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/persistence/.'], 'error');\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishCellEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishCellEditMode();\n    }\n  });\n  const setCellEditingEditCellValue = React.useCallback(async params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (column.preProcessEditCellProps) {\n      const hasChanged = value !== editingState[id][field].value;\n      newProps = _extends({}, newProps, {\n        isProcessingProps: true\n      });\n      updateOrDeleteFieldState(id, field, newProps);\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps,\n        hasChanged\n      }));\n    }\n\n    // Check again if the cell is in edit mode because the user may have\n    // discarded the changes while the props were being processed.\n    if (apiRef.current.getCellMode(id, field) === GridCellModes.View) {\n      return false;\n    }\n    editingState = gridEditRowsStateSelector(apiRef);\n    newProps = _extends({}, newProps, {\n      isProcessingProps: false\n    });\n    // We don't update the value with the one coming from the props pre-processing\n    // because when the promise resolves it may be already outdated. The only\n    // exception to this rule is when there's no pre-processing.\n    newProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n    updateOrDeleteFieldState(id, field, newProps);\n    editingState = gridEditRowsStateSelector(apiRef);\n    return !editingState[id]?.[field]?.error;\n  }, [apiRef, throwIfNotEditable, throwIfNotInMode, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromCellEditing = React.useCallback((id, field) => {\n    const column = apiRef.current.getColumn(field);\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id] || !editingState[id][field]) {\n      return apiRef.current.getRow(id);\n    }\n    const {\n      value\n    } = editingState[id][field];\n    return column.valueSetter ? column.valueSetter(value, row, column, apiRef) : _extends({}, row, {\n      [field]: value\n    });\n  }, [apiRef]);\n  const editingApi = {\n    getCellMode,\n    startCellEditMode,\n    stopCellEditMode\n  };\n  const editingPrivateApi = {\n    setCellEditingEditCellValue,\n    getRowWithUpdatedValuesFromCellEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (cellModesModelProp) {\n      updateCellModesModel(cellModesModelProp);\n    }\n  }, [cellModesModelProp, updateCellModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopCellEditMode may change it later\n    const copyOfPrevCellModes = prevCellModesModel.current;\n    prevCellModesModel.current = deepClone(cellModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(cellModesModel).forEach(([id, fields]) => {\n      Object.entries(fields).forEach(([field, params]) => {\n        const prevMode = copyOfPrevCellModes[id]?.[field]?.mode || GridCellModes.View;\n        const originalId = rowsLookup[id] ? apiRef.current.getRowId(rowsLookup[id]) : id;\n        if (params.mode === GridCellModes.Edit && prevMode === GridCellModes.View) {\n          updateStateToStartCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        } else if (params.mode === GridCellModes.View && prevMode === GridCellModes.Edit) {\n          updateStateToStopCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        }\n      });\n    });\n  }, [apiRef, cellModesModel, updateStateToStartCellEditMode, updateStateToStopCellEditMode]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;EAC/BC,UAAU,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,6BAA6B;AAChF,SAASC,aAAa,EAAEC,aAAa,QAAQ,qCAAqC;AAClF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,yBAAyB,QAAQ,2BAA2B;AACrE,SAASC,cAAc,EAAEC,eAAe,QAAQ,iCAAiC;AACjF,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,8CAA8C;AAChH,SAASC,mBAAmB,QAAQ,YAAY;AAChD,OAAO,MAAMC,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAMC,iBAAiB,GAAGxB,KAAK,CAACyB,MAAM,CAACJ,cAAc,CAAC;EACtD,MAAMK,kBAAkB,GAAG1B,KAAK,CAACyB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C,MAAM;IACJE,gBAAgB;IAChBC,uBAAuB;IACvBP,cAAc,EAAEQ,kBAAkB;IAClCC;EACF,CAAC,GAAGV,KAAK;EACT,MAAMW,mBAAmB,GAAGC,QAAQ,IAAI,CAAC,GAAGC,IAAI,KAAK;IACnD,IAAIb,KAAK,CAACc,QAAQ,KAAK3B,aAAa,CAAC4B,IAAI,EAAE;MACzCH,QAAQ,CAAC,GAAGC,IAAI,CAAC;IACnB;EACF,CAAC;EACD,MAAMG,kBAAkB,GAAGpC,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC1D,MAAMC,MAAM,GAAGrB,MAAM,CAACsB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;IACtD,IAAI,CAACpB,MAAM,CAACsB,OAAO,CAACE,cAAc,CAACH,MAAM,CAAC,EAAE;MAC1C,MAAM,IAAII,KAAK,CAAC,2BAA2BN,EAAE,cAAcC,KAAK,mBAAmB,CAAC;IACtF;EACF,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EACZ,MAAM0B,gBAAgB,GAAG7C,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAEO,IAAI,KAAK;IAC9D,IAAI3B,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACT,EAAE,EAAEC,KAAK,CAAC,KAAKO,IAAI,EAAE;MAClD,MAAM,IAAIF,KAAK,CAAC,2BAA2BN,EAAE,cAAcC,KAAK,cAAcO,IAAI,QAAQ,CAAC;IAC7F;EACF,CAAC,EAAE,CAAC3B,MAAM,CAAC,CAAC;EACZ,MAAM6B,qBAAqB,GAAGhD,KAAK,CAACqC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IACjE,IAAI,CAACT,MAAM,CAACU,UAAU,EAAE;MACtB;IACF;IACA,IAAIV,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAAC4C,IAAI,EAAE;MAC1C;IACF;IACA,MAAMC,SAAS,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;MACrCc,MAAM,EAAEvC,wBAAwB,CAACwC;IACnC,CAAC,CAAC;IACFpC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,eAAe,EAAEH,SAAS,EAAEJ,KAAK,CAAC;EAChE,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMsC,kBAAkB,GAAGzD,KAAK,CAACqC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IAC9D,IAAIT,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAACkD,IAAI,EAAE;MAC1C;IACF;IACA,IAAIvC,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACP,MAAM,CAACF,EAAE,EAAEE,MAAM,CAACD,KAAK,CAAC,KAAK/B,aAAa,CAACkD,IAAI,EAAE;MAC9E;IACF;IACA,MAAML,SAAS,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;MACrCc,MAAM,EAAEtC,uBAAuB,CAAC2C;IAClC,CAAC,CAAC;IACFxC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEJ,KAAK,CAAC;EAC/D,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMyC,iBAAiB,GAAG5D,KAAK,CAACqC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IAC7D,IAAIT,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAAC4C,IAAI,EAAE;MAC1C;MACA;MACA,IAAIH,KAAK,CAACY,KAAK,KAAK,GAAG,EAAE;QACvB;MACF;MACA,IAAIP,MAAM;MACV,IAAIL,KAAK,CAACa,GAAG,KAAK,QAAQ,EAAE;QAC1BR,MAAM,GAAGtC,uBAAuB,CAAC+C,aAAa;MAChD,CAAC,MAAM,IAAId,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;QAChCR,MAAM,GAAGtC,uBAAuB,CAACgD,YAAY;MAC/C,CAAC,MAAM,IAAIf,KAAK,CAACa,GAAG,KAAK,KAAK,EAAE;QAC9BR,MAAM,GAAGL,KAAK,CAACgB,QAAQ,GAAGjD,uBAAuB,CAACkD,eAAe,GAAGlD,uBAAuB,CAACmD,UAAU;QACtGlB,KAAK,CAACmB,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1B;MACA,IAAId,MAAM,EAAE;QACV,MAAMD,SAAS,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;UACrCc;QACF,CAAC,CAAC;QACFnC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEJ,KAAK,CAAC;MAC/D;IACF,CAAC,MAAM,IAAIT,MAAM,CAACU,UAAU,EAAE;MAC5B,IAAII,MAAM;MACV,MAAMe,eAAe,GAAGlD,MAAM,CAACsB,OAAO,CAAC6B,4BAA4B,CAAC,iBAAiB,EAAE,IAAI,EAAE;QAC3FrB,KAAK;QACLsB,UAAU,EAAE/B,MAAM;QAClBN,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAACmC,eAAe,EAAE;QACpB;MACF;MACA,IAAI1D,cAAc,CAACsC,KAAK,CAAC,EAAE;QACzBK,MAAM,GAAGvC,wBAAwB,CAACyD,gBAAgB;MACpD,CAAC,MAAM,IAAI5D,eAAe,CAACqC,KAAK,CAAC,EAAE;QACjCK,MAAM,GAAGvC,wBAAwB,CAAC0D,YAAY;MAChD,CAAC,MAAM,IAAIxB,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;QAChCR,MAAM,GAAGvC,wBAAwB,CAACiD,YAAY;MAChD,CAAC,MAAM,IAAIf,KAAK,CAACa,GAAG,KAAK,WAAW,IAAIb,KAAK,CAACa,GAAG,KAAK,QAAQ,EAAE;QAC9DR,MAAM,GAAGvC,wBAAwB,CAAC2D,aAAa;MACjD;MACA,IAAIpB,MAAM,EAAE;QACV,MAAMD,SAAS,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;UACrCc,MAAM;UACNQ,GAAG,EAAEb,KAAK,CAACa;QACb,CAAC,CAAC;QACF3C,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,eAAe,EAAEH,SAAS,EAAEJ,KAAK,CAAC;MAChE;IACF;EACF,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMwD,mBAAmB,GAAG3E,KAAK,CAACqC,WAAW,CAACG,MAAM,IAAI;IACtD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLe;IACF,CAAC,GAAGd,MAAM;IACV,MAAMoC,uBAAuB,GAAG;MAC9BtC,EAAE;MACFC;IACF,CAAC;IACD,IAAIe,MAAM,KAAKvC,wBAAwB,CAACyD,gBAAgB,IAAIlB,MAAM,KAAKvC,wBAAwB,CAAC2D,aAAa,IAAIpB,MAAM,KAAKvC,wBAAwB,CAAC0D,YAAY,EAAE;MACjKG,uBAAuB,CAACC,WAAW,GAAG,IAAI;IAC5C;IACA1D,MAAM,CAACsB,OAAO,CAACqC,iBAAiB,CAACF,uBAAuB,CAAC;EAC3D,CAAC,EAAE,CAACzD,MAAM,CAAC,CAAC;EACZ,MAAM4D,kBAAkB,GAAG/E,KAAK,CAACqC,WAAW,CAACG,MAAM,IAAI;IACrD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLe;IACF,CAAC,GAAGd,MAAM;IACVrB,MAAM,CAACsB,OAAO,CAACuC,+BAA+B,CAAC1C,EAAE,EAAEC,KAAK,CAAC;IACzD,IAAI0C,gBAAgB;IACpB,IAAI3B,MAAM,KAAKtC,uBAAuB,CAACgD,YAAY,EAAE;MACnDiB,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI3B,MAAM,KAAKtC,uBAAuB,CAACmD,UAAU,EAAE;MACxDc,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI3B,MAAM,KAAKtC,uBAAuB,CAACkD,eAAe,EAAE;MAC7De,gBAAgB,GAAG,MAAM;IAC3B;IACA,MAAMC,mBAAmB,GAAG5B,MAAM,KAAK,eAAe;IACtDnC,MAAM,CAACsB,OAAO,CAAC0C,gBAAgB,CAAC;MAC9B7C,EAAE;MACFC,KAAK;MACL2C,mBAAmB;MACnBD;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9D,MAAM,CAAC,CAAC;EACZ,MAAMiE,kBAAkB,GAAGpD,QAAQ,IAAI,OAAO,GAAGC,IAAI,KAAK;IACxD,IAAID,QAAQ,EAAE;MACZ,MAAM;QACJM,EAAE;QACFC;MACF,CAAC,GAAGN,IAAI,CAAC,CAAC,CAAC;MACX,MAAMoD,aAAa,GAAGlE,MAAM,CAACsB,OAAO,CAAC6C,KAAK,CAACC,QAAQ;MACnD,MAAMC,cAAc,GAAGH,aAAa,CAAC/C,EAAE,CAAC,CAACC,KAAK,CAAC,EAAEkD,KAAK;MACtD,IAAI,CAACD,cAAc,EAAE;QACnBxD,QAAQ,CAAC,GAAGC,IAAI,CAAC;MACnB;IACF;EACF,CAAC;EACD5B,YAAY,CAACc,MAAM,EAAE,iBAAiB,EAAEY,mBAAmB,CAACiB,qBAAqB,CAAC,CAAC;EACnF3C,YAAY,CAACc,MAAM,EAAE,cAAc,EAAEY,mBAAmB,CAAC0B,kBAAkB,CAAC,CAAC;EAC7EpD,YAAY,CAACc,MAAM,EAAE,aAAa,EAAEY,mBAAmB,CAAC6B,iBAAiB,CAAC,CAAC;EAC3EvD,YAAY,CAACc,MAAM,EAAE,eAAe,EAAEY,mBAAmB,CAAC4C,mBAAmB,CAAC,CAAC;EAC/EtE,YAAY,CAACc,MAAM,EAAE,cAAc,EAAEY,mBAAmB,CAACgD,kBAAkB,CAAC,CAAC;EAC7EzE,oBAAoB,CAACa,MAAM,EAAE,eAAe,EAAEC,KAAK,CAACsE,eAAe,CAAC;EACpEpF,oBAAoB,CAACa,MAAM,EAAE,cAAc,EAAEiE,kBAAkB,CAAChE,KAAK,CAACuE,cAAc,CAAC,CAAC;EACtF,MAAM5C,WAAW,GAAG/C,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IACnD,MAAMqD,YAAY,GAAGlF,yBAAyB,CAACS,MAAM,CAAC;IACtD,MAAM0E,SAAS,GAAGD,YAAY,CAACtD,EAAE,CAAC,IAAIsD,YAAY,CAACtD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC7D,OAAOsD,SAAS,GAAGrF,aAAa,CAAC4C,IAAI,GAAG5C,aAAa,CAACkD,IAAI;EAC5D,CAAC,EAAE,CAACvC,MAAM,CAAC,CAAC;EACZ,MAAM2E,oBAAoB,GAAG5F,gBAAgB,CAAC6F,QAAQ,IAAI;IACxD,MAAMC,2BAA2B,GAAGD,QAAQ,KAAK3E,KAAK,CAACC,cAAc;IACrE,IAAIS,sBAAsB,IAAIkE,2BAA2B,EAAE;MACzDlE,sBAAsB,CAACiE,QAAQ,EAAE;QAC/BE,GAAG,EAAE9E,MAAM,CAACsB;MACd,CAAC,CAAC;IACJ;IACA,IAAIrB,KAAK,CAACC,cAAc,IAAI2E,2BAA2B,EAAE;MACvD,OAAO,CAAC;IACV;IACA1E,iBAAiB,CAACyE,QAAQ,CAAC;IAC3BvE,iBAAiB,CAACiB,OAAO,GAAGsD,QAAQ;IACpC5E,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,sBAAsB,EAAEuC,QAAQ,CAAC;EAC/D,CAAC,CAAC;EACF,MAAMG,2BAA2B,GAAGlG,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAE4D,QAAQ,KAAK;IAC7E;IACA;IACA,MAAMJ,QAAQ,GAAGlG,QAAQ,CAAC,CAAC,CAAC,EAAE2B,iBAAiB,CAACiB,OAAO,CAAC;IACxD,IAAI0D,QAAQ,KAAK,IAAI,EAAE;MACrBJ,QAAQ,CAACzD,EAAE,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,EAAEkG,QAAQ,CAACzD,EAAE,CAAC,EAAE;QACxC,CAACC,KAAK,GAAG1C,QAAQ,CAAC,CAAC,CAAC,EAAEsG,QAAQ;MAChC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMC,YAAY,GAAGL,QAAQ,CAACzD,EAAE,CAAC;QAC/B+D,WAAW,GAAG1G,6BAA6B,CAACyG,YAAY,EAAE,CAAC7D,KAAK,CAAC,CAAC+D,GAAG,CAAC1G,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1FmG,QAAQ,CAACzD,EAAE,CAAC,GAAG+D,WAAW;MAC1B,IAAIE,MAAM,CAACC,IAAI,CAACT,QAAQ,CAACzD,EAAE,CAAC,CAAC,CAACmE,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAOV,QAAQ,CAACzD,EAAE,CAAC;MACrB;IACF;IACAwD,oBAAoB,CAACC,QAAQ,CAAC;EAChC,CAAC,EAAE,CAACD,oBAAoB,CAAC,CAAC;EAC1B,MAAMY,wBAAwB,GAAG1G,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAE4D,QAAQ,KAAK;IAC1EhF,MAAM,CAACsB,OAAO,CAACkE,QAAQ,CAACrB,KAAK,IAAI;MAC/B,MAAMsB,eAAe,GAAG/G,QAAQ,CAAC,CAAC,CAAC,EAAEyF,KAAK,CAACC,QAAQ,CAAC;MACpD,IAAIY,QAAQ,KAAK,IAAI,EAAE;QACrBS,eAAe,CAACtE,EAAE,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,EAAE+G,eAAe,CAACtE,EAAE,CAAC,EAAE;UACtD,CAACC,KAAK,GAAG1C,QAAQ,CAAC,CAAC,CAAC,EAAEsG,QAAQ;QAChC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOS,eAAe,CAACtE,EAAE,CAAC,CAACC,KAAK,CAAC;QACjC,IAAIgE,MAAM,CAACC,IAAI,CAACI,eAAe,CAACtE,EAAE,CAAC,CAAC,CAACmE,MAAM,KAAK,CAAC,EAAE;UACjD,OAAOG,eAAe,CAACtE,EAAE,CAAC;QAC5B;MACF;MACA,OAAOzC,QAAQ,CAAC,CAAC,CAAC,EAAEyF,KAAK,EAAE;QACzBC,QAAQ,EAAEqB;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzF,MAAM,CAAC,CAAC;EACZ,MAAM2D,iBAAiB,GAAG9E,KAAK,CAACqC,WAAW,CAACG,MAAM,IAAI;IACpD,MAAM;QACFF,EAAE;QACFC;MACF,CAAC,GAAGC,MAAM;MACVqE,KAAK,GAAGlH,6BAA6B,CAAC6C,MAAM,EAAE1C,SAAS,CAAC;IAC1DsC,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7BM,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAACkD,IAAI,CAAC;IAC/CwC,2BAA2B,CAAC5D,EAAE,EAAEC,KAAK,EAAE1C,QAAQ,CAAC;MAC9CiD,IAAI,EAAEtC,aAAa,CAAC4C;IACtB,CAAC,EAAEyD,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACzE,kBAAkB,EAAES,gBAAgB,EAAEqD,2BAA2B,CAAC,CAAC;EACvE,MAAMY,8BAA8B,GAAG5G,gBAAgB,CAAC,MAAMsC,MAAM,IAAI;IACtE,MAAM;MACJF,EAAE;MACFC,KAAK;MACLsC,WAAW;MACXkC;IACF,CAAC,GAAGvE,MAAM;IACV,MAAMwE,KAAK,GAAG7F,MAAM,CAACsB,OAAO,CAACwE,YAAY,CAAC3E,EAAE,EAAEC,KAAK,CAAC;IACpD,IAAI2E,QAAQ,GAAGF,KAAK;IACpB,IAAInC,WAAW,EAAE;MACfqC,QAAQ,GAAGjG,mBAAmB,CAACE,MAAM,CAACsB,OAAO,CAAC0E,SAAS,CAAC5E,KAAK,CAAC,CAAC;IACjE,CAAC,MAAM,IAAIwE,YAAY,EAAE;MACvBG,QAAQ,GAAGH,YAAY;IACzB;IACA,MAAMK,MAAM,GAAGjG,MAAM,CAACsB,OAAO,CAAC0E,SAAS,CAAC5E,KAAK,CAAC;IAC9C,MAAM8E,0BAA0B,GAAG,CAAC,CAACD,MAAM,CAACE,uBAAuB,IAAIzC,WAAW;IAClF,IAAIsB,QAAQ,GAAG;MACba,KAAK,EAAEE,QAAQ;MACfzB,KAAK,EAAE,KAAK;MACZ8B,iBAAiB,EAAEF;IACrB,CAAC;IACDX,wBAAwB,CAACpE,EAAE,EAAEC,KAAK,EAAE4D,QAAQ,CAAC;IAC7ChF,MAAM,CAACsB,OAAO,CAAC+E,YAAY,CAAClF,EAAE,EAAEC,KAAK,CAAC;IACtC,IAAI8E,0BAA0B,EAAE;MAC9BlB,QAAQ,GAAG,MAAMsB,OAAO,CAACC,OAAO,CAACN,MAAM,CAACE,uBAAuB,CAAC;QAC9DhF,EAAE;QACFqF,GAAG,EAAExG,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;QAC9BlB,KAAK,EAAE+E,QAAQ;QACf0B,UAAU,EAAEX,QAAQ,KAAKF;MAC3B,CAAC,CAAC,CAAC;MACH;MACA,IAAI7F,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACT,EAAE,EAAEC,KAAK,CAAC,KAAK/B,aAAa,CAAC4C,IAAI,EAAE;QAChE,MAAMwC,YAAY,GAAGlF,yBAAyB,CAACS,MAAM,CAAC;QACtDuF,wBAAwB,CAACpE,EAAE,EAAEC,KAAK,EAAE1C,QAAQ,CAAC,CAAC,CAAC,EAAEsG,QAAQ,EAAE;UACzDa,KAAK,EAAEpB,YAAY,CAACtD,EAAE,CAAC,CAACC,KAAK,CAAC,CAACyE,KAAK;UACpCO,iBAAiB,EAAE;QACrB,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC,CAAC;EACF,MAAMpC,gBAAgB,GAAGnF,KAAK,CAACqC,WAAW,CAACG,MAAM,IAAI;IACnD,MAAM;QACFF,EAAE;QACFC;MACF,CAAC,GAAGC,MAAM;MACVqE,KAAK,GAAGlH,6BAA6B,CAAC6C,MAAM,EAAEzC,UAAU,CAAC;IAC3D8C,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/C8C,2BAA2B,CAAC5D,EAAE,EAAEC,KAAK,EAAE1C,QAAQ,CAAC;MAC9CiD,IAAI,EAAEtC,aAAa,CAACkD;IACtB,CAAC,EAAEmD,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAChE,gBAAgB,EAAEqD,2BAA2B,CAAC,CAAC;EACnD,MAAM4B,6BAA6B,GAAG5H,gBAAgB,CAAC,MAAMsC,MAAM,IAAI;IACrE,MAAM;MACJF,EAAE;MACFC,KAAK;MACL2C,mBAAmB;MACnBD,gBAAgB,GAAG;IACrB,CAAC,GAAGzC,MAAM;IACVK,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/CjC,MAAM,CAACsB,OAAO,CAACuC,+BAA+B,CAAC1C,EAAE,EAAEC,KAAK,CAAC;IACzD,MAAMwF,kBAAkB,GAAGA,CAAA,KAAM;MAC/BrB,wBAAwB,CAACpE,EAAE,EAAEC,KAAK,EAAE,IAAI,CAAC;MACzC2D,2BAA2B,CAAC5D,EAAE,EAAEC,KAAK,EAAE,IAAI,CAAC;MAC5C,IAAI0C,gBAAgB,KAAK,MAAM,EAAE;QAC/B9D,MAAM,CAACsB,OAAO,CAACuF,uBAAuB,CAAC1F,EAAE,EAAEC,KAAK,EAAE0C,gBAAgB,CAAC;MACrE;IACF,CAAC;IACD,IAAIC,mBAAmB,EAAE;MACvB6C,kBAAkB,CAAC,CAAC;MACpB;IACF;IACA,MAAMnC,YAAY,GAAGlF,yBAAyB,CAACS,MAAM,CAAC;IACtD,MAAM;MACJsE,KAAK;MACL8B;IACF,CAAC,GAAG3B,YAAY,CAACtD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC3B,MAAMoF,GAAG,GAAGxG,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;IACrC,IAAImD,KAAK,IAAI8B,iBAAiB,EAAE;MAC9B;MACA;MACA7F,kBAAkB,CAACe,OAAO,CAACH,EAAE,CAAC,CAACC,KAAK,CAAC,CAACO,IAAI,GAAGtC,aAAa,CAAC4C,IAAI;MAC/D;MACA8C,2BAA2B,CAAC5D,EAAE,EAAEC,KAAK,EAAE;QACrCO,IAAI,EAAEtC,aAAa,CAAC4C;MACtB,CAAC,CAAC;MACF;IACF;IACA,MAAM6E,SAAS,GAAG9G,MAAM,CAACsB,OAAO,CAACyF,sCAAsC,CAAC5F,EAAE,EAAEC,KAAK,CAAC;IAClF,IAAInB,KAAK,CAAC+G,UAAU,EAAEC,SAAS,EAAE;MAC/B,IAAIhI,WAAW,CAACuH,GAAG,EAAEM,SAAS,CAAC,EAAE;QAC/BF,kBAAkB,CAAC,CAAC;QACpB;MACF;MACA,MAAMM,WAAW,GAAGA,CAAA,KAAM;QACxB3G,kBAAkB,CAACe,OAAO,CAACH,EAAE,CAAC,CAACC,KAAK,CAAC,CAACO,IAAI,GAAGtC,aAAa,CAAC4C,IAAI;QAC/D;QACA8C,2BAA2B,CAAC5D,EAAE,EAAEC,KAAK,EAAE;UACrCO,IAAI,EAAEtC,aAAa,CAAC4C;QACtB,CAAC,CAAC;MACJ,CAAC;MACD,MAAMkF,eAAe,GAAG;QACtBC,KAAK,EAAEjG,EAAE;QACTkG,UAAU,EAAEP,SAAS;QACrBQ,WAAW,EAAEd;MACf,CAAC;MACD,IAAI;QACF,MAAMxG,MAAM,CAACsB,OAAO,CAAC0F,UAAU,CAACO,OAAO,CAACJ,eAAe,CAAC;QACxDP,kBAAkB,CAAC,CAAC;MACtB,CAAC,CAAC,MAAM;QACNM,WAAW,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAI1G,gBAAgB,EAAE;MAC3B,MAAM0G,WAAW,GAAGM,WAAW,IAAI;QACjCjH,kBAAkB,CAACe,OAAO,CAACH,EAAE,CAAC,CAACC,KAAK,CAAC,CAACO,IAAI,GAAGtC,aAAa,CAAC4C,IAAI;QAC/D;QACA8C,2BAA2B,CAAC5D,EAAE,EAAEC,KAAK,EAAE;UACrCO,IAAI,EAAEtC,aAAa,CAAC4C;QACtB,CAAC,CAAC;QACF,IAAIxB,uBAAuB,EAAE;UAC3BA,uBAAuB,CAAC+G,WAAW,CAAC;QACtC,CAAC,MAAM,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UAChD7I,QAAQ,CAAC,CAAC,4HAA4H,EAAE,mJAAmJ,EAAE,8EAA8E,CAAC,EAAE,OAAO,CAAC;QACxX;MACF,CAAC;MACD,IAAI;QACFwH,OAAO,CAACC,OAAO,CAAC/F,gBAAgB,CAACsG,SAAS,EAAEN,GAAG,EAAE;UAC/CY,KAAK,EAAEjG;QACT,CAAC,CAAC,CAAC,CAACyG,IAAI,CAACC,cAAc,IAAI;UACzB7H,MAAM,CAACsB,OAAO,CAACwG,UAAU,CAAC,CAACD,cAAc,CAAC,CAAC;UAC3CjB,kBAAkB,CAAC,CAAC;QACtB,CAAC,CAAC,CAACmB,KAAK,CAACb,WAAW,CAAC;MACvB,CAAC,CAAC,OAAOM,WAAW,EAAE;QACpBN,WAAW,CAACM,WAAW,CAAC;MAC1B;IACF,CAAC,MAAM;MACLxH,MAAM,CAACsB,OAAO,CAACwG,UAAU,CAAC,CAAChB,SAAS,CAAC,CAAC;MACtCF,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,CAAC;EACF,MAAMoB,2BAA2B,GAAGnJ,KAAK,CAACqC,WAAW,CAAC,MAAMG,MAAM,IAAI;IACpE,MAAM;MACJF,EAAE;MACFC,KAAK;MACLyE,KAAK;MACLoC,UAAU;MACVC,wBAAwB,EAAEC;IAC5B,CAAC,GAAG9G,MAAM;IACVJ,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7BM,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/C,MAAMgE,MAAM,GAAGjG,MAAM,CAACsB,OAAO,CAAC0E,SAAS,CAAC5E,KAAK,CAAC;IAC9C,MAAMoF,GAAG,GAAGxG,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;IACrC,IAAIiH,WAAW,GAAGvC,KAAK;IACvB,IAAII,MAAM,CAACoC,WAAW,IAAI,CAACF,eAAe,EAAE;MAC1CC,WAAW,GAAGnC,MAAM,CAACoC,WAAW,CAACxC,KAAK,EAAEW,GAAG,EAAEP,MAAM,EAAEjG,MAAM,CAAC;IAC9D;IACA,IAAIyE,YAAY,GAAGlF,yBAAyB,CAACS,MAAM,CAAC;IACpD,IAAIgF,QAAQ,GAAGtG,QAAQ,CAAC,CAAC,CAAC,EAAE+F,YAAY,CAACtD,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACnDyE,KAAK,EAAEuC,WAAW;MAClBE,YAAY,EAAEL,UAAU,GAAG,2BAA2B,GAAG;IAC3D,CAAC,CAAC;IACF,IAAIhC,MAAM,CAACE,uBAAuB,EAAE;MAClC,MAAMO,UAAU,GAAGb,KAAK,KAAKpB,YAAY,CAACtD,EAAE,CAAC,CAACC,KAAK,CAAC,CAACyE,KAAK;MAC1Db,QAAQ,GAAGtG,QAAQ,CAAC,CAAC,CAAC,EAAEsG,QAAQ,EAAE;QAChCoB,iBAAiB,EAAE;MACrB,CAAC,CAAC;MACFb,wBAAwB,CAACpE,EAAE,EAAEC,KAAK,EAAE4D,QAAQ,CAAC;MAC7CA,QAAQ,GAAG,MAAMsB,OAAO,CAACC,OAAO,CAACN,MAAM,CAACE,uBAAuB,CAAC;QAC9DhF,EAAE;QACFqF,GAAG;QACHvG,KAAK,EAAE+E,QAAQ;QACf0B;MACF,CAAC,CAAC,CAAC;IACL;;IAEA;IACA;IACA,IAAI1G,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACT,EAAE,EAAEC,KAAK,CAAC,KAAK/B,aAAa,CAACkD,IAAI,EAAE;MAChE,OAAO,KAAK;IACd;IACAkC,YAAY,GAAGlF,yBAAyB,CAACS,MAAM,CAAC;IAChDgF,QAAQ,GAAGtG,QAAQ,CAAC,CAAC,CAAC,EAAEsG,QAAQ,EAAE;MAChCoB,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACF;IACA;IACA;IACApB,QAAQ,CAACa,KAAK,GAAGI,MAAM,CAACE,uBAAuB,GAAG1B,YAAY,CAACtD,EAAE,CAAC,CAACC,KAAK,CAAC,CAACyE,KAAK,GAAGuC,WAAW;IAC7F7C,wBAAwB,CAACpE,EAAE,EAAEC,KAAK,EAAE4D,QAAQ,CAAC;IAC7CP,YAAY,GAAGlF,yBAAyB,CAACS,MAAM,CAAC;IAChD,OAAO,CAACyE,YAAY,CAACtD,EAAE,CAAC,GAAGC,KAAK,CAAC,EAAEkD,KAAK;EAC1C,CAAC,EAAE,CAACtE,MAAM,EAAEiB,kBAAkB,EAAES,gBAAgB,EAAE6D,wBAAwB,CAAC,CAAC;EAC5E,MAAMwB,sCAAsC,GAAGlI,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC9E,MAAM6E,MAAM,GAAGjG,MAAM,CAACsB,OAAO,CAAC0E,SAAS,CAAC5E,KAAK,CAAC;IAC9C,MAAMqD,YAAY,GAAGlF,yBAAyB,CAACS,MAAM,CAAC;IACtD,MAAMwG,GAAG,GAAGxG,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;IACrC,IAAI,CAACsD,YAAY,CAACtD,EAAE,CAAC,IAAI,CAACsD,YAAY,CAACtD,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACjD,OAAOpB,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;IAClC;IACA,MAAM;MACJ0E;IACF,CAAC,GAAGpB,YAAY,CAACtD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC3B,OAAO6E,MAAM,CAACsC,WAAW,GAAGtC,MAAM,CAACsC,WAAW,CAAC1C,KAAK,EAAEW,GAAG,EAAEP,MAAM,EAAEjG,MAAM,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAE8H,GAAG,EAAE;MAC7F,CAACpF,KAAK,GAAGyE;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7F,MAAM,CAAC,CAAC;EACZ,MAAMwI,UAAU,GAAG;IACjB5G,WAAW;IACX+B,iBAAiB;IACjBK;EACF,CAAC;EACD,MAAMyE,iBAAiB,GAAG;IACxBT,2BAA2B;IAC3BjB;EACF,CAAC;EACDzH,gBAAgB,CAACU,MAAM,EAAEwI,UAAU,EAAE,QAAQ,CAAC;EAC9ClJ,gBAAgB,CAACU,MAAM,EAAEyI,iBAAiB,EAAE,SAAS,CAAC;EACtD5J,KAAK,CAAC6J,SAAS,CAAC,MAAM;IACpB,IAAIhI,kBAAkB,EAAE;MACtBiE,oBAAoB,CAACjE,kBAAkB,CAAC;IAC1C;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAEiE,oBAAoB,CAAC,CAAC;;EAE9C;EACA3F,iBAAiB,CAAC,MAAM;IACtB,MAAM2J,UAAU,GAAGjJ,sBAAsB,CAACM,MAAM,CAAC;;IAEjD;IACA,MAAM4I,mBAAmB,GAAGrI,kBAAkB,CAACe,OAAO;IACtDf,kBAAkB,CAACe,OAAO,GAAG3B,SAAS,CAACO,cAAc,CAAC,CAAC,CAAC;;IAExDkF,MAAM,CAACyD,OAAO,CAAC3I,cAAc,CAAC,CAAC4I,OAAO,CAAC,CAAC,CAAC3H,EAAE,EAAE4H,MAAM,CAAC,KAAK;MACvD3D,MAAM,CAACyD,OAAO,CAACE,MAAM,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC1H,KAAK,EAAEC,MAAM,CAAC,KAAK;QAClD,MAAM2H,QAAQ,GAAGJ,mBAAmB,CAACzH,EAAE,CAAC,GAAGC,KAAK,CAAC,EAAEO,IAAI,IAAItC,aAAa,CAACkD,IAAI;QAC7E,MAAM0G,UAAU,GAAGN,UAAU,CAACxH,EAAE,CAAC,GAAGnB,MAAM,CAACsB,OAAO,CAAC4H,QAAQ,CAACP,UAAU,CAACxH,EAAE,CAAC,CAAC,GAAGA,EAAE;QAChF,IAAIE,MAAM,CAACM,IAAI,KAAKtC,aAAa,CAAC4C,IAAI,IAAI+G,QAAQ,KAAK3J,aAAa,CAACkD,IAAI,EAAE;UACzEoD,8BAA8B,CAACjH,QAAQ,CAAC;YACtCyC,EAAE,EAAE8H,UAAU;YACd7H;UACF,CAAC,EAAEC,MAAM,CAAC,CAAC;QACb,CAAC,MAAM,IAAIA,MAAM,CAACM,IAAI,KAAKtC,aAAa,CAACkD,IAAI,IAAIyG,QAAQ,KAAK3J,aAAa,CAAC4C,IAAI,EAAE;UAChF0E,6BAA6B,CAACjI,QAAQ,CAAC;YACrCyC,EAAE,EAAE8H,UAAU;YACd7H;UACF,CAAC,EAAEC,MAAM,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrB,MAAM,EAAEE,cAAc,EAAEyF,8BAA8B,EAAEgB,6BAA6B,CAAC,CAAC;AAC7F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}