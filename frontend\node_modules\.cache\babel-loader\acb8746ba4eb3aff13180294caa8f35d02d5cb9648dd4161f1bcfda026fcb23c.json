{"ast": null, "code": "import { useGridLogger } from \"./useGridLogger.js\";\nimport { useGridEventPriority } from \"./useGridEvent.js\";\nexport const useGridNativeEventListener = (apiRef, ref, eventName, handler, options) => {\n  const logger = useGridLogger(apiRef, 'useNativeEventListener');\n  useGridEventPriority(apiRef, 'rootMount', () => {\n    const targetElement = ref();\n    if (!targetElement || !eventName) {\n      return undefined;\n    }\n    logger.debug(`Binding native ${eventName} event`);\n    targetElement.addEventListener(eventName, handler, options);\n    return () => {\n      logger.debug(`Clearing native ${eventName} event`);\n      targetElement.removeEventListener(eventName, handler, options);\n    };\n  });\n};", "map": {"version": 3, "names": ["useGridLogger", "useGridEventPriority", "useGridNativeEventListener", "apiRef", "ref", "eventName", "handler", "options", "logger", "targetElement", "undefined", "debug", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridNativeEventListener.js"], "sourcesContent": ["import { useGridLogger } from \"./useGridLogger.js\";\nimport { useGridEventPriority } from \"./useGridEvent.js\";\nexport const useGridNativeEventListener = (apiRef, ref, eventName, handler, options) => {\n  const logger = useGridLogger(apiRef, 'useNativeEventListener');\n  useGridEventPriority(apiRef, 'rootMount', () => {\n    const targetElement = ref();\n    if (!targetElement || !eventName) {\n      return undefined;\n    }\n    logger.debug(`Binding native ${eventName} event`);\n    targetElement.addEventListener(eventName, handler, options);\n    return () => {\n      logger.debug(`Clearing native ${eventName} event`);\n      targetElement.removeEventListener(eventName, handler, options);\n    };\n  });\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,KAAK;EACtF,MAAMC,MAAM,GAAGR,aAAa,CAACG,MAAM,EAAE,wBAAwB,CAAC;EAC9DF,oBAAoB,CAACE,MAAM,EAAE,WAAW,EAAE,MAAM;IAC9C,MAAMM,aAAa,GAAGL,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACK,aAAa,IAAI,CAACJ,SAAS,EAAE;MAChC,OAAOK,SAAS;IAClB;IACAF,MAAM,CAACG,KAAK,CAAC,kBAAkBN,SAAS,QAAQ,CAAC;IACjDI,aAAa,CAACG,gBAAgB,CAACP,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;IAC3D,OAAO,MAAM;MACXC,MAAM,CAACG,KAAK,CAAC,mBAAmBN,SAAS,QAAQ,CAAC;MAClDI,aAAa,CAACI,mBAAmB,CAACR,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;IAChE,CAAC;EACH,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}