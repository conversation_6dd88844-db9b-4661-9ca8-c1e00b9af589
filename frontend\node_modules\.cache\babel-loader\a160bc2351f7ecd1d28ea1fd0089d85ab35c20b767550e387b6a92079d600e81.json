{"ast": null, "code": "function identity(x) {\n  return x;\n}\nexport { identity };", "map": {"version": 3, "names": ["identity", "x"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/identity.mjs"], "sourcesContent": ["function identity(x) {\n    return x;\n}\n\nexport { identity };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,CAAC,EAAE;EACjB,OAAOA,CAAC;AACZ;AAEA,SAASD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}