{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"id\", \"formattedValue\", \"row\", \"rowNode\", \"colDef\", \"isEditable\", \"cellMode\", \"hasFocus\", \"tabIndex\", \"api\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { checkboxPropsSelector } from \"../../hooks/features/rowSelection/utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['checkboxInput']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridCellCheckboxForwardRef = forwardRef(function GridCellCheckboxRenderer(props, ref) {\n  const {\n      field,\n      id,\n      rowNode,\n      tabIndex\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleChange = event => {\n    const params = {\n      value: event.target.checked,\n      id\n    };\n    apiRef.current.publishEvent('rowSelectionCheckboxChange', params, event);\n  };\n  React.useLayoutEffect(() => {\n    if (tabIndex === 0) {\n      const element = apiRef.current.getCellElement(id, field);\n      if (element) {\n        element.tabIndex = -1;\n      }\n    }\n  }, [apiRef, tabIndex, id, field]);\n  const handleKeyDown = React.useCallback(event => {\n    if (event.key === ' ') {\n      // We call event.stopPropagation to avoid selecting the row and also scrolling to bottom\n      // TODO: Remove and add a check inside useGridKeyboardNavigation\n      event.stopPropagation();\n    }\n  }, []);\n  const isSelectable = apiRef.current.isRowSelectable(id);\n  const {\n    isIndeterminate,\n    isChecked\n  } = useGridSelector(apiRef, checkboxPropsSelector, {\n    groupId: id,\n    autoSelectParents: rootProps.rowSelectionPropagation?.parents ?? false\n  });\n  if (rowNode.type === 'footer' || rowNode.type === 'pinnedRow') {\n    return null;\n  }\n  const label = apiRef.current.getLocaleText(isChecked && !isIndeterminate ? 'checkboxSelectionUnselectRow' : 'checkboxSelectionSelectRow');\n  return /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n    tabIndex: tabIndex,\n    checked: isChecked && !isIndeterminate,\n    onChange: handleChange,\n    className: classes.root,\n    slotProps: {\n      htmlInput: {\n        'aria-label': label,\n        name: 'select_row'\n      }\n    },\n    onKeyDown: handleKeyDown,\n    indeterminate: isIndeterminate,\n    disabled: !isSelectable\n  }, rootProps.slotProps?.baseCheckbox, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridCellCheckboxForwardRef.displayName = \"GridCellCheckboxForwardRef\";\nprocess.env.NODE_ENV !== \"production\" ? GridCellCheckboxForwardRef.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridCellCheckboxForwardRef };\nexport const GridCellCheckboxRenderer = GridCellCheckboxForwardRef;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "composeClasses", "forwardRef", "useGridApiContext", "useGridRootProps", "getDataGridUtilityClass", "useGridSelector", "checkboxPropsSelector", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "GridCellCheckboxForwardRef", "GridCellCheckboxRenderer", "props", "ref", "field", "id", "rowNode", "tabIndex", "other", "apiRef", "rootProps", "handleChange", "event", "params", "value", "target", "checked", "current", "publishEvent", "useLayoutEffect", "element", "getCellElement", "handleKeyDown", "useCallback", "key", "stopPropagation", "isSelectable", "isRowSelectable", "isIndeterminate", "isChecked", "groupId", "autoSelectParents", "rowSelectionPropagation", "parents", "type", "label", "getLocaleText", "baseCheckbox", "onChange", "className", "slotProps", "htmlInput", "name", "onKeyDown", "indeterminate", "disabled", "process", "env", "NODE_ENV", "displayName", "propTypes", "api", "object", "isRequired", "cellMode", "oneOf", "colDef", "string", "focusElementRef", "oneOfType", "func", "shape", "focus", "formattedValue", "any", "hasFocus", "bool", "number", "isEditable", "row"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/columnSelection/GridCellCheckboxRenderer.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"id\", \"formattedValue\", \"row\", \"rowNode\", \"colDef\", \"isEditable\", \"cellMode\", \"hasFocus\", \"tabIndex\", \"api\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { checkboxPropsSelector } from \"../../hooks/features/rowSelection/utils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['checkboxInput']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridCellCheckboxForwardRef = forwardRef(function GridCellCheckboxRenderer(props, ref) {\n  const {\n      field,\n      id,\n      rowNode,\n      tabIndex\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleChange = event => {\n    const params = {\n      value: event.target.checked,\n      id\n    };\n    apiRef.current.publishEvent('rowSelectionCheckboxChange', params, event);\n  };\n  React.useLayoutEffect(() => {\n    if (tabIndex === 0) {\n      const element = apiRef.current.getCellElement(id, field);\n      if (element) {\n        element.tabIndex = -1;\n      }\n    }\n  }, [apiRef, tabIndex, id, field]);\n  const handleKeyDown = React.useCallback(event => {\n    if (event.key === ' ') {\n      // We call event.stopPropagation to avoid selecting the row and also scrolling to bottom\n      // TODO: Remove and add a check inside useGridKeyboardNavigation\n      event.stopPropagation();\n    }\n  }, []);\n  const isSelectable = apiRef.current.isRowSelectable(id);\n  const {\n    isIndeterminate,\n    isChecked\n  } = useGridSelector(apiRef, checkboxPropsSelector, {\n    groupId: id,\n    autoSelectParents: rootProps.rowSelectionPropagation?.parents ?? false\n  });\n  if (rowNode.type === 'footer' || rowNode.type === 'pinnedRow') {\n    return null;\n  }\n  const label = apiRef.current.getLocaleText(isChecked && !isIndeterminate ? 'checkboxSelectionUnselectRow' : 'checkboxSelectionSelectRow');\n  return /*#__PURE__*/_jsx(rootProps.slots.baseCheckbox, _extends({\n    tabIndex: tabIndex,\n    checked: isChecked && !isIndeterminate,\n    onChange: handleChange,\n    className: classes.root,\n    slotProps: {\n      htmlInput: {\n        'aria-label': label,\n        name: 'select_row'\n      }\n    },\n    onKeyDown: handleKeyDown,\n    indeterminate: isIndeterminate,\n    disabled: !isSelectable\n  }, rootProps.slotProps?.baseCheckbox, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridCellCheckboxForwardRef.displayName = \"GridCellCheckboxForwardRef\";\nprocess.env.NODE_ENV !== \"production\" ? GridCellCheckboxForwardRef.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * GridApi that let you manipulate the grid.\n   */\n  api: PropTypes.object.isRequired,\n  /**\n   * The mode of the cell.\n   */\n  cellMode: PropTypes.oneOf(['edit', 'view']).isRequired,\n  /**\n   * The column of the row that the current cell belongs to.\n   */\n  colDef: PropTypes.object.isRequired,\n  /**\n   * The column field of the cell that triggered the event.\n   */\n  field: PropTypes.string.isRequired,\n  /**\n   * A ref allowing to set imperative focus.\n   * It can be passed to the element that should receive focus.\n   * @ignore - do not document.\n   */\n  focusElementRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focus: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * The cell value formatted with the column valueFormatter.\n   */\n  formattedValue: PropTypes.any,\n  /**\n   * If true, the cell is the active element.\n   */\n  hasFocus: PropTypes.bool.isRequired,\n  /**\n   * The grid row id.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * If true, the cell is editable.\n   */\n  isEditable: PropTypes.bool,\n  /**\n   * The row model of the row that the current cell belongs to.\n   */\n  row: PropTypes.any.isRequired,\n  /**\n   * The node of the row that the current cell belongs to.\n   */\n  rowNode: PropTypes.object.isRequired,\n  /**\n   * the tabIndex value.\n   */\n  tabIndex: PropTypes.oneOf([-1, 0]).isRequired,\n  /**\n   * The cell value.\n   * If the column has `valueGetter`, use `params.row` to directly access the fields.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { GridCellCheckboxForwardRef };\nexport const GridCellCheckboxRenderer = GridCellCheckboxForwardRef;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC;AACxI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,eAAe;EACxB,CAAC;EACD,OAAOb,cAAc,CAACY,KAAK,EAAER,uBAAuB,EAAEO,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,0BAA0B,GAAGb,UAAU,CAAC,SAASc,wBAAwBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC1F,MAAM;MACFC,KAAK;MACLC,EAAE;MACFC,OAAO;MACPC;IACF,CAAC,GAAGL,KAAK;IACTM,KAAK,GAAG1B,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAM0B,MAAM,GAAGrB,iBAAiB,CAAC,CAAC;EAClC,MAAMsB,SAAS,GAAGrB,gBAAgB,CAAC,CAAC;EACpC,MAAMO,UAAU,GAAG;IACjBC,OAAO,EAAEa,SAAS,CAACb;EACrB,CAAC;EACD,MAAMA,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMe,YAAY,GAAGC,KAAK,IAAI;IAC5B,MAAMC,MAAM,GAAG;MACbC,KAAK,EAAEF,KAAK,CAACG,MAAM,CAACC,OAAO;MAC3BX;IACF,CAAC;IACDI,MAAM,CAACQ,OAAO,CAACC,YAAY,CAAC,4BAA4B,EAAEL,MAAM,EAAED,KAAK,CAAC;EAC1E,CAAC;EACD5B,KAAK,CAACmC,eAAe,CAAC,MAAM;IAC1B,IAAIZ,QAAQ,KAAK,CAAC,EAAE;MAClB,MAAMa,OAAO,GAAGX,MAAM,CAACQ,OAAO,CAACI,cAAc,CAAChB,EAAE,EAAED,KAAK,CAAC;MACxD,IAAIgB,OAAO,EAAE;QACXA,OAAO,CAACb,QAAQ,GAAG,CAAC,CAAC;MACvB;IACF;EACF,CAAC,EAAE,CAACE,MAAM,EAAEF,QAAQ,EAAEF,EAAE,EAAED,KAAK,CAAC,CAAC;EACjC,MAAMkB,aAAa,GAAGtC,KAAK,CAACuC,WAAW,CAACX,KAAK,IAAI;IAC/C,IAAIA,KAAK,CAACY,GAAG,KAAK,GAAG,EAAE;MACrB;MACA;MACAZ,KAAK,CAACa,eAAe,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,YAAY,GAAGjB,MAAM,CAACQ,OAAO,CAACU,eAAe,CAACtB,EAAE,CAAC;EACvD,MAAM;IACJuB,eAAe;IACfC;EACF,CAAC,GAAGtC,eAAe,CAACkB,MAAM,EAAEjB,qBAAqB,EAAE;IACjDsC,OAAO,EAAEzB,EAAE;IACX0B,iBAAiB,EAAErB,SAAS,CAACsB,uBAAuB,EAAEC,OAAO,IAAI;EACnE,CAAC,CAAC;EACF,IAAI3B,OAAO,CAAC4B,IAAI,KAAK,QAAQ,IAAI5B,OAAO,CAAC4B,IAAI,KAAK,WAAW,EAAE;IAC7D,OAAO,IAAI;EACb;EACA,MAAMC,KAAK,GAAG1B,MAAM,CAACQ,OAAO,CAACmB,aAAa,CAACP,SAAS,IAAI,CAACD,eAAe,GAAG,8BAA8B,GAAG,4BAA4B,CAAC;EACzI,OAAO,aAAalC,IAAI,CAACgB,SAAS,CAACZ,KAAK,CAACuC,YAAY,EAAExD,QAAQ,CAAC;IAC9D0B,QAAQ,EAAEA,QAAQ;IAClBS,OAAO,EAAEa,SAAS,IAAI,CAACD,eAAe;IACtCU,QAAQ,EAAE3B,YAAY;IACtB4B,SAAS,EAAE1C,OAAO,CAACE,IAAI;IACvByC,SAAS,EAAE;MACTC,SAAS,EAAE;QACT,YAAY,EAAEN,KAAK;QACnBO,IAAI,EAAE;MACR;IACF,CAAC;IACDC,SAAS,EAAErB,aAAa;IACxBsB,aAAa,EAAEhB,eAAe;IAC9BiB,QAAQ,EAAE,CAACnB;EACb,CAAC,EAAEhB,SAAS,CAAC8B,SAAS,EAAEH,YAAY,EAAE7B,KAAK,EAAE;IAC3CL,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAI2C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEhD,0BAA0B,CAACiD,WAAW,GAAG,4BAA4B;AAChHH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhD,0BAA0B,CAACkD,SAAS,GAAG;EAC7E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,GAAG,EAAElE,SAAS,CAACmE,MAAM,CAACC,UAAU;EAChC;AACF;AACA;EACEC,QAAQ,EAAErE,SAAS,CAACsE,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAACF,UAAU;EACtD;AACF;AACA;EACEG,MAAM,EAAEvE,SAAS,CAACmE,MAAM,CAACC,UAAU;EACnC;AACF;AACA;EACEjD,KAAK,EAAEnB,SAAS,CAACwE,MAAM,CAACJ,UAAU;EAClC;AACF;AACA;AACA;AACA;EACEK,eAAe,EAAEzE,SAAS,CAAC0E,SAAS,CAAC,CAAC1E,SAAS,CAAC2E,IAAI,EAAE3E,SAAS,CAAC4E,KAAK,CAAC;IACpE5C,OAAO,EAAEhC,SAAS,CAAC4E,KAAK,CAAC;MACvBC,KAAK,EAAE7E,SAAS,CAAC2E,IAAI,CAACP;IACxB,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;EACEU,cAAc,EAAE9E,SAAS,CAAC+E,GAAG;EAC7B;AACF;AACA;EACEC,QAAQ,EAAEhF,SAAS,CAACiF,IAAI,CAACb,UAAU;EACnC;AACF;AACA;EACEhD,EAAE,EAAEpB,SAAS,CAAC0E,SAAS,CAAC,CAAC1E,SAAS,CAACkF,MAAM,EAAElF,SAAS,CAACwE,MAAM,CAAC,CAAC,CAACJ,UAAU;EACxE;AACF;AACA;EACEe,UAAU,EAAEnF,SAAS,CAACiF,IAAI;EAC1B;AACF;AACA;EACEG,GAAG,EAAEpF,SAAS,CAAC+E,GAAG,CAACX,UAAU;EAC7B;AACF;AACA;EACE/C,OAAO,EAAErB,SAAS,CAACmE,MAAM,CAACC,UAAU;EACpC;AACF;AACA;EACE9C,QAAQ,EAAEtB,SAAS,CAACsE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACF,UAAU;EAC7C;AACF;AACA;AACA;EACEvC,KAAK,EAAE7B,SAAS,CAAC+E;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,SAAShE,0BAA0B;AACnC,OAAO,MAAMC,wBAAwB,GAAGD,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}