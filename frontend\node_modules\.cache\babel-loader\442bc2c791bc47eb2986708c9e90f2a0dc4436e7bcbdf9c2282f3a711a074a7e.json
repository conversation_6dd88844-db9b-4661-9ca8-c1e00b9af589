{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { EMPTY_RENDER_CONTEXT } from '@mui/x-virtualizer';\nimport { isJSDOM } from \"../../../utils/isJSDOM.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridEventPriority } from \"../../utils/index.js\";\nconst HAS_LAYOUT = !isJSDOM;\n\n// HACK: Typescript doesn't use the alias name (\"GridVirtualizationState\") and changes\n// the autogenerated docs result, but this would otherwise be just:\n//   export type GridVirtualizationState = VirtualizationState;\n\n// XXX: We want to use the virtualizer as the source of truth for its state, but this needs to\n// stay because some parts of the grid require the `virtualization` state during initialization.\nexport const virtualizationStateInitializer = (state, props) => {\n  const {\n    disableVirtualization,\n    autoHeight\n  } = props;\n  const virtualization = {\n    enabled: !disableVirtualization && HAS_LAYOUT,\n    enabledForColumns: !disableVirtualization && HAS_LAYOUT,\n    enabledForRows: !disableVirtualization && !autoHeight && HAS_LAYOUT,\n    renderContext: EMPTY_RENDER_CONTEXT\n  };\n  return _extends({}, state, {\n    virtualization\n  });\n};\nexport function useGridVirtualization(apiRef, rootProps) {\n  const {\n    virtualizer\n  } = apiRef.current;\n  const {\n    autoHeight,\n    disableVirtualization\n  } = rootProps;\n\n  /*\n   * API METHODS\n   */\n\n  const setVirtualization = enabled => {\n    enabled &&= HAS_LAYOUT;\n    virtualizer.store.set('virtualization', _extends({}, virtualizer.store.state.virtualization, {\n      enabled,\n      enabledForColumns: enabled,\n      enabledForRows: enabled && !autoHeight\n    }));\n  };\n  const setColumnVirtualization = enabled => {\n    enabled &&= HAS_LAYOUT;\n    virtualizer.store.set('virtualization', _extends({}, virtualizer.store.state.virtualization, {\n      enabledForColumns: enabled\n    }));\n  };\n  const api = {\n    unstable_setVirtualization: setVirtualization,\n    unstable_setColumnVirtualization: setColumnVirtualization\n  };\n  useGridApiMethod(apiRef, api, 'public');\n  const forceUpdateRenderContext = virtualizer.api.forceUpdateRenderContext;\n  apiRef.current.register('private', {\n    updateRenderContext: forceUpdateRenderContext\n  });\n\n  /*\n   * EFFECTS\n   */\n\n  useGridEventPriority(apiRef, 'sortedRowsSet', forceUpdateRenderContext);\n  useGridEventPriority(apiRef, 'paginationModelChange', forceUpdateRenderContext);\n  useGridEventPriority(apiRef, 'columnsChange', forceUpdateRenderContext);\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(() => {\n    setVirtualization(!rootProps.disableVirtualization);\n  }, [disableVirtualization, autoHeight]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}", "map": {"version": 3, "names": ["_extends", "React", "EMPTY_RENDER_CONTEXT", "isJSDOM", "useGridApiMethod", "useGridEventPriority", "HAS_LAYOUT", "virtualizationStateInitializer", "state", "props", "disableVirtualization", "autoHeight", "virtualization", "enabled", "enabledForColumns", "enabledForRows", "renderContext", "useGridVirtualization", "apiRef", "rootProps", "virtualizer", "current", "setVirtualization", "store", "set", "setColumnVirtualization", "api", "unstable_setVirtualization", "unstable_setColumnVirtualization", "forceUpdateRenderContext", "register", "updateRenderContext", "useEffect"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/virtualization/useGridVirtualization.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { EMPTY_RENDER_CONTEXT } from '@mui/x-virtualizer';\nimport { isJSDOM } from \"../../../utils/isJSDOM.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridEventPriority } from \"../../utils/index.js\";\nconst HAS_LAYOUT = !isJSDOM;\n\n// HACK: Typescript doesn't use the alias name (\"GridVirtualizationState\") and changes\n// the autogenerated docs result, but this would otherwise be just:\n//   export type GridVirtualizationState = VirtualizationState;\n\n// XXX: We want to use the virtualizer as the source of truth for its state, but this needs to\n// stay because some parts of the grid require the `virtualization` state during initialization.\nexport const virtualizationStateInitializer = (state, props) => {\n  const {\n    disableVirtualization,\n    autoHeight\n  } = props;\n  const virtualization = {\n    enabled: !disableVirtualization && HAS_LAYOUT,\n    enabledForColumns: !disableVirtualization && HAS_LAYOUT,\n    enabledForRows: !disableVirtualization && !autoHeight && HAS_LAYOUT,\n    renderContext: EMPTY_RENDER_CONTEXT\n  };\n  return _extends({}, state, {\n    virtualization\n  });\n};\nexport function useGridVirtualization(apiRef, rootProps) {\n  const {\n    virtualizer\n  } = apiRef.current;\n  const {\n    autoHeight,\n    disableVirtualization\n  } = rootProps;\n\n  /*\n   * API METHODS\n   */\n\n  const setVirtualization = enabled => {\n    enabled &&= HAS_LAYOUT;\n    virtualizer.store.set('virtualization', _extends({}, virtualizer.store.state.virtualization, {\n      enabled,\n      enabledForColumns: enabled,\n      enabledForRows: enabled && !autoHeight\n    }));\n  };\n  const setColumnVirtualization = enabled => {\n    enabled &&= HAS_LAYOUT;\n    virtualizer.store.set('virtualization', _extends({}, virtualizer.store.state.virtualization, {\n      enabledForColumns: enabled\n    }));\n  };\n  const api = {\n    unstable_setVirtualization: setVirtualization,\n    unstable_setColumnVirtualization: setColumnVirtualization\n  };\n  useGridApiMethod(apiRef, api, 'public');\n  const forceUpdateRenderContext = virtualizer.api.forceUpdateRenderContext;\n  apiRef.current.register('private', {\n    updateRenderContext: forceUpdateRenderContext\n  });\n\n  /*\n   * EFFECTS\n   */\n\n  useGridEventPriority(apiRef, 'sortedRowsSet', forceUpdateRenderContext);\n  useGridEventPriority(apiRef, 'paginationModelChange', forceUpdateRenderContext);\n  useGridEventPriority(apiRef, 'columnsChange', forceUpdateRenderContext);\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(() => {\n    setVirtualization(!rootProps.disableVirtualization);\n  }, [disableVirtualization, autoHeight]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,QAAQ,oBAAoB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,MAAMC,UAAU,GAAG,CAACH,OAAO;;AAE3B;AACA;AACA;;AAEA;AACA;AACA,OAAO,MAAMI,8BAA8B,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC9D,MAAM;IACJC,qBAAqB;IACrBC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,cAAc,GAAG;IACrBC,OAAO,EAAE,CAACH,qBAAqB,IAAIJ,UAAU;IAC7CQ,iBAAiB,EAAE,CAACJ,qBAAqB,IAAIJ,UAAU;IACvDS,cAAc,EAAE,CAACL,qBAAqB,IAAI,CAACC,UAAU,IAAIL,UAAU;IACnEU,aAAa,EAAEd;EACjB,CAAC;EACD,OAAOF,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IACzBI;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,SAASK,qBAAqBA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACvD,MAAM;IACJC;EACF,CAAC,GAAGF,MAAM,CAACG,OAAO;EAClB,MAAM;IACJV,UAAU;IACVD;EACF,CAAC,GAAGS,SAAS;;EAEb;AACF;AACA;;EAEE,MAAMG,iBAAiB,GAAGT,OAAO,IAAI;IACnCA,OAAO,KAAKP,UAAU;IACtBc,WAAW,CAACG,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAExB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,WAAW,CAACG,KAAK,CAACf,KAAK,CAACI,cAAc,EAAE;MAC3FC,OAAO;MACPC,iBAAiB,EAAED,OAAO;MAC1BE,cAAc,EAAEF,OAAO,IAAI,CAACF;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMc,uBAAuB,GAAGZ,OAAO,IAAI;IACzCA,OAAO,KAAKP,UAAU;IACtBc,WAAW,CAACG,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAExB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,WAAW,CAACG,KAAK,CAACf,KAAK,CAACI,cAAc,EAAE;MAC3FE,iBAAiB,EAAED;IACrB,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMa,GAAG,GAAG;IACVC,0BAA0B,EAAEL,iBAAiB;IAC7CM,gCAAgC,EAAEH;EACpC,CAAC;EACDrB,gBAAgB,CAACc,MAAM,EAAEQ,GAAG,EAAE,QAAQ,CAAC;EACvC,MAAMG,wBAAwB,GAAGT,WAAW,CAACM,GAAG,CAACG,wBAAwB;EACzEX,MAAM,CAACG,OAAO,CAACS,QAAQ,CAAC,SAAS,EAAE;IACjCC,mBAAmB,EAAEF;EACvB,CAAC,CAAC;;EAEF;AACF;AACA;;EAEExB,oBAAoB,CAACa,MAAM,EAAE,eAAe,EAAEW,wBAAwB,CAAC;EACvExB,oBAAoB,CAACa,MAAM,EAAE,uBAAuB,EAAEW,wBAAwB,CAAC;EAC/ExB,oBAAoB,CAACa,MAAM,EAAE,eAAe,EAAEW,wBAAwB,CAAC;;EAEvE;EACA5B,KAAK,CAAC+B,SAAS,CAAC,MAAM;IACpBV,iBAAiB,CAAC,CAACH,SAAS,CAACT,qBAAqB,CAAC;EACrD,CAAC,EAAE,CAACA,qBAAqB,EAAEC,UAAU,CAAC,CAAC;EACvC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}