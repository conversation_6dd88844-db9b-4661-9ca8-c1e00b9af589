{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"classes\", \"onClose\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { useCSSVariablesClass } from \"../../utils/css/context.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const gridPanelClasses = generateUtilityClasses('MuiDataGrid', ['panel', 'paper']);\nconst GridPanelRoot = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'panel'\n})({\n  zIndex: vars.zIndex.panel\n});\nconst GridPanelContent = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'panelContent'\n})({\n  backgroundColor: vars.colors.background.overlay,\n  borderRadius: vars.radius.base,\n  boxShadow: vars.shadows.overlay,\n  display: 'flex',\n  maxWidth: `calc(100vw - ${vars.spacing(2)})`,\n  overflow: 'auto'\n});\nconst GridPanel = forwardRef((props, ref) => {\n  const {\n      children,\n      className,\n      onClose\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = gridPanelClasses;\n  const [isPlaced, setIsPlaced] = React.useState(false);\n  const variablesClass = useCSSVariablesClass();\n  const onDidShow = useEventCallback(() => setIsPlaced(true));\n  const onDidHide = useEventCallback(() => setIsPlaced(false));\n  const handleClickAway = useEventCallback(() => {\n    onClose?.();\n  });\n  const handleKeyDown = useEventCallback(event => {\n    if (event.key === 'Escape') {\n      onClose?.();\n    }\n  });\n  const [fallbackTarget, setFallbackTarget] = React.useState(null);\n  React.useEffect(() => {\n    const panelAnchor = apiRef.current.rootElementRef?.current?.querySelector('[data-id=\"gridPanelAnchor\"]');\n    if (panelAnchor) {\n      setFallbackTarget(panelAnchor);\n    }\n  }, [apiRef]);\n  if (!fallbackTarget) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridPanelRoot, _extends({\n    as: rootProps.slots.basePopper,\n    ownerState: rootProps,\n    placement: \"bottom-end\",\n    className: clsx(classes.panel, className, variablesClass),\n    flip: true,\n    onDidShow: onDidShow,\n    onDidHide: onDidHide,\n    onClickAway: handleClickAway,\n    clickAwayMouseEvent: \"onPointerUp\",\n    clickAwayTouchEvent: false,\n    focusTrap: true\n  }, other, rootProps.slotProps?.basePopper, {\n    target: props.target ?? fallbackTarget,\n    ref: ref,\n    children: /*#__PURE__*/_jsx(GridPanelContent, {\n      className: classes.paper,\n      ownerState: rootProps,\n      onKeyDown: handleKeyDown,\n      children: isPlaced && children\n    })\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridPanel.displayName = \"GridPanel\";\nprocess.env.NODE_ENV !== \"production\" ? GridPanel.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  flip: PropTypes.bool,\n  id: PropTypes.string,\n  onClose: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  target: PropTypes /* @typescript-to-proptypes-ignore */.any\n} : void 0;\nexport { GridPanel };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "styled", "generateUtilityClasses", "useEventCallback", "forwardRef", "vars", "useCSSVariablesClass", "useGridApiContext", "useGridRootProps", "NotRendered", "jsx", "_jsx", "gridPanelClasses", "GridPanelRoot", "name", "slot", "zIndex", "panel", "GridPanelContent", "backgroundColor", "colors", "background", "overlay", "borderRadius", "radius", "base", "boxShadow", "shadows", "display", "max<PERSON><PERSON><PERSON>", "spacing", "overflow", "GridPanel", "props", "ref", "children", "className", "onClose", "other", "apiRef", "rootProps", "classes", "isPlaced", "setIsPlaced", "useState", "variablesClass", "onDidShow", "onDidHide", "handleClickAway", "handleKeyDown", "event", "key", "fallback<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "panelAnchor", "current", "rootElementRef", "querySelector", "as", "slots", "basePopper", "ownerState", "placement", "flip", "onClickAway", "clickAwayMouseEvent", "clickAwayTouchEvent", "focusTrap", "slotProps", "target", "paper", "onKeyDown", "process", "env", "NODE_ENV", "displayName", "propTypes", "node", "object", "string", "bool", "id", "func", "open", "isRequired", "any"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/panel/GridPanel.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"classes\", \"onClose\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from '@mui/material/styles';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { useCSSVariablesClass } from \"../../utils/css/context.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const gridPanelClasses = generateUtilityClasses('MuiDataGrid', ['panel', 'paper']);\nconst GridPanelRoot = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'panel'\n})({\n  zIndex: vars.zIndex.panel\n});\nconst GridPanelContent = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'panelContent'\n})({\n  backgroundColor: vars.colors.background.overlay,\n  borderRadius: vars.radius.base,\n  boxShadow: vars.shadows.overlay,\n  display: 'flex',\n  maxWidth: `calc(100vw - ${vars.spacing(2)})`,\n  overflow: 'auto'\n});\nconst GridPanel = forwardRef((props, ref) => {\n  const {\n      children,\n      className,\n      onClose\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = gridPanelClasses;\n  const [isPlaced, setIsPlaced] = React.useState(false);\n  const variablesClass = useCSSVariablesClass();\n  const onDidShow = useEventCallback(() => setIsPlaced(true));\n  const onDidHide = useEventCallback(() => setIsPlaced(false));\n  const handleClickAway = useEventCallback(() => {\n    onClose?.();\n  });\n  const handleKeyDown = useEventCallback(event => {\n    if (event.key === 'Escape') {\n      onClose?.();\n    }\n  });\n  const [fallbackTarget, setFallbackTarget] = React.useState(null);\n  React.useEffect(() => {\n    const panelAnchor = apiRef.current.rootElementRef?.current?.querySelector('[data-id=\"gridPanelAnchor\"]');\n    if (panelAnchor) {\n      setFallbackTarget(panelAnchor);\n    }\n  }, [apiRef]);\n  if (!fallbackTarget) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridPanelRoot, _extends({\n    as: rootProps.slots.basePopper,\n    ownerState: rootProps,\n    placement: \"bottom-end\",\n    className: clsx(classes.panel, className, variablesClass),\n    flip: true,\n    onDidShow: onDidShow,\n    onDidHide: onDidHide,\n    onClickAway: handleClickAway,\n    clickAwayMouseEvent: \"onPointerUp\",\n    clickAwayTouchEvent: false,\n    focusTrap: true\n  }, other, rootProps.slotProps?.basePopper, {\n    target: props.target ?? fallbackTarget,\n    ref: ref,\n    children: /*#__PURE__*/_jsx(GridPanelContent, {\n      className: classes.paper,\n      ownerState: rootProps,\n      onKeyDown: handleKeyDown,\n      children: isPlaced && children\n    })\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridPanel.displayName = \"GridPanel\";\nprocess.env.NODE_ENV !== \"production\" ? GridPanel.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  flip: PropTypes.bool,\n  id: PropTypes.string,\n  onClose: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  target: PropTypes /* @typescript-to-proptypes-ignore */.any\n} : void 0;\nexport { GridPanel };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,IAAI,QAAQ,iCAAiC;AACtD,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,gBAAgB,GAAGV,sBAAsB,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACzF,MAAMW,aAAa,GAAGZ,MAAM,CAACQ,WAAW,EAAE;EACxCK,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,MAAM,EAAEX,IAAI,CAACW,MAAM,CAACC;AACtB,CAAC,CAAC;AACF,MAAMC,gBAAgB,GAAGjB,MAAM,CAAC,KAAK,EAAE;EACrCa,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDI,eAAe,EAAEd,IAAI,CAACe,MAAM,CAACC,UAAU,CAACC,OAAO;EAC/CC,YAAY,EAAElB,IAAI,CAACmB,MAAM,CAACC,IAAI;EAC9BC,SAAS,EAAErB,IAAI,CAACsB,OAAO,CAACL,OAAO;EAC/BM,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,gBAAgBxB,IAAI,CAACyB,OAAO,CAAC,CAAC,CAAC,GAAG;EAC5CC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMC,SAAS,GAAG5B,UAAU,CAAC,CAAC6B,KAAK,EAAEC,GAAG,KAAK;EAC3C,MAAM;MACFC,QAAQ;MACRC,SAAS;MACTC;IACF,CAAC,GAAGJ,KAAK;IACTK,KAAK,GAAG1C,6BAA6B,CAACqC,KAAK,EAAEpC,SAAS,CAAC;EACzD,MAAM0C,MAAM,GAAGhC,iBAAiB,CAAC,CAAC;EAClC,MAAMiC,SAAS,GAAGhC,gBAAgB,CAAC,CAAC;EACpC,MAAMiC,OAAO,GAAG7B,gBAAgB;EAChC,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,KAAK,CAAC8C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMC,cAAc,GAAGvC,oBAAoB,CAAC,CAAC;EAC7C,MAAMwC,SAAS,GAAG3C,gBAAgB,CAAC,MAAMwC,WAAW,CAAC,IAAI,CAAC,CAAC;EAC3D,MAAMI,SAAS,GAAG5C,gBAAgB,CAAC,MAAMwC,WAAW,CAAC,KAAK,CAAC,CAAC;EAC5D,MAAMK,eAAe,GAAG7C,gBAAgB,CAAC,MAAM;IAC7CkC,OAAO,GAAG,CAAC;EACb,CAAC,CAAC;EACF,MAAMY,aAAa,GAAG9C,gBAAgB,CAAC+C,KAAK,IAAI;IAC9C,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC1Bd,OAAO,GAAG,CAAC;IACb;EACF,CAAC,CAAC;EACF,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,KAAK,CAAC8C,QAAQ,CAAC,IAAI,CAAC;EAChE9C,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAGhB,MAAM,CAACiB,OAAO,CAACC,cAAc,EAAED,OAAO,EAAEE,aAAa,CAAC,6BAA6B,CAAC;IACxG,IAAIH,WAAW,EAAE;MACfF,iBAAiB,CAACE,WAAW,CAAC;IAChC;EACF,CAAC,EAAE,CAAChB,MAAM,CAAC,CAAC;EACZ,IAAI,CAACa,cAAc,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,aAAazC,IAAI,CAACE,aAAa,EAAElB,QAAQ,CAAC;IAC/CgE,EAAE,EAAEnB,SAAS,CAACoB,KAAK,CAACC,UAAU;IAC9BC,UAAU,EAAEtB,SAAS;IACrBuB,SAAS,EAAE,YAAY;IACvB3B,SAAS,EAAEpC,IAAI,CAACyC,OAAO,CAACxB,KAAK,EAAEmB,SAAS,EAAES,cAAc,CAAC;IACzDmB,IAAI,EAAE,IAAI;IACVlB,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA,SAAS;IACpBkB,WAAW,EAAEjB,eAAe;IAC5BkB,mBAAmB,EAAE,aAAa;IAClCC,mBAAmB,EAAE,KAAK;IAC1BC,SAAS,EAAE;EACb,CAAC,EAAE9B,KAAK,EAAEE,SAAS,CAAC6B,SAAS,EAAER,UAAU,EAAE;IACzCS,MAAM,EAAErC,KAAK,CAACqC,MAAM,IAAIlB,cAAc;IACtClB,GAAG,EAAEA,GAAG;IACRC,QAAQ,EAAE,aAAaxB,IAAI,CAACO,gBAAgB,EAAE;MAC5CkB,SAAS,EAAEK,OAAO,CAAC8B,KAAK;MACxBT,UAAU,EAAEtB,SAAS;MACrBgC,SAAS,EAAEvB,aAAa;MACxBd,QAAQ,EAAEO,QAAQ,IAAIP;IACxB,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIsC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE3C,SAAS,CAAC4C,WAAW,GAAG,WAAW;AAC9EH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3C,SAAS,CAAC6C,SAAS,GAAG;EAC5D;EACA;EACA;EACA;EACA1C,QAAQ,EAAEpC,SAAS,CAAC+E,IAAI;EACxB;AACF;AACA;EACErC,OAAO,EAAE1C,SAAS,CAACgF,MAAM;EACzB3C,SAAS,EAAErC,SAAS,CAACiF,MAAM;EAC3BhB,IAAI,EAAEjE,SAAS,CAACkF,IAAI;EACpBC,EAAE,EAAEnF,SAAS,CAACiF,MAAM;EACpB3C,OAAO,EAAEtC,SAAS,CAACoF,IAAI;EACvBC,IAAI,EAAErF,SAAS,CAACkF,IAAI,CAACI,UAAU;EAC/Bf,MAAM,EAAEvE,SAAS,CAAC,sCAAsCuF;AAC1D,CAAC,GAAG,KAAK,CAAC;AACV,SAAStD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}