{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPreferencePanelStateSelector } from \"./gridPreferencePanelSelector.js\";\nexport const preferencePanelStateInitializer = (state, props) => _extends({}, state, {\n  preferencePanel: props.initialState?.preferencePanel ?? {\n    open: false\n  }\n});\n\n/**\n * TODO: Add a single `setPreferencePanel` method to avoid multiple `setState`\n */\nexport const useGridPreferencesPanel = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPreferencesPanel');\n\n  /**\n   * API METHODS\n   */\n  const hidePreferences = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      if (!state.preferencePanel.open) {\n        return state;\n      }\n      logger.debug('Hiding Preferences Panel');\n      const preferencePanelState = gridPreferencePanelStateSelector(apiRef);\n      apiRef.current.publishEvent('preferencePanelClose', {\n        openedPanelValue: preferencePanelState.openedPanelValue\n      });\n      return _extends({}, state, {\n        preferencePanel: {\n          open: false\n        }\n      });\n    });\n  }, [apiRef, logger]);\n  const showPreferences = React.useCallback((newValue, panelId, labelId) => {\n    logger.debug('Opening Preferences Panel');\n    apiRef.current.setState(state => _extends({}, state, {\n      preferencePanel: _extends({}, state.preferencePanel, {\n        open: true,\n        openedPanelValue: newValue,\n        panelId,\n        labelId\n      })\n    }));\n    apiRef.current.publishEvent('preferencePanelOpen', {\n      openedPanelValue: newValue\n    });\n  }, [logger, apiRef]);\n  useGridApiMethod(apiRef, {\n    showPreferences,\n    hidePreferences\n  }, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const preferencePanelToExport = gridPreferencePanelStateSelector(apiRef);\n    const shouldExportPreferencePanel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the panel was initialized\n    props.initialState?.preferencePanel != null ||\n    // Always export if the panel is opened\n    preferencePanelToExport.open;\n    if (!shouldExportPreferencePanel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      preferencePanel: preferencePanelToExport\n    });\n  }, [apiRef, props.initialState?.preferencePanel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const preferencePanel = context.stateToRestore.preferencePanel;\n    if (preferencePanel != null) {\n      apiRef.current.setState(state => _extends({}, state, {\n        preferencePanel\n      }));\n    }\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n};", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "useGridLogger", "useGridRegisterPipeProcessor", "gridPreferencePanelStateSelector", "preferencePanelStateInitializer", "state", "props", "preferencePanel", "initialState", "open", "useGridPreferencesPanel", "apiRef", "logger", "hidePreferences", "useCallback", "current", "setState", "debug", "preferencePanelState", "publishEvent", "openedPanelValue", "showPreferences", "newValue", "panelId", "labelId", "stateExportPreProcessing", "prevState", "context", "preferencePanelToExport", "shouldExportPreferencePanel", "exportOnlyDirtyModels", "stateRestorePreProcessing", "params", "stateToRestore"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/preferencesPanel/useGridPreferencesPanel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPreferencePanelStateSelector } from \"./gridPreferencePanelSelector.js\";\nexport const preferencePanelStateInitializer = (state, props) => _extends({}, state, {\n  preferencePanel: props.initialState?.preferencePanel ?? {\n    open: false\n  }\n});\n\n/**\n * TODO: Add a single `setPreferencePanel` method to avoid multiple `setState`\n */\nexport const useGridPreferencesPanel = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPreferencesPanel');\n\n  /**\n   * API METHODS\n   */\n  const hidePreferences = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      if (!state.preferencePanel.open) {\n        return state;\n      }\n      logger.debug('Hiding Preferences Panel');\n      const preferencePanelState = gridPreferencePanelStateSelector(apiRef);\n      apiRef.current.publishEvent('preferencePanelClose', {\n        openedPanelValue: preferencePanelState.openedPanelValue\n      });\n      return _extends({}, state, {\n        preferencePanel: {\n          open: false\n        }\n      });\n    });\n  }, [apiRef, logger]);\n  const showPreferences = React.useCallback((newValue, panelId, labelId) => {\n    logger.debug('Opening Preferences Panel');\n    apiRef.current.setState(state => _extends({}, state, {\n      preferencePanel: _extends({}, state.preferencePanel, {\n        open: true,\n        openedPanelValue: newValue,\n        panelId,\n        labelId\n      })\n    }));\n    apiRef.current.publishEvent('preferencePanelOpen', {\n      openedPanelValue: newValue\n    });\n  }, [logger, apiRef]);\n  useGridApiMethod(apiRef, {\n    showPreferences,\n    hidePreferences\n  }, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const preferencePanelToExport = gridPreferencePanelStateSelector(apiRef);\n    const shouldExportPreferencePanel =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the panel was initialized\n    props.initialState?.preferencePanel != null ||\n    // Always export if the panel is opened\n    preferencePanelToExport.open;\n    if (!shouldExportPreferencePanel) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      preferencePanel: preferencePanelToExport\n    });\n  }, [apiRef, props.initialState?.preferencePanel]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const preferencePanel = context.stateToRestore.preferencePanel;\n    if (preferencePanel != null) {\n      apiRef.current.setState(state => _extends({}, state, {\n        preferencePanel\n      }));\n    }\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,gCAAgC,QAAQ,kCAAkC;AACnF,OAAO,MAAMC,+BAA+B,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAKR,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;EACnFE,eAAe,EAAED,KAAK,CAACE,YAAY,EAAED,eAAe,IAAI;IACtDE,IAAI,EAAE;EACR;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,MAAM,EAAEL,KAAK,KAAK;EACxD,MAAMM,MAAM,GAAGX,aAAa,CAACU,MAAM,EAAE,yBAAyB,CAAC;;EAE/D;AACF;AACA;EACE,MAAME,eAAe,GAAGd,KAAK,CAACe,WAAW,CAAC,MAAM;IAC9CH,MAAM,CAACI,OAAO,CAACC,QAAQ,CAACX,KAAK,IAAI;MAC/B,IAAI,CAACA,KAAK,CAACE,eAAe,CAACE,IAAI,EAAE;QAC/B,OAAOJ,KAAK;MACd;MACAO,MAAM,CAACK,KAAK,CAAC,0BAA0B,CAAC;MACxC,MAAMC,oBAAoB,GAAGf,gCAAgC,CAACQ,MAAM,CAAC;MACrEA,MAAM,CAACI,OAAO,CAACI,YAAY,CAAC,sBAAsB,EAAE;QAClDC,gBAAgB,EAAEF,oBAAoB,CAACE;MACzC,CAAC,CAAC;MACF,OAAOtB,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;QACzBE,eAAe,EAAE;UACfE,IAAI,EAAE;QACR;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACE,MAAM,EAAEC,MAAM,CAAC,CAAC;EACpB,MAAMS,eAAe,GAAGtB,KAAK,CAACe,WAAW,CAAC,CAACQ,QAAQ,EAAEC,OAAO,EAAEC,OAAO,KAAK;IACxEZ,MAAM,CAACK,KAAK,CAAC,2BAA2B,CAAC;IACzCN,MAAM,CAACI,OAAO,CAACC,QAAQ,CAACX,KAAK,IAAIP,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;MACnDE,eAAe,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,CAACE,eAAe,EAAE;QACnDE,IAAI,EAAE,IAAI;QACVW,gBAAgB,EAAEE,QAAQ;QAC1BC,OAAO;QACPC;MACF,CAAC;IACH,CAAC,CAAC,CAAC;IACHb,MAAM,CAACI,OAAO,CAACI,YAAY,CAAC,qBAAqB,EAAE;MACjDC,gBAAgB,EAAEE;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACV,MAAM,EAAED,MAAM,CAAC,CAAC;EACpBX,gBAAgB,CAACW,MAAM,EAAE;IACvBU,eAAe;IACfR;EACF,CAAC,EAAE,QAAQ,CAAC;;EAEZ;AACF;AACA;EACE,MAAMY,wBAAwB,GAAG1B,KAAK,CAACe,WAAW,CAAC,CAACY,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,uBAAuB,GAAGzB,gCAAgC,CAACQ,MAAM,CAAC;IACxE,MAAMkB,2BAA2B;IACjC;IACA,CAACF,OAAO,CAACG,qBAAqB;IAC9B;IACAxB,KAAK,CAACE,YAAY,EAAED,eAAe,IAAI,IAAI;IAC3C;IACAqB,uBAAuB,CAACnB,IAAI;IAC5B,IAAI,CAACoB,2BAA2B,EAAE;MAChC,OAAOH,SAAS;IAClB;IACA,OAAO5B,QAAQ,CAAC,CAAC,CAAC,EAAE4B,SAAS,EAAE;MAC7BnB,eAAe,EAAEqB;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjB,MAAM,EAAEL,KAAK,CAACE,YAAY,EAAED,eAAe,CAAC,CAAC;EACjD,MAAMwB,yBAAyB,GAAGhC,KAAK,CAACe,WAAW,CAAC,CAACkB,MAAM,EAAEL,OAAO,KAAK;IACvE,MAAMpB,eAAe,GAAGoB,OAAO,CAACM,cAAc,CAAC1B,eAAe;IAC9D,IAAIA,eAAe,IAAI,IAAI,EAAE;MAC3BI,MAAM,CAACI,OAAO,CAACC,QAAQ,CAACX,KAAK,IAAIP,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;QACnDE;MACF,CAAC,CAAC,CAAC;IACL;IACA,OAAOyB,MAAM;EACf,CAAC,EAAE,CAACrB,MAAM,CAAC,CAAC;EACZT,4BAA4B,CAACS,MAAM,EAAE,aAAa,EAAEc,wBAAwB,CAAC;EAC7EvB,4BAA4B,CAACS,MAAM,EAAE,cAAc,EAAEoB,yBAAyB,CAAC;AACjF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}