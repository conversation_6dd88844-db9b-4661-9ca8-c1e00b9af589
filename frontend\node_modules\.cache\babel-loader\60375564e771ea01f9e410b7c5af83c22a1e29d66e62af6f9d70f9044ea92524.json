{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridNoResultsOverlay = forwardRef(function GridNoResultsOverlay(props, ref) {\n  const apiRef = useGridApiContext();\n  const noResultsOverlayLabel = apiRef.current.getLocaleText('noResultsOverlayLabel');\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({}, props, {\n    ref: ref,\n    children: noResultsOverlayLabel\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridNoResultsOverlay.displayName = \"GridNoResultsOverlay\";", "map": {"version": 3, "names": ["_extends", "React", "forwardRef", "useGridApiContext", "GridOverlay", "jsx", "_jsx", "GridNoResultsOverlay", "props", "ref", "apiRef", "noResultsOverlayLabel", "current", "getLocaleText", "children", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/GridNoResultsOverlay.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useGridApiContext } from \"../hooks/utils/useGridApiContext.js\";\nimport { GridOverlay } from \"./containers/GridOverlay.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridNoResultsOverlay = forwardRef(function GridNoResultsOverlay(props, ref) {\n  const apiRef = useGridApiContext();\n  const noResultsOverlayLabel = apiRef.current.getLocaleText('noResultsOverlayLabel');\n  return /*#__PURE__*/_jsx(GridOverlay, _extends({}, props, {\n    ref: ref,\n    children: noResultsOverlayLabel\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridNoResultsOverlay.displayName = \"GridNoResultsOverlay\";"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,oBAAoB,GAAGL,UAAU,CAAC,SAASK,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvF,MAAMC,MAAM,GAAGP,iBAAiB,CAAC,CAAC;EAClC,MAAMQ,qBAAqB,GAAGD,MAAM,CAACE,OAAO,CAACC,aAAa,CAAC,uBAAuB,CAAC;EACnF,OAAO,aAAaP,IAAI,CAACF,WAAW,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,EAAE;IACxDC,GAAG,EAAEA,GAAG;IACRK,QAAQ,EAAEH;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEV,oBAAoB,CAACW,WAAW,GAAG,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}