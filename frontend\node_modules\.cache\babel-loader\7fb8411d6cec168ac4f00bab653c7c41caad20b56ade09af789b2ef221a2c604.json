{"ast": null, "code": "import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridRowTreeSelector, gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { GRID_ID_AUTOGENERATED, isAutogeneratedRowNode } from \"../rows/gridRowsUtils.js\";\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nconst gridSortingStateSelector = createRootSelector(state => state.sorting);\n\n/**\n * Get the id of the rows after the sorting process.\n * @category Sorting\n */\nexport const gridSortedRowIdsSelector = createSelector(gridSortingStateSelector, sortingState => sortingState.sortedRows);\n\n/**\n * Get the id and the model of the rows after the sorting process.\n * @category Sorting\n */\nexport const gridSortedRowEntriesSelector = createSelectorMemoized(gridSortedRowIdsSelector, gridRowsLookupSelector, gridRowTreeSelector, (sortedIds, idRowsLookup, rowTree) => sortedIds.reduce((acc, id) => {\n  const model = idRowsLookup[id];\n  if (model) {\n    acc.push({\n      id,\n      model\n    });\n  } else {\n    const rowNode = rowTree[id];\n    if (rowNode && isAutogeneratedRowNode(rowNode)) {\n      acc.push({\n        id,\n        model: {\n          [GRID_ID_AUTOGENERATED]: id\n        }\n      });\n    }\n  }\n  return acc;\n}, []));\n\n/**\n * Get the current sorting model.\n * @category Sorting\n */\nexport const gridSortModelSelector = createSelector(gridSortingStateSelector, sorting => sorting.sortModel);\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nexport const gridSortColumnLookupSelector = createSelectorMemoized(gridSortModelSelector, sortModel => {\n  const result = sortModel.reduce((res, sortItem, index) => {\n    res[sortItem.field] = {\n      sortDirection: sortItem.sort,\n      sortIndex: sortModel.length > 1 ? index + 1 : undefined\n    };\n    return res;\n  }, {});\n  return result;\n});\n\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nexport const gridSortedRowIndexLookupSelector = createSelectorMemoized(gridSortedRowIdsSelector, sortedIds => {\n  return sortedIds.reduce((acc, id, index) => {\n    acc[id] = index;\n    return acc;\n  }, Object.create(null));\n});", "map": {"version": 3, "names": ["createRootSelector", "createSelector", "createSelectorMemoized", "gridRowTreeSelector", "gridRowsLookupSelector", "GRID_ID_AUTOGENERATED", "isAutogeneratedRowNode", "gridSortingStateSelector", "state", "sorting", "gridSortedRowIdsSelector", "sortingState", "sortedRows", "gridSortedRowEntriesSelector", "sortedIds", "idRowsLookup", "rowTree", "reduce", "acc", "id", "model", "push", "rowNode", "gridSortModelSelector", "sortModel", "gridSortColumnLookupSelector", "result", "res", "sortItem", "index", "field", "sortDirection", "sort", "sortIndex", "length", "undefined", "gridSortedRowIndexLookupSelector", "Object", "create"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/sorting/gridSortingSelector.js"], "sourcesContent": ["import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridRowTreeSelector, gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { GRID_ID_AUTOGENERATED, isAutogeneratedRowNode } from \"../rows/gridRowsUtils.js\";\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nconst gridSortingStateSelector = createRootSelector(state => state.sorting);\n\n/**\n * Get the id of the rows after the sorting process.\n * @category Sorting\n */\nexport const gridSortedRowIdsSelector = createSelector(gridSortingStateSelector, sortingState => sortingState.sortedRows);\n\n/**\n * Get the id and the model of the rows after the sorting process.\n * @category Sorting\n */\nexport const gridSortedRowEntriesSelector = createSelectorMemoized(gridSortedRowIdsSelector, gridRowsLookupSelector, gridRowTreeSelector, (sortedIds, idRowsLookup, rowTree) => sortedIds.reduce((acc, id) => {\n  const model = idRowsLookup[id];\n  if (model) {\n    acc.push({\n      id,\n      model\n    });\n  } else {\n    const rowNode = rowTree[id];\n    if (rowNode && isAutogeneratedRowNode(rowNode)) {\n      acc.push({\n        id,\n        model: {\n          [GRID_ID_AUTOGENERATED]: id\n        }\n      });\n    }\n  }\n  return acc;\n}, []));\n\n/**\n * Get the current sorting model.\n * @category Sorting\n */\nexport const gridSortModelSelector = createSelector(gridSortingStateSelector, sorting => sorting.sortModel);\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nexport const gridSortColumnLookupSelector = createSelectorMemoized(gridSortModelSelector, sortModel => {\n  const result = sortModel.reduce((res, sortItem, index) => {\n    res[sortItem.field] = {\n      sortDirection: sortItem.sort,\n      sortIndex: sortModel.length > 1 ? index + 1 : undefined\n    };\n    return res;\n  }, {});\n  return result;\n});\n\n/**\n * @category Sorting\n * @ignore - do not document.\n */\nexport const gridSortedRowIndexLookupSelector = createSelectorMemoized(gridSortedRowIdsSelector, sortedIds => {\n  return sortedIds.reduce((acc, id, index) => {\n    acc[id] = index;\n    return acc;\n  }, Object.create(null));\n});"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,cAAc,EAAEC,sBAAsB,QAAQ,kCAAkC;AAC7G,SAASC,mBAAmB,EAAEC,sBAAsB,QAAQ,6BAA6B;AACzF,SAASC,qBAAqB,EAAEC,sBAAsB,QAAQ,0BAA0B;AACxF;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAGP,kBAAkB,CAACQ,KAAK,IAAIA,KAAK,CAACC,OAAO,CAAC;;AAE3E;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGT,cAAc,CAACM,wBAAwB,EAAEI,YAAY,IAAIA,YAAY,CAACC,UAAU,CAAC;;AAEzH;AACA;AACA;AACA;AACA,OAAO,MAAMC,4BAA4B,GAAGX,sBAAsB,CAACQ,wBAAwB,EAAEN,sBAAsB,EAAED,mBAAmB,EAAE,CAACW,SAAS,EAAEC,YAAY,EAAEC,OAAO,KAAKF,SAAS,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,EAAE,KAAK;EAC5M,MAAMC,KAAK,GAAGL,YAAY,CAACI,EAAE,CAAC;EAC9B,IAAIC,KAAK,EAAE;IACTF,GAAG,CAACG,IAAI,CAAC;MACPF,EAAE;MACFC;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,MAAME,OAAO,GAAGN,OAAO,CAACG,EAAE,CAAC;IAC3B,IAAIG,OAAO,IAAIhB,sBAAsB,CAACgB,OAAO,CAAC,EAAE;MAC9CJ,GAAG,CAACG,IAAI,CAAC;QACPF,EAAE;QACFC,KAAK,EAAE;UACL,CAACf,qBAAqB,GAAGc;QAC3B;MACF,CAAC,CAAC;IACJ;EACF;EACA,OAAOD,GAAG;AACZ,CAAC,EAAE,EAAE,CAAC,CAAC;;AAEP;AACA;AACA;AACA;AACA,OAAO,MAAMK,qBAAqB,GAAGtB,cAAc,CAACM,wBAAwB,EAAEE,OAAO,IAAIA,OAAO,CAACe,SAAS,CAAC;AAC3G;AACA;AACA;AACA;AACA,OAAO,MAAMC,4BAA4B,GAAGvB,sBAAsB,CAACqB,qBAAqB,EAAEC,SAAS,IAAI;EACrG,MAAME,MAAM,GAAGF,SAAS,CAACP,MAAM,CAAC,CAACU,GAAG,EAAEC,QAAQ,EAAEC,KAAK,KAAK;IACxDF,GAAG,CAACC,QAAQ,CAACE,KAAK,CAAC,GAAG;MACpBC,aAAa,EAAEH,QAAQ,CAACI,IAAI;MAC5BC,SAAS,EAAET,SAAS,CAACU,MAAM,GAAG,CAAC,GAAGL,KAAK,GAAG,CAAC,GAAGM;IAChD,CAAC;IACD,OAAOR,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMU,gCAAgC,GAAGlC,sBAAsB,CAACQ,wBAAwB,EAAEI,SAAS,IAAI;EAC5G,OAAOA,SAAS,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,EAAE,EAAEU,KAAK,KAAK;IAC1CX,GAAG,CAACC,EAAE,CAAC,GAAGU,KAAK;IACf,OAAOX,GAAG;EACZ,CAAC,EAAEmB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}