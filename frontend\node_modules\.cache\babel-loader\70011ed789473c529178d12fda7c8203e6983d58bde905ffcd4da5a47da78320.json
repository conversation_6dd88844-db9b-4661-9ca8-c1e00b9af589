{"ast": null, "code": "import * as React from 'react';\nexport const useGridLocaleText = (apiRef, props) => {\n  const getLocaleText = React.useCallback(key => {\n    if (props.localeText[key] == null) {\n      throw new Error(`Missing translation for key ${key}.`);\n    }\n    return props.localeText[key];\n  }, [props.localeText]);\n  apiRef.current.register('public', {\n    getLocaleText\n  });\n};", "map": {"version": 3, "names": ["React", "useGridLocaleText", "apiRef", "props", "getLocaleText", "useCallback", "key", "localeText", "Error", "current", "register"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/core/useGridLocaleText.js"], "sourcesContent": ["import * as React from 'react';\nexport const useGridLocaleText = (apiRef, props) => {\n  const getLocaleText = React.useCallback(key => {\n    if (props.localeText[key] == null) {\n      throw new Error(`Missing translation for key ${key}.`);\n    }\n    return props.localeText[key];\n  }, [props.localeText]);\n  apiRef.current.register('public', {\n    getLocaleText\n  });\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAClD,MAAMC,aAAa,GAAGJ,KAAK,CAACK,WAAW,CAACC,GAAG,IAAI;IAC7C,IAAIH,KAAK,CAACI,UAAU,CAACD,GAAG,CAAC,IAAI,IAAI,EAAE;MACjC,MAAM,IAAIE,KAAK,CAAC,+BAA+BF,GAAG,GAAG,CAAC;IACxD;IACA,OAAOH,KAAK,CAACI,UAAU,CAACD,GAAG,CAAC;EAC9B,CAAC,EAAE,CAACH,KAAK,CAACI,UAAU,CAAC,CAAC;EACtBL,MAAM,CAACO,OAAO,CAACC,QAAQ,CAAC,QAAQ,EAAE;IAChCN;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}