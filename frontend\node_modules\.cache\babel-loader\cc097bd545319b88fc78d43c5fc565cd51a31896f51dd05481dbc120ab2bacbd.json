{"ast": null, "code": "function compact(arr) {\n  const result = [];\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    if (item) {\n      result.push(item);\n    }\n  }\n  return result;\n}\nexport { compact };", "map": {"version": 3, "names": ["compact", "arr", "result", "i", "length", "item", "push"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/compact.mjs"], "sourcesContent": ["function compact(arr) {\n    const result = [];\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        if (item) {\n            result.push(item);\n        }\n    }\n    return result;\n}\n\nexport { compact };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAE;EAClB,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGJ,GAAG,CAACE,CAAC,CAAC;IACnB,IAAIE,IAAI,EAAE;MACNH,MAAM,CAACI,IAAI,CAACD,IAAI,CAAC;IACrB;EACJ;EACA,OAAOH,MAAM;AACjB;AAEA,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}