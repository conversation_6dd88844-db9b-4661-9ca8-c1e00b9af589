{"ast": null, "code": "export { EventManager } from \"./EventManager.js\";", "map": {"version": 3, "names": ["EventManager"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/EventManager/index.js"], "sourcesContent": ["export { EventManager } from \"./EventManager.js\";"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}