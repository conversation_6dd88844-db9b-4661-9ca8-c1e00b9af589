{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nexport const GridConfigurationContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") GridConfigurationContext.displayName = \"GridConfigurationContext\";", "map": {"version": 3, "names": ["React", "GridConfigurationContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/GridConfigurationContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nexport const GridConfigurationContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") GridConfigurationContext.displayName = \"GridConfigurationContext\";"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,wBAAwB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACnF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEL,wBAAwB,CAACM,WAAW,GAAG,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}