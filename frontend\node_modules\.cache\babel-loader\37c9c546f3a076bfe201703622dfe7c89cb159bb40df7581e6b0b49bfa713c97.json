{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridPaginationSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { gridRowCountSelector } from \"./gridRowsSelector.js\";\nimport { gridRowHeightSelector } from \"../dimensions/gridDimensionsSelectors.js\";\nexport const rowsMetaStateInitializer = (state, props, apiRef) => {\n  // FIXME: This should be handled in the virtualizer eventually, but there are interdependencies\n  // between state initializers that need to be untangled carefully.\n\n  const baseRowHeight = gridRowHeightSelector(apiRef);\n  const dataRowCount = gridRowCountSelector(apiRef);\n  const pagination = gridPaginationSelector(apiRef);\n  const rowCount = Math.min(pagination.enabled ? pagination.paginationModel.pageSize : dataRowCount, dataRowCount);\n  return _extends({}, state, {\n    rowsMeta: {\n      currentPageTotalHeight: rowCount * baseRowHeight,\n      positions: Array.from({\n        length: rowCount\n      }, (_, i) => i * baseRowHeight),\n      pinnedTopRowsTotalHeight: 0,\n      pinnedBottomRowsTotalHeight: 0\n    }\n  });\n};\n\n/**\n * @requires useGridPageSize (method)\n * @requires useGridPage (method)\n */\nexport const useGridRowsMeta = (apiRef, _props) => {\n  const virtualizer = apiRef.current.virtualizer;\n  const {\n    getRowHeight,\n    setLastMeasuredRowIndex,\n    storeRowHeightMeasurement,\n    resetRowHeights,\n    hydrateRowsMeta,\n    observeRowHeight,\n    rowHasAutoHeight,\n    getRowHeightEntry,\n    getLastMeasuredRowIndex\n  } = virtualizer.api.rowsMeta;\n  useGridRegisterPipeApplier(apiRef, 'rowHeight', hydrateRowsMeta);\n  const rowsMetaApi = {\n    unstable_getRowHeight: getRowHeight,\n    unstable_setLastMeasuredRowIndex: setLastMeasuredRowIndex,\n    unstable_storeRowHeightMeasurement: storeRowHeightMeasurement,\n    resetRowHeights\n  };\n  const rowsMetaPrivateApi = {\n    hydrateRowsMeta,\n    observeRowHeight,\n    rowHasAutoHeight,\n    getRowHeightEntry,\n    getLastMeasuredRowIndex\n  };\n  useGridApiMethod(apiRef, rowsMetaApi, 'public');\n  useGridApiMethod(apiRef, rowsMetaPrivateApi, 'private');\n};", "map": {"version": 3, "names": ["_extends", "useGridApiMethod", "gridPaginationSelector", "useGridRegisterPipeApplier", "gridRowCountSelector", "gridRowHeightSelector", "rowsMetaStateInitializer", "state", "props", "apiRef", "baseRowHeight", "dataRowCount", "pagination", "rowCount", "Math", "min", "enabled", "paginationModel", "pageSize", "rowsMeta", "currentPageTotalHeight", "positions", "Array", "from", "length", "_", "i", "pinnedTopRowsTotalHeight", "pinnedBottomRowsTotalHeight", "useGridRowsMeta", "_props", "virtualizer", "current", "getRowHeight", "setLastMeasuredRowIndex", "storeRowHeightMeasurement", "resetRowHeights", "hydrateRowsMeta", "observeRowHeight", "rowHasAutoHeight", "getRowHeightEntry", "getLastMeasuredRowIndex", "api", "rowsMetaApi", "unstable_getRowHeight", "unstable_setLastMeasuredRowIndex", "unstable_storeRowHeightMeasurement", "rowsMetaPrivateApi"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rows/useGridRowsMeta.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridPaginationSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { gridRowCountSelector } from \"./gridRowsSelector.js\";\nimport { gridRowHeightSelector } from \"../dimensions/gridDimensionsSelectors.js\";\nexport const rowsMetaStateInitializer = (state, props, apiRef) => {\n  // FIXME: This should be handled in the virtualizer eventually, but there are interdependencies\n  // between state initializers that need to be untangled carefully.\n\n  const baseRowHeight = gridRowHeightSelector(apiRef);\n  const dataRowCount = gridRowCountSelector(apiRef);\n  const pagination = gridPaginationSelector(apiRef);\n  const rowCount = Math.min(pagination.enabled ? pagination.paginationModel.pageSize : dataRowCount, dataRowCount);\n  return _extends({}, state, {\n    rowsMeta: {\n      currentPageTotalHeight: rowCount * baseRowHeight,\n      positions: Array.from({\n        length: rowCount\n      }, (_, i) => i * baseRowHeight),\n      pinnedTopRowsTotalHeight: 0,\n      pinnedBottomRowsTotalHeight: 0\n    }\n  });\n};\n\n/**\n * @requires useGridPageSize (method)\n * @requires useGridPage (method)\n */\nexport const useGridRowsMeta = (apiRef, _props) => {\n  const virtualizer = apiRef.current.virtualizer;\n  const {\n    getRowHeight,\n    setLastMeasuredRowIndex,\n    storeRowHeightMeasurement,\n    resetRowHeights,\n    hydrateRowsMeta,\n    observeRowHeight,\n    rowHasAutoHeight,\n    getRowHeightEntry,\n    getLastMeasuredRowIndex\n  } = virtualizer.api.rowsMeta;\n  useGridRegisterPipeApplier(apiRef, 'rowHeight', hydrateRowsMeta);\n  const rowsMetaApi = {\n    unstable_getRowHeight: getRowHeight,\n    unstable_setLastMeasuredRowIndex: setLastMeasuredRowIndex,\n    unstable_storeRowHeightMeasurement: storeRowHeightMeasurement,\n    resetRowHeights\n  };\n  const rowsMetaPrivateApi = {\n    hydrateRowsMeta,\n    observeRowHeight,\n    rowHasAutoHeight,\n    getRowHeightEntry,\n    getLastMeasuredRowIndex\n  };\n  useGridApiMethod(apiRef, rowsMetaApi, 'public');\n  useGridApiMethod(apiRef, rowsMetaPrivateApi, 'private');\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,0BAA0B,QAAQ,oCAAoC;AAC/E,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAChE;EACA;;EAEA,MAAMC,aAAa,GAAGL,qBAAqB,CAACI,MAAM,CAAC;EACnD,MAAME,YAAY,GAAGP,oBAAoB,CAACK,MAAM,CAAC;EACjD,MAAMG,UAAU,GAAGV,sBAAsB,CAACO,MAAM,CAAC;EACjD,MAAMI,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,UAAU,CAACI,OAAO,GAAGJ,UAAU,CAACK,eAAe,CAACC,QAAQ,GAAGP,YAAY,EAAEA,YAAY,CAAC;EAChH,OAAOX,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;IACzBY,QAAQ,EAAE;MACRC,sBAAsB,EAAEP,QAAQ,GAAGH,aAAa;MAChDW,SAAS,EAAEC,KAAK,CAACC,IAAI,CAAC;QACpBC,MAAM,EAAEX;MACV,CAAC,EAAE,CAACY,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGhB,aAAa,CAAC;MAC/BiB,wBAAwB,EAAE,CAAC;MAC3BC,2BAA2B,EAAE;IAC/B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGA,CAACpB,MAAM,EAAEqB,MAAM,KAAK;EACjD,MAAMC,WAAW,GAAGtB,MAAM,CAACuB,OAAO,CAACD,WAAW;EAC9C,MAAM;IACJE,YAAY;IACZC,uBAAuB;IACvBC,yBAAyB;IACzBC,eAAe;IACfC,eAAe;IACfC,gBAAgB;IAChBC,gBAAgB;IAChBC,iBAAiB;IACjBC;EACF,CAAC,GAAGV,WAAW,CAACW,GAAG,CAACvB,QAAQ;EAC5BhB,0BAA0B,CAACM,MAAM,EAAE,WAAW,EAAE4B,eAAe,CAAC;EAChE,MAAMM,WAAW,GAAG;IAClBC,qBAAqB,EAAEX,YAAY;IACnCY,gCAAgC,EAAEX,uBAAuB;IACzDY,kCAAkC,EAAEX,yBAAyB;IAC7DC;EACF,CAAC;EACD,MAAMW,kBAAkB,GAAG;IACzBV,eAAe;IACfC,gBAAgB;IAChBC,gBAAgB;IAChBC,iBAAiB;IACjBC;EACF,CAAC;EACDxC,gBAAgB,CAACQ,MAAM,EAAEkC,WAAW,EAAE,QAAQ,CAAC;EAC/C1C,gBAAgB,CAACQ,MAAM,EAAEsC,kBAAkB,EAAE,SAAS,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}