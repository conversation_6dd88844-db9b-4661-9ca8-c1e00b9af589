{"ast": null, "code": "function curryRight(func) {\n  if (func.length === 0 || func.length === 1) {\n    return func;\n  }\n  return function (arg) {\n    return makeCurryRight(func, func.length, [arg]);\n  };\n}\nfunction makeCurryRight(origin, argsLength, args) {\n  if (args.length === argsLength) {\n    return origin(...args);\n  } else {\n    const next = function (arg) {\n      return makeCurryRight(origin, argsLength, [arg, ...args]);\n    };\n    return next;\n  }\n}\nexport { curryRight };", "map": {"version": 3, "names": ["curryRight", "func", "length", "arg", "makeCurryRight", "origin", "arg<PERSON><PERSON><PERSON><PERSON>", "args", "next"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/curryRight.mjs"], "sourcesContent": ["function curryRight(func) {\n    if (func.length === 0 || func.length === 1) {\n        return func;\n    }\n    return function (arg) {\n        return makeCurryRight(func, func.length, [arg]);\n    };\n}\nfunction makeCurryRight(origin, argsLength, args) {\n    if (args.length === argsLength) {\n        return origin(...args);\n    }\n    else {\n        const next = function (arg) {\n            return makeCurryRight(origin, argsLength, [arg, ...args]);\n        };\n        return next;\n    }\n}\n\nexport { curryRight };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,IAAI,EAAE;EACtB,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,IAAID,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACxC,OAAOD,IAAI;EACf;EACA,OAAO,UAAUE,GAAG,EAAE;IAClB,OAAOC,cAAc,CAACH,IAAI,EAAEA,IAAI,CAACC,MAAM,EAAE,CAACC,GAAG,CAAC,CAAC;EACnD,CAAC;AACL;AACA,SAASC,cAAcA,CAACC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAE;EAC9C,IAAIA,IAAI,CAACL,MAAM,KAAKI,UAAU,EAAE;IAC5B,OAAOD,MAAM,CAAC,GAAGE,IAAI,CAAC;EAC1B,CAAC,MACI;IACD,MAAMC,IAAI,GAAG,SAAAA,CAAUL,GAAG,EAAE;MACxB,OAAOC,cAAc,CAACC,MAAM,EAAEC,UAAU,EAAE,CAACH,GAAG,EAAE,GAAGI,IAAI,CAAC,CAAC;IAC7D,CAAC;IACD,OAAOC,IAAI;EACf;AACJ;AAEA,SAASR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}