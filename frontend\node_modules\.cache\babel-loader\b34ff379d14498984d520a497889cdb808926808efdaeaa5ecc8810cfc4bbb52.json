{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridPreferencePanelStateSelector = createRootSelector(state => state.preferencePanel);\nexport const gridPreferencePanelSelectorWithLabel = createSelector(gridPreferencePanelStateSelector, (panel, labelId) => {\n  if (panel.open && panel.labelId === labelId) {\n    return true;\n  }\n  return false;\n});", "map": {"version": 3, "names": ["createSelector", "createRootSelector", "gridPreferencePanelStateSelector", "state", "preferencePanel", "gridPreferencePanelSelectorWithLabel", "panel", "labelId", "open"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/preferencesPanel/gridPreferencePanelSelector.js"], "sourcesContent": ["import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridPreferencePanelStateSelector = createRootSelector(state => state.preferencePanel);\nexport const gridPreferencePanelSelectorWithLabel = createSelector(gridPreferencePanelStateSelector, (panel, labelId) => {\n  if (panel.open && panel.labelId === labelId) {\n    return true;\n  }\n  return false;\n});"], "mappings": "AAAA,SAASA,cAAc,EAAEC,kBAAkB,QAAQ,kCAAkC;AACrF,OAAO,MAAMC,gCAAgC,GAAGD,kBAAkB,CAACE,KAAK,IAAIA,KAAK,CAACC,eAAe,CAAC;AAClG,OAAO,MAAMC,oCAAoC,GAAGL,cAAc,CAACE,gCAAgC,EAAE,CAACI,KAAK,EAAEC,OAAO,KAAK;EACvH,IAAID,KAAK,CAACE,IAAI,IAAIF,KAAK,CAACC,OAAO,KAAKA,OAAO,EAAE;IAC3C,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}