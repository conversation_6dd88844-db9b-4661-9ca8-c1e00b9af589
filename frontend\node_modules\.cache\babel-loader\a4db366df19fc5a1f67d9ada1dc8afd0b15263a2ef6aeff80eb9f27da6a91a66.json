{"ast": null, "code": "import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\n/**\n * Get the columns state\n * @category Virtualization\n */\nexport const gridVirtualizationSelector = createRootSelector(state => state.virtualization);\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n * @deprecated Use `gridVirtualizationColumnEnabledSelector` and `gridVirtualizationRowEnabledSelector`\n */\nexport const gridVirtualizationEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabled);\n\n/**\n * Get the enabled state for column virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationColumnEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForColumns);\n\n/**\n * Get the enabled state for row virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationRowEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForRows);\n\n/**\n * Get the render context\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextSelector = createSelector(gridVirtualizationSelector, state => state.renderContext);\nconst firstColumnIndexSelector = createRootSelector(state => state.virtualization.renderContext.firstColumnIndex);\nconst lastColumnIndexSelector = createRootSelector(state => state.virtualization.renderContext.lastColumnIndex);\n\n/**\n * Get the render context, with only columns filled in.\n * This is cached, so it can be used to only re-render when the column interval changes.\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextColumnsSelector = createSelectorMemoized(firstColumnIndexSelector, lastColumnIndexSelector, (firstColumnIndex, lastColumnIndex) => ({\n  firstColumnIndex,\n  lastColumnIndex\n}));", "map": {"version": 3, "names": ["createRootSelector", "createSelector", "createSelectorMemoized", "gridVirtualizationSelector", "state", "virtualization", "gridVirtualizationEnabledSelector", "enabled", "gridVirtualizationColumnEnabledSelector", "enabledForColumns", "gridVirtualizationRowEnabledSelector", "enabledForRows", "gridRenderContextSelector", "renderContext", "firstColumnIndexSelector", "firstColumnIndex", "lastColumnIndexSelector", "lastColumnIndex", "gridRenderContextColumnsSelector"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/virtualization/gridVirtualizationSelectors.js"], "sourcesContent": ["import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\n/**\n * Get the columns state\n * @category Virtualization\n */\nexport const gridVirtualizationSelector = createRootSelector(state => state.virtualization);\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n * @deprecated Use `gridVirtualizationColumnEnabledSelector` and `gridVirtualizationRowEnabledSelector`\n */\nexport const gridVirtualizationEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabled);\n\n/**\n * Get the enabled state for column virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationColumnEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForColumns);\n\n/**\n * Get the enabled state for row virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationRowEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForRows);\n\n/**\n * Get the render context\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextSelector = createSelector(gridVirtualizationSelector, state => state.renderContext);\nconst firstColumnIndexSelector = createRootSelector(state => state.virtualization.renderContext.firstColumnIndex);\nconst lastColumnIndexSelector = createRootSelector(state => state.virtualization.renderContext.lastColumnIndex);\n\n/**\n * Get the render context, with only columns filled in.\n * This is cached, so it can be used to only re-render when the column interval changes.\n * @category Virtualization\n * @ignore - do not document.\n */\nexport const gridRenderContextColumnsSelector = createSelectorMemoized(firstColumnIndexSelector, lastColumnIndexSelector, (firstColumnIndex, lastColumnIndex) => ({\n  firstColumnIndex,\n  lastColumnIndex\n}));"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,cAAc,EAAEC,sBAAsB,QAAQ,kCAAkC;AAC7G;AACA;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,GAAGH,kBAAkB,CAACI,KAAK,IAAIA,KAAK,CAACC,cAAc,CAAC;;AAE3F;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iCAAiC,GAAGL,cAAc,CAACE,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;;AAEnH;AACA;AACA;AACA;AACA,OAAO,MAAMC,uCAAuC,GAAGP,cAAc,CAACE,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACK,iBAAiB,CAAC;;AAEnI;AACA;AACA;AACA;AACA,OAAO,MAAMC,oCAAoC,GAAGT,cAAc,CAACE,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACO,cAAc,CAAC;;AAE7H;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,GAAGX,cAAc,CAACE,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACS,aAAa,CAAC;AACjH,MAAMC,wBAAwB,GAAGd,kBAAkB,CAACI,KAAK,IAAIA,KAAK,CAACC,cAAc,CAACQ,aAAa,CAACE,gBAAgB,CAAC;AACjH,MAAMC,uBAAuB,GAAGhB,kBAAkB,CAACI,KAAK,IAAIA,KAAK,CAACC,cAAc,CAACQ,aAAa,CAACI,eAAe,CAAC;;AAE/G;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gCAAgC,GAAGhB,sBAAsB,CAACY,wBAAwB,EAAEE,uBAAuB,EAAE,CAACD,gBAAgB,EAAEE,eAAe,MAAM;EAChKF,gBAAgB;EAChBE;AACF,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}