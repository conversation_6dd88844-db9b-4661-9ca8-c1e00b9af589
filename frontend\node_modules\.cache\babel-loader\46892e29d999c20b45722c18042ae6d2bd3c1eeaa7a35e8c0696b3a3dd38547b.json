{"ast": null, "code": "import { toInteger } from '../compat/util/toInteger.mjs';\nfunction take(arr, count, guard) {\n  count = guard || count === undefined ? 1 : toInteger(count);\n  return arr.slice(0, count);\n}\nexport { take };", "map": {"version": 3, "names": ["toInteger", "take", "arr", "count", "guard", "undefined", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/take.mjs"], "sourcesContent": ["import { toInteger } from '../compat/util/toInteger.mjs';\n\nfunction take(arr, count, guard) {\n    count = guard || count === undefined ? 1 : toInteger(count);\n    return arr.slice(0, count);\n}\n\nexport { take };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,8BAA8B;AAExD,SAASC,IAAIA,CAACC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAC7BD,KAAK,GAAGC,KAAK,IAAID,KAAK,KAAKE,SAAS,GAAG,CAAC,GAAGL,SAAS,CAACG,KAAK,CAAC;EAC3D,OAAOD,GAAG,CAACI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;AAC9B;AAEA,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}