{"ast": null, "code": "import { at } from './at.mjs';\nfunction pullAt(arr, indicesToRemove) {\n  const removed = at(arr, indicesToRemove);\n  const indices = new Set(indicesToRemove.slice().sort((x, y) => y - x));\n  for (const index of indices) {\n    arr.splice(index, 1);\n  }\n  return removed;\n}\nexport { pullAt };", "map": {"version": 3, "names": ["at", "pullAt", "arr", "indicesToRemove", "removed", "indices", "Set", "slice", "sort", "x", "y", "index", "splice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/pullAt.mjs"], "sourcesContent": ["import { at } from './at.mjs';\n\nfunction pullAt(arr, indicesToRemove) {\n    const removed = at(arr, indicesToRemove);\n    const indices = new Set(indicesToRemove.slice().sort((x, y) => y - x));\n    for (const index of indices) {\n        arr.splice(index, 1);\n    }\n    return removed;\n}\n\nexport { pullAt };\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,UAAU;AAE7B,SAASC,MAAMA,CAACC,GAAG,EAAEC,eAAe,EAAE;EAClC,MAAMC,OAAO,GAAGJ,EAAE,CAACE,GAAG,EAAEC,eAAe,CAAC;EACxC,MAAME,OAAO,GAAG,IAAIC,GAAG,CAACH,eAAe,CAACI,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;EACtE,KAAK,MAAME,KAAK,IAAIN,OAAO,EAAE;IACzBH,GAAG,CAACU,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EACxB;EACA,OAAOP,OAAO;AAClB;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}