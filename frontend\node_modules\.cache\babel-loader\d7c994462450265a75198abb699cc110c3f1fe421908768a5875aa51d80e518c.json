{"ast": null, "code": "async function asyncNoop() {}\nexport { asyncNoop };", "map": {"version": 3, "names": ["asyncNoop"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/asyncNoop.mjs"], "sourcesContent": ["async function asyncNoop() { }\n\nexport { asyncNoop };\n"], "mappings": "AAAA,eAAeA,SAASA,CAAA,EAAG,CAAE;AAE7B,SAASA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}