{"ast": null, "code": "import { words } from './words.mjs';\nfunction upperCase(str) {\n  const words$1 = words(str);\n  let result = '';\n  for (let i = 0; i < words$1.length; i++) {\n    result += words$1[i].toUpperCase();\n    if (i < words$1.length - 1) {\n      result += ' ';\n    }\n  }\n  return result;\n}\nexport { upperCase };", "map": {"version": 3, "names": ["words", "upperCase", "str", "words$1", "result", "i", "length", "toUpperCase"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/string/upperCase.mjs"], "sourcesContent": ["import { words } from './words.mjs';\n\nfunction upperCase(str) {\n    const words$1 = words(str);\n    let result = '';\n    for (let i = 0; i < words$1.length; i++) {\n        result += words$1[i].toUpperCase();\n        if (i < words$1.length - 1) {\n            result += ' ';\n        }\n    }\n    return result;\n}\n\nexport { upperCase };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AAEnC,SAASC,SAASA,CAACC,GAAG,EAAE;EACpB,MAAMC,OAAO,GAAGH,KAAK,CAACE,GAAG,CAAC;EAC1B,IAAIE,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrCD,MAAM,IAAID,OAAO,CAACE,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;IAClC,IAAIF,CAAC,GAAGF,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;MACxBF,MAAM,IAAI,GAAG;IACjB;EACJ;EACA,OAAOA,MAAM;AACjB;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}