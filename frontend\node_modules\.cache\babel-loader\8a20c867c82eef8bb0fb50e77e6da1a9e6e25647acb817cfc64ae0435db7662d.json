{"ast": null, "code": "import { isLeaf } from \"../../../models/gridColumnGrouping.js\";\n// This is the recurrence function that help writing `unwrapGroupingColumnModel()`\nconst recurrentUnwrapGroupingColumnModel = (columnGroupNode, parents, unwrappedGroupingModelToComplete) => {\n  if (isLeaf(columnGroupNode)) {\n    if (unwrappedGroupingModelToComplete[columnGroupNode.field] !== undefined) {\n      throw new Error([`MUI X: columnGroupingModel contains duplicated field`, `column field ${columnGroupNode.field} occurs two times in the grouping model:`, `- ${unwrappedGroupingModelToComplete[columnGroupNode.field].join(' > ')}`, `- ${parents.join(' > ')}`].join('\\n'));\n    }\n    unwrappedGroupingModelToComplete[columnGroupNode.field] = parents;\n    return;\n  }\n  const {\n    groupId,\n    children\n  } = columnGroupNode;\n  children.forEach(child => {\n    recurrentUnwrapGroupingColumnModel(child, [...parents, groupId], unwrappedGroupingModelToComplete);\n  });\n};\n\n/**\n * This is a function that provide for each column the array of its parents.\n * Parents are ordered from the root to the leaf.\n * @param columnGroupingModel The model such as provided in DataGrid props\n * @returns An object `{[field]: groupIds}` where `groupIds` is the parents of the column `field`\n */\nexport const unwrapGroupingColumnModel = columnGroupingModel => {\n  if (!columnGroupingModel) {\n    return {};\n  }\n  const unwrappedSubTree = {};\n  columnGroupingModel.forEach(columnGroupNode => {\n    recurrentUnwrapGroupingColumnModel(columnGroupNode, [], unwrappedSubTree);\n  });\n  return unwrappedSubTree;\n};\nexport const getColumnGroupsHeaderStructure = (orderedColumns, unwrappedGroupingModel, pinnedFields) => {\n  const getParents = field => unwrappedGroupingModel[field] ?? [];\n  const groupingHeaderStructure = [];\n  const maxDepth = Math.max(0, ...orderedColumns.map(field => getParents(field).length));\n  const haveSameParents = (field1, field2, depth) => {\n    const a = getParents(field1);\n    const b = getParents(field2);\n    for (let i = 0; i <= depth; i += 1) {\n      if (a[i] !== b[i]) {\n        return false;\n      }\n    }\n    return true;\n  };\n  const haveDifferentContainers = (field1, field2) => {\n    const left = pinnedFields?.left;\n    const right = pinnedFields?.right;\n    const inLeft1 = !!left?.includes(field1);\n    const inLeft2 = !!left?.includes(field2);\n    const inRight1 = !!right?.includes(field1);\n    const inRight2 = !!right?.includes(field2);\n    return inLeft1 !== inLeft2 || inRight1 !== inRight2;\n  };\n  for (let depth = 0; depth < maxDepth; depth += 1) {\n    const depthStructure = [];\n    for (let i = 0; i < orderedColumns.length; i += 1) {\n      const field = orderedColumns[i];\n      const groupId = getParents(field)[depth] ?? null;\n      if (depthStructure.length === 0) {\n        depthStructure.push({\n          columnFields: [field],\n          groupId\n        });\n        continue;\n      }\n      const lastGroup = depthStructure[depthStructure.length - 1];\n      const prevField = lastGroup.columnFields[lastGroup.columnFields.length - 1];\n      if (lastGroup.groupId !== groupId || !haveSameParents(prevField, field, depth) || haveDifferentContainers(prevField, field)) {\n        depthStructure.push({\n          columnFields: [field],\n          groupId\n        });\n      } else {\n        lastGroup.columnFields.push(field);\n      }\n    }\n    groupingHeaderStructure.push(depthStructure);\n  }\n  return groupingHeaderStructure;\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "recurrentUnwrapGroupingColumnModel", "columnGroupNode", "parents", "unwrappedGroupingModelToComplete", "field", "undefined", "Error", "join", "groupId", "children", "for<PERSON>ach", "child", "unwrapGroupingColumnModel", "columnGroupingModel", "unwrappedSubTree", "getColumnGroupsHeaderStructure", "orderedColumns", "unwrappedGroupingModel", "pinnedFields", "getParents", "groupingHeaderStructure", "max<PERSON><PERSON><PERSON>", "Math", "max", "map", "length", "haveSameParents", "field1", "field2", "depth", "a", "b", "i", "haveDifferentContainers", "left", "right", "inLeft1", "includes", "inLeft2", "inRight1", "inRight2", "depthStructure", "push", "columnFields", "lastGroup", "prevField"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columnGrouping/gridColumnGroupsUtils.js"], "sourcesContent": ["import { isLeaf } from \"../../../models/gridColumnGrouping.js\";\n// This is the recurrence function that help writing `unwrapGroupingColumnModel()`\nconst recurrentUnwrapGroupingColumnModel = (columnGroupNode, parents, unwrappedGroupingModelToComplete) => {\n  if (isLeaf(columnGroupNode)) {\n    if (unwrappedGroupingModelToComplete[columnGroupNode.field] !== undefined) {\n      throw new Error([`MUI X: columnGroupingModel contains duplicated field`, `column field ${columnGroupNode.field} occurs two times in the grouping model:`, `- ${unwrappedGroupingModelToComplete[columnGroupNode.field].join(' > ')}`, `- ${parents.join(' > ')}`].join('\\n'));\n    }\n    unwrappedGroupingModelToComplete[columnGroupNode.field] = parents;\n    return;\n  }\n  const {\n    groupId,\n    children\n  } = columnGroupNode;\n  children.forEach(child => {\n    recurrentUnwrapGroupingColumnModel(child, [...parents, groupId], unwrappedGroupingModelToComplete);\n  });\n};\n\n/**\n * This is a function that provide for each column the array of its parents.\n * Parents are ordered from the root to the leaf.\n * @param columnGroupingModel The model such as provided in DataGrid props\n * @returns An object `{[field]: groupIds}` where `groupIds` is the parents of the column `field`\n */\nexport const unwrapGroupingColumnModel = columnGroupingModel => {\n  if (!columnGroupingModel) {\n    return {};\n  }\n  const unwrappedSubTree = {};\n  columnGroupingModel.forEach(columnGroupNode => {\n    recurrentUnwrapGroupingColumnModel(columnGroupNode, [], unwrappedSubTree);\n  });\n  return unwrappedSubTree;\n};\nexport const getColumnGroupsHeaderStructure = (orderedColumns, unwrappedGroupingModel, pinnedFields) => {\n  const getParents = field => unwrappedGroupingModel[field] ?? [];\n  const groupingHeaderStructure = [];\n  const maxDepth = Math.max(0, ...orderedColumns.map(field => getParents(field).length));\n  const haveSameParents = (field1, field2, depth) => {\n    const a = getParents(field1);\n    const b = getParents(field2);\n    for (let i = 0; i <= depth; i += 1) {\n      if (a[i] !== b[i]) {\n        return false;\n      }\n    }\n    return true;\n  };\n  const haveDifferentContainers = (field1, field2) => {\n    const left = pinnedFields?.left;\n    const right = pinnedFields?.right;\n    const inLeft1 = !!left?.includes(field1);\n    const inLeft2 = !!left?.includes(field2);\n    const inRight1 = !!right?.includes(field1);\n    const inRight2 = !!right?.includes(field2);\n    return inLeft1 !== inLeft2 || inRight1 !== inRight2;\n  };\n  for (let depth = 0; depth < maxDepth; depth += 1) {\n    const depthStructure = [];\n    for (let i = 0; i < orderedColumns.length; i += 1) {\n      const field = orderedColumns[i];\n      const groupId = getParents(field)[depth] ?? null;\n      if (depthStructure.length === 0) {\n        depthStructure.push({\n          columnFields: [field],\n          groupId\n        });\n        continue;\n      }\n      const lastGroup = depthStructure[depthStructure.length - 1];\n      const prevField = lastGroup.columnFields[lastGroup.columnFields.length - 1];\n      if (lastGroup.groupId !== groupId || !haveSameParents(prevField, field, depth) || haveDifferentContainers(prevField, field)) {\n        depthStructure.push({\n          columnFields: [field],\n          groupId\n        });\n      } else {\n        lastGroup.columnFields.push(field);\n      }\n    }\n    groupingHeaderStructure.push(depthStructure);\n  }\n  return groupingHeaderStructure;\n};"], "mappings": "AAAA,SAASA,MAAM,QAAQ,uCAAuC;AAC9D;AACA,MAAMC,kCAAkC,GAAGA,CAACC,eAAe,EAAEC,OAAO,EAAEC,gCAAgC,KAAK;EACzG,IAAIJ,MAAM,CAACE,eAAe,CAAC,EAAE;IAC3B,IAAIE,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,KAAKC,SAAS,EAAE;MACzE,MAAM,IAAIC,KAAK,CAAC,CAAC,sDAAsD,EAAE,gBAAgBL,eAAe,CAACG,KAAK,0CAA0C,EAAE,KAAKD,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,CAACG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,KAAKL,OAAO,CAACK,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/Q;IACAJ,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,GAAGF,OAAO;IACjE;EACF;EACA,MAAM;IACJM,OAAO;IACPC;EACF,CAAC,GAAGR,eAAe;EACnBQ,QAAQ,CAACC,OAAO,CAACC,KAAK,IAAI;IACxBX,kCAAkC,CAACW,KAAK,EAAE,CAAC,GAAGT,OAAO,EAAEM,OAAO,CAAC,EAAEL,gCAAgC,CAAC;EACpG,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,yBAAyB,GAAGC,mBAAmB,IAAI;EAC9D,IAAI,CAACA,mBAAmB,EAAE;IACxB,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3BD,mBAAmB,CAACH,OAAO,CAACT,eAAe,IAAI;IAC7CD,kCAAkC,CAACC,eAAe,EAAE,EAAE,EAAEa,gBAAgB,CAAC;EAC3E,CAAC,CAAC;EACF,OAAOA,gBAAgB;AACzB,CAAC;AACD,OAAO,MAAMC,8BAA8B,GAAGA,CAACC,cAAc,EAAEC,sBAAsB,EAAEC,YAAY,KAAK;EACtG,MAAMC,UAAU,GAAGf,KAAK,IAAIa,sBAAsB,CAACb,KAAK,CAAC,IAAI,EAAE;EAC/D,MAAMgB,uBAAuB,GAAG,EAAE;EAClC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAGP,cAAc,CAACQ,GAAG,CAACpB,KAAK,IAAIe,UAAU,CAACf,KAAK,CAAC,CAACqB,MAAM,CAAC,CAAC;EACtF,MAAMC,eAAe,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,KAAK;IACjD,MAAMC,CAAC,GAAGX,UAAU,CAACQ,MAAM,CAAC;IAC5B,MAAMI,CAAC,GAAGZ,UAAU,CAACS,MAAM,CAAC;IAC5B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIH,KAAK,EAAEG,CAAC,IAAI,CAAC,EAAE;MAClC,IAAIF,CAAC,CAACE,CAAC,CAAC,KAAKD,CAAC,CAACC,CAAC,CAAC,EAAE;QACjB,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD,MAAMC,uBAAuB,GAAGA,CAACN,MAAM,EAAEC,MAAM,KAAK;IAClD,MAAMM,IAAI,GAAGhB,YAAY,EAAEgB,IAAI;IAC/B,MAAMC,KAAK,GAAGjB,YAAY,EAAEiB,KAAK;IACjC,MAAMC,OAAO,GAAG,CAAC,CAACF,IAAI,EAAEG,QAAQ,CAACV,MAAM,CAAC;IACxC,MAAMW,OAAO,GAAG,CAAC,CAACJ,IAAI,EAAEG,QAAQ,CAACT,MAAM,CAAC;IACxC,MAAMW,QAAQ,GAAG,CAAC,CAACJ,KAAK,EAAEE,QAAQ,CAACV,MAAM,CAAC;IAC1C,MAAMa,QAAQ,GAAG,CAAC,CAACL,KAAK,EAAEE,QAAQ,CAACT,MAAM,CAAC;IAC1C,OAAOQ,OAAO,KAAKE,OAAO,IAAIC,QAAQ,KAAKC,QAAQ;EACrD,CAAC;EACD,KAAK,IAAIX,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGR,QAAQ,EAAEQ,KAAK,IAAI,CAAC,EAAE;IAChD,MAAMY,cAAc,GAAG,EAAE;IACzB,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,cAAc,CAACS,MAAM,EAAEO,CAAC,IAAI,CAAC,EAAE;MACjD,MAAM5B,KAAK,GAAGY,cAAc,CAACgB,CAAC,CAAC;MAC/B,MAAMxB,OAAO,GAAGW,UAAU,CAACf,KAAK,CAAC,CAACyB,KAAK,CAAC,IAAI,IAAI;MAChD,IAAIY,cAAc,CAAChB,MAAM,KAAK,CAAC,EAAE;QAC/BgB,cAAc,CAACC,IAAI,CAAC;UAClBC,YAAY,EAAE,CAACvC,KAAK,CAAC;UACrBI;QACF,CAAC,CAAC;QACF;MACF;MACA,MAAMoC,SAAS,GAAGH,cAAc,CAACA,cAAc,CAAChB,MAAM,GAAG,CAAC,CAAC;MAC3D,MAAMoB,SAAS,GAAGD,SAAS,CAACD,YAAY,CAACC,SAAS,CAACD,YAAY,CAAClB,MAAM,GAAG,CAAC,CAAC;MAC3E,IAAImB,SAAS,CAACpC,OAAO,KAAKA,OAAO,IAAI,CAACkB,eAAe,CAACmB,SAAS,EAAEzC,KAAK,EAAEyB,KAAK,CAAC,IAAII,uBAAuB,CAACY,SAAS,EAAEzC,KAAK,CAAC,EAAE;QAC3HqC,cAAc,CAACC,IAAI,CAAC;UAClBC,YAAY,EAAE,CAACvC,KAAK,CAAC;UACrBI;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLoC,SAAS,CAACD,YAAY,CAACD,IAAI,CAACtC,KAAK,CAAC;MACpC;IACF;IACAgB,uBAAuB,CAACsB,IAAI,CAACD,cAAc,CAAC;EAC9C;EACA,OAAOrB,uBAAuB;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}