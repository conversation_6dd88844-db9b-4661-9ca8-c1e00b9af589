{"ast": null, "code": "function intersection(firstArr, secondArr) {\n  const secondSet = new Set(secondArr);\n  return firstArr.filter(item => {\n    return secondSet.has(item);\n  });\n}\nexport { intersection };", "map": {"version": 3, "names": ["intersection", "firstArr", "secondArr", "secondSet", "Set", "filter", "item", "has"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/intersection.mjs"], "sourcesContent": ["function intersection(firstArr, secondArr) {\n    const secondSet = new Set(secondArr);\n    return firstArr.filter(item => {\n        return secondSet.has(item);\n    });\n}\n\nexport { intersection };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACvC,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAACF,SAAS,CAAC;EACpC,OAAOD,QAAQ,CAACI,MAAM,CAACC,IAAI,IAAI;IAC3B,OAAOH,SAAS,CAACI,GAAG,CAACD,IAAI,CAAC;EAC9B,CAAC,CAAC;AACN;AAEA,SAASN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}