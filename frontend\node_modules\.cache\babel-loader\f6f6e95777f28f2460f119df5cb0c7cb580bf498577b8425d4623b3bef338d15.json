{"ast": null, "code": "export const checkColumnVisibilityModelsSame = (a, b) => {\n  // Filter `false` values only, as `true` and not having a key are the same\n  const aFalseValues = new Set(Object.keys(a).filter(key => a[key] === false));\n  const bFalseValues = new Set(Object.keys(b).filter(key => b[key] === false));\n  if (aFalseValues.size !== bFalseValues.size) {\n    return false;\n  }\n  let result = true;\n  aFalseValues.forEach(key => {\n    if (!bFalseValues.has(key)) {\n      result = false;\n    }\n  });\n  return result;\n};\nexport const defaultSearchPredicate = (column, searchValue) => (column.headerName || column.field).toLowerCase().indexOf(searchValue) > -1;", "map": {"version": 3, "names": ["checkColumnVisibilityModelsSame", "a", "b", "aFalseValues", "Set", "Object", "keys", "filter", "key", "bFalseValues", "size", "result", "for<PERSON>ach", "has", "defaultSearchPredicate", "column", "searchValue", "headerName", "field", "toLowerCase", "indexOf"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/columnsManagement/utils.js"], "sourcesContent": ["export const checkColumnVisibilityModelsSame = (a, b) => {\n  // Filter `false` values only, as `true` and not having a key are the same\n  const aFalseValues = new Set(Object.keys(a).filter(key => a[key] === false));\n  const bFalseValues = new Set(Object.keys(b).filter(key => b[key] === false));\n  if (aFalseValues.size !== bFalseValues.size) {\n    return false;\n  }\n  let result = true;\n  aFalseValues.forEach(key => {\n    if (!bFalseValues.has(key)) {\n      result = false;\n    }\n  });\n  return result;\n};\nexport const defaultSearchPredicate = (column, searchValue) => (column.headerName || column.field).toLowerCase().indexOf(searchValue) > -1;"], "mappings": "AAAA,OAAO,MAAMA,+BAA+B,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EACvD;EACA,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACC,IAAI,CAACL,CAAC,CAAC,CAACM,MAAM,CAACC,GAAG,IAAIP,CAAC,CAACO,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;EAC5E,MAAMC,YAAY,GAAG,IAAIL,GAAG,CAACC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC,CAACK,MAAM,CAACC,GAAG,IAAIN,CAAC,CAACM,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;EAC5E,IAAIL,YAAY,CAACO,IAAI,KAAKD,YAAY,CAACC,IAAI,EAAE;IAC3C,OAAO,KAAK;EACd;EACA,IAAIC,MAAM,GAAG,IAAI;EACjBR,YAAY,CAACS,OAAO,CAACJ,<PERSON>G,IAAI;IAC1B,IAAI,CAACC,YAAY,CAACI,GAAG,CAACL,GAAG,CAAC,EAAE;MAC1BG,MAAM,GAAG,KAAK;IAChB;EACF,CAAC,CAAC;EACF,OAAOA,MAAM;AACf,CAAC;AACD,OAAO,MAAMG,sBAAsB,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAK,CAACD,MAAM,CAACE,UAAU,IAAIF,MAAM,CAACG,KAAK,EAAEC,WAAW,CAAC,CAAC,CAACC,OAAO,CAACJ,WAAW,CAAC,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}