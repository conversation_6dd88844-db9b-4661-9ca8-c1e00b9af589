{"ast": null, "code": "function isTypedArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\nexport { isTypedArray };", "map": {"version": 3, "names": ["isTypedArray", "x", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DataView"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isTypedArray.mjs"], "sourcesContent": ["function isTypedArray(x) {\n    return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n\nexport { isTypedArray };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,CAAC,EAAE;EACrB,OAAOC,WAAW,CAACC,MAAM,CAACF,CAAC,CAAC,IAAI,EAAEA,CAAC,YAAYG,QAAQ,CAAC;AAC5D;AAEA,SAASJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}