{"ast": null, "code": "function partition(arr, isInTruthy) {\n  const truthy = [];\n  const falsy = [];\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    if (isInTruthy(item)) {\n      truthy.push(item);\n    } else {\n      falsy.push(item);\n    }\n  }\n  return [truthy, falsy];\n}\nexport { partition };", "map": {"version": 3, "names": ["partition", "arr", "isInTruthy", "truthy", "falsy", "i", "length", "item", "push"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/partition.mjs"], "sourcesContent": ["function partition(arr, isInTruthy) {\n    const truthy = [];\n    const falsy = [];\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        if (isInTruthy(item)) {\n            truthy.push(item);\n        }\n        else {\n            falsy.push(item);\n        }\n    }\n    return [truthy, falsy];\n}\n\nexport { partition };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,GAAG,EAAEC,UAAU,EAAE;EAChC,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGN,GAAG,CAACI,CAAC,CAAC;IACnB,IAAIH,UAAU,CAACK,IAAI,CAAC,EAAE;MAClBJ,MAAM,CAACK,IAAI,CAACD,IAAI,CAAC;IACrB,CAAC,MACI;MACDH,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;IACpB;EACJ;EACA,OAAO,CAACJ,MAAM,EAAEC,KAAK,CAAC;AAC1B;AAEA,SAASJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}