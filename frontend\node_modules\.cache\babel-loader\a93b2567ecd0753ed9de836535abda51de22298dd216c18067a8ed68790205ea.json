{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridLogger, useGridSelector, useGridApiMethod } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPaginationMetaSelector } from \"./gridPaginationSelector.js\";\nexport const useGridPaginationMeta = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPaginationMeta');\n  const paginationMeta = useGridSelector(apiRef, gridPaginationMetaSelector);\n  apiRef.current.registerControlState({\n    stateId: 'paginationMeta',\n    propModel: props.paginationMeta,\n    propOnChange: props.onPaginationMetaChange,\n    stateSelector: gridPaginationMetaSelector,\n    changeEvent: 'paginationMetaChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setPaginationMeta = React.useCallback(newPaginationMeta => {\n    if (paginationMeta === newPaginationMeta) {\n      return;\n    }\n    logger.debug(\"Setting 'paginationMeta' to\", newPaginationMeta);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        meta: newPaginationMeta\n      })\n    }));\n  }, [apiRef, logger, paginationMeta]);\n  const paginationMetaApi = {\n    setPaginationMeta\n  };\n  useGridApiMethod(apiRef, paginationMetaApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedPaginationMeta = gridPaginationMetaSelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `paginationMeta` is controlled\n    props.paginationMeta != null ||\n    // Always export if the `paginationMeta` has been initialized\n    props.initialState?.pagination?.meta != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        meta: exportedPaginationMeta\n      })\n    });\n  }, [apiRef, props.paginationMeta, props.initialState?.pagination?.meta]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredPaginationMeta = context.stateToRestore.pagination?.meta ? context.stateToRestore.pagination.meta : gridPaginationMetaSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        meta: restoredPaginationMeta\n      })\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (props.paginationMeta) {\n      apiRef.current.setPaginationMeta(props.paginationMeta);\n    }\n  }, [apiRef, props.paginationMeta]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useGridLogger", "useGridSelector", "useGridApiMethod", "useGridRegisterPipeProcessor", "gridPaginationMetaSelector", "useGridPaginationMeta", "apiRef", "props", "logger", "paginationMeta", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onPaginationMetaChange", "stateSelector", "changeEvent", "setPaginationMeta", "useCallback", "newPaginationMeta", "debug", "setState", "state", "pagination", "meta", "paginationMetaApi", "stateExportPreProcessing", "prevState", "context", "exportedPaginationMeta", "shouldExportRowCount", "exportOnlyDirtyModels", "initialState", "stateRestorePreProcessing", "params", "restoredPaginationMeta", "stateToRestore", "useEffect"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/pagination/useGridPaginationMeta.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridLogger, useGridSelector, useGridApiMethod } from \"../../utils/index.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { gridPaginationMetaSelector } from \"./gridPaginationSelector.js\";\nexport const useGridPaginationMeta = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridPaginationMeta');\n  const paginationMeta = useGridSelector(apiRef, gridPaginationMetaSelector);\n  apiRef.current.registerControlState({\n    stateId: 'paginationMeta',\n    propModel: props.paginationMeta,\n    propOnChange: props.onPaginationMetaChange,\n    stateSelector: gridPaginationMetaSelector,\n    changeEvent: 'paginationMetaChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setPaginationMeta = React.useCallback(newPaginationMeta => {\n    if (paginationMeta === newPaginationMeta) {\n      return;\n    }\n    logger.debug(\"Setting 'paginationMeta' to\", newPaginationMeta);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        meta: newPaginationMeta\n      })\n    }));\n  }, [apiRef, logger, paginationMeta]);\n  const paginationMetaApi = {\n    setPaginationMeta\n  };\n  useGridApiMethod(apiRef, paginationMetaApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    const exportedPaginationMeta = gridPaginationMetaSelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `paginationMeta` is controlled\n    props.paginationMeta != null ||\n    // Always export if the `paginationMeta` has been initialized\n    props.initialState?.pagination?.meta != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        meta: exportedPaginationMeta\n      })\n    });\n  }, [apiRef, props.paginationMeta, props.initialState?.pagination?.meta]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    const restoredPaginationMeta = context.stateToRestore.pagination?.meta ? context.stateToRestore.pagination.meta : gridPaginationMetaSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        meta: restoredPaginationMeta\n      })\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (props.paginationMeta) {\n      apiRef.current.setPaginationMeta(props.paginationMeta);\n    }\n  }, [apiRef, props.paginationMeta]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,sBAAsB;AACvF,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,0BAA0B,QAAQ,6BAA6B;AACxE,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACtD,MAAMC,MAAM,GAAGR,aAAa,CAACM,MAAM,EAAE,uBAAuB,CAAC;EAC7D,MAAMG,cAAc,GAAGR,eAAe,CAACK,MAAM,EAAEF,0BAA0B,CAAC;EAC1EE,MAAM,CAACI,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAEN,KAAK,CAACE,cAAc;IAC/BK,YAAY,EAAEP,KAAK,CAACQ,sBAAsB;IAC1CC,aAAa,EAAEZ,0BAA0B;IACzCa,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAMC,iBAAiB,GAAGnB,KAAK,CAACoB,WAAW,CAACC,iBAAiB,IAAI;IAC/D,IAAIX,cAAc,KAAKW,iBAAiB,EAAE;MACxC;IACF;IACAZ,MAAM,CAACa,KAAK,CAAC,6BAA6B,EAAED,iBAAiB,CAAC;IAC9Dd,MAAM,CAACI,OAAO,CAACY,QAAQ,CAACC,KAAK,IAAIzB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;MACnDC,UAAU,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,CAACC,UAAU,EAAE;QACzCC,IAAI,EAAEL;MACR,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACd,MAAM,EAAEE,MAAM,EAAEC,cAAc,CAAC,CAAC;EACpC,MAAMiB,iBAAiB,GAAG;IACxBR;EACF,CAAC;EACDhB,gBAAgB,CAACI,MAAM,EAAEoB,iBAAiB,EAAE,QAAQ,CAAC;;EAErD;AACF;AACA;EACE,MAAMC,wBAAwB,GAAG5B,KAAK,CAACoB,WAAW,CAAC,CAACS,SAAS,EAAEC,OAAO,KAAK;IACzE,MAAMC,sBAAsB,GAAG1B,0BAA0B,CAACE,MAAM,CAAC;IACjE,MAAMyB,oBAAoB;IAC1B;IACA,CAACF,OAAO,CAACG,qBAAqB;IAC9B;IACAzB,KAAK,CAACE,cAAc,IAAI,IAAI;IAC5B;IACAF,KAAK,CAAC0B,YAAY,EAAET,UAAU,EAAEC,IAAI,IAAI,IAAI;IAC5C,IAAI,CAACM,oBAAoB,EAAE;MACzB,OAAOH,SAAS;IAClB;IACA,OAAO9B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,SAAS,EAAE;MAC7BJ,UAAU,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,SAAS,CAACJ,UAAU,EAAE;QAC7CC,IAAI,EAAEK;MACR,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxB,MAAM,EAAEC,KAAK,CAACE,cAAc,EAAEF,KAAK,CAAC0B,YAAY,EAAET,UAAU,EAAEC,IAAI,CAAC,CAAC;EACxE,MAAMS,yBAAyB,GAAGnC,KAAK,CAACoB,WAAW,CAAC,CAACgB,MAAM,EAAEN,OAAO,KAAK;IACvE,MAAMO,sBAAsB,GAAGP,OAAO,CAACQ,cAAc,CAACb,UAAU,EAAEC,IAAI,GAAGI,OAAO,CAACQ,cAAc,CAACb,UAAU,CAACC,IAAI,GAAGrB,0BAA0B,CAACE,MAAM,CAAC;IACpJA,MAAM,CAACI,OAAO,CAACY,QAAQ,CAACC,KAAK,IAAIzB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;MACnDC,UAAU,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,CAACC,UAAU,EAAE;QACzCC,IAAI,EAAEW;MACR,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAOD,MAAM;EACf,CAAC,EAAE,CAAC7B,MAAM,CAAC,CAAC;EACZH,4BAA4B,CAACG,MAAM,EAAE,aAAa,EAAEqB,wBAAwB,CAAC;EAC7ExB,4BAA4B,CAACG,MAAM,EAAE,cAAc,EAAE4B,yBAAyB,CAAC;;EAE/E;AACF;AACA;EACEnC,KAAK,CAACuC,SAAS,CAAC,MAAM;IACpB,IAAI/B,KAAK,CAACE,cAAc,EAAE;MACxBH,MAAM,CAACI,OAAO,CAACQ,iBAAiB,CAACX,KAAK,CAACE,cAAc,CAAC;IACxD;EACF,CAAC,EAAE,CAACH,MAAM,EAAEC,KAAK,CAACE,cAAc,CAAC,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}