{"ast": null, "code": "import { createSvgIcon as createSvgIconMaterial } from '@mui/material/utils';\nexport const createSvgIcon = createSvgIconMaterial;", "map": {"version": 3, "names": ["createSvgIcon", "createSvgIconMaterial"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/material/icons/createSvgIcon.js"], "sourcesContent": ["import { createSvgIcon as createSvgIconMaterial } from '@mui/material/utils';\nexport const createSvgIcon = createSvgIconMaterial;"], "mappings": "AAAA,SAASA,aAAa,IAAIC,qBAAqB,QAAQ,qBAAqB;AAC5E,OAAO,MAAMD,aAAa,GAAGC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}