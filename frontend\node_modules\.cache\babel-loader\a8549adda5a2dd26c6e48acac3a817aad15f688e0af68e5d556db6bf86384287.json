{"ast": null, "code": "const is = Object.is;\n\n/**\n * Fast shallow compare for objects.\n * @returns true if objects are equal.\n */\nexport function fastObjectShallowCompare(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (!(a instanceof Object) || !(b instanceof Object)) {\n    return false;\n  }\n  let aLength = 0;\n  let bLength = 0;\n\n  /* eslint-disable guard-for-in */\n  for (const key in a) {\n    aLength += 1;\n    if (!is(a[key], b[key])) {\n      return false;\n    }\n    if (!(key in b)) {\n      return false;\n    }\n  }\n\n  /* eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars */\n  for (const _ in b) {\n    bLength += 1;\n  }\n  return aLength === bLength;\n}", "map": {"version": 3, "names": ["is", "Object", "fastObjectShallowCompare", "a", "b", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "key", "_"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/fastObjectShallowCompare/fastObjectShallowCompare.js"], "sourcesContent": ["const is = Object.is;\n\n/**\n * Fast shallow compare for objects.\n * @returns true if objects are equal.\n */\nexport function fastObjectShallowCompare(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (!(a instanceof Object) || !(b instanceof Object)) {\n    return false;\n  }\n  let aLength = 0;\n  let bLength = 0;\n\n  /* eslint-disable guard-for-in */\n  for (const key in a) {\n    aLength += 1;\n    if (!is(a[key], b[key])) {\n      return false;\n    }\n    if (!(key in b)) {\n      return false;\n    }\n  }\n\n  /* eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars */\n  for (const _ in b) {\n    bLength += 1;\n  }\n  return aLength === bLength;\n}"], "mappings": "AAAA,MAAMA,EAAE,GAAGC,MAAM,CAACD,EAAE;;AAEpB;AACA;AACA;AACA;AACA,OAAO,SAASE,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7C,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;EACA,IAAI,EAAED,CAAC,YAAYF,MAAM,CAAC,IAAI,EAAEG,CAAC,YAAYH,MAAM,CAAC,EAAE;IACpD,OAAO,KAAK;EACd;EACA,IAAII,OAAO,GAAG,CAAC;EACf,IAAIC,OAAO,GAAG,CAAC;;EAEf;EACA,KAAK,MAAMC,GAAG,IAAIJ,CAAC,EAAE;IACnBE,OAAO,IAAI,CAAC;IACZ,IAAI,CAACL,EAAE,CAACG,CAAC,CAACI,GAAG,CAAC,EAAEH,CAAC,CAACG,GAAG,CAAC,CAAC,EAAE;MACvB,OAAO,KAAK;IACd;IACA,IAAI,EAAEA,GAAG,IAAIH,CAAC,CAAC,EAAE;MACf,OAAO,KAAK;IACd;EACF;;EAEA;EACA,KAAK,MAAMI,CAAC,IAAIJ,CAAC,EAAE;IACjBE,OAAO,IAAI,CAAC;EACd;EACA,OAAOD,OAAO,KAAKC,OAAO;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}