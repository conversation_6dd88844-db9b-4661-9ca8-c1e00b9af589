{"ast": null, "code": "import { difference } from './difference.mjs';\nfunction isSubset(superset, subset) {\n  return difference(subset, superset).length === 0;\n}\nexport { isSubset };", "map": {"version": 3, "names": ["difference", "isSubset", "superset", "subset", "length"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/isSubset.mjs"], "sourcesContent": ["import { difference } from './difference.mjs';\n\nfunction isSubset(superset, subset) {\n    return difference(subset, superset).length === 0;\n}\n\nexport { isSubset };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAE7C,SAASC,QAAQA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAChC,OAAOH,UAAU,CAACG,MAAM,EAAED,QAAQ,CAAC,CAACE,MAAM,KAAK,CAAC;AACpD;AAEA,SAASH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}