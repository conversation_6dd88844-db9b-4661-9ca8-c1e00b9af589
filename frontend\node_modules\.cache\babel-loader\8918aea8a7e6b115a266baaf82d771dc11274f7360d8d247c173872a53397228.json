{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridPanelContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") GridPanelContext.displayName = \"GridPanelContext\";\nexport function useGridPanelContext() {\n  const context = React.useContext(GridPanelContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context.');\n  }\n  return context;\n}\nexport function GridPanelContextProvider({\n  children\n}) {\n  const columnsPanelTriggerRef = React.useRef(null);\n  const filterPanelTriggerRef = React.useRef(null);\n  const aiAssistantPanelTriggerRef = React.useRef(null);\n  const value = React.useMemo(() => ({\n    columnsPanelTriggerRef,\n    filterPanelTriggerRef,\n    aiAssistantPanelTriggerRef\n  }), []);\n  return /*#__PURE__*/_jsx(GridPanelContext.Provider, {\n    value: value,\n    children: children\n  });\n}", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "GridPanelContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName", "useGridPanelContext", "context", "useContext", "Error", "GridPanelContextProvider", "children", "columnsPanelTriggerRef", "useRef", "filterPanelTriggerRef", "aiAssistantPanelTriggerRef", "value", "useMemo", "Provider"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/panel/GridPanelContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const GridPanelContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") GridPanelContext.displayName = \"GridPanelContext\";\nexport function useGridPanelContext() {\n  const context = React.useContext(GridPanelContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context.');\n  }\n  return context;\n}\nexport function GridPanelContextProvider({\n  children\n}) {\n  const columnsPanelTriggerRef = React.useRef(null);\n  const filterPanelTriggerRef = React.useRef(null);\n  const aiAssistantPanelTriggerRef = React.useRef(null);\n  const value = React.useMemo(() => ({\n    columnsPanelTriggerRef,\n    filterPanelTriggerRef,\n    aiAssistantPanelTriggerRef\n  }), []);\n  return /*#__PURE__*/_jsx(GridPanelContext.Provider, {\n    value: value,\n    children: children\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,gBAAgB,GAAG,aAAaH,KAAK,CAACI,aAAa,CAACC,SAAS,CAAC;AAC3E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEL,gBAAgB,CAACM,WAAW,GAAG,kBAAkB;AAC5F,OAAO,SAASC,mBAAmBA,CAAA,EAAG;EACpC,MAAMC,OAAO,GAAGX,KAAK,CAACY,UAAU,CAACT,gBAAgB,CAAC;EAClD,IAAIQ,OAAO,KAAKN,SAAS,EAAE;IACzB,MAAM,IAAIQ,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EACA,OAAOF,OAAO;AAChB;AACA,OAAO,SAASG,wBAAwBA,CAAC;EACvCC;AACF,CAAC,EAAE;EACD,MAAMC,sBAAsB,GAAGhB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EACjD,MAAMC,qBAAqB,GAAGlB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EAChD,MAAME,0BAA0B,GAAGnB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EACrD,MAAMG,KAAK,GAAGpB,KAAK,CAACqB,OAAO,CAAC,OAAO;IACjCL,sBAAsB;IACtBE,qBAAqB;IACrBC;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,OAAO,aAAajB,IAAI,CAACC,gBAAgB,CAACmB,QAAQ,EAAE;IAClDF,KAAK,EAAEA,KAAK;IACZL,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}