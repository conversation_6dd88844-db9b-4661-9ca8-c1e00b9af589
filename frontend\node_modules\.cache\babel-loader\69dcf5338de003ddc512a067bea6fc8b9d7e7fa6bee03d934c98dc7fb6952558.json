{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridPreferencePanelStateSelector } from \"../../hooks/features/preferencesPanel/gridPreferencePanelSelector.js\";\nimport { GridPreferencePanelsValue } from \"../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridPanelContext } from \"../panel/GridPanelContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @deprecated Use the {@link https://mui.com/x/react-data-grid/components/columns-panel/ Columns Panel Trigger} component instead. This component will be removed in a future major release.\n */\nconst GridToolbarColumnsButton = forwardRef(function GridToolbarColumnsButton(props, ref) {\n  const {\n    slotProps = {}\n  } = props;\n  const buttonProps = slotProps.button || {};\n  const tooltipProps = slotProps.tooltip || {};\n  const columnButtonId = useId();\n  const columnPanelId = useId();\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const {\n    columnsPanelTriggerRef\n  } = useGridPanelContext();\n  const preferencePanel = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const handleRef = useForkRef(ref, columnsPanelTriggerRef);\n  const showColumns = event => {\n    if (preferencePanel.open && preferencePanel.openedPanelValue === GridPreferencePanelsValue.columns) {\n      apiRef.current.hidePreferences();\n    } else {\n      apiRef.current.showPreferences(GridPreferencePanelsValue.columns, columnPanelId, columnButtonId);\n    }\n    buttonProps.onClick?.(event);\n  };\n\n  // Disable the button if the corresponding is disabled\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  const isOpen = preferencePanel.open && preferencePanel.panelId === columnPanelId;\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: apiRef.current.getLocaleText('toolbarColumnsLabel'),\n    enterDelay: 1000\n  }, rootProps.slotProps?.baseTooltip, tooltipProps, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n      id: columnButtonId,\n      size: \"small\",\n      \"aria-label\": apiRef.current.getLocaleText('toolbarColumnsLabel'),\n      \"aria-haspopup\": \"menu\",\n      \"aria-expanded\": isOpen,\n      \"aria-controls\": isOpen ? columnPanelId : undefined,\n      startIcon: /*#__PURE__*/_jsx(rootProps.slots.columnSelectorIcon, {})\n    }, rootProps.slotProps?.baseButton, buttonProps, {\n      onPointerUp: event => {\n        if (preferencePanel.open) {\n          event.stopPropagation();\n        }\n        buttonProps.onPointerUp?.(event);\n      },\n      onClick: showColumns,\n      ref: handleRef,\n      children: apiRef.current.getLocaleText('toolbarColumns')\n    }))\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridToolbarColumnsButton.displayName = \"GridToolbarColumnsButton\";\nprocess.env.NODE_ENV !== \"production\" ? GridToolbarColumnsButton.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.object\n} : void 0;\nexport { GridToolbarColumnsButton };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "useId", "forwardRef", "useForkRef", "useGridSelector", "gridPreferencePanelStateSelector", "GridPreferencePanelsValue", "useGridApiContext", "useGridRootProps", "useGridPanelContext", "jsx", "_jsx", "GridToolbarColumnsButton", "props", "ref", "slotProps", "buttonProps", "button", "tooltipProps", "tooltip", "columnButtonId", "columnPanelId", "apiRef", "rootProps", "columnsPanelTriggerRef", "preferencePanel", "handleRef", "showColumns", "event", "open", "openedPanelValue", "columns", "current", "hidePreferences", "showPreferences", "onClick", "disableColumnSelector", "isOpen", "panelId", "slots", "baseTooltip", "title", "getLocaleText", "enterDelay", "children", "baseButton", "id", "size", "undefined", "startIcon", "columnSelectorIcon", "onPointerUp", "stopPropagation", "process", "env", "NODE_ENV", "displayName", "propTypes", "object"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/toolbar/GridToolbarColumnsButton.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport useForkRef from '@mui/utils/useForkRef';\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridPreferencePanelStateSelector } from \"../../hooks/features/preferencesPanel/gridPreferencePanelSelector.js\";\nimport { GridPreferencePanelsValue } from \"../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridPanelContext } from \"../panel/GridPanelContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @deprecated Use the {@link https://mui.com/x/react-data-grid/components/columns-panel/ Columns Panel Trigger} component instead. This component will be removed in a future major release.\n */\nconst GridToolbarColumnsButton = forwardRef(function GridToolbarColumnsButton(props, ref) {\n  const {\n    slotProps = {}\n  } = props;\n  const buttonProps = slotProps.button || {};\n  const tooltipProps = slotProps.tooltip || {};\n  const columnButtonId = useId();\n  const columnPanelId = useId();\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const {\n    columnsPanelTriggerRef\n  } = useGridPanelContext();\n  const preferencePanel = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const handleRef = useForkRef(ref, columnsPanelTriggerRef);\n  const showColumns = event => {\n    if (preferencePanel.open && preferencePanel.openedPanelValue === GridPreferencePanelsValue.columns) {\n      apiRef.current.hidePreferences();\n    } else {\n      apiRef.current.showPreferences(GridPreferencePanelsValue.columns, columnPanelId, columnButtonId);\n    }\n    buttonProps.onClick?.(event);\n  };\n\n  // Disable the button if the corresponding is disabled\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  const isOpen = preferencePanel.open && preferencePanel.panelId === columnPanelId;\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: apiRef.current.getLocaleText('toolbarColumnsLabel'),\n    enterDelay: 1000\n  }, rootProps.slotProps?.baseTooltip, tooltipProps, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.baseButton, _extends({\n      id: columnButtonId,\n      size: \"small\",\n      \"aria-label\": apiRef.current.getLocaleText('toolbarColumnsLabel'),\n      \"aria-haspopup\": \"menu\",\n      \"aria-expanded\": isOpen,\n      \"aria-controls\": isOpen ? columnPanelId : undefined,\n      startIcon: /*#__PURE__*/_jsx(rootProps.slots.columnSelectorIcon, {})\n    }, rootProps.slotProps?.baseButton, buttonProps, {\n      onPointerUp: event => {\n        if (preferencePanel.open) {\n          event.stopPropagation();\n        }\n        buttonProps.onPointerUp?.(event);\n      },\n      onClick: showColumns,\n      ref: handleRef,\n      children: apiRef.current.getLocaleText('toolbarColumns')\n    }))\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridToolbarColumnsButton.displayName = \"GridToolbarColumnsButton\";\nprocess.env.NODE_ENV !== \"production\" ? GridToolbarColumnsButton.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.object\n} : void 0;\nexport { GridToolbarColumnsButton };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,UAAU,QAAQ,6BAA6B;AACxD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,gCAAgC,QAAQ,sEAAsE;AACvH,SAASC,yBAAyB,QAAQ,oEAAoE;AAC9G,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA,MAAMC,wBAAwB,GAAGV,UAAU,CAAC,SAASU,wBAAwBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxF,MAAM;IACJC,SAAS,GAAG,CAAC;EACf,CAAC,GAAGF,KAAK;EACT,MAAMG,WAAW,GAAGD,SAAS,CAACE,MAAM,IAAI,CAAC,CAAC;EAC1C,MAAMC,YAAY,GAAGH,SAAS,CAACI,OAAO,IAAI,CAAC,CAAC;EAC5C,MAAMC,cAAc,GAAGnB,KAAK,CAAC,CAAC;EAC9B,MAAMoB,aAAa,GAAGpB,KAAK,CAAC,CAAC;EAC7B,MAAMqB,MAAM,GAAGf,iBAAiB,CAAC,CAAC;EAClC,MAAMgB,SAAS,GAAGf,gBAAgB,CAAC,CAAC;EACpC,MAAM;IACJgB;EACF,CAAC,GAAGf,mBAAmB,CAAC,CAAC;EACzB,MAAMgB,eAAe,GAAGrB,eAAe,CAACkB,MAAM,EAAEjB,gCAAgC,CAAC;EACjF,MAAMqB,SAAS,GAAGvB,UAAU,CAACW,GAAG,EAAEU,sBAAsB,CAAC;EACzD,MAAMG,WAAW,GAAGC,KAAK,IAAI;IAC3B,IAAIH,eAAe,CAACI,IAAI,IAAIJ,eAAe,CAACK,gBAAgB,KAAKxB,yBAAyB,CAACyB,OAAO,EAAE;MAClGT,MAAM,CAACU,OAAO,CAACC,eAAe,CAAC,CAAC;IAClC,CAAC,MAAM;MACLX,MAAM,CAACU,OAAO,CAACE,eAAe,CAAC5B,yBAAyB,CAACyB,OAAO,EAAEV,aAAa,EAAED,cAAc,CAAC;IAClG;IACAJ,WAAW,CAACmB,OAAO,GAAGP,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,IAAIL,SAAS,CAACa,qBAAqB,EAAE;IACnC,OAAO,IAAI;EACb;EACA,MAAMC,MAAM,GAAGZ,eAAe,CAACI,IAAI,IAAIJ,eAAe,CAACa,OAAO,KAAKjB,aAAa;EAChF,OAAO,aAAaV,IAAI,CAACY,SAAS,CAACgB,KAAK,CAACC,WAAW,EAAE1C,QAAQ,CAAC;IAC7D2C,KAAK,EAAEnB,MAAM,CAACU,OAAO,CAACU,aAAa,CAAC,qBAAqB,CAAC;IAC1DC,UAAU,EAAE;EACd,CAAC,EAAEpB,SAAS,CAACR,SAAS,EAAEyB,WAAW,EAAEtB,YAAY,EAAE;IACjD0B,QAAQ,EAAE,aAAajC,IAAI,CAACY,SAAS,CAACgB,KAAK,CAACM,UAAU,EAAE/C,QAAQ,CAAC;MAC/DgD,EAAE,EAAE1B,cAAc;MAClB2B,IAAI,EAAE,OAAO;MACb,YAAY,EAAEzB,MAAM,CAACU,OAAO,CAACU,aAAa,CAAC,qBAAqB,CAAC;MACjE,eAAe,EAAE,MAAM;MACvB,eAAe,EAAEL,MAAM;MACvB,eAAe,EAAEA,MAAM,GAAGhB,aAAa,GAAG2B,SAAS;MACnDC,SAAS,EAAE,aAAatC,IAAI,CAACY,SAAS,CAACgB,KAAK,CAACW,kBAAkB,EAAE,CAAC,CAAC;IACrE,CAAC,EAAE3B,SAAS,CAACR,SAAS,EAAE8B,UAAU,EAAE7B,WAAW,EAAE;MAC/CmC,WAAW,EAAEvB,KAAK,IAAI;QACpB,IAAIH,eAAe,CAACI,IAAI,EAAE;UACxBD,KAAK,CAACwB,eAAe,CAAC,CAAC;QACzB;QACApC,WAAW,CAACmC,WAAW,GAAGvB,KAAK,CAAC;MAClC,CAAC;MACDO,OAAO,EAAER,WAAW;MACpBb,GAAG,EAAEY,SAAS;MACdkB,QAAQ,EAAEtB,MAAM,CAACU,OAAO,CAACU,aAAa,CAAC,gBAAgB;IACzD,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE3C,wBAAwB,CAAC4C,WAAW,GAAG,0BAA0B;AAC5GH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3C,wBAAwB,CAAC6C,SAAS,GAAG;EAC3E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACE1C,SAAS,EAAEf,SAAS,CAAC0D;AACvB,CAAC,GAAG,KAAK,CAAC;AACV,SAAS9C,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}