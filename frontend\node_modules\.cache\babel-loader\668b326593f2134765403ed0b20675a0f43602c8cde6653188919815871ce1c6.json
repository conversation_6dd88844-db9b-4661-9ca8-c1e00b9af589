{"ast": null, "code": "export * from \"./gridColumnGroupsSelector.js\";\nexport {};", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columnGrouping/index.js"], "sourcesContent": ["export * from \"./gridColumnGroupsSelector.js\";\nexport {};"], "mappings": "AAAA,cAAc,+BAA+B;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}