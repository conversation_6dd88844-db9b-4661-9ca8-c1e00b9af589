{"ast": null, "code": "import composeClasses from '@mui/utils/composeClasses';\nimport { getDataGridUtilityClass } from \"../constants/gridClasses.js\";\nexport function composeGridClasses(classes, slots) {\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n}", "map": {"version": 3, "names": ["composeClasses", "getDataGridUtilityClass", "composeGridClasses", "classes", "slots"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/composeGridClasses.js"], "sourcesContent": ["import composeClasses from '@mui/utils/composeClasses';\nimport { getDataGridUtilityClass } from \"../constants/gridClasses.js\";\nexport function composeGridClasses(classes, slots) {\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2BAA2B;AACtD,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,KAAK,EAAE;EACjD,OAAOJ,cAAc,CAACI,KAAK,EAAEH,uBAAuB,EAAEE,OAAO,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}