{"ast": null, "code": "class AbortError extends Error {\n  constructor(message = 'The operation was aborted') {\n    super(message);\n    this.name = 'AbortError';\n  }\n}\nexport { AbortError };", "map": {"version": 3, "names": ["AbortError", "Error", "constructor", "message", "name"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/error/AbortError.mjs"], "sourcesContent": ["class AbortError extends Error {\n    constructor(message = 'The operation was aborted') {\n        super(message);\n        this.name = 'AbortError';\n    }\n}\n\nexport { AbortError };\n"], "mappings": "AAAA,MAAMA,UAAU,SAASC,KAAK,CAAC;EAC3BC,WAAWA,CAACC,OAAO,GAAG,2BAA2B,EAAE;IAC/C,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,YAAY;EAC5B;AACJ;AAEA,SAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}