{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { isObjectEmpty } from '@mui/x-internals/isObjectEmpty';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridRowCountSelector, gridRowsLookupSelector, gridRowTreeSelector, gridRowGroupingNameSelector, gridRowTreeDepthsSelector, gridDataRowIdsSelector, gridRowMaximumTreeDepthSelector, gridRowGroupsToFetchSelector, gridRowNodeSelector, gridDataRowsSelector } from \"./gridRowsSelector.js\";\nimport { gridRowIdSelector } from \"../../core/gridPropsSelectors.js\";\nimport { useTimeout } from \"../../utils/useTimeout.js\";\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridSortedRowIdsSelector } from \"../sorting/gridSortingSelector.js\";\nimport { gridFilteredRowsLookupSelector } from \"../filter/gridFilterSelector.js\";\nimport { getTreeNodeDescendants, createRowsInternalCache, getRowsStateFromCache, isAutogeneratedRowNode, GRID_ROOT_GROUP_ID, GRID_ID_AUTOGENERATED, updateCacheWithNewRows, getTopLevelRowCount, getRowIdFromRowModel, computeRowsUpdates } from \"./gridRowsUtils.js\";\nimport { useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { GridStrategyGroup } from \"../../core/strategyProcessing/index.js\";\nimport { gridPivotActiveSelector } from \"../pivoting/index.js\";\nexport const rowsStateInitializer = (state, props, apiRef) => {\n  const isDataSourceAvailable = !!props.dataSource;\n  apiRef.current.caches.rows = createRowsInternalCache({\n    rows: isDataSourceAvailable ? [] : props.rows,\n    getRowId: props.getRowId,\n    loading: props.loading,\n    rowCount: props.rowCount\n  });\n  return _extends({}, state, {\n    rows: getRowsStateFromCache({\n      apiRef,\n      rowCountProp: props.rowCount,\n      loadingProp: isDataSourceAvailable ? true : props.loading,\n      previousTree: null,\n      previousTreeDepths: null\n    })\n  });\n};\nexport const useGridRows = (apiRef, props, configuration) => {\n  if (process.env.NODE_ENV !== 'production') {\n    try {\n      // Freeze the `rows` prop so developers have a fast failure if they try to use Array.prototype.push().\n      Object.freeze(props.rows);\n    } catch (error) {\n      // Sometimes, it's impossible to freeze, so we give up on it.\n    }\n  }\n  const logger = useGridLogger(apiRef, 'useGridRows');\n  const lastUpdateMs = React.useRef(Date.now());\n  const lastRowCount = React.useRef(props.rowCount);\n  const timeout = useTimeout();\n\n  // Get overridable methods from configuration\n  const {\n    setRowIndex\n  } = configuration.hooks.useGridRowsOverridableMethods(apiRef, props);\n  const getRow = React.useCallback(id => {\n    const model = gridRowsLookupSelector(apiRef)[id];\n    if (model) {\n      return model;\n    }\n    const node = gridRowNodeSelector(apiRef, id);\n    if (node && isAutogeneratedRowNode(node)) {\n      return {\n        [GRID_ID_AUTOGENERATED]: id\n      };\n    }\n    return null;\n  }, [apiRef]);\n  const getRowId = React.useCallback(row => gridRowIdSelector(apiRef, row), [apiRef]);\n  const throttledRowsChange = React.useCallback(({\n    cache,\n    throttle\n  }) => {\n    const run = () => {\n      lastUpdateMs.current = Date.now();\n      apiRef.current.setState(state => _extends({}, state, {\n        rows: getRowsStateFromCache({\n          apiRef,\n          rowCountProp: props.rowCount,\n          loadingProp: props.loading,\n          previousTree: gridRowTreeSelector(apiRef),\n          previousTreeDepths: gridRowTreeDepthsSelector(apiRef),\n          previousGroupsToFetch: gridRowGroupsToFetchSelector(apiRef)\n        })\n      }));\n      apiRef.current.publishEvent('rowsSet');\n    };\n    timeout.clear();\n    apiRef.current.caches.rows = cache;\n    if (!throttle) {\n      run();\n      return;\n    }\n    const throttleRemainingTimeMs = props.throttleRowsMs - (Date.now() - lastUpdateMs.current);\n    if (throttleRemainingTimeMs > 0) {\n      timeout.start(throttleRemainingTimeMs, run);\n      return;\n    }\n    run();\n  }, [props.throttleRowsMs, props.rowCount, props.loading, apiRef, timeout]);\n\n  /**\n   * API METHODS\n   */\n  const setRows = React.useCallback(rows => {\n    logger.debug(`Updating all rows, new length ${rows.length}`);\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotRows(rows, false);\n      return;\n    }\n    const cache = createRowsInternalCache({\n      rows,\n      getRowId: props.getRowId,\n      loading: props.loading,\n      rowCount: props.rowCount\n    });\n    const prevCache = apiRef.current.caches.rows;\n    cache.rowsBeforePartialUpdates = prevCache.rowsBeforePartialUpdates;\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [logger, props.getRowId, props.loading, props.rowCount, throttledRowsChange, apiRef]);\n  const updateRows = React.useCallback(updates => {\n    if (props.signature === GridSignature.DataGrid && updates.length > 1) {\n      throw new Error(['MUI X: You cannot update several rows at once in `apiRef.current.updateRows` on the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotRows(updates);\n      return;\n    }\n    const nonPinnedRowsUpdates = computeRowsUpdates(apiRef, updates, props.getRowId);\n    const cache = updateCacheWithNewRows({\n      updates: nonPinnedRowsUpdates,\n      getRowId: props.getRowId,\n      previousCache: apiRef.current.caches.rows\n    });\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [props.signature, props.getRowId, throttledRowsChange, apiRef]);\n  const updateNestedRows = React.useCallback((updates, groupKeys) => {\n    const nonPinnedRowsUpdates = computeRowsUpdates(apiRef, updates, props.getRowId);\n    const cache = updateCacheWithNewRows({\n      updates: nonPinnedRowsUpdates,\n      getRowId: props.getRowId,\n      previousCache: apiRef.current.caches.rows,\n      groupKeys: groupKeys ?? []\n    });\n    throttledRowsChange({\n      cache,\n      throttle: false\n    });\n  }, [props.getRowId, throttledRowsChange, apiRef]);\n  const setLoading = React.useCallback(loading => {\n    logger.debug(`Setting loading to ${loading}`);\n    apiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        loading\n      })\n    }));\n    apiRef.current.caches.rows.loadingPropBeforePartialUpdates = loading;\n  }, [apiRef, logger]);\n  const getRowModels = React.useCallback(() => {\n    const dataRows = gridDataRowIdsSelector(apiRef);\n    const idRowsLookup = gridRowsLookupSelector(apiRef);\n    return new Map(dataRows.map(id => [id, idRowsLookup[id] ?? {}]));\n  }, [apiRef]);\n  const getRowsCount = React.useCallback(() => gridRowCountSelector(apiRef), [apiRef]);\n  const getAllRowIds = React.useCallback(() => gridDataRowIdsSelector(apiRef), [apiRef]);\n  const getRowIndexRelativeToVisibleRows = React.useCallback(id => {\n    const {\n      rowIdToIndexMap\n    } = getVisibleRows(apiRef);\n    return rowIdToIndexMap.get(id);\n  }, [apiRef]);\n  const setRowChildrenExpansion = React.useCallback((id, isExpanded) => {\n    const currentNode = gridRowNodeSelector(apiRef, id);\n    if (!currentNode) {\n      throw new Error(`MUI X: No row with id #${id} found.`);\n    }\n    if (currentNode.type !== 'group') {\n      throw new Error('MUI X: Only group nodes can be expanded or collapsed.');\n    }\n    const newNode = _extends({}, currentNode, {\n      childrenExpanded: isExpanded\n    });\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [id]: newNode\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowExpansionChange', newNode);\n  }, [apiRef]);\n  const getRowNode = React.useCallback(id => gridRowNodeSelector(apiRef, id) ?? null, [apiRef]);\n  const getRowGroupChildren = React.useCallback(({\n    skipAutoGeneratedRows = true,\n    groupId,\n    applySorting,\n    applyFiltering,\n    directChildrenOnly = false\n  }) => {\n    const tree = gridRowTreeSelector(apiRef);\n    let children;\n    if (applySorting) {\n      const groupNode = tree[groupId];\n      if (!groupNode) {\n        return [];\n      }\n      const sortedRowIds = gridSortedRowIdsSelector(apiRef);\n      children = [];\n      const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;\n      for (let index = startIndex; index < sortedRowIds.length && (directChildrenOnly ? tree[sortedRowIds[index]].depth === groupNode.depth + 1 : tree[sortedRowIds[index]].depth > groupNode.depth); index += 1) {\n        const id = sortedRowIds[index];\n        if (!skipAutoGeneratedRows || !isAutogeneratedRowNode(tree[id])) {\n          children.push(id);\n        }\n      }\n    } else {\n      children = getTreeNodeDescendants(tree, groupId, skipAutoGeneratedRows, directChildrenOnly);\n    }\n    if (applyFiltering) {\n      const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n      children = isObjectEmpty(filteredRowsLookup) ? children : children.filter(childId => filteredRowsLookup[childId] !== false);\n    }\n    return children;\n  }, [apiRef]);\n  const replaceRows = React.useCallback((firstRowToRender, newRows) => {\n    if (props.signature === GridSignature.DataGrid && newRows.length > 1) {\n      throw new Error(['MUI X: You cannot replace rows using `apiRef.current.unstable_replaceRows` on the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    if (newRows.length === 0) {\n      return;\n    }\n    const treeDepth = gridRowMaximumTreeDepthSelector(apiRef);\n    if (treeDepth > 1) {\n      throw new Error('`apiRef.current.unstable_replaceRows` is not compatible with tree data and row grouping');\n    }\n    const tree = _extends({}, gridRowTreeSelector(apiRef));\n    const dataRowIdToModelLookup = _extends({}, gridRowsLookupSelector(apiRef));\n    const rootGroup = tree[GRID_ROOT_GROUP_ID];\n    const rootGroupChildren = [...rootGroup.children];\n    const seenIds = new Set();\n    for (let i = 0; i < newRows.length; i += 1) {\n      const rowModel = newRows[i];\n      const rowId = getRowIdFromRowModel(rowModel, props.getRowId, 'A row was provided without id when calling replaceRows().');\n      const [removedRowId] = rootGroupChildren.splice(firstRowToRender + i, 1, rowId);\n      if (!seenIds.has(removedRowId)) {\n        delete dataRowIdToModelLookup[removedRowId];\n        delete tree[removedRowId];\n      }\n      const rowTreeNodeConfig = {\n        id: rowId,\n        depth: 0,\n        parent: GRID_ROOT_GROUP_ID,\n        type: 'leaf',\n        groupingKey: null\n      };\n      dataRowIdToModelLookup[rowId] = rowModel;\n      tree[rowId] = rowTreeNodeConfig;\n      seenIds.add(rowId);\n    }\n    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n      children: rootGroupChildren\n    });\n\n    // Removes potential remaining skeleton rows from the dataRowIds.\n    const dataRowIds = rootGroupChildren.filter(childId => tree[childId]?.type === 'leaf');\n    apiRef.current.caches.rows.dataRowIdToModelLookup = dataRowIdToModelLookup;\n    apiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        loading: props.loading,\n        totalRowCount: Math.max(props.rowCount || 0, rootGroupChildren.length),\n        dataRowIdToModelLookup,\n        dataRowIds,\n        tree\n      })\n    }));\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, props.signature, props.getRowId, props.loading, props.rowCount]);\n  const rowApi = {\n    getRow,\n    setLoading,\n    getRowId,\n    getRowModels,\n    getRowsCount,\n    getAllRowIds,\n    setRows,\n    updateRows,\n    getRowNode,\n    getRowIndexRelativeToVisibleRows,\n    unstable_replaceRows: replaceRows\n  };\n  const rowProApi = {\n    setRowIndex,\n    setRowChildrenExpansion,\n    getRowGroupChildren\n  };\n  const rowProPrivateApi = {\n    updateNestedRows\n  };\n\n  /**\n   * EVENTS\n   */\n  const groupRows = React.useCallback(() => {\n    logger.info(`Row grouping pre-processing have changed, regenerating the row tree`);\n    let cache;\n    if (apiRef.current.caches.rows.rowsBeforePartialUpdates === props.rows) {\n      // The `props.rows` did not change since the last row grouping\n      // We can use the current rows cache which contains the partial updates done recently.\n      cache = _extends({}, apiRef.current.caches.rows, {\n        updates: {\n          type: 'full',\n          rows: gridDataRowIdsSelector(apiRef)\n        }\n      });\n    } else {\n      // The `props.rows` has changed since the last row grouping\n      // We must use the new `props.rows` on the new grouping\n      // This occurs because this event is triggered before the `useEffect` on the rows when both the grouping pre-processing and the rows changes on the same render\n      cache = createRowsInternalCache({\n        rows: props.rows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      });\n    }\n    throttledRowsChange({\n      cache,\n      throttle: false\n    });\n  }, [logger, apiRef, props.rows, props.getRowId, props.loading, props.rowCount, throttledRowsChange]);\n  const previousDataSource = useLazyRef(() => props.dataSource);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (props.dataSource && props.dataSource !== previousDataSource.current) {\n      previousDataSource.current = props.dataSource;\n      return;\n    }\n    if (methodName === 'rowTreeCreation') {\n      groupRows();\n    }\n  }, [groupRows, previousDataSource, props.dataSource]);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    // `rowTreeCreation` is the only processor ran when `strategyAvailabilityChange` is fired.\n    // All the other processors listen to `rowsSet` which will be published by the `groupRows` method below.\n    if (apiRef.current.getActiveStrategy(GridStrategyGroup.RowTree) !== gridRowGroupingNameSelector(apiRef)) {\n      groupRows();\n    }\n  }, [apiRef, groupRows]);\n  useGridEvent(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridEvent(apiRef, 'strategyAvailabilityChange', handleStrategyActivityChange);\n\n  /**\n   * APPLIERS\n   */\n  const applyHydrateRowsProcessor = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const response = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n        tree: gridRowTreeSelector(apiRef),\n        treeDepths: gridRowTreeDepthsSelector(apiRef),\n        dataRowIds: gridDataRowIdsSelector(apiRef),\n        dataRowIdToModelLookup: gridRowsLookupSelector(apiRef)\n      });\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, response, {\n          totalTopLevelRowCount: getTopLevelRowCount({\n            tree: response.tree,\n            rowCountProp: props.rowCount\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, props.rowCount]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateRows', applyHydrateRowsProcessor);\n  useGridApiMethod(apiRef, rowApi, 'public');\n  useGridApiMethod(apiRef, rowProApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n  useGridApiMethod(apiRef, rowProPrivateApi, 'private');\n\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridRows`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    let isRowCountPropUpdated = false;\n    if (props.rowCount !== lastRowCount.current) {\n      isRowCountPropUpdated = true;\n      lastRowCount.current = props.rowCount;\n    }\n    const currentRows = props.dataSource ? gridDataRowsSelector(apiRef) : props.rows;\n    const areNewRowsAlreadyInState = apiRef.current.caches.rows.rowsBeforePartialUpdates === currentRows;\n    const isNewLoadingAlreadyInState = apiRef.current.caches.rows.loadingPropBeforePartialUpdates === props.loading;\n    const isNewRowCountAlreadyInState = apiRef.current.caches.rows.rowCountPropBeforePartialUpdates === props.rowCount;\n\n    // The new rows have already been applied (most likely in the `'rowGroupsPreProcessingChange'` listener)\n    if (areNewRowsAlreadyInState) {\n      // If the loading prop has changed, we need to update its value in the state because it won't be done by `throttledRowsChange`\n      if (!isNewLoadingAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            loading: props.loading\n          })\n        }));\n        apiRef.current.caches.rows.loadingPropBeforePartialUpdates = props.loading;\n      }\n      if (!isNewRowCountAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            totalRowCount: Math.max(props.rowCount || 0, state.rows.totalRowCount),\n            totalTopLevelRowCount: Math.max(props.rowCount || 0, state.rows.totalTopLevelRowCount)\n          })\n        }));\n        apiRef.current.caches.rows.rowCountPropBeforePartialUpdates = props.rowCount;\n      }\n      if (!isRowCountPropUpdated) {\n        return;\n      }\n    }\n    logger.debug(`Updating all rows, new length ${currentRows?.length}`);\n    throttledRowsChange({\n      cache: createRowsInternalCache({\n        rows: currentRows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      }),\n      throttle: false\n    });\n  }, [props.rows, props.rowCount, props.getRowId, props.loading, props.dataSource, logger, throttledRowsChange, apiRef]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useLazyRef", "isObjectEmpty", "useGridApiMethod", "useGridLogger", "gridRowCountSelector", "gridRowsLookupSelector", "gridRowTreeSelector", "gridRowGroupingNameSelector", "gridRowTreeDepthsSelector", "gridDataRowIdsSelector", "gridRowMaximumTreeDepthSelector", "gridRowGroupsToFetchSelector", "gridRowNodeSelector", "gridDataRowsSelector", "gridRowIdSelector", "useTimeout", "GridSignature", "useGridEvent", "getVisibleRows", "gridSortedRowIdsSelector", "gridFilteredRowsLookupSelector", "getTreeNodeDescendants", "createRowsInternalCache", "getRowsStateFromCache", "isAutogeneratedRowNode", "GRID_ROOT_GROUP_ID", "GRID_ID_AUTOGENERATED", "updateCacheWithNewRows", "getTopLevelRowCount", "getRowIdFromRowModel", "computeRowsUpdates", "useGridRegisterPipeApplier", "GridStrategyGroup", "gridPivotActiveSelector", "rowsStateInitializer", "state", "props", "apiRef", "isDataSourceAvailable", "dataSource", "current", "caches", "rows", "getRowId", "loading", "rowCount", "rowCountProp", "loadingProp", "previousTree", "previousTreeDepths", "useGridRows", "configuration", "process", "env", "NODE_ENV", "Object", "freeze", "error", "logger", "lastUpdateMs", "useRef", "Date", "now", "lastRowCount", "timeout", "setRowIndex", "hooks", "useGridRowsOverridableMethods", "getRow", "useCallback", "id", "model", "node", "row", "throttledRowsChange", "cache", "throttle", "run", "setState", "previousGroupsToFetch", "publishEvent", "clear", "throttleRemainingTimeMs", "throttleRowsMs", "start", "setRows", "debug", "length", "updateNonPivotRows", "prevCache", "rowsBeforePartialUpdates", "updateRows", "updates", "signature", "DataGrid", "Error", "join", "nonPinnedRowsUpdates", "previousCache", "updateNestedRows", "groupKeys", "setLoading", "loadingPropBeforePartialUpdates", "getRowModels", "dataRows", "idRowsLookup", "Map", "map", "getRowsCount", "getAllRowIds", "getRowIndexRelativeToVisibleRows", "rowIdToIndexMap", "get", "setRowChildrenExpansion", "isExpanded", "currentNode", "type", "newNode", "childrenExpanded", "tree", "getRowNode", "getRowGroupChildren", "skipAutoGeneratedRows", "groupId", "applySorting", "applyFiltering", "directChildrenOnly", "children", "groupNode", "sortedRowIds", "startIndex", "findIndex", "index", "depth", "push", "filteredRowsLookup", "filter", "childId", "replaceRows", "firstRowToRender", "newRows", "<PERSON><PERSON><PERSON><PERSON>", "dataRowIdToModelLookup", "rootGroup", "rootGroupChildren", "seenIds", "Set", "i", "rowModel", "rowId", "removedRowId", "splice", "has", "rowTreeNodeConfig", "parent", "grouping<PERSON>ey", "add", "dataRowIds", "totalRowCount", "Math", "max", "row<PERSON><PERSON>", "unstable_replaceRows", "rowProApi", "rowProPrivateApi", "groupRows", "info", "previousDataSource", "handleStrategyProcessorChange", "methodName", "handleStrategyActivityChange", "getActiveStrategy", "RowTree", "applyHydrateRowsProcessor", "response", "unstable_applyPipeProcessors", "treeDepths", "totalTopLevelRowCount", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "isRowCountPropUpdated", "currentRows", "areNewRowsAlreadyInState", "isNewLoadingAlreadyInState", "isNewRowCountAlreadyInState", "rowCountPropBeforePartialUpdates"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rows/useGridRows.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport { isObjectEmpty } from '@mui/x-internals/isObjectEmpty';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridRowCountSelector, gridRowsLookupSelector, gridRowTreeSelector, gridRowGroupingNameSelector, gridRowTreeDepthsSelector, gridDataRowIdsSelector, gridRowMaximumTreeDepthSelector, gridRowGroupsToFetchSelector, gridRowNodeSelector, gridDataRowsSelector } from \"./gridRowsSelector.js\";\nimport { gridRowIdSelector } from \"../../core/gridPropsSelectors.js\";\nimport { useTimeout } from \"../../utils/useTimeout.js\";\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridSortedRowIdsSelector } from \"../sorting/gridSortingSelector.js\";\nimport { gridFilteredRowsLookupSelector } from \"../filter/gridFilterSelector.js\";\nimport { getTreeNodeDescendants, createRowsInternalCache, getRowsStateFromCache, isAutogeneratedRowNode, GRID_ROOT_GROUP_ID, GRID_ID_AUTOGENERATED, updateCacheWithNewRows, getTopLevelRowCount, getRowIdFromRowModel, computeRowsUpdates } from \"./gridRowsUtils.js\";\nimport { useGridRegisterPipeApplier } from \"../../core/pipeProcessing/index.js\";\nimport { GridStrategyGroup } from \"../../core/strategyProcessing/index.js\";\nimport { gridPivotActiveSelector } from \"../pivoting/index.js\";\nexport const rowsStateInitializer = (state, props, apiRef) => {\n  const isDataSourceAvailable = !!props.dataSource;\n  apiRef.current.caches.rows = createRowsInternalCache({\n    rows: isDataSourceAvailable ? [] : props.rows,\n    getRowId: props.getRowId,\n    loading: props.loading,\n    rowCount: props.rowCount\n  });\n  return _extends({}, state, {\n    rows: getRowsStateFromCache({\n      apiRef,\n      rowCountProp: props.rowCount,\n      loadingProp: isDataSourceAvailable ? true : props.loading,\n      previousTree: null,\n      previousTreeDepths: null\n    })\n  });\n};\nexport const useGridRows = (apiRef, props, configuration) => {\n  if (process.env.NODE_ENV !== 'production') {\n    try {\n      // Freeze the `rows` prop so developers have a fast failure if they try to use Array.prototype.push().\n      Object.freeze(props.rows);\n    } catch (error) {\n      // Sometimes, it's impossible to freeze, so we give up on it.\n    }\n  }\n  const logger = useGridLogger(apiRef, 'useGridRows');\n  const lastUpdateMs = React.useRef(Date.now());\n  const lastRowCount = React.useRef(props.rowCount);\n  const timeout = useTimeout();\n\n  // Get overridable methods from configuration\n  const {\n    setRowIndex\n  } = configuration.hooks.useGridRowsOverridableMethods(apiRef, props);\n  const getRow = React.useCallback(id => {\n    const model = gridRowsLookupSelector(apiRef)[id];\n    if (model) {\n      return model;\n    }\n    const node = gridRowNodeSelector(apiRef, id);\n    if (node && isAutogeneratedRowNode(node)) {\n      return {\n        [GRID_ID_AUTOGENERATED]: id\n      };\n    }\n    return null;\n  }, [apiRef]);\n  const getRowId = React.useCallback(row => gridRowIdSelector(apiRef, row), [apiRef]);\n  const throttledRowsChange = React.useCallback(({\n    cache,\n    throttle\n  }) => {\n    const run = () => {\n      lastUpdateMs.current = Date.now();\n      apiRef.current.setState(state => _extends({}, state, {\n        rows: getRowsStateFromCache({\n          apiRef,\n          rowCountProp: props.rowCount,\n          loadingProp: props.loading,\n          previousTree: gridRowTreeSelector(apiRef),\n          previousTreeDepths: gridRowTreeDepthsSelector(apiRef),\n          previousGroupsToFetch: gridRowGroupsToFetchSelector(apiRef)\n        })\n      }));\n      apiRef.current.publishEvent('rowsSet');\n    };\n    timeout.clear();\n    apiRef.current.caches.rows = cache;\n    if (!throttle) {\n      run();\n      return;\n    }\n    const throttleRemainingTimeMs = props.throttleRowsMs - (Date.now() - lastUpdateMs.current);\n    if (throttleRemainingTimeMs > 0) {\n      timeout.start(throttleRemainingTimeMs, run);\n      return;\n    }\n    run();\n  }, [props.throttleRowsMs, props.rowCount, props.loading, apiRef, timeout]);\n\n  /**\n   * API METHODS\n   */\n  const setRows = React.useCallback(rows => {\n    logger.debug(`Updating all rows, new length ${rows.length}`);\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotRows(rows, false);\n      return;\n    }\n    const cache = createRowsInternalCache({\n      rows,\n      getRowId: props.getRowId,\n      loading: props.loading,\n      rowCount: props.rowCount\n    });\n    const prevCache = apiRef.current.caches.rows;\n    cache.rowsBeforePartialUpdates = prevCache.rowsBeforePartialUpdates;\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [logger, props.getRowId, props.loading, props.rowCount, throttledRowsChange, apiRef]);\n  const updateRows = React.useCallback(updates => {\n    if (props.signature === GridSignature.DataGrid && updates.length > 1) {\n      throw new Error(['MUI X: You cannot update several rows at once in `apiRef.current.updateRows` on the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    if (gridPivotActiveSelector(apiRef)) {\n      apiRef.current.updateNonPivotRows(updates);\n      return;\n    }\n    const nonPinnedRowsUpdates = computeRowsUpdates(apiRef, updates, props.getRowId);\n    const cache = updateCacheWithNewRows({\n      updates: nonPinnedRowsUpdates,\n      getRowId: props.getRowId,\n      previousCache: apiRef.current.caches.rows\n    });\n    throttledRowsChange({\n      cache,\n      throttle: true\n    });\n  }, [props.signature, props.getRowId, throttledRowsChange, apiRef]);\n  const updateNestedRows = React.useCallback((updates, groupKeys) => {\n    const nonPinnedRowsUpdates = computeRowsUpdates(apiRef, updates, props.getRowId);\n    const cache = updateCacheWithNewRows({\n      updates: nonPinnedRowsUpdates,\n      getRowId: props.getRowId,\n      previousCache: apiRef.current.caches.rows,\n      groupKeys: groupKeys ?? []\n    });\n    throttledRowsChange({\n      cache,\n      throttle: false\n    });\n  }, [props.getRowId, throttledRowsChange, apiRef]);\n  const setLoading = React.useCallback(loading => {\n    logger.debug(`Setting loading to ${loading}`);\n    apiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        loading\n      })\n    }));\n    apiRef.current.caches.rows.loadingPropBeforePartialUpdates = loading;\n  }, [apiRef, logger]);\n  const getRowModels = React.useCallback(() => {\n    const dataRows = gridDataRowIdsSelector(apiRef);\n    const idRowsLookup = gridRowsLookupSelector(apiRef);\n    return new Map(dataRows.map(id => [id, idRowsLookup[id] ?? {}]));\n  }, [apiRef]);\n  const getRowsCount = React.useCallback(() => gridRowCountSelector(apiRef), [apiRef]);\n  const getAllRowIds = React.useCallback(() => gridDataRowIdsSelector(apiRef), [apiRef]);\n  const getRowIndexRelativeToVisibleRows = React.useCallback(id => {\n    const {\n      rowIdToIndexMap\n    } = getVisibleRows(apiRef);\n    return rowIdToIndexMap.get(id);\n  }, [apiRef]);\n  const setRowChildrenExpansion = React.useCallback((id, isExpanded) => {\n    const currentNode = gridRowNodeSelector(apiRef, id);\n    if (!currentNode) {\n      throw new Error(`MUI X: No row with id #${id} found.`);\n    }\n    if (currentNode.type !== 'group') {\n      throw new Error('MUI X: Only group nodes can be expanded or collapsed.');\n    }\n    const newNode = _extends({}, currentNode, {\n      childrenExpanded: isExpanded\n    });\n    apiRef.current.setState(state => {\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [id]: newNode\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowExpansionChange', newNode);\n  }, [apiRef]);\n  const getRowNode = React.useCallback(id => gridRowNodeSelector(apiRef, id) ?? null, [apiRef]);\n  const getRowGroupChildren = React.useCallback(({\n    skipAutoGeneratedRows = true,\n    groupId,\n    applySorting,\n    applyFiltering,\n    directChildrenOnly = false\n  }) => {\n    const tree = gridRowTreeSelector(apiRef);\n    let children;\n    if (applySorting) {\n      const groupNode = tree[groupId];\n      if (!groupNode) {\n        return [];\n      }\n      const sortedRowIds = gridSortedRowIdsSelector(apiRef);\n      children = [];\n      const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;\n      for (let index = startIndex; index < sortedRowIds.length && (directChildrenOnly ? tree[sortedRowIds[index]].depth === groupNode.depth + 1 : tree[sortedRowIds[index]].depth > groupNode.depth); index += 1) {\n        const id = sortedRowIds[index];\n        if (!skipAutoGeneratedRows || !isAutogeneratedRowNode(tree[id])) {\n          children.push(id);\n        }\n      }\n    } else {\n      children = getTreeNodeDescendants(tree, groupId, skipAutoGeneratedRows, directChildrenOnly);\n    }\n    if (applyFiltering) {\n      const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n      children = isObjectEmpty(filteredRowsLookup) ? children : children.filter(childId => filteredRowsLookup[childId] !== false);\n    }\n    return children;\n  }, [apiRef]);\n  const replaceRows = React.useCallback((firstRowToRender, newRows) => {\n    if (props.signature === GridSignature.DataGrid && newRows.length > 1) {\n      throw new Error(['MUI X: You cannot replace rows using `apiRef.current.unstable_replaceRows` on the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n    }\n    if (newRows.length === 0) {\n      return;\n    }\n    const treeDepth = gridRowMaximumTreeDepthSelector(apiRef);\n    if (treeDepth > 1) {\n      throw new Error('`apiRef.current.unstable_replaceRows` is not compatible with tree data and row grouping');\n    }\n    const tree = _extends({}, gridRowTreeSelector(apiRef));\n    const dataRowIdToModelLookup = _extends({}, gridRowsLookupSelector(apiRef));\n    const rootGroup = tree[GRID_ROOT_GROUP_ID];\n    const rootGroupChildren = [...rootGroup.children];\n    const seenIds = new Set();\n    for (let i = 0; i < newRows.length; i += 1) {\n      const rowModel = newRows[i];\n      const rowId = getRowIdFromRowModel(rowModel, props.getRowId, 'A row was provided without id when calling replaceRows().');\n      const [removedRowId] = rootGroupChildren.splice(firstRowToRender + i, 1, rowId);\n      if (!seenIds.has(removedRowId)) {\n        delete dataRowIdToModelLookup[removedRowId];\n        delete tree[removedRowId];\n      }\n      const rowTreeNodeConfig = {\n        id: rowId,\n        depth: 0,\n        parent: GRID_ROOT_GROUP_ID,\n        type: 'leaf',\n        groupingKey: null\n      };\n      dataRowIdToModelLookup[rowId] = rowModel;\n      tree[rowId] = rowTreeNodeConfig;\n      seenIds.add(rowId);\n    }\n    tree[GRID_ROOT_GROUP_ID] = _extends({}, rootGroup, {\n      children: rootGroupChildren\n    });\n\n    // Removes potential remaining skeleton rows from the dataRowIds.\n    const dataRowIds = rootGroupChildren.filter(childId => tree[childId]?.type === 'leaf');\n    apiRef.current.caches.rows.dataRowIdToModelLookup = dataRowIdToModelLookup;\n    apiRef.current.setState(state => _extends({}, state, {\n      rows: _extends({}, state.rows, {\n        loading: props.loading,\n        totalRowCount: Math.max(props.rowCount || 0, rootGroupChildren.length),\n        dataRowIdToModelLookup,\n        dataRowIds,\n        tree\n      })\n    }));\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, props.signature, props.getRowId, props.loading, props.rowCount]);\n  const rowApi = {\n    getRow,\n    setLoading,\n    getRowId,\n    getRowModels,\n    getRowsCount,\n    getAllRowIds,\n    setRows,\n    updateRows,\n    getRowNode,\n    getRowIndexRelativeToVisibleRows,\n    unstable_replaceRows: replaceRows\n  };\n  const rowProApi = {\n    setRowIndex,\n    setRowChildrenExpansion,\n    getRowGroupChildren\n  };\n  const rowProPrivateApi = {\n    updateNestedRows\n  };\n\n  /**\n   * EVENTS\n   */\n  const groupRows = React.useCallback(() => {\n    logger.info(`Row grouping pre-processing have changed, regenerating the row tree`);\n    let cache;\n    if (apiRef.current.caches.rows.rowsBeforePartialUpdates === props.rows) {\n      // The `props.rows` did not change since the last row grouping\n      // We can use the current rows cache which contains the partial updates done recently.\n      cache = _extends({}, apiRef.current.caches.rows, {\n        updates: {\n          type: 'full',\n          rows: gridDataRowIdsSelector(apiRef)\n        }\n      });\n    } else {\n      // The `props.rows` has changed since the last row grouping\n      // We must use the new `props.rows` on the new grouping\n      // This occurs because this event is triggered before the `useEffect` on the rows when both the grouping pre-processing and the rows changes on the same render\n      cache = createRowsInternalCache({\n        rows: props.rows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      });\n    }\n    throttledRowsChange({\n      cache,\n      throttle: false\n    });\n  }, [logger, apiRef, props.rows, props.getRowId, props.loading, props.rowCount, throttledRowsChange]);\n  const previousDataSource = useLazyRef(() => props.dataSource);\n  const handleStrategyProcessorChange = React.useCallback(methodName => {\n    if (props.dataSource && props.dataSource !== previousDataSource.current) {\n      previousDataSource.current = props.dataSource;\n      return;\n    }\n    if (methodName === 'rowTreeCreation') {\n      groupRows();\n    }\n  }, [groupRows, previousDataSource, props.dataSource]);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    // `rowTreeCreation` is the only processor ran when `strategyAvailabilityChange` is fired.\n    // All the other processors listen to `rowsSet` which will be published by the `groupRows` method below.\n    if (apiRef.current.getActiveStrategy(GridStrategyGroup.RowTree) !== gridRowGroupingNameSelector(apiRef)) {\n      groupRows();\n    }\n  }, [apiRef, groupRows]);\n  useGridEvent(apiRef, 'activeStrategyProcessorChange', handleStrategyProcessorChange);\n  useGridEvent(apiRef, 'strategyAvailabilityChange', handleStrategyActivityChange);\n\n  /**\n   * APPLIERS\n   */\n  const applyHydrateRowsProcessor = React.useCallback(() => {\n    apiRef.current.setState(state => {\n      const response = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n        tree: gridRowTreeSelector(apiRef),\n        treeDepths: gridRowTreeDepthsSelector(apiRef),\n        dataRowIds: gridDataRowIdsSelector(apiRef),\n        dataRowIdToModelLookup: gridRowsLookupSelector(apiRef)\n      });\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, response, {\n          totalTopLevelRowCount: getTopLevelRowCount({\n            tree: response.tree,\n            rowCountProp: props.rowCount\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef, props.rowCount]);\n  useGridRegisterPipeApplier(apiRef, 'hydrateRows', applyHydrateRowsProcessor);\n  useGridApiMethod(apiRef, rowApi, 'public');\n  useGridApiMethod(apiRef, rowProApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n  useGridApiMethod(apiRef, rowProPrivateApi, 'private');\n\n  // The effect do not track any value defined synchronously during the 1st render by hooks called after `useGridRows`\n  // As a consequence, the state generated by the 1st run of this useEffect will always be equal to the initialization one\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    let isRowCountPropUpdated = false;\n    if (props.rowCount !== lastRowCount.current) {\n      isRowCountPropUpdated = true;\n      lastRowCount.current = props.rowCount;\n    }\n    const currentRows = props.dataSource ? gridDataRowsSelector(apiRef) : props.rows;\n    const areNewRowsAlreadyInState = apiRef.current.caches.rows.rowsBeforePartialUpdates === currentRows;\n    const isNewLoadingAlreadyInState = apiRef.current.caches.rows.loadingPropBeforePartialUpdates === props.loading;\n    const isNewRowCountAlreadyInState = apiRef.current.caches.rows.rowCountPropBeforePartialUpdates === props.rowCount;\n\n    // The new rows have already been applied (most likely in the `'rowGroupsPreProcessingChange'` listener)\n    if (areNewRowsAlreadyInState) {\n      // If the loading prop has changed, we need to update its value in the state because it won't be done by `throttledRowsChange`\n      if (!isNewLoadingAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            loading: props.loading\n          })\n        }));\n        apiRef.current.caches.rows.loadingPropBeforePartialUpdates = props.loading;\n      }\n      if (!isNewRowCountAlreadyInState) {\n        apiRef.current.setState(state => _extends({}, state, {\n          rows: _extends({}, state.rows, {\n            totalRowCount: Math.max(props.rowCount || 0, state.rows.totalRowCount),\n            totalTopLevelRowCount: Math.max(props.rowCount || 0, state.rows.totalTopLevelRowCount)\n          })\n        }));\n        apiRef.current.caches.rows.rowCountPropBeforePartialUpdates = props.rowCount;\n      }\n      if (!isRowCountPropUpdated) {\n        return;\n      }\n    }\n    logger.debug(`Updating all rows, new length ${currentRows?.length}`);\n    throttledRowsChange({\n      cache: createRowsInternalCache({\n        rows: currentRows,\n        getRowId: props.getRowId,\n        loading: props.loading,\n        rowCount: props.rowCount\n      }),\n      throttle: false\n    });\n  }, [props.rows, props.rowCount, props.getRowId, props.loading, props.dataSource, logger, throttledRowsChange, apiRef]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,oBAAoB,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,2BAA2B,EAAEC,yBAAyB,EAAEC,sBAAsB,EAAEC,+BAA+B,EAAEC,4BAA4B,EAAEC,mBAAmB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnS,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,8BAA8B,QAAQ,iCAAiC;AAChF,SAASC,sBAAsB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,oBAAoB;AACrQ,SAASC,0BAA0B,QAAQ,oCAAoC;AAC/E,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;EAC5D,MAAMC,qBAAqB,GAAG,CAAC,CAACF,KAAK,CAACG,UAAU;EAChDF,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,GAAGpB,uBAAuB,CAAC;IACnDoB,IAAI,EAAEJ,qBAAqB,GAAG,EAAE,GAAGF,KAAK,CAACM,IAAI;IAC7CC,QAAQ,EAAEP,KAAK,CAACO,QAAQ;IACxBC,OAAO,EAAER,KAAK,CAACQ,OAAO;IACtBC,QAAQ,EAAET,KAAK,CAACS;EAClB,CAAC,CAAC;EACF,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,EAAE;IACzBO,IAAI,EAAEnB,qBAAqB,CAAC;MAC1Bc,MAAM;MACNS,YAAY,EAAEV,KAAK,CAACS,QAAQ;MAC5BE,WAAW,EAAET,qBAAqB,GAAG,IAAI,GAAGF,KAAK,CAACQ,OAAO;MACzDI,YAAY,EAAE,IAAI;MAClBC,kBAAkB,EAAE;IACtB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMC,WAAW,GAAGA,CAACb,MAAM,EAAED,KAAK,EAAEe,aAAa,KAAK;EAC3D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI;MACF;MACAC,MAAM,CAACC,MAAM,CAACpB,KAAK,CAACM,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd;IAAA;EAEJ;EACA,MAAMC,MAAM,GAAGvD,aAAa,CAACkC,MAAM,EAAE,aAAa,CAAC;EACnD,MAAMsB,YAAY,GAAG5D,KAAK,CAAC6D,MAAM,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC7C,MAAMC,YAAY,GAAGhE,KAAK,CAAC6D,MAAM,CAACxB,KAAK,CAACS,QAAQ,CAAC;EACjD,MAAMmB,OAAO,GAAGjD,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAM;IACJkD;EACF,CAAC,GAAGd,aAAa,CAACe,KAAK,CAACC,6BAA6B,CAAC9B,MAAM,EAAED,KAAK,CAAC;EACpE,MAAMgC,MAAM,GAAGrE,KAAK,CAACsE,WAAW,CAACC,EAAE,IAAI;IACrC,MAAMC,KAAK,GAAGlE,sBAAsB,CAACgC,MAAM,CAAC,CAACiC,EAAE,CAAC;IAChD,IAAIC,KAAK,EAAE;MACT,OAAOA,KAAK;IACd;IACA,MAAMC,IAAI,GAAG5D,mBAAmB,CAACyB,MAAM,EAAEiC,EAAE,CAAC;IAC5C,IAAIE,IAAI,IAAIhD,sBAAsB,CAACgD,IAAI,CAAC,EAAE;MACxC,OAAO;QACL,CAAC9C,qBAAqB,GAAG4C;MAC3B,CAAC;IACH;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACjC,MAAM,CAAC,CAAC;EACZ,MAAMM,QAAQ,GAAG5C,KAAK,CAACsE,WAAW,CAACI,GAAG,IAAI3D,iBAAiB,CAACuB,MAAM,EAAEoC,GAAG,CAAC,EAAE,CAACpC,MAAM,CAAC,CAAC;EACnF,MAAMqC,mBAAmB,GAAG3E,KAAK,CAACsE,WAAW,CAAC,CAAC;IAC7CM,KAAK;IACLC;EACF,CAAC,KAAK;IACJ,MAAMC,GAAG,GAAGA,CAAA,KAAM;MAChBlB,YAAY,CAACnB,OAAO,GAAGqB,IAAI,CAACC,GAAG,CAAC,CAAC;MACjCzB,MAAM,CAACG,OAAO,CAACsC,QAAQ,CAAC3C,KAAK,IAAIrC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,EAAE;QACnDO,IAAI,EAAEnB,qBAAqB,CAAC;UAC1Bc,MAAM;UACNS,YAAY,EAAEV,KAAK,CAACS,QAAQ;UAC5BE,WAAW,EAAEX,KAAK,CAACQ,OAAO;UAC1BI,YAAY,EAAE1C,mBAAmB,CAAC+B,MAAM,CAAC;UACzCY,kBAAkB,EAAEzC,yBAAyB,CAAC6B,MAAM,CAAC;UACrD0C,qBAAqB,EAAEpE,4BAA4B,CAAC0B,MAAM;QAC5D,CAAC;MACH,CAAC,CAAC,CAAC;MACHA,MAAM,CAACG,OAAO,CAACwC,YAAY,CAAC,SAAS,CAAC;IACxC,CAAC;IACDhB,OAAO,CAACiB,KAAK,CAAC,CAAC;IACf5C,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,GAAGiC,KAAK;IAClC,IAAI,CAACC,QAAQ,EAAE;MACbC,GAAG,CAAC,CAAC;MACL;IACF;IACA,MAAMK,uBAAuB,GAAG9C,KAAK,CAAC+C,cAAc,IAAItB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGH,YAAY,CAACnB,OAAO,CAAC;IAC1F,IAAI0C,uBAAuB,GAAG,CAAC,EAAE;MAC/BlB,OAAO,CAACoB,KAAK,CAACF,uBAAuB,EAAEL,GAAG,CAAC;MAC3C;IACF;IACAA,GAAG,CAAC,CAAC;EACP,CAAC,EAAE,CAACzC,KAAK,CAAC+C,cAAc,EAAE/C,KAAK,CAACS,QAAQ,EAAET,KAAK,CAACQ,OAAO,EAAEP,MAAM,EAAE2B,OAAO,CAAC,CAAC;;EAE1E;AACF;AACA;EACE,MAAMqB,OAAO,GAAGtF,KAAK,CAACsE,WAAW,CAAC3B,IAAI,IAAI;IACxCgB,MAAM,CAAC4B,KAAK,CAAC,iCAAiC5C,IAAI,CAAC6C,MAAM,EAAE,CAAC;IAC5D,IAAItD,uBAAuB,CAACI,MAAM,CAAC,EAAE;MACnCA,MAAM,CAACG,OAAO,CAACgD,kBAAkB,CAAC9C,IAAI,EAAE,KAAK,CAAC;MAC9C;IACF;IACA,MAAMiC,KAAK,GAAGrD,uBAAuB,CAAC;MACpCoB,IAAI;MACJC,QAAQ,EAAEP,KAAK,CAACO,QAAQ;MACxBC,OAAO,EAAER,KAAK,CAACQ,OAAO;MACtBC,QAAQ,EAAET,KAAK,CAACS;IAClB,CAAC,CAAC;IACF,MAAM4C,SAAS,GAAGpD,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI;IAC5CiC,KAAK,CAACe,wBAAwB,GAAGD,SAAS,CAACC,wBAAwB;IACnEhB,mBAAmB,CAAC;MAClBC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClB,MAAM,EAAEtB,KAAK,CAACO,QAAQ,EAAEP,KAAK,CAACQ,OAAO,EAAER,KAAK,CAACS,QAAQ,EAAE6B,mBAAmB,EAAErC,MAAM,CAAC,CAAC;EACxF,MAAMsD,UAAU,GAAG5F,KAAK,CAACsE,WAAW,CAACuB,OAAO,IAAI;IAC9C,IAAIxD,KAAK,CAACyD,SAAS,KAAK7E,aAAa,CAAC8E,QAAQ,IAAIF,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;MACpE,MAAM,IAAIQ,KAAK,CAAC,CAAC,+FAA+F,EAAE,yFAAyF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1N;IACA,IAAI/D,uBAAuB,CAACI,MAAM,CAAC,EAAE;MACnCA,MAAM,CAACG,OAAO,CAACgD,kBAAkB,CAACI,OAAO,CAAC;MAC1C;IACF;IACA,MAAMK,oBAAoB,GAAGnE,kBAAkB,CAACO,MAAM,EAAEuD,OAAO,EAAExD,KAAK,CAACO,QAAQ,CAAC;IAChF,MAAMgC,KAAK,GAAGhD,sBAAsB,CAAC;MACnCiE,OAAO,EAAEK,oBAAoB;MAC7BtD,QAAQ,EAAEP,KAAK,CAACO,QAAQ;MACxBuD,aAAa,EAAE7D,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC;IACvC,CAAC,CAAC;IACFgC,mBAAmB,CAAC;MAClBC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxC,KAAK,CAACyD,SAAS,EAAEzD,KAAK,CAACO,QAAQ,EAAE+B,mBAAmB,EAAErC,MAAM,CAAC,CAAC;EAClE,MAAM8D,gBAAgB,GAAGpG,KAAK,CAACsE,WAAW,CAAC,CAACuB,OAAO,EAAEQ,SAAS,KAAK;IACjE,MAAMH,oBAAoB,GAAGnE,kBAAkB,CAACO,MAAM,EAAEuD,OAAO,EAAExD,KAAK,CAACO,QAAQ,CAAC;IAChF,MAAMgC,KAAK,GAAGhD,sBAAsB,CAAC;MACnCiE,OAAO,EAAEK,oBAAoB;MAC7BtD,QAAQ,EAAEP,KAAK,CAACO,QAAQ;MACxBuD,aAAa,EAAE7D,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI;MACzC0D,SAAS,EAAEA,SAAS,IAAI;IAC1B,CAAC,CAAC;IACF1B,mBAAmB,CAAC;MAClBC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxC,KAAK,CAACO,QAAQ,EAAE+B,mBAAmB,EAAErC,MAAM,CAAC,CAAC;EACjD,MAAMgE,UAAU,GAAGtG,KAAK,CAACsE,WAAW,CAACzB,OAAO,IAAI;IAC9Cc,MAAM,CAAC4B,KAAK,CAAC,sBAAsB1C,OAAO,EAAE,CAAC;IAC7CP,MAAM,CAACG,OAAO,CAACsC,QAAQ,CAAC3C,KAAK,IAAIrC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,EAAE;MACnDO,IAAI,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,CAACO,IAAI,EAAE;QAC7BE;MACF,CAAC;IACH,CAAC,CAAC,CAAC;IACHP,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,CAAC4D,+BAA+B,GAAG1D,OAAO;EACtE,CAAC,EAAE,CAACP,MAAM,EAAEqB,MAAM,CAAC,CAAC;EACpB,MAAM6C,YAAY,GAAGxG,KAAK,CAACsE,WAAW,CAAC,MAAM;IAC3C,MAAMmC,QAAQ,GAAG/F,sBAAsB,CAAC4B,MAAM,CAAC;IAC/C,MAAMoE,YAAY,GAAGpG,sBAAsB,CAACgC,MAAM,CAAC;IACnD,OAAO,IAAIqE,GAAG,CAACF,QAAQ,CAACG,GAAG,CAACrC,EAAE,IAAI,CAACA,EAAE,EAAEmC,YAAY,CAACnC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE,CAAC,EAAE,CAACjC,MAAM,CAAC,CAAC;EACZ,MAAMuE,YAAY,GAAG7G,KAAK,CAACsE,WAAW,CAAC,MAAMjE,oBAAoB,CAACiC,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACpF,MAAMwE,YAAY,GAAG9G,KAAK,CAACsE,WAAW,CAAC,MAAM5D,sBAAsB,CAAC4B,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACtF,MAAMyE,gCAAgC,GAAG/G,KAAK,CAACsE,WAAW,CAACC,EAAE,IAAI;IAC/D,MAAM;MACJyC;IACF,CAAC,GAAG7F,cAAc,CAACmB,MAAM,CAAC;IAC1B,OAAO0E,eAAe,CAACC,GAAG,CAAC1C,EAAE,CAAC;EAChC,CAAC,EAAE,CAACjC,MAAM,CAAC,CAAC;EACZ,MAAM4E,uBAAuB,GAAGlH,KAAK,CAACsE,WAAW,CAAC,CAACC,EAAE,EAAE4C,UAAU,KAAK;IACpE,MAAMC,WAAW,GAAGvG,mBAAmB,CAACyB,MAAM,EAAEiC,EAAE,CAAC;IACnD,IAAI,CAAC6C,WAAW,EAAE;MAChB,MAAM,IAAIpB,KAAK,CAAC,0BAA0BzB,EAAE,SAAS,CAAC;IACxD;IACA,IAAI6C,WAAW,CAACC,IAAI,KAAK,OAAO,EAAE;MAChC,MAAM,IAAIrB,KAAK,CAAC,uDAAuD,CAAC;IAC1E;IACA,MAAMsB,OAAO,GAAGvH,QAAQ,CAAC,CAAC,CAAC,EAAEqH,WAAW,EAAE;MACxCG,gBAAgB,EAAEJ;IACpB,CAAC,CAAC;IACF7E,MAAM,CAACG,OAAO,CAACsC,QAAQ,CAAC3C,KAAK,IAAI;MAC/B,OAAOrC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,EAAE;QACzBO,IAAI,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,CAACO,IAAI,EAAE;UAC7B6E,IAAI,EAAEzH,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,CAACO,IAAI,CAAC6E,IAAI,EAAE;YAClC,CAACjD,EAAE,GAAG+C;UACR,CAAC;QACH,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IACFhF,MAAM,CAACG,OAAO,CAACwC,YAAY,CAAC,oBAAoB,EAAEqC,OAAO,CAAC;EAC5D,CAAC,EAAE,CAAChF,MAAM,CAAC,CAAC;EACZ,MAAMmF,UAAU,GAAGzH,KAAK,CAACsE,WAAW,CAACC,EAAE,IAAI1D,mBAAmB,CAACyB,MAAM,EAAEiC,EAAE,CAAC,IAAI,IAAI,EAAE,CAACjC,MAAM,CAAC,CAAC;EAC7F,MAAMoF,mBAAmB,GAAG1H,KAAK,CAACsE,WAAW,CAAC,CAAC;IAC7CqD,qBAAqB,GAAG,IAAI;IAC5BC,OAAO;IACPC,YAAY;IACZC,cAAc;IACdC,kBAAkB,GAAG;EACvB,CAAC,KAAK;IACJ,MAAMP,IAAI,GAAGjH,mBAAmB,CAAC+B,MAAM,CAAC;IACxC,IAAI0F,QAAQ;IACZ,IAAIH,YAAY,EAAE;MAChB,MAAMI,SAAS,GAAGT,IAAI,CAACI,OAAO,CAAC;MAC/B,IAAI,CAACK,SAAS,EAAE;QACd,OAAO,EAAE;MACX;MACA,MAAMC,YAAY,GAAG9G,wBAAwB,CAACkB,MAAM,CAAC;MACrD0F,QAAQ,GAAG,EAAE;MACb,MAAMG,UAAU,GAAGD,YAAY,CAACE,SAAS,CAAC7D,EAAE,IAAIA,EAAE,KAAKqD,OAAO,CAAC,GAAG,CAAC;MACnE,KAAK,IAAIS,KAAK,GAAGF,UAAU,EAAEE,KAAK,GAAGH,YAAY,CAAC1C,MAAM,KAAKuC,kBAAkB,GAAGP,IAAI,CAACU,YAAY,CAACG,KAAK,CAAC,CAAC,CAACC,KAAK,KAAKL,SAAS,CAACK,KAAK,GAAG,CAAC,GAAGd,IAAI,CAACU,YAAY,CAACG,KAAK,CAAC,CAAC,CAACC,KAAK,GAAGL,SAAS,CAACK,KAAK,CAAC,EAAED,KAAK,IAAI,CAAC,EAAE;QAC1M,MAAM9D,EAAE,GAAG2D,YAAY,CAACG,KAAK,CAAC;QAC9B,IAAI,CAACV,qBAAqB,IAAI,CAAClG,sBAAsB,CAAC+F,IAAI,CAACjD,EAAE,CAAC,CAAC,EAAE;UAC/DyD,QAAQ,CAACO,IAAI,CAAChE,EAAE,CAAC;QACnB;MACF;IACF,CAAC,MAAM;MACLyD,QAAQ,GAAG1G,sBAAsB,CAACkG,IAAI,EAAEI,OAAO,EAAED,qBAAqB,EAAEI,kBAAkB,CAAC;IAC7F;IACA,IAAID,cAAc,EAAE;MAClB,MAAMU,kBAAkB,GAAGnH,8BAA8B,CAACiB,MAAM,CAAC;MACjE0F,QAAQ,GAAG9H,aAAa,CAACsI,kBAAkB,CAAC,GAAGR,QAAQ,GAAGA,QAAQ,CAACS,MAAM,CAACC,OAAO,IAAIF,kBAAkB,CAACE,OAAO,CAAC,KAAK,KAAK,CAAC;IAC7H;IACA,OAAOV,QAAQ;EACjB,CAAC,EAAE,CAAC1F,MAAM,CAAC,CAAC;EACZ,MAAMqG,WAAW,GAAG3I,KAAK,CAACsE,WAAW,CAAC,CAACsE,gBAAgB,EAAEC,OAAO,KAAK;IACnE,IAAIxG,KAAK,CAACyD,SAAS,KAAK7E,aAAa,CAAC8E,QAAQ,IAAI8C,OAAO,CAACrD,MAAM,GAAG,CAAC,EAAE;MACpE,MAAM,IAAIQ,KAAK,CAAC,CAAC,6FAA6F,EAAE,yFAAyF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxN;IACA,IAAI4C,OAAO,CAACrD,MAAM,KAAK,CAAC,EAAE;MACxB;IACF;IACA,MAAMsD,SAAS,GAAGnI,+BAA+B,CAAC2B,MAAM,CAAC;IACzD,IAAIwG,SAAS,GAAG,CAAC,EAAE;MACjB,MAAM,IAAI9C,KAAK,CAAC,yFAAyF,CAAC;IAC5G;IACA,MAAMwB,IAAI,GAAGzH,QAAQ,CAAC,CAAC,CAAC,EAAEQ,mBAAmB,CAAC+B,MAAM,CAAC,CAAC;IACtD,MAAMyG,sBAAsB,GAAGhJ,QAAQ,CAAC,CAAC,CAAC,EAAEO,sBAAsB,CAACgC,MAAM,CAAC,CAAC;IAC3E,MAAM0G,SAAS,GAAGxB,IAAI,CAAC9F,kBAAkB,CAAC;IAC1C,MAAMuH,iBAAiB,GAAG,CAAC,GAAGD,SAAS,CAAChB,QAAQ,CAAC;IACjD,MAAMkB,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,OAAO,CAACrD,MAAM,EAAE4D,CAAC,IAAI,CAAC,EAAE;MAC1C,MAAMC,QAAQ,GAAGR,OAAO,CAACO,CAAC,CAAC;MAC3B,MAAME,KAAK,GAAGxH,oBAAoB,CAACuH,QAAQ,EAAEhH,KAAK,CAACO,QAAQ,EAAE,2DAA2D,CAAC;MACzH,MAAM,CAAC2G,YAAY,CAAC,GAAGN,iBAAiB,CAACO,MAAM,CAACZ,gBAAgB,GAAGQ,CAAC,EAAE,CAAC,EAAEE,KAAK,CAAC;MAC/E,IAAI,CAACJ,OAAO,CAACO,GAAG,CAACF,YAAY,CAAC,EAAE;QAC9B,OAAOR,sBAAsB,CAACQ,YAAY,CAAC;QAC3C,OAAO/B,IAAI,CAAC+B,YAAY,CAAC;MAC3B;MACA,MAAMG,iBAAiB,GAAG;QACxBnF,EAAE,EAAE+E,KAAK;QACThB,KAAK,EAAE,CAAC;QACRqB,MAAM,EAAEjI,kBAAkB;QAC1B2F,IAAI,EAAE,MAAM;QACZuC,WAAW,EAAE;MACf,CAAC;MACDb,sBAAsB,CAACO,KAAK,CAAC,GAAGD,QAAQ;MACxC7B,IAAI,CAAC8B,KAAK,CAAC,GAAGI,iBAAiB;MAC/BR,OAAO,CAACW,GAAG,CAACP,KAAK,CAAC;IACpB;IACA9B,IAAI,CAAC9F,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAEiJ,SAAS,EAAE;MACjDhB,QAAQ,EAAEiB;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMa,UAAU,GAAGb,iBAAiB,CAACR,MAAM,CAACC,OAAO,IAAIlB,IAAI,CAACkB,OAAO,CAAC,EAAErB,IAAI,KAAK,MAAM,CAAC;IACtF/E,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,CAACoG,sBAAsB,GAAGA,sBAAsB;IAC1EzG,MAAM,CAACG,OAAO,CAACsC,QAAQ,CAAC3C,KAAK,IAAIrC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,EAAE;MACnDO,IAAI,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,CAACO,IAAI,EAAE;QAC7BE,OAAO,EAAER,KAAK,CAACQ,OAAO;QACtBkH,aAAa,EAAEC,IAAI,CAACC,GAAG,CAAC5H,KAAK,CAACS,QAAQ,IAAI,CAAC,EAAEmG,iBAAiB,CAACzD,MAAM,CAAC;QACtEuD,sBAAsB;QACtBe,UAAU;QACVtC;MACF,CAAC;IACH,CAAC,CAAC,CAAC;IACHlF,MAAM,CAACG,OAAO,CAACwC,YAAY,CAAC,SAAS,CAAC;EACxC,CAAC,EAAE,CAAC3C,MAAM,EAAED,KAAK,CAACyD,SAAS,EAAEzD,KAAK,CAACO,QAAQ,EAAEP,KAAK,CAACQ,OAAO,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC;EAC5E,MAAMoH,MAAM,GAAG;IACb7F,MAAM;IACNiC,UAAU;IACV1D,QAAQ;IACR4D,YAAY;IACZK,YAAY;IACZC,YAAY;IACZxB,OAAO;IACPM,UAAU;IACV6B,UAAU;IACVV,gCAAgC;IAChCoD,oBAAoB,EAAExB;EACxB,CAAC;EACD,MAAMyB,SAAS,GAAG;IAChBlG,WAAW;IACXgD,uBAAuB;IACvBQ;EACF,CAAC;EACD,MAAM2C,gBAAgB,GAAG;IACvBjE;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMkE,SAAS,GAAGtK,KAAK,CAACsE,WAAW,CAAC,MAAM;IACxCX,MAAM,CAAC4G,IAAI,CAAC,qEAAqE,CAAC;IAClF,IAAI3F,KAAK;IACT,IAAItC,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,CAACgD,wBAAwB,KAAKtD,KAAK,CAACM,IAAI,EAAE;MACtE;MACA;MACAiC,KAAK,GAAG7E,QAAQ,CAAC,CAAC,CAAC,EAAEuC,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,EAAE;QAC/CkD,OAAO,EAAE;UACPwB,IAAI,EAAE,MAAM;UACZ1E,IAAI,EAAEjC,sBAAsB,CAAC4B,MAAM;QACrC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA;MACA;MACAsC,KAAK,GAAGrD,uBAAuB,CAAC;QAC9BoB,IAAI,EAAEN,KAAK,CAACM,IAAI;QAChBC,QAAQ,EAAEP,KAAK,CAACO,QAAQ;QACxBC,OAAO,EAAER,KAAK,CAACQ,OAAO;QACtBC,QAAQ,EAAET,KAAK,CAACS;MAClB,CAAC,CAAC;IACJ;IACA6B,mBAAmB,CAAC;MAClBC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClB,MAAM,EAAErB,MAAM,EAAED,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACO,QAAQ,EAAEP,KAAK,CAACQ,OAAO,EAAER,KAAK,CAACS,QAAQ,EAAE6B,mBAAmB,CAAC,CAAC;EACpG,MAAM6F,kBAAkB,GAAGvK,UAAU,CAAC,MAAMoC,KAAK,CAACG,UAAU,CAAC;EAC7D,MAAMiI,6BAA6B,GAAGzK,KAAK,CAACsE,WAAW,CAACoG,UAAU,IAAI;IACpE,IAAIrI,KAAK,CAACG,UAAU,IAAIH,KAAK,CAACG,UAAU,KAAKgI,kBAAkB,CAAC/H,OAAO,EAAE;MACvE+H,kBAAkB,CAAC/H,OAAO,GAAGJ,KAAK,CAACG,UAAU;MAC7C;IACF;IACA,IAAIkI,UAAU,KAAK,iBAAiB,EAAE;MACpCJ,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACA,SAAS,EAAEE,kBAAkB,EAAEnI,KAAK,CAACG,UAAU,CAAC,CAAC;EACrD,MAAMmI,4BAA4B,GAAG3K,KAAK,CAACsE,WAAW,CAAC,MAAM;IAC3D;IACA;IACA,IAAIhC,MAAM,CAACG,OAAO,CAACmI,iBAAiB,CAAC3I,iBAAiB,CAAC4I,OAAO,CAAC,KAAKrK,2BAA2B,CAAC8B,MAAM,CAAC,EAAE;MACvGgI,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAAChI,MAAM,EAAEgI,SAAS,CAAC,CAAC;EACvBpJ,YAAY,CAACoB,MAAM,EAAE,+BAA+B,EAAEmI,6BAA6B,CAAC;EACpFvJ,YAAY,CAACoB,MAAM,EAAE,4BAA4B,EAAEqI,4BAA4B,CAAC;;EAEhF;AACF;AACA;EACE,MAAMG,yBAAyB,GAAG9K,KAAK,CAACsE,WAAW,CAAC,MAAM;IACxDhC,MAAM,CAACG,OAAO,CAACsC,QAAQ,CAAC3C,KAAK,IAAI;MAC/B,MAAM2I,QAAQ,GAAGzI,MAAM,CAACG,OAAO,CAACuI,4BAA4B,CAAC,aAAa,EAAE;QAC1ExD,IAAI,EAAEjH,mBAAmB,CAAC+B,MAAM,CAAC;QACjC2I,UAAU,EAAExK,yBAAyB,CAAC6B,MAAM,CAAC;QAC7CwH,UAAU,EAAEpJ,sBAAsB,CAAC4B,MAAM,CAAC;QAC1CyG,sBAAsB,EAAEzI,sBAAsB,CAACgC,MAAM;MACvD,CAAC,CAAC;MACF,OAAOvC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,EAAE;QACzBO,IAAI,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,CAACO,IAAI,EAAEoI,QAAQ,EAAE;UACvCG,qBAAqB,EAAErJ,mBAAmB,CAAC;YACzC2F,IAAI,EAAEuD,QAAQ,CAACvD,IAAI;YACnBzE,YAAY,EAAEV,KAAK,CAACS;UACtB,CAAC;QACH,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IACFR,MAAM,CAACG,OAAO,CAACwC,YAAY,CAAC,SAAS,CAAC;EACxC,CAAC,EAAE,CAAC3C,MAAM,EAAED,KAAK,CAACS,QAAQ,CAAC,CAAC;EAC5Bd,0BAA0B,CAACM,MAAM,EAAE,aAAa,EAAEwI,yBAAyB,CAAC;EAC5E3K,gBAAgB,CAACmC,MAAM,EAAE4H,MAAM,EAAE,QAAQ,CAAC;EAC1C/J,gBAAgB,CAACmC,MAAM,EAAE8H,SAAS,EAAE/H,KAAK,CAACyD,SAAS,KAAK7E,aAAa,CAAC8E,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;EACtG5F,gBAAgB,CAACmC,MAAM,EAAE+H,gBAAgB,EAAE,SAAS,CAAC;;EAErD;EACA;EACA,MAAMc,aAAa,GAAGnL,KAAK,CAAC6D,MAAM,CAAC,IAAI,CAAC;EACxC7D,KAAK,CAACoL,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,CAAC1I,OAAO,EAAE;MACzB0I,aAAa,CAAC1I,OAAO,GAAG,KAAK;MAC7B;IACF;IACA,IAAI4I,qBAAqB,GAAG,KAAK;IACjC,IAAIhJ,KAAK,CAACS,QAAQ,KAAKkB,YAAY,CAACvB,OAAO,EAAE;MAC3C4I,qBAAqB,GAAG,IAAI;MAC5BrH,YAAY,CAACvB,OAAO,GAAGJ,KAAK,CAACS,QAAQ;IACvC;IACA,MAAMwI,WAAW,GAAGjJ,KAAK,CAACG,UAAU,GAAG1B,oBAAoB,CAACwB,MAAM,CAAC,GAAGD,KAAK,CAACM,IAAI;IAChF,MAAM4I,wBAAwB,GAAGjJ,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,CAACgD,wBAAwB,KAAK2F,WAAW;IACpG,MAAME,0BAA0B,GAAGlJ,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,CAAC4D,+BAA+B,KAAKlE,KAAK,CAACQ,OAAO;IAC/G,MAAM4I,2BAA2B,GAAGnJ,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,CAAC+I,gCAAgC,KAAKrJ,KAAK,CAACS,QAAQ;;IAElH;IACA,IAAIyI,wBAAwB,EAAE;MAC5B;MACA,IAAI,CAACC,0BAA0B,EAAE;QAC/BlJ,MAAM,CAACG,OAAO,CAACsC,QAAQ,CAAC3C,KAAK,IAAIrC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,EAAE;UACnDO,IAAI,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,CAACO,IAAI,EAAE;YAC7BE,OAAO,EAAER,KAAK,CAACQ;UACjB,CAAC;QACH,CAAC,CAAC,CAAC;QACHP,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,CAAC4D,+BAA+B,GAAGlE,KAAK,CAACQ,OAAO;MAC5E;MACA,IAAI,CAAC4I,2BAA2B,EAAE;QAChCnJ,MAAM,CAACG,OAAO,CAACsC,QAAQ,CAAC3C,KAAK,IAAIrC,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,EAAE;UACnDO,IAAI,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEqC,KAAK,CAACO,IAAI,EAAE;YAC7BoH,aAAa,EAAEC,IAAI,CAACC,GAAG,CAAC5H,KAAK,CAACS,QAAQ,IAAI,CAAC,EAAEV,KAAK,CAACO,IAAI,CAACoH,aAAa,CAAC;YACtEmB,qBAAqB,EAAElB,IAAI,CAACC,GAAG,CAAC5H,KAAK,CAACS,QAAQ,IAAI,CAAC,EAAEV,KAAK,CAACO,IAAI,CAACuI,qBAAqB;UACvF,CAAC;QACH,CAAC,CAAC,CAAC;QACH5I,MAAM,CAACG,OAAO,CAACC,MAAM,CAACC,IAAI,CAAC+I,gCAAgC,GAAGrJ,KAAK,CAACS,QAAQ;MAC9E;MACA,IAAI,CAACuI,qBAAqB,EAAE;QAC1B;MACF;IACF;IACA1H,MAAM,CAAC4B,KAAK,CAAC,iCAAiC+F,WAAW,EAAE9F,MAAM,EAAE,CAAC;IACpEb,mBAAmB,CAAC;MAClBC,KAAK,EAAErD,uBAAuB,CAAC;QAC7BoB,IAAI,EAAE2I,WAAW;QACjB1I,QAAQ,EAAEP,KAAK,CAACO,QAAQ;QACxBC,OAAO,EAAER,KAAK,CAACQ,OAAO;QACtBC,QAAQ,EAAET,KAAK,CAACS;MAClB,CAAC,CAAC;MACF+B,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxC,KAAK,CAACM,IAAI,EAAEN,KAAK,CAACS,QAAQ,EAAET,KAAK,CAACO,QAAQ,EAAEP,KAAK,CAACQ,OAAO,EAAER,KAAK,CAACG,UAAU,EAAEmB,MAAM,EAAEgB,mBAAmB,EAAErC,MAAM,CAAC,CAAC;AACxH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}