{"ast": null, "code": "'use client';\n\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\"],\n  _excluded2 = [\"id\"];\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useGridEvent, useGridEventPriority } from \"../../utils/useGridEvent.js\";\nimport { GridEditModes, GridRowModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector, gridRowIsEditingSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridColumnDefinitionsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridRowEditStopReasons, GridRowEditStartReasons } from \"../../../models/params/gridRowParams.js\";\nimport { GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridRowEditing = (apiRef, props) => {\n  const [rowModesModel, setRowModesModel] = React.useState({});\n  const rowModesModelRef = React.useRef(rowModesModel);\n  const prevRowModesModel = React.useRef({});\n  const prevRowValuesLookup = React.useRef({});\n  const focusTimeout = React.useRef(undefined);\n  const nextFocusedCell = React.useRef(null);\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    rowModesModel: rowModesModelProp,\n    onRowModesModelChange\n  } = props;\n  const runIfEditModeIsRow = callback => (...args) => {\n    if (props.editMode === GridEditModes.Row) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, mode) => {\n    if (apiRef.current.getRowMode(id) !== mode) {\n      throw new Error(`MUI X: The row with id=${id} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const hasFieldsWithErrors = React.useCallback(rowId => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    return Object.values(editingState[rowId]).some(fieldProps => fieldProps.error);\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.Edit) {\n      return;\n    }\n    const rowParams = apiRef.current.getRowParams(params.id);\n    const newParams = _extends({}, rowParams, {\n      field: params.field,\n      reason: GridRowEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('rowEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusIn = React.useCallback(params => {\n    nextFocusedCell.current = params;\n  }, []);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n      return;\n    }\n    // The mechanism to detect if we can stop editing a row is different from\n    // the cell editing. Instead of triggering it when clicking outside a cell,\n    // we must check if another cell in the same row was not clicked. To achieve\n    // that, first we keep track of all cells that gained focus. When a cell loses\n    // focus we check if the next cell that received focus is from a different row.\n    nextFocusedCell.current = null;\n    focusTimeout.current = setTimeout(() => {\n      if (nextFocusedCell.current?.id !== params.id) {\n        // The row might have been deleted during the click\n        if (!apiRef.current.getRow(params.id)) {\n          return;\n        }\n\n        // The row may already changed its mode\n        if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n          return;\n        }\n        if (hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason: GridRowEditStopReasons.rowFocusOut\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    });\n  }, [apiRef, hasFieldsWithErrors]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(focusTimeout.current);\n    };\n  }, []);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridRowModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridRowEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        const columnFields = gridVisibleColumnFieldsSelector(apiRef).filter(field => {\n          const column = apiRef.current.getColumn(field);\n          if (column.type === GRID_ACTIONS_COLUMN_TYPE) {\n            return true;\n          }\n          return apiRef.current.isCellEditable(apiRef.current.getCellParams(params.id, field));\n        });\n        if (event.shiftKey) {\n          if (params.field === columnFields[0]) {\n            // Exit if user pressed Shift+Tab on the first field\n            reason = GridRowEditStopReasons.shiftTabKeyDown;\n          }\n        } else if (params.field === columnFields[columnFields.length - 1]) {\n          // Exit if user pressed Tab on the last field\n          reason = GridRowEditStopReasons.tabKeyDown;\n        }\n\n        // Always prevent going to the next element in the tab sequence because the focus is\n        // handled manually to support edit components rendered inside Portals\n        event.preventDefault();\n        if (!reason) {\n          const index = columnFields.findIndex(field => field === params.field);\n          const nextFieldToFocus = columnFields[event.shiftKey ? index - 1 : index + 1];\n          apiRef.current.setCellFocus(params.id, nextFieldToFocus);\n        }\n      }\n      if (reason) {\n        if (reason !== GridRowEditStopReasons.escapeKeyDown && hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const newParams = _extends({}, apiRef.current.getRowParams(params.id), {\n          reason,\n          field: params.field\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'row'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridRowEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason\n        });\n        apiRef.current.publishEvent('rowEditStart', newParams, event);\n      }\n    }\n  }, [apiRef, hasFieldsWithErrors]);\n  const handleRowEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startRowEditModeParams = {\n      id,\n      fieldToFocus: field\n    };\n    if (reason === GridRowEditStartReasons.printableKeyDown || reason === GridRowEditStartReasons.deleteKeyDown) {\n      startRowEditModeParams.deleteValue = !!field;\n    }\n    apiRef.current.startRowEditMode(startRowEditModeParams);\n  }, [apiRef]);\n  const handleRowEditStop = React.useCallback(params => {\n    const {\n      id,\n      reason,\n      field\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    let cellToFocusAfter;\n    if (reason === GridRowEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridRowEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridRowEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopRowEditMode({\n      id,\n      ignoreModifications,\n      field,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridEvent(apiRef, 'cellDoubleClick', runIfEditModeIsRow(handleCellDoubleClick));\n  useGridEvent(apiRef, 'cellFocusIn', runIfEditModeIsRow(handleCellFocusIn));\n  useGridEvent(apiRef, 'cellFocusOut', runIfEditModeIsRow(handleCellFocusOut));\n  useGridEvent(apiRef, 'cellKeyDown', runIfEditModeIsRow(handleCellKeyDown));\n  useGridEvent(apiRef, 'rowEditStart', runIfEditModeIsRow(handleRowEditStart));\n  useGridEvent(apiRef, 'rowEditStop', runIfEditModeIsRow(handleRowEditStop));\n  useGridEventPriority(apiRef, 'rowEditStart', props.onRowEditStart);\n  useGridEventPriority(apiRef, 'rowEditStop', props.onRowEditStop);\n  const getRowMode = React.useCallback(id => {\n    const isEditing = gridRowIsEditingSelector(apiRef, {\n      rowId: id,\n      editMode: props.editMode\n    });\n    return isEditing ? GridRowModes.Edit : GridRowModes.View;\n  }, [apiRef, props.editMode]);\n  const updateRowModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.rowModesModel;\n    if (onRowModesModelChange && isNewModelDifferentFromProp) {\n      onRowModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.rowModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setRowModesModel(newModel);\n    rowModesModelRef.current = newModel;\n    apiRef.current.publishEvent('rowModesModelChange', newModel);\n  });\n  const updateRowInRowModesModel = React.useCallback((id, newProps) => {\n    const newModel = _extends({}, rowModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newProps);\n    } else {\n      delete newModel[id];\n    }\n    updateRowModesModel(newModel);\n  }, [updateRowModesModel]);\n  const updateOrDeleteRowState = React.useCallback((id, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = newProps;\n      } else {\n        delete newEditingState[id];\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const startRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotInMode(id, GridRowModes.View);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.Edit\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStartRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      fieldToFocus,\n      deleteValue,\n      initialValue\n    } = params;\n    const row = apiRef.current.getRow(id);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const newProps = columns.reduce((acc, col) => {\n      const field = col.field;\n      const cellParams = apiRef.current.getCellParams(id, field);\n      if (!cellParams.isEditable) {\n        return acc;\n      }\n      const column = apiRef.current.getColumn(field);\n      let newValue = apiRef.current.getCellValue(id, field);\n      if (fieldToFocus === field && (deleteValue || initialValue)) {\n        if (deleteValue) {\n          newValue = getDefaultCellValue(column);\n        } else if (initialValue) {\n          newValue = initialValue;\n        }\n      }\n      acc[field] = {\n        value: newValue,\n        error: false,\n        isProcessingProps: column.editable && !!column.preProcessEditCellProps && deleteValue\n      };\n      return acc;\n    }, {});\n    prevRowValuesLookup.current[id] = row;\n    updateOrDeleteRowState(id, newProps);\n    if (fieldToFocus) {\n      apiRef.current.setCellFocus(id, fieldToFocus);\n    }\n    columns.filter(column => {\n      const isCellEditable = apiRef.current.getCellParams(id, column.field).isEditable;\n      return isCellEditable && column.editable && !!column.preProcessEditCellProps && deleteValue;\n    }).forEach(column => {\n      const field = column.field;\n      const value = apiRef.current.getCellValue(id, field);\n      const newValue = deleteValue ? getDefaultCellValue(column) : initialValue ?? value;\n      Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps[field],\n        hasChanged: newValue !== value\n      })).then(processedProps => {\n        // Check if still in edit mode before updating\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          const editingState = gridEditRowsStateSelector(apiRef);\n          updateOrDeleteFieldState(id, field, _extends({}, processedProps, {\n            value: editingState[id][field].value,\n            isProcessingProps: false\n          }));\n        }\n      });\n    });\n  });\n  const stopRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, GridRowModes.Edit);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.View\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStopRowEditMode = useEventCallback(async params => {\n    const {\n      id,\n      ignoreModifications,\n      field: focusedField,\n      cellToFocusAfter = 'none'\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    const finishRowEditMode = () => {\n      if (cellToFocusAfter !== 'none' && focusedField) {\n        apiRef.current.moveFocusToRelativeCell(id, focusedField, cellToFocusAfter);\n      }\n      updateOrDeleteRowState(id, null);\n      updateRowInRowModesModel(id, null);\n      delete prevRowValuesLookup.current[id];\n    };\n    if (ignoreModifications) {\n      finishRowEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = prevRowValuesLookup.current[id];\n    const isSomeFieldProcessingProps = Object.values(editingState[id]).some(fieldProps => fieldProps.isProcessingProps);\n    if (isSomeFieldProcessingProps) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      return;\n    }\n    if (hasFieldsWithErrors(id)) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      // Revert the mode in the rowModesModel prop back to \"edit\"\n      updateRowInRowModesModel(id, {\n        mode: GridRowModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n    if (props.dataSource?.updateRow) {\n      if (isDeepEqual(row, rowUpdate)) {\n        finishRowEditMode();\n        return;\n      }\n      const handleError = () => {\n        prevRowModesModel.current[id].mode = GridRowModes.Edit;\n        // Revert the mode in the rowModesModel prop back to \"edit\"\n        updateRowInRowModesModel(id, {\n          mode: GridRowModes.Edit\n        });\n      };\n      const updateRowParams = {\n        rowId: id,\n        updatedRow: rowUpdate,\n        previousRow: row\n      };\n      try {\n        await apiRef.current.dataSource.editRow(updateRowParams);\n        finishRowEditMode();\n      } catch {\n        handleError();\n      }\n    } else if (processRowUpdate) {\n      const handleError = errorThrown => {\n        // The row might have been deleted\n        if (prevRowModesModel.current[id]) {\n          prevRowModesModel.current[id].mode = GridRowModes.Edit;\n          // Revert the mode in the rowModesModel prop back to \"edit\"\n          updateRowInRowModesModel(id, {\n            mode: GridRowModes.Edit\n          });\n        }\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate()` threw an error which was not handled because `onProcessRowUpdateError()` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError()` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/persistence/.'], 'error');\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishRowEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishRowEditMode();\n    }\n  });\n  const setRowEditingEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (!column.preProcessEditCellProps) {\n      updateOrDeleteFieldState(id, field, newProps);\n    }\n    return new Promise(resolve => {\n      const promises = [];\n      if (column.preProcessEditCellProps) {\n        const hasChanged = newProps.value !== editingState[id][field].value;\n        newProps = _extends({}, newProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, field, newProps);\n        const _editingState$id = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id, [field].map(_toPropertyKey));\n        const promise = Promise.resolve(column.preProcessEditCellProps({\n          id,\n          row,\n          props: newProps,\n          hasChanged,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          editingState = gridEditRowsStateSelector(apiRef);\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          // We don't reuse the value from the props pre-processing because when the\n          // promise resolves it may be already outdated. The only exception to this rule\n          // is when there's no pre-processing.\n          processedProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n          updateOrDeleteFieldState(id, field, processedProps);\n        });\n        promises.push(promise);\n      }\n      Object.entries(editingState[id]).forEach(([thisField, fieldProps]) => {\n        if (thisField === field) {\n          return;\n        }\n        const fieldColumn = apiRef.current.getColumn(thisField);\n        if (!fieldColumn.preProcessEditCellProps) {\n          return;\n        }\n        fieldProps = _extends({}, fieldProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, thisField, fieldProps);\n        editingState = gridEditRowsStateSelector(apiRef);\n        const _editingState$id2 = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id2, [thisField].map(_toPropertyKey));\n        const promise = Promise.resolve(fieldColumn.preProcessEditCellProps({\n          id,\n          row,\n          props: fieldProps,\n          hasChanged: false,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          updateOrDeleteFieldState(id, thisField, processedProps);\n        });\n        promises.push(promise);\n      });\n      Promise.all(promises).then(() => {\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          editingState = gridEditRowsStateSelector(apiRef);\n          resolve(!editingState[id][field].error);\n        } else {\n          resolve(false);\n        }\n      });\n    });\n  }, [apiRef, throwIfNotEditable, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromRowEditing = React.useCallback(id => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id]) {\n      return apiRef.current.getRow(id);\n    }\n    let rowUpdate = _extends({}, prevRowValuesLookup.current[id], row);\n    Object.entries(editingState[id]).forEach(([field, fieldProps]) => {\n      const column = apiRef.current.getColumn(field);\n      // Column might have been removed\n      // see https://github.com/mui/mui-x/pull/16888\n      if (column?.valueSetter) {\n        rowUpdate = column.valueSetter(fieldProps.value, rowUpdate, column, apiRef);\n      } else {\n        rowUpdate[field] = fieldProps.value;\n      }\n    });\n    return rowUpdate;\n  }, [apiRef]);\n  const editingApi = {\n    getRowMode,\n    startRowEditMode,\n    stopRowEditMode\n  };\n  const editingPrivateApi = {\n    setRowEditingEditCellValue,\n    getRowWithUpdatedValuesFromRowEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (rowModesModelProp) {\n      updateRowModesModel(rowModesModelProp);\n    }\n  }, [rowModesModelProp, updateRowModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopRowEditMode may change it later\n    const copyOfPrevRowModesModel = prevRowModesModel.current;\n    prevRowModesModel.current = deepClone(rowModesModel); // Do a deep-clone because the attributes might be changed later\n\n    const ids = new Set([...Object.keys(rowModesModel), ...Object.keys(copyOfPrevRowModesModel)]);\n    Array.from(ids).forEach(id => {\n      const params = rowModesModel[id] ?? {\n        mode: GridRowModes.View\n      };\n      const prevMode = copyOfPrevRowModesModel[id]?.mode || GridRowModes.View;\n      const originalId = rowsLookup[id] ? apiRef.current.getRowId(rowsLookup[id]) : id;\n      if (params.mode === GridRowModes.Edit && prevMode === GridRowModes.View) {\n        updateStateToStartRowEditMode(_extends({\n          id: originalId\n        }, params));\n      } else if (params.mode === GridRowModes.View && prevMode === GridRowModes.Edit) {\n        updateStateToStopRowEditMode(_extends({\n          id: originalId\n        }, params));\n      }\n    });\n  }, [apiRef, rowModesModel, updateStateToStartRowEditMode, updateStateToStopRowEditMode]);\n};", "map": {"version": 3, "names": ["_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "useEventCallback", "useEnhancedEffect", "warnOnce", "isDeepEqual", "useGridEvent", "useGridEventPriority", "GridEditModes", "GridRowModes", "useGridApiMethod", "gridEditRowsStateSelector", "gridRowIsEditingSelector", "isPrintableKey", "isPasteShortcut", "gridColumnDefinitionsSelector", "gridVisibleColumnFieldsSelector", "gridRowsLookupSelector", "deepClone", "GridRowEditStopReasons", "GridRowEditStartReasons", "GRID_ACTIONS_COLUMN_TYPE", "getDefaultCellValue", "useGridRowEditing", "apiRef", "props", "rowModesModel", "setRowModesModel", "useState", "rowModesModelRef", "useRef", "prevRowModesModel", "prevRowValuesLookup", "focusTimeout", "undefined", "nextFocusedCell", "processRowUpdate", "onProcessRowUpdateError", "rowModesModelProp", "onRowModesModelChange", "runIfEditModeIsRow", "callback", "args", "editMode", "Row", "throwIfNotEditable", "useCallback", "id", "field", "params", "current", "getCellParams", "isCellEditable", "Error", "throwIfNotInMode", "mode", "getRowMode", "hasFieldsWithErrors", "rowId", "editingState", "Object", "values", "some", "fieldProps", "error", "handleCellDoubleClick", "event", "isEditable", "Edit", "rowParams", "getRowParams", "newParams", "reason", "cellDoubleClick", "publishEvent", "handleCellFocusIn", "handleCellFocusOut", "View", "setTimeout", "getRow", "rowFocusOut", "useEffect", "clearTimeout", "handleCellKeyDown", "cellMode", "which", "key", "escapeKeyDown", "enterKeyDown", "columnFields", "filter", "column", "getColumn", "type", "shift<PERSON>ey", "shiftTabKeyDown", "length", "tabKeyDown", "preventDefault", "index", "findIndex", "nextFieldToFocus", "setCellFocus", "canStartEditing", "unstable_applyPipeProcessors", "cellParams", "printableKeyDown", "deleteKeyDown", "handleRowEditStart", "startRowEditModeParams", "fieldToFocus", "deleteValue", "startRowEditMode", "handleRowEditStop", "runPendingEditCellValueMutation", "cellToFocusAfter", "ignoreModifications", "stopRowEditMode", "onRowEditStart", "onRowEditStop", "isEditing", "updateRowModesModel", "newModel", "isNewModelDifferentFromProp", "api", "updateRowInRowModesModel", "newProps", "updateOrDeleteRowState", "setState", "state", "newEditingState", "editRows", "updateOrDeleteFieldState", "keys", "other", "updateStateToStartRowEditMode", "initialValue", "row", "columns", "reduce", "acc", "col", "newValue", "getCellValue", "value", "isProcessingProps", "editable", "preProcessEditCellProps", "for<PERSON>ach", "Promise", "resolve", "has<PERSON><PERSON>ed", "then", "processedProps", "updateStateToStopRowEditMode", "focusedField", "finishRowEditMode", "moveFocusToRelativeCell", "isSomeFieldProcessingProps", "rowUpdate", "getRowWithUpdatedValuesFromRowEditing", "dataSource", "updateRow", "handleError", "updateRowParams", "updatedRow", "previousRow", "editRow", "errorThrown", "process", "env", "NODE_ENV", "finalRowUpdate", "updateRows", "catch", "setRowEditingEditCellValue", "debounceMs", "unstable_skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsedValue", "valueParser", "changeReason", "promises", "_editingState$id", "otherFieldsProps", "map", "promise", "push", "entries", "thisField", "fieldColumn", "_editingState$id2", "all", "valueSetter", "editingApi", "editingPrivateApi", "rowsLookup", "copyOfPrevRowModesModel", "ids", "Set", "Array", "from", "prevMode", "originalId", "getRowId"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/editing/useGridRowEditing.js"], "sourcesContent": ["'use client';\n\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\"],\n  _excluded2 = [\"id\"];\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { useGridEvent, useGridEventPriority } from \"../../utils/useGridEvent.js\";\nimport { GridEditModes, GridRowModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridEditRowsStateSelector, gridRowIsEditingSelector } from \"./gridEditingSelectors.js\";\nimport { isPrintableKey, isPasteShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridColumnDefinitionsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { deepClone } from \"../../../utils/utils.js\";\nimport { GridRowEditStopReasons, GridRowEditStartReasons } from \"../../../models/params/gridRowParams.js\";\nimport { GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { getDefaultCellValue } from \"./utils.js\";\nexport const useGridRowEditing = (apiRef, props) => {\n  const [rowModesModel, setRowModesModel] = React.useState({});\n  const rowModesModelRef = React.useRef(rowModesModel);\n  const prevRowModesModel = React.useRef({});\n  const prevRowValuesLookup = React.useRef({});\n  const focusTimeout = React.useRef(undefined);\n  const nextFocusedCell = React.useRef(null);\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    rowModesModel: rowModesModelProp,\n    onRowModesModelChange\n  } = props;\n  const runIfEditModeIsRow = callback => (...args) => {\n    if (props.editMode === GridEditModes.Row) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI X: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, mode) => {\n    if (apiRef.current.getRowMode(id) !== mode) {\n      throw new Error(`MUI X: The row with id=${id} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const hasFieldsWithErrors = React.useCallback(rowId => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    return Object.values(editingState[rowId]).some(fieldProps => fieldProps.error);\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.Edit) {\n      return;\n    }\n    const rowParams = apiRef.current.getRowParams(params.id);\n    const newParams = _extends({}, rowParams, {\n      field: params.field,\n      reason: GridRowEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('rowEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusIn = React.useCallback(params => {\n    nextFocusedCell.current = params;\n  }, []);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n      return;\n    }\n    // The mechanism to detect if we can stop editing a row is different from\n    // the cell editing. Instead of triggering it when clicking outside a cell,\n    // we must check if another cell in the same row was not clicked. To achieve\n    // that, first we keep track of all cells that gained focus. When a cell loses\n    // focus we check if the next cell that received focus is from a different row.\n    nextFocusedCell.current = null;\n    focusTimeout.current = setTimeout(() => {\n      if (nextFocusedCell.current?.id !== params.id) {\n        // The row might have been deleted during the click\n        if (!apiRef.current.getRow(params.id)) {\n          return;\n        }\n\n        // The row may already changed its mode\n        if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n          return;\n        }\n        if (hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason: GridRowEditStopReasons.rowFocusOut\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    });\n  }, [apiRef, hasFieldsWithErrors]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(focusTimeout.current);\n    };\n  }, []);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridRowModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: to replace at one point. See https://github.com/mui/material-ui/pull/39713#discussion_r1381678957.\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridRowEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        const columnFields = gridVisibleColumnFieldsSelector(apiRef).filter(field => {\n          const column = apiRef.current.getColumn(field);\n          if (column.type === GRID_ACTIONS_COLUMN_TYPE) {\n            return true;\n          }\n          return apiRef.current.isCellEditable(apiRef.current.getCellParams(params.id, field));\n        });\n        if (event.shiftKey) {\n          if (params.field === columnFields[0]) {\n            // Exit if user pressed Shift+Tab on the first field\n            reason = GridRowEditStopReasons.shiftTabKeyDown;\n          }\n        } else if (params.field === columnFields[columnFields.length - 1]) {\n          // Exit if user pressed Tab on the last field\n          reason = GridRowEditStopReasons.tabKeyDown;\n        }\n\n        // Always prevent going to the next element in the tab sequence because the focus is\n        // handled manually to support edit components rendered inside Portals\n        event.preventDefault();\n        if (!reason) {\n          const index = columnFields.findIndex(field => field === params.field);\n          const nextFieldToFocus = columnFields[event.shiftKey ? index - 1 : index + 1];\n          apiRef.current.setCellFocus(params.id, nextFieldToFocus);\n        }\n      }\n      if (reason) {\n        if (reason !== GridRowEditStopReasons.escapeKeyDown && hasFieldsWithErrors(params.id)) {\n          return;\n        }\n        const newParams = _extends({}, apiRef.current.getRowParams(params.id), {\n          reason,\n          field: params.field\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'row'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (isPasteShortcut(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Backspace' || event.key === 'Delete') {\n        reason = GridRowEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason\n        });\n        apiRef.current.publishEvent('rowEditStart', newParams, event);\n      }\n    }\n  }, [apiRef, hasFieldsWithErrors]);\n  const handleRowEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startRowEditModeParams = {\n      id,\n      fieldToFocus: field\n    };\n    if (reason === GridRowEditStartReasons.printableKeyDown || reason === GridRowEditStartReasons.deleteKeyDown) {\n      startRowEditModeParams.deleteValue = !!field;\n    }\n    apiRef.current.startRowEditMode(startRowEditModeParams);\n  }, [apiRef]);\n  const handleRowEditStop = React.useCallback(params => {\n    const {\n      id,\n      reason,\n      field\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    let cellToFocusAfter;\n    if (reason === GridRowEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridRowEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridRowEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopRowEditMode({\n      id,\n      ignoreModifications,\n      field,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridEvent(apiRef, 'cellDoubleClick', runIfEditModeIsRow(handleCellDoubleClick));\n  useGridEvent(apiRef, 'cellFocusIn', runIfEditModeIsRow(handleCellFocusIn));\n  useGridEvent(apiRef, 'cellFocusOut', runIfEditModeIsRow(handleCellFocusOut));\n  useGridEvent(apiRef, 'cellKeyDown', runIfEditModeIsRow(handleCellKeyDown));\n  useGridEvent(apiRef, 'rowEditStart', runIfEditModeIsRow(handleRowEditStart));\n  useGridEvent(apiRef, 'rowEditStop', runIfEditModeIsRow(handleRowEditStop));\n  useGridEventPriority(apiRef, 'rowEditStart', props.onRowEditStart);\n  useGridEventPriority(apiRef, 'rowEditStop', props.onRowEditStop);\n  const getRowMode = React.useCallback(id => {\n    const isEditing = gridRowIsEditingSelector(apiRef, {\n      rowId: id,\n      editMode: props.editMode\n    });\n    return isEditing ? GridRowModes.Edit : GridRowModes.View;\n  }, [apiRef, props.editMode]);\n  const updateRowModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.rowModesModel;\n    if (onRowModesModelChange && isNewModelDifferentFromProp) {\n      onRowModesModelChange(newModel, {\n        api: apiRef.current\n      });\n    }\n    if (props.rowModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setRowModesModel(newModel);\n    rowModesModelRef.current = newModel;\n    apiRef.current.publishEvent('rowModesModelChange', newModel);\n  });\n  const updateRowInRowModesModel = React.useCallback((id, newProps) => {\n    const newModel = _extends({}, rowModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newProps);\n    } else {\n      delete newModel[id];\n    }\n    updateRowModesModel(newModel);\n  }, [updateRowModesModel]);\n  const updateOrDeleteRowState = React.useCallback((id, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = newProps;\n      } else {\n        delete newEditingState[id];\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n  }, [apiRef]);\n  const startRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotInMode(id, GridRowModes.View);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.Edit\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStartRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      fieldToFocus,\n      deleteValue,\n      initialValue\n    } = params;\n    const row = apiRef.current.getRow(id);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const newProps = columns.reduce((acc, col) => {\n      const field = col.field;\n      const cellParams = apiRef.current.getCellParams(id, field);\n      if (!cellParams.isEditable) {\n        return acc;\n      }\n      const column = apiRef.current.getColumn(field);\n      let newValue = apiRef.current.getCellValue(id, field);\n      if (fieldToFocus === field && (deleteValue || initialValue)) {\n        if (deleteValue) {\n          newValue = getDefaultCellValue(column);\n        } else if (initialValue) {\n          newValue = initialValue;\n        }\n      }\n      acc[field] = {\n        value: newValue,\n        error: false,\n        isProcessingProps: column.editable && !!column.preProcessEditCellProps && deleteValue\n      };\n      return acc;\n    }, {});\n    prevRowValuesLookup.current[id] = row;\n    updateOrDeleteRowState(id, newProps);\n    if (fieldToFocus) {\n      apiRef.current.setCellFocus(id, fieldToFocus);\n    }\n    columns.filter(column => {\n      const isCellEditable = apiRef.current.getCellParams(id, column.field).isEditable;\n      return isCellEditable && column.editable && !!column.preProcessEditCellProps && deleteValue;\n    }).forEach(column => {\n      const field = column.field;\n      const value = apiRef.current.getCellValue(id, field);\n      const newValue = deleteValue ? getDefaultCellValue(column) : initialValue ?? value;\n      Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps[field],\n        hasChanged: newValue !== value\n      })).then(processedProps => {\n        // Check if still in edit mode before updating\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          const editingState = gridEditRowsStateSelector(apiRef);\n          updateOrDeleteFieldState(id, field, _extends({}, processedProps, {\n            value: editingState[id][field].value,\n            isProcessingProps: false\n          }));\n        }\n      });\n    });\n  });\n  const stopRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, GridRowModes.Edit);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.View\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStopRowEditMode = useEventCallback(async params => {\n    const {\n      id,\n      ignoreModifications,\n      field: focusedField,\n      cellToFocusAfter = 'none'\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    const finishRowEditMode = () => {\n      if (cellToFocusAfter !== 'none' && focusedField) {\n        apiRef.current.moveFocusToRelativeCell(id, focusedField, cellToFocusAfter);\n      }\n      updateOrDeleteRowState(id, null);\n      updateRowInRowModesModel(id, null);\n      delete prevRowValuesLookup.current[id];\n    };\n    if (ignoreModifications) {\n      finishRowEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = prevRowValuesLookup.current[id];\n    const isSomeFieldProcessingProps = Object.values(editingState[id]).some(fieldProps => fieldProps.isProcessingProps);\n    if (isSomeFieldProcessingProps) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      return;\n    }\n    if (hasFieldsWithErrors(id)) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      // Revert the mode in the rowModesModel prop back to \"edit\"\n      updateRowInRowModesModel(id, {\n        mode: GridRowModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n    if (props.dataSource?.updateRow) {\n      if (isDeepEqual(row, rowUpdate)) {\n        finishRowEditMode();\n        return;\n      }\n      const handleError = () => {\n        prevRowModesModel.current[id].mode = GridRowModes.Edit;\n        // Revert the mode in the rowModesModel prop back to \"edit\"\n        updateRowInRowModesModel(id, {\n          mode: GridRowModes.Edit\n        });\n      };\n      const updateRowParams = {\n        rowId: id,\n        updatedRow: rowUpdate,\n        previousRow: row\n      };\n      try {\n        await apiRef.current.dataSource.editRow(updateRowParams);\n        finishRowEditMode();\n      } catch {\n        handleError();\n      }\n    } else if (processRowUpdate) {\n      const handleError = errorThrown => {\n        // The row might have been deleted\n        if (prevRowModesModel.current[id]) {\n          prevRowModesModel.current[id].mode = GridRowModes.Edit;\n          // Revert the mode in the rowModesModel prop back to \"edit\"\n          updateRowInRowModesModel(id, {\n            mode: GridRowModes.Edit\n          });\n        }\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `processRowUpdate()` threw an error which was not handled because `onProcessRowUpdateError()` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError()` prop, for example `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/editing/persistence/.'], 'error');\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row, {\n          rowId: id\n        })).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishRowEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishRowEditMode();\n    }\n  });\n  const setRowEditingEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, row, column, apiRef);\n    }\n    let editingState = gridEditRowsStateSelector(apiRef);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (!column.preProcessEditCellProps) {\n      updateOrDeleteFieldState(id, field, newProps);\n    }\n    return new Promise(resolve => {\n      const promises = [];\n      if (column.preProcessEditCellProps) {\n        const hasChanged = newProps.value !== editingState[id][field].value;\n        newProps = _extends({}, newProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, field, newProps);\n        const _editingState$id = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id, [field].map(_toPropertyKey));\n        const promise = Promise.resolve(column.preProcessEditCellProps({\n          id,\n          row,\n          props: newProps,\n          hasChanged,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          editingState = gridEditRowsStateSelector(apiRef);\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          // We don't reuse the value from the props pre-processing because when the\n          // promise resolves it may be already outdated. The only exception to this rule\n          // is when there's no pre-processing.\n          processedProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n          updateOrDeleteFieldState(id, field, processedProps);\n        });\n        promises.push(promise);\n      }\n      Object.entries(editingState[id]).forEach(([thisField, fieldProps]) => {\n        if (thisField === field) {\n          return;\n        }\n        const fieldColumn = apiRef.current.getColumn(thisField);\n        if (!fieldColumn.preProcessEditCellProps) {\n          return;\n        }\n        fieldProps = _extends({}, fieldProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, thisField, fieldProps);\n        editingState = gridEditRowsStateSelector(apiRef);\n        const _editingState$id2 = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id2, [thisField].map(_toPropertyKey));\n        const promise = Promise.resolve(fieldColumn.preProcessEditCellProps({\n          id,\n          row,\n          props: fieldProps,\n          hasChanged: false,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          updateOrDeleteFieldState(id, thisField, processedProps);\n        });\n        promises.push(promise);\n      });\n      Promise.all(promises).then(() => {\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          editingState = gridEditRowsStateSelector(apiRef);\n          resolve(!editingState[id][field].error);\n        } else {\n          resolve(false);\n        }\n      });\n    });\n  }, [apiRef, throwIfNotEditable, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromRowEditing = React.useCallback(id => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id]) {\n      return apiRef.current.getRow(id);\n    }\n    let rowUpdate = _extends({}, prevRowValuesLookup.current[id], row);\n    Object.entries(editingState[id]).forEach(([field, fieldProps]) => {\n      const column = apiRef.current.getColumn(field);\n      // Column might have been removed\n      // see https://github.com/mui/mui-x/pull/16888\n      if (column?.valueSetter) {\n        rowUpdate = column.valueSetter(fieldProps.value, rowUpdate, column, apiRef);\n      } else {\n        rowUpdate[field] = fieldProps.value;\n      }\n    });\n    return rowUpdate;\n  }, [apiRef]);\n  const editingApi = {\n    getRowMode,\n    startRowEditMode,\n    stopRowEditMode\n  };\n  const editingPrivateApi = {\n    setRowEditingEditCellValue,\n    getRowWithUpdatedValuesFromRowEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (rowModesModelProp) {\n      updateRowModesModel(rowModesModelProp);\n    }\n  }, [rowModesModelProp, updateRowModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopRowEditMode may change it later\n    const copyOfPrevRowModesModel = prevRowModesModel.current;\n    prevRowModesModel.current = deepClone(rowModesModel); // Do a deep-clone because the attributes might be changed later\n\n    const ids = new Set([...Object.keys(rowModesModel), ...Object.keys(copyOfPrevRowModesModel)]);\n    Array.from(ids).forEach(id => {\n      const params = rowModesModel[id] ?? {\n        mode: GridRowModes.View\n      };\n      const prevMode = copyOfPrevRowModesModel[id]?.mode || GridRowModes.View;\n      const originalId = rowsLookup[id] ? apiRef.current.getRowId(rowsLookup[id]) : id;\n      if (params.mode === GridRowModes.Edit && prevMode === GridRowModes.View) {\n        updateStateToStartRowEditMode(_extends({\n          id: originalId\n        }, params));\n      } else if (params.mode === GridRowModes.View && prevMode === GridRowModes.Edit) {\n        updateStateToStopRowEditMode(_extends({\n          id: originalId\n        }, params));\n      }\n    });\n  }, [apiRef, rowModesModel, updateStateToStartRowEditMode, updateStateToStopRowEditMode]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,IAAI,CAAC;EACtBC,UAAU,GAAG,CAAC,IAAI,CAAC;AACrB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,6BAA6B;AAChF,SAASC,aAAa,EAAEC,YAAY,QAAQ,qCAAqC;AACjF,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,yBAAyB,EAAEC,wBAAwB,QAAQ,2BAA2B;AAC/F,SAASC,cAAc,EAAEC,eAAe,QAAQ,iCAAiC;AACjF,SAASC,6BAA6B,EAAEC,+BAA+B,QAAQ,mCAAmC;AAClH,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,yCAAyC;AACzG,SAASC,wBAAwB,QAAQ,0BAA0B;AACnE,SAASC,mBAAmB,QAAQ,YAAY;AAChD,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAClD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMC,gBAAgB,GAAG5B,KAAK,CAAC6B,MAAM,CAACJ,aAAa,CAAC;EACpD,MAAMK,iBAAiB,GAAG9B,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAME,mBAAmB,GAAG/B,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMG,YAAY,GAAGhC,KAAK,CAAC6B,MAAM,CAACI,SAAS,CAAC;EAC5C,MAAMC,eAAe,GAAGlC,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAM;IACJM,gBAAgB;IAChBC,uBAAuB;IACvBX,aAAa,EAAEY,iBAAiB;IAChCC;EACF,CAAC,GAAGd,KAAK;EACT,MAAMe,kBAAkB,GAAGC,QAAQ,IAAI,CAAC,GAAGC,IAAI,KAAK;IAClD,IAAIjB,KAAK,CAACkB,QAAQ,KAAKnC,aAAa,CAACoC,GAAG,EAAE;MACxCH,QAAQ,CAAC,GAAGC,IAAI,CAAC;IACnB;EACF,CAAC;EACD,MAAMG,kBAAkB,GAAG5C,KAAK,CAAC6C,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC1D,MAAMC,MAAM,GAAGzB,MAAM,CAAC0B,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;IACtD,IAAI,CAACxB,MAAM,CAAC0B,OAAO,CAACE,cAAc,CAACH,MAAM,CAAC,EAAE;MAC1C,MAAM,IAAII,KAAK,CAAC,2BAA2BN,EAAE,cAAcC,KAAK,mBAAmB,CAAC;IACtF;EACF,CAAC,EAAE,CAACxB,MAAM,CAAC,CAAC;EACZ,MAAM8B,gBAAgB,GAAGrD,KAAK,CAAC6C,WAAW,CAAC,CAACC,EAAE,EAAEQ,IAAI,KAAK;IACvD,IAAI/B,MAAM,CAAC0B,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKQ,IAAI,EAAE;MAC1C,MAAM,IAAIF,KAAK,CAAC,0BAA0BN,EAAE,cAAcQ,IAAI,QAAQ,CAAC;IACzE;EACF,CAAC,EAAE,CAAC/B,MAAM,CAAC,CAAC;EACZ,MAAMiC,mBAAmB,GAAGxD,KAAK,CAAC6C,WAAW,CAACY,KAAK,IAAI;IACrD,MAAMC,YAAY,GAAGhD,yBAAyB,CAACa,MAAM,CAAC;IACtD,OAAOoC,MAAM,CAACC,MAAM,CAACF,YAAY,CAACD,KAAK,CAAC,CAAC,CAACI,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACC,KAAK,CAAC;EAChF,CAAC,EAAE,CAACxC,MAAM,CAAC,CAAC;EACZ,MAAMyC,qBAAqB,GAAGhE,KAAK,CAAC6C,WAAW,CAAC,CAACG,MAAM,EAAEiB,KAAK,KAAK;IACjE,IAAI,CAACjB,MAAM,CAACkB,UAAU,EAAE;MACtB;IACF;IACA,IAAI3C,MAAM,CAAC0B,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKtC,YAAY,CAAC2D,IAAI,EAAE;MAC9D;IACF;IACA,MAAMC,SAAS,GAAG7C,MAAM,CAAC0B,OAAO,CAACoB,YAAY,CAACrB,MAAM,CAACF,EAAE,CAAC;IACxD,MAAMwB,SAAS,GAAGzE,QAAQ,CAAC,CAAC,CAAC,EAAEuE,SAAS,EAAE;MACxCrB,KAAK,EAAEC,MAAM,CAACD,KAAK;MACnBwB,MAAM,EAAEpD,uBAAuB,CAACqD;IAClC,CAAC,CAAC;IACFjD,MAAM,CAAC0B,OAAO,CAACwB,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEL,KAAK,CAAC;EAC/D,CAAC,EAAE,CAAC1C,MAAM,CAAC,CAAC;EACZ,MAAMmD,iBAAiB,GAAG1E,KAAK,CAAC6C,WAAW,CAACG,MAAM,IAAI;IACpDd,eAAe,CAACe,OAAO,GAAGD,MAAM;EAClC,CAAC,EAAE,EAAE,CAAC;EACN,MAAM2B,kBAAkB,GAAG3E,KAAK,CAAC6C,WAAW,CAAC,CAACG,MAAM,EAAEiB,KAAK,KAAK;IAC9D,IAAI,CAACjB,MAAM,CAACkB,UAAU,EAAE;MACtB;IACF;IACA,IAAI3C,MAAM,CAAC0B,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKtC,YAAY,CAACoE,IAAI,EAAE;MAC9D;IACF;IACA;IACA;IACA;IACA;IACA;IACA1C,eAAe,CAACe,OAAO,GAAG,IAAI;IAC9BjB,YAAY,CAACiB,OAAO,GAAG4B,UAAU,CAAC,MAAM;MACtC,IAAI3C,eAAe,CAACe,OAAO,EAAEH,EAAE,KAAKE,MAAM,CAACF,EAAE,EAAE;QAC7C;QACA,IAAI,CAACvB,MAAM,CAAC0B,OAAO,CAAC6B,MAAM,CAAC9B,MAAM,CAACF,EAAE,CAAC,EAAE;UACrC;QACF;;QAEA;QACA,IAAIvB,MAAM,CAAC0B,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKtC,YAAY,CAACoE,IAAI,EAAE;UAC9D;QACF;QACA,IAAIpB,mBAAmB,CAACR,MAAM,CAACF,EAAE,CAAC,EAAE;UAClC;QACF;QACA,MAAMsB,SAAS,GAAG7C,MAAM,CAAC0B,OAAO,CAACoB,YAAY,CAACrB,MAAM,CAACF,EAAE,CAAC;QACxD,MAAMwB,SAAS,GAAGzE,QAAQ,CAAC,CAAC,CAAC,EAAEuE,SAAS,EAAE;UACxCrB,KAAK,EAAEC,MAAM,CAACD,KAAK;UACnBwB,MAAM,EAAErD,sBAAsB,CAAC6D;QACjC,CAAC,CAAC;QACFxD,MAAM,CAAC0B,OAAO,CAACwB,YAAY,CAAC,aAAa,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC9D;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1C,MAAM,EAAEiC,mBAAmB,CAAC,CAAC;EACjCxD,KAAK,CAACgF,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXC,YAAY,CAACjD,YAAY,CAACiB,OAAO,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMiC,iBAAiB,GAAGlF,KAAK,CAAC6C,WAAW,CAAC,CAACG,MAAM,EAAEiB,KAAK,KAAK;IAC7D,IAAIjB,MAAM,CAACmC,QAAQ,KAAK3E,YAAY,CAAC2D,IAAI,EAAE;MACzC;MACA;MACA,IAAIF,KAAK,CAACmB,KAAK,KAAK,GAAG,EAAE;QACvB;MACF;MACA,IAAIb,MAAM;MACV,IAAIN,KAAK,CAACoB,GAAG,KAAK,QAAQ,EAAE;QAC1Bd,MAAM,GAAGrD,sBAAsB,CAACoE,aAAa;MAC/C,CAAC,MAAM,IAAIrB,KAAK,CAACoB,GAAG,KAAK,OAAO,EAAE;QAChCd,MAAM,GAAGrD,sBAAsB,CAACqE,YAAY;MAC9C,CAAC,MAAM,IAAItB,KAAK,CAACoB,GAAG,KAAK,KAAK,EAAE;QAC9B,MAAMG,YAAY,GAAGzE,+BAA+B,CAACQ,MAAM,CAAC,CAACkE,MAAM,CAAC1C,KAAK,IAAI;UAC3E,MAAM2C,MAAM,GAAGnE,MAAM,CAAC0B,OAAO,CAAC0C,SAAS,CAAC5C,KAAK,CAAC;UAC9C,IAAI2C,MAAM,CAACE,IAAI,KAAKxE,wBAAwB,EAAE;YAC5C,OAAO,IAAI;UACb;UACA,OAAOG,MAAM,CAAC0B,OAAO,CAACE,cAAc,CAAC5B,MAAM,CAAC0B,OAAO,CAACC,aAAa,CAACF,MAAM,CAACF,EAAE,EAAEC,KAAK,CAAC,CAAC;QACtF,CAAC,CAAC;QACF,IAAIkB,KAAK,CAAC4B,QAAQ,EAAE;UAClB,IAAI7C,MAAM,CAACD,KAAK,KAAKyC,YAAY,CAAC,CAAC,CAAC,EAAE;YACpC;YACAjB,MAAM,GAAGrD,sBAAsB,CAAC4E,eAAe;UACjD;QACF,CAAC,MAAM,IAAI9C,MAAM,CAACD,KAAK,KAAKyC,YAAY,CAACA,YAAY,CAACO,MAAM,GAAG,CAAC,CAAC,EAAE;UACjE;UACAxB,MAAM,GAAGrD,sBAAsB,CAAC8E,UAAU;QAC5C;;QAEA;QACA;QACA/B,KAAK,CAACgC,cAAc,CAAC,CAAC;QACtB,IAAI,CAAC1B,MAAM,EAAE;UACX,MAAM2B,KAAK,GAAGV,YAAY,CAACW,SAAS,CAACpD,KAAK,IAAIA,KAAK,KAAKC,MAAM,CAACD,KAAK,CAAC;UACrE,MAAMqD,gBAAgB,GAAGZ,YAAY,CAACvB,KAAK,CAAC4B,QAAQ,GAAGK,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC;UAC7E3E,MAAM,CAAC0B,OAAO,CAACoD,YAAY,CAACrD,MAAM,CAACF,EAAE,EAAEsD,gBAAgB,CAAC;QAC1D;MACF;MACA,IAAI7B,MAAM,EAAE;QACV,IAAIA,MAAM,KAAKrD,sBAAsB,CAACoE,aAAa,IAAI9B,mBAAmB,CAACR,MAAM,CAACF,EAAE,CAAC,EAAE;UACrF;QACF;QACA,MAAMwB,SAAS,GAAGzE,QAAQ,CAAC,CAAC,CAAC,EAAE0B,MAAM,CAAC0B,OAAO,CAACoB,YAAY,CAACrB,MAAM,CAACF,EAAE,CAAC,EAAE;UACrEyB,MAAM;UACNxB,KAAK,EAAEC,MAAM,CAACD;QAChB,CAAC,CAAC;QACFxB,MAAM,CAAC0B,OAAO,CAACwB,YAAY,CAAC,aAAa,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC9D;IACF,CAAC,MAAM,IAAIjB,MAAM,CAACkB,UAAU,EAAE;MAC5B,IAAIK,MAAM;MACV,MAAM+B,eAAe,GAAG/E,MAAM,CAAC0B,OAAO,CAACsD,4BAA4B,CAAC,iBAAiB,EAAE,IAAI,EAAE;QAC3FtC,KAAK;QACLuC,UAAU,EAAExD,MAAM;QAClBN,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAAC4D,eAAe,EAAE;QACpB;MACF;MACA,IAAI1F,cAAc,CAACqD,KAAK,CAAC,EAAE;QACzBM,MAAM,GAAGpD,uBAAuB,CAACsF,gBAAgB;MACnD,CAAC,MAAM,IAAI5F,eAAe,CAACoD,KAAK,CAAC,EAAE;QACjCM,MAAM,GAAGpD,uBAAuB,CAACsF,gBAAgB;MACnD,CAAC,MAAM,IAAIxC,KAAK,CAACoB,GAAG,KAAK,OAAO,EAAE;QAChCd,MAAM,GAAGpD,uBAAuB,CAACoE,YAAY;MAC/C,CAAC,MAAM,IAAItB,KAAK,CAACoB,GAAG,KAAK,WAAW,IAAIpB,KAAK,CAACoB,GAAG,KAAK,QAAQ,EAAE;QAC9Dd,MAAM,GAAGpD,uBAAuB,CAACuF,aAAa;MAChD;MACA,IAAInC,MAAM,EAAE;QACV,MAAMH,SAAS,GAAG7C,MAAM,CAAC0B,OAAO,CAACoB,YAAY,CAACrB,MAAM,CAACF,EAAE,CAAC;QACxD,MAAMwB,SAAS,GAAGzE,QAAQ,CAAC,CAAC,CAAC,EAAEuE,SAAS,EAAE;UACxCrB,KAAK,EAAEC,MAAM,CAACD,KAAK;UACnBwB;QACF,CAAC,CAAC;QACFhD,MAAM,CAAC0B,OAAO,CAACwB,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC/D;IACF;EACF,CAAC,EAAE,CAAC1C,MAAM,EAAEiC,mBAAmB,CAAC,CAAC;EACjC,MAAMmD,kBAAkB,GAAG3G,KAAK,CAAC6C,WAAW,CAACG,MAAM,IAAI;IACrD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLwB;IACF,CAAC,GAAGvB,MAAM;IACV,MAAM4D,sBAAsB,GAAG;MAC7B9D,EAAE;MACF+D,YAAY,EAAE9D;IAChB,CAAC;IACD,IAAIwB,MAAM,KAAKpD,uBAAuB,CAACsF,gBAAgB,IAAIlC,MAAM,KAAKpD,uBAAuB,CAACuF,aAAa,EAAE;MAC3GE,sBAAsB,CAACE,WAAW,GAAG,CAAC,CAAC/D,KAAK;IAC9C;IACAxB,MAAM,CAAC0B,OAAO,CAAC8D,gBAAgB,CAACH,sBAAsB,CAAC;EACzD,CAAC,EAAE,CAACrF,MAAM,CAAC,CAAC;EACZ,MAAMyF,iBAAiB,GAAGhH,KAAK,CAAC6C,WAAW,CAACG,MAAM,IAAI;IACpD,MAAM;MACJF,EAAE;MACFyB,MAAM;MACNxB;IACF,CAAC,GAAGC,MAAM;IACVzB,MAAM,CAAC0B,OAAO,CAACgE,+BAA+B,CAACnE,EAAE,CAAC;IAClD,IAAIoE,gBAAgB;IACpB,IAAI3C,MAAM,KAAKrD,sBAAsB,CAACqE,YAAY,EAAE;MAClD2B,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI3C,MAAM,KAAKrD,sBAAsB,CAAC8E,UAAU,EAAE;MACvDkB,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI3C,MAAM,KAAKrD,sBAAsB,CAAC4E,eAAe,EAAE;MAC5DoB,gBAAgB,GAAG,MAAM;IAC3B;IACA,MAAMC,mBAAmB,GAAG5C,MAAM,KAAK,eAAe;IACtDhD,MAAM,CAAC0B,OAAO,CAACmE,eAAe,CAAC;MAC7BtE,EAAE;MACFqE,mBAAmB;MACnBpE,KAAK;MACLmE;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC3F,MAAM,CAAC,CAAC;EACZlB,YAAY,CAACkB,MAAM,EAAE,iBAAiB,EAAEgB,kBAAkB,CAACyB,qBAAqB,CAAC,CAAC;EAClF3D,YAAY,CAACkB,MAAM,EAAE,aAAa,EAAEgB,kBAAkB,CAACmC,iBAAiB,CAAC,CAAC;EAC1ErE,YAAY,CAACkB,MAAM,EAAE,cAAc,EAAEgB,kBAAkB,CAACoC,kBAAkB,CAAC,CAAC;EAC5EtE,YAAY,CAACkB,MAAM,EAAE,aAAa,EAAEgB,kBAAkB,CAAC2C,iBAAiB,CAAC,CAAC;EAC1E7E,YAAY,CAACkB,MAAM,EAAE,cAAc,EAAEgB,kBAAkB,CAACoE,kBAAkB,CAAC,CAAC;EAC5EtG,YAAY,CAACkB,MAAM,EAAE,aAAa,EAAEgB,kBAAkB,CAACyE,iBAAiB,CAAC,CAAC;EAC1E1G,oBAAoB,CAACiB,MAAM,EAAE,cAAc,EAAEC,KAAK,CAAC6F,cAAc,CAAC;EAClE/G,oBAAoB,CAACiB,MAAM,EAAE,aAAa,EAAEC,KAAK,CAAC8F,aAAa,CAAC;EAChE,MAAM/D,UAAU,GAAGvD,KAAK,CAAC6C,WAAW,CAACC,EAAE,IAAI;IACzC,MAAMyE,SAAS,GAAG5G,wBAAwB,CAACY,MAAM,EAAE;MACjDkC,KAAK,EAAEX,EAAE;MACTJ,QAAQ,EAAElB,KAAK,CAACkB;IAClB,CAAC,CAAC;IACF,OAAO6E,SAAS,GAAG/G,YAAY,CAAC2D,IAAI,GAAG3D,YAAY,CAACoE,IAAI;EAC1D,CAAC,EAAE,CAACrD,MAAM,EAAEC,KAAK,CAACkB,QAAQ,CAAC,CAAC;EAC5B,MAAM8E,mBAAmB,GAAGvH,gBAAgB,CAACwH,QAAQ,IAAI;IACvD,MAAMC,2BAA2B,GAAGD,QAAQ,KAAKjG,KAAK,CAACC,aAAa;IACpE,IAAIa,qBAAqB,IAAIoF,2BAA2B,EAAE;MACxDpF,qBAAqB,CAACmF,QAAQ,EAAE;QAC9BE,GAAG,EAAEpG,MAAM,CAAC0B;MACd,CAAC,CAAC;IACJ;IACA,IAAIzB,KAAK,CAACC,aAAa,IAAIiG,2BAA2B,EAAE;MACtD,OAAO,CAAC;IACV;IACAhG,gBAAgB,CAAC+F,QAAQ,CAAC;IAC1B7F,gBAAgB,CAACqB,OAAO,GAAGwE,QAAQ;IACnClG,MAAM,CAAC0B,OAAO,CAACwB,YAAY,CAAC,qBAAqB,EAAEgD,QAAQ,CAAC;EAC9D,CAAC,CAAC;EACF,MAAMG,wBAAwB,GAAG5H,KAAK,CAAC6C,WAAW,CAAC,CAACC,EAAE,EAAE+E,QAAQ,KAAK;IACnE,MAAMJ,QAAQ,GAAG5H,QAAQ,CAAC,CAAC,CAAC,EAAE+B,gBAAgB,CAACqB,OAAO,CAAC;IACvD,IAAI4E,QAAQ,KAAK,IAAI,EAAE;MACrBJ,QAAQ,CAAC3E,EAAE,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,EAAEgI,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL,OAAOJ,QAAQ,CAAC3E,EAAE,CAAC;IACrB;IACA0E,mBAAmB,CAACC,QAAQ,CAAC;EAC/B,CAAC,EAAE,CAACD,mBAAmB,CAAC,CAAC;EACzB,MAAMM,sBAAsB,GAAG9H,KAAK,CAAC6C,WAAW,CAAC,CAACC,EAAE,EAAE+E,QAAQ,KAAK;IACjEtG,MAAM,CAAC0B,OAAO,CAAC8E,QAAQ,CAACC,KAAK,IAAI;MAC/B,MAAMC,eAAe,GAAGpI,QAAQ,CAAC,CAAC,CAAC,EAAEmI,KAAK,CAACE,QAAQ,CAAC;MACpD,IAAIL,QAAQ,KAAK,IAAI,EAAE;QACrBI,eAAe,CAACnF,EAAE,CAAC,GAAG+E,QAAQ;MAChC,CAAC,MAAM;QACL,OAAOI,eAAe,CAACnF,EAAE,CAAC;MAC5B;MACA,OAAOjD,QAAQ,CAAC,CAAC,CAAC,EAAEmI,KAAK,EAAE;QACzBE,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1G,MAAM,CAAC,CAAC;EACZ,MAAM4G,wBAAwB,GAAGnI,KAAK,CAAC6C,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAE8E,QAAQ,KAAK;IAC1EtG,MAAM,CAAC0B,OAAO,CAAC8E,QAAQ,CAACC,KAAK,IAAI;MAC/B,MAAMC,eAAe,GAAGpI,QAAQ,CAAC,CAAC,CAAC,EAAEmI,KAAK,CAACE,QAAQ,CAAC;MACpD,IAAIL,QAAQ,KAAK,IAAI,EAAE;QACrBI,eAAe,CAACnF,EAAE,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,EAAEoI,eAAe,CAACnF,EAAE,CAAC,EAAE;UACtD,CAACC,KAAK,GAAGlD,QAAQ,CAAC,CAAC,CAAC,EAAEgI,QAAQ;QAChC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOI,eAAe,CAACnF,EAAE,CAAC,CAACC,KAAK,CAAC;QACjC,IAAIY,MAAM,CAACyE,IAAI,CAACH,eAAe,CAACnF,EAAE,CAAC,CAAC,CAACiD,MAAM,KAAK,CAAC,EAAE;UACjD,OAAOkC,eAAe,CAACnF,EAAE,CAAC;QAC5B;MACF;MACA,OAAOjD,QAAQ,CAAC,CAAC,CAAC,EAAEmI,KAAK,EAAE;QACzBE,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1G,MAAM,CAAC,CAAC;EACZ,MAAMwF,gBAAgB,GAAG/G,KAAK,CAAC6C,WAAW,CAACG,MAAM,IAAI;IACnD,MAAM;QACFF;MACF,CAAC,GAAGE,MAAM;MACVqF,KAAK,GAAGzI,6BAA6B,CAACoD,MAAM,EAAElD,SAAS,CAAC;IAC1DuD,gBAAgB,CAACP,EAAE,EAAEtC,YAAY,CAACoE,IAAI,CAAC;IACvCgD,wBAAwB,CAAC9E,EAAE,EAAEjD,QAAQ,CAAC;MACpCyD,IAAI,EAAE9C,YAAY,CAAC2D;IACrB,CAAC,EAAEkE,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAChF,gBAAgB,EAAEuE,wBAAwB,CAAC,CAAC;EAChD,MAAMU,6BAA6B,GAAGrI,gBAAgB,CAAC+C,MAAM,IAAI;IAC/D,MAAM;MACJF,EAAE;MACF+D,YAAY;MACZC,WAAW;MACXyB;IACF,CAAC,GAAGvF,MAAM;IACV,MAAMwF,GAAG,GAAGjH,MAAM,CAAC0B,OAAO,CAAC6B,MAAM,CAAChC,EAAE,CAAC;IACrC,MAAM2F,OAAO,GAAG3H,6BAA6B,CAACS,MAAM,CAAC;IACrD,MAAMsG,QAAQ,GAAGY,OAAO,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAC5C,MAAM7F,KAAK,GAAG6F,GAAG,CAAC7F,KAAK;MACvB,MAAMyD,UAAU,GAAGjF,MAAM,CAAC0B,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;MAC1D,IAAI,CAACyD,UAAU,CAACtC,UAAU,EAAE;QAC1B,OAAOyE,GAAG;MACZ;MACA,MAAMjD,MAAM,GAAGnE,MAAM,CAAC0B,OAAO,CAAC0C,SAAS,CAAC5C,KAAK,CAAC;MAC9C,IAAI8F,QAAQ,GAAGtH,MAAM,CAAC0B,OAAO,CAAC6F,YAAY,CAAChG,EAAE,EAAEC,KAAK,CAAC;MACrD,IAAI8D,YAAY,KAAK9D,KAAK,KAAK+D,WAAW,IAAIyB,YAAY,CAAC,EAAE;QAC3D,IAAIzB,WAAW,EAAE;UACf+B,QAAQ,GAAGxH,mBAAmB,CAACqE,MAAM,CAAC;QACxC,CAAC,MAAM,IAAI6C,YAAY,EAAE;UACvBM,QAAQ,GAAGN,YAAY;QACzB;MACF;MACAI,GAAG,CAAC5F,KAAK,CAAC,GAAG;QACXgG,KAAK,EAAEF,QAAQ;QACf9E,KAAK,EAAE,KAAK;QACZiF,iBAAiB,EAAEtD,MAAM,CAACuD,QAAQ,IAAI,CAAC,CAACvD,MAAM,CAACwD,uBAAuB,IAAIpC;MAC5E,CAAC;MACD,OAAO6B,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN5G,mBAAmB,CAACkB,OAAO,CAACH,EAAE,CAAC,GAAG0F,GAAG;IACrCV,sBAAsB,CAAChF,EAAE,EAAE+E,QAAQ,CAAC;IACpC,IAAIhB,YAAY,EAAE;MAChBtF,MAAM,CAAC0B,OAAO,CAACoD,YAAY,CAACvD,EAAE,EAAE+D,YAAY,CAAC;IAC/C;IACA4B,OAAO,CAAChD,MAAM,CAACC,MAAM,IAAI;MACvB,MAAMvC,cAAc,GAAG5B,MAAM,CAAC0B,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAE4C,MAAM,CAAC3C,KAAK,CAAC,CAACmB,UAAU;MAChF,OAAOf,cAAc,IAAIuC,MAAM,CAACuD,QAAQ,IAAI,CAAC,CAACvD,MAAM,CAACwD,uBAAuB,IAAIpC,WAAW;IAC7F,CAAC,CAAC,CAACqC,OAAO,CAACzD,MAAM,IAAI;MACnB,MAAM3C,KAAK,GAAG2C,MAAM,CAAC3C,KAAK;MAC1B,MAAMgG,KAAK,GAAGxH,MAAM,CAAC0B,OAAO,CAAC6F,YAAY,CAAChG,EAAE,EAAEC,KAAK,CAAC;MACpD,MAAM8F,QAAQ,GAAG/B,WAAW,GAAGzF,mBAAmB,CAACqE,MAAM,CAAC,GAAG6C,YAAY,IAAIQ,KAAK;MAClFK,OAAO,CAACC,OAAO,CAAC3D,MAAM,CAACwD,uBAAuB,CAAC;QAC7CpG,EAAE;QACF0F,GAAG;QACHhH,KAAK,EAAEqG,QAAQ,CAAC9E,KAAK,CAAC;QACtBuG,UAAU,EAAET,QAAQ,KAAKE;MAC3B,CAAC,CAAC,CAAC,CAACQ,IAAI,CAACC,cAAc,IAAI;QACzB;QACA,IAAIjI,MAAM,CAAC0B,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKtC,YAAY,CAAC2D,IAAI,EAAE;UACvD,MAAMT,YAAY,GAAGhD,yBAAyB,CAACa,MAAM,CAAC;UACtD4G,wBAAwB,CAACrF,EAAE,EAAEC,KAAK,EAAElD,QAAQ,CAAC,CAAC,CAAC,EAAE2J,cAAc,EAAE;YAC/DT,KAAK,EAAErF,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,CAACgG,KAAK;YACpCC,iBAAiB,EAAE;UACrB,CAAC,CAAC,CAAC;QACL;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAM5B,eAAe,GAAGpH,KAAK,CAAC6C,WAAW,CAACG,MAAM,IAAI;IAClD,MAAM;QACFF;MACF,CAAC,GAAGE,MAAM;MACVqF,KAAK,GAAGzI,6BAA6B,CAACoD,MAAM,EAAEjD,UAAU,CAAC;IAC3DsD,gBAAgB,CAACP,EAAE,EAAEtC,YAAY,CAAC2D,IAAI,CAAC;IACvCyD,wBAAwB,CAAC9E,EAAE,EAAEjD,QAAQ,CAAC;MACpCyD,IAAI,EAAE9C,YAAY,CAACoE;IACrB,CAAC,EAAEyD,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAChF,gBAAgB,EAAEuE,wBAAwB,CAAC,CAAC;EAChD,MAAM6B,4BAA4B,GAAGxJ,gBAAgB,CAAC,MAAM+C,MAAM,IAAI;IACpE,MAAM;MACJF,EAAE;MACFqE,mBAAmB;MACnBpE,KAAK,EAAE2G,YAAY;MACnBxC,gBAAgB,GAAG;IACrB,CAAC,GAAGlE,MAAM;IACVzB,MAAM,CAAC0B,OAAO,CAACgE,+BAA+B,CAACnE,EAAE,CAAC;IAClD,MAAM6G,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAIzC,gBAAgB,KAAK,MAAM,IAAIwC,YAAY,EAAE;QAC/CnI,MAAM,CAAC0B,OAAO,CAAC2G,uBAAuB,CAAC9G,EAAE,EAAE4G,YAAY,EAAExC,gBAAgB,CAAC;MAC5E;MACAY,sBAAsB,CAAChF,EAAE,EAAE,IAAI,CAAC;MAChC8E,wBAAwB,CAAC9E,EAAE,EAAE,IAAI,CAAC;MAClC,OAAOf,mBAAmB,CAACkB,OAAO,CAACH,EAAE,CAAC;IACxC,CAAC;IACD,IAAIqE,mBAAmB,EAAE;MACvBwC,iBAAiB,CAAC,CAAC;MACnB;IACF;IACA,MAAMjG,YAAY,GAAGhD,yBAAyB,CAACa,MAAM,CAAC;IACtD,MAAMiH,GAAG,GAAGzG,mBAAmB,CAACkB,OAAO,CAACH,EAAE,CAAC;IAC3C,MAAM+G,0BAA0B,GAAGlG,MAAM,CAACC,MAAM,CAACF,YAAY,CAACZ,EAAE,CAAC,CAAC,CAACe,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACkF,iBAAiB,CAAC;IACnH,IAAIa,0BAA0B,EAAE;MAC9B/H,iBAAiB,CAACmB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG9C,YAAY,CAAC2D,IAAI;MACtD;IACF;IACA,IAAIX,mBAAmB,CAACV,EAAE,CAAC,EAAE;MAC3BhB,iBAAiB,CAACmB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG9C,YAAY,CAAC2D,IAAI;MACtD;MACAyD,wBAAwB,CAAC9E,EAAE,EAAE;QAC3BQ,IAAI,EAAE9C,YAAY,CAAC2D;MACrB,CAAC,CAAC;MACF;IACF;IACA,MAAM2F,SAAS,GAAGvI,MAAM,CAAC0B,OAAO,CAAC8G,qCAAqC,CAACjH,EAAE,CAAC;IAC1E,IAAItB,KAAK,CAACwI,UAAU,EAAEC,SAAS,EAAE;MAC/B,IAAI7J,WAAW,CAACoI,GAAG,EAAEsB,SAAS,CAAC,EAAE;QAC/BH,iBAAiB,CAAC,CAAC;QACnB;MACF;MACA,MAAMO,WAAW,GAAGA,CAAA,KAAM;QACxBpI,iBAAiB,CAACmB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG9C,YAAY,CAAC2D,IAAI;QACtD;QACAyD,wBAAwB,CAAC9E,EAAE,EAAE;UAC3BQ,IAAI,EAAE9C,YAAY,CAAC2D;QACrB,CAAC,CAAC;MACJ,CAAC;MACD,MAAMgG,eAAe,GAAG;QACtB1G,KAAK,EAAEX,EAAE;QACTsH,UAAU,EAAEN,SAAS;QACrBO,WAAW,EAAE7B;MACf,CAAC;MACD,IAAI;QACF,MAAMjH,MAAM,CAAC0B,OAAO,CAAC+G,UAAU,CAACM,OAAO,CAACH,eAAe,CAAC;QACxDR,iBAAiB,CAAC,CAAC;MACrB,CAAC,CAAC,MAAM;QACNO,WAAW,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAI/H,gBAAgB,EAAE;MAC3B,MAAM+H,WAAW,GAAGK,WAAW,IAAI;QACjC;QACA,IAAIzI,iBAAiB,CAACmB,OAAO,CAACH,EAAE,CAAC,EAAE;UACjChB,iBAAiB,CAACmB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG9C,YAAY,CAAC2D,IAAI;UACtD;UACAyD,wBAAwB,CAAC9E,EAAE,EAAE;YAC3BQ,IAAI,EAAE9C,YAAY,CAAC2D;UACrB,CAAC,CAAC;QACJ;QACA,IAAI/B,uBAAuB,EAAE;UAC3BA,uBAAuB,CAACmI,WAAW,CAAC;QACtC,CAAC,MAAM,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UAChDvK,QAAQ,CAAC,CAAC,4HAA4H,EAAE,mJAAmJ,EAAE,8EAA8E,CAAC,EAAE,OAAO,CAAC;QACxX;MACF,CAAC;MACD,IAAI;QACFiJ,OAAO,CAACC,OAAO,CAAClH,gBAAgB,CAAC2H,SAAS,EAAEtB,GAAG,EAAE;UAC/C/E,KAAK,EAAEX;QACT,CAAC,CAAC,CAAC,CAACyG,IAAI,CAACoB,cAAc,IAAI;UACzBpJ,MAAM,CAAC0B,OAAO,CAAC2H,UAAU,CAAC,CAACD,cAAc,CAAC,CAAC;UAC3ChB,iBAAiB,CAAC,CAAC;QACrB,CAAC,CAAC,CAACkB,KAAK,CAACX,WAAW,CAAC;MACvB,CAAC,CAAC,OAAOK,WAAW,EAAE;QACpBL,WAAW,CAACK,WAAW,CAAC;MAC1B;IACF,CAAC,MAAM;MACLhJ,MAAM,CAAC0B,OAAO,CAAC2H,UAAU,CAAC,CAACd,SAAS,CAAC,CAAC;MACtCH,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,MAAMmB,0BAA0B,GAAG9K,KAAK,CAAC6C,WAAW,CAACG,MAAM,IAAI;IAC7D,MAAM;MACJF,EAAE;MACFC,KAAK;MACLgG,KAAK;MACLgC,UAAU;MACVC,wBAAwB,EAAEC;IAC5B,CAAC,GAAGjI,MAAM;IACVJ,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7B,MAAM2C,MAAM,GAAGnE,MAAM,CAAC0B,OAAO,CAAC0C,SAAS,CAAC5C,KAAK,CAAC;IAC9C,MAAMyF,GAAG,GAAGjH,MAAM,CAAC0B,OAAO,CAAC6B,MAAM,CAAChC,EAAE,CAAC;IACrC,IAAIoI,WAAW,GAAGnC,KAAK;IACvB,IAAIrD,MAAM,CAACyF,WAAW,IAAI,CAACF,eAAe,EAAE;MAC1CC,WAAW,GAAGxF,MAAM,CAACyF,WAAW,CAACpC,KAAK,EAAEP,GAAG,EAAE9C,MAAM,EAAEnE,MAAM,CAAC;IAC9D;IACA,IAAImC,YAAY,GAAGhD,yBAAyB,CAACa,MAAM,CAAC;IACpD,IAAIsG,QAAQ,GAAGhI,QAAQ,CAAC,CAAC,CAAC,EAAE6D,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACnDgG,KAAK,EAAEmC,WAAW;MAClBE,YAAY,EAAEL,UAAU,GAAG,2BAA2B,GAAG;IAC3D,CAAC,CAAC;IACF,IAAI,CAACrF,MAAM,CAACwD,uBAAuB,EAAE;MACnCf,wBAAwB,CAACrF,EAAE,EAAEC,KAAK,EAAE8E,QAAQ,CAAC;IAC/C;IACA,OAAO,IAAIuB,OAAO,CAACC,OAAO,IAAI;MAC5B,MAAMgC,QAAQ,GAAG,EAAE;MACnB,IAAI3F,MAAM,CAACwD,uBAAuB,EAAE;QAClC,MAAMI,UAAU,GAAGzB,QAAQ,CAACkB,KAAK,KAAKrF,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,CAACgG,KAAK;QACnElB,QAAQ,GAAGhI,QAAQ,CAAC,CAAC,CAAC,EAAEgI,QAAQ,EAAE;UAChCmB,iBAAiB,EAAE;QACrB,CAAC,CAAC;QACFb,wBAAwB,CAACrF,EAAE,EAAEC,KAAK,EAAE8E,QAAQ,CAAC;QAC7C,MAAMyD,gBAAgB,GAAG5H,YAAY,CAACZ,EAAE,CAAC;UACvCyI,gBAAgB,GAAG3L,6BAA6B,CAAC0L,gBAAgB,EAAE,CAACvI,KAAK,CAAC,CAACyI,GAAG,CAAC7L,cAAc,CAAC,CAAC;QACjG,MAAM8L,OAAO,GAAGrC,OAAO,CAACC,OAAO,CAAC3D,MAAM,CAACwD,uBAAuB,CAAC;UAC7DpG,EAAE;UACF0F,GAAG;UACHhH,KAAK,EAAEqG,QAAQ;UACfyB,UAAU;UACViC;QACF,CAAC,CAAC,CAAC,CAAChC,IAAI,CAACC,cAAc,IAAI;UACzB;UACA;UACA,IAAIjI,MAAM,CAAC0B,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKtC,YAAY,CAACoE,IAAI,EAAE;YACvDyE,OAAO,CAAC,KAAK,CAAC;YACd;UACF;UACA3F,YAAY,GAAGhD,yBAAyB,CAACa,MAAM,CAAC;UAChDiI,cAAc,GAAG3J,QAAQ,CAAC,CAAC,CAAC,EAAE2J,cAAc,EAAE;YAC5CR,iBAAiB,EAAE;UACrB,CAAC,CAAC;UACF;UACA;UACA;UACAQ,cAAc,CAACT,KAAK,GAAGrD,MAAM,CAACwD,uBAAuB,GAAGxF,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,CAACgG,KAAK,GAAGmC,WAAW;UACnG/C,wBAAwB,CAACrF,EAAE,EAAEC,KAAK,EAAEyG,cAAc,CAAC;QACrD,CAAC,CAAC;QACF6B,QAAQ,CAACK,IAAI,CAACD,OAAO,CAAC;MACxB;MACA9H,MAAM,CAACgI,OAAO,CAACjI,YAAY,CAACZ,EAAE,CAAC,CAAC,CAACqG,OAAO,CAAC,CAAC,CAACyC,SAAS,EAAE9H,UAAU,CAAC,KAAK;QACpE,IAAI8H,SAAS,KAAK7I,KAAK,EAAE;UACvB;QACF;QACA,MAAM8I,WAAW,GAAGtK,MAAM,CAAC0B,OAAO,CAAC0C,SAAS,CAACiG,SAAS,CAAC;QACvD,IAAI,CAACC,WAAW,CAAC3C,uBAAuB,EAAE;UACxC;QACF;QACApF,UAAU,GAAGjE,QAAQ,CAAC,CAAC,CAAC,EAAEiE,UAAU,EAAE;UACpCkF,iBAAiB,EAAE;QACrB,CAAC,CAAC;QACFb,wBAAwB,CAACrF,EAAE,EAAE8I,SAAS,EAAE9H,UAAU,CAAC;QACnDJ,YAAY,GAAGhD,yBAAyB,CAACa,MAAM,CAAC;QAChD,MAAMuK,iBAAiB,GAAGpI,YAAY,CAACZ,EAAE,CAAC;UACxCyI,gBAAgB,GAAG3L,6BAA6B,CAACkM,iBAAiB,EAAE,CAACF,SAAS,CAAC,CAACJ,GAAG,CAAC7L,cAAc,CAAC,CAAC;QACtG,MAAM8L,OAAO,GAAGrC,OAAO,CAACC,OAAO,CAACwC,WAAW,CAAC3C,uBAAuB,CAAC;UAClEpG,EAAE;UACF0F,GAAG;UACHhH,KAAK,EAAEsC,UAAU;UACjBwF,UAAU,EAAE,KAAK;UACjBiC;QACF,CAAC,CAAC,CAAC,CAAChC,IAAI,CAACC,cAAc,IAAI;UACzB;UACA;UACA,IAAIjI,MAAM,CAAC0B,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKtC,YAAY,CAACoE,IAAI,EAAE;YACvDyE,OAAO,CAAC,KAAK,CAAC;YACd;UACF;UACAG,cAAc,GAAG3J,QAAQ,CAAC,CAAC,CAAC,EAAE2J,cAAc,EAAE;YAC5CR,iBAAiB,EAAE;UACrB,CAAC,CAAC;UACFb,wBAAwB,CAACrF,EAAE,EAAE8I,SAAS,EAAEpC,cAAc,CAAC;QACzD,CAAC,CAAC;QACF6B,QAAQ,CAACK,IAAI,CAACD,OAAO,CAAC;MACxB,CAAC,CAAC;MACFrC,OAAO,CAAC2C,GAAG,CAACV,QAAQ,CAAC,CAAC9B,IAAI,CAAC,MAAM;QAC/B,IAAIhI,MAAM,CAAC0B,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKtC,YAAY,CAAC2D,IAAI,EAAE;UACvDT,YAAY,GAAGhD,yBAAyB,CAACa,MAAM,CAAC;UAChD8H,OAAO,CAAC,CAAC3F,YAAY,CAACZ,EAAE,CAAC,CAACC,KAAK,CAAC,CAACgB,KAAK,CAAC;QACzC,CAAC,MAAM;UACLsF,OAAO,CAAC,KAAK,CAAC;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9H,MAAM,EAAEqB,kBAAkB,EAAEuF,wBAAwB,CAAC,CAAC;EAC1D,MAAM4B,qCAAqC,GAAG/J,KAAK,CAAC6C,WAAW,CAACC,EAAE,IAAI;IACpE,MAAMY,YAAY,GAAGhD,yBAAyB,CAACa,MAAM,CAAC;IACtD,MAAMiH,GAAG,GAAGjH,MAAM,CAAC0B,OAAO,CAAC6B,MAAM,CAAChC,EAAE,CAAC;IACrC,IAAI,CAACY,YAAY,CAACZ,EAAE,CAAC,EAAE;MACrB,OAAOvB,MAAM,CAAC0B,OAAO,CAAC6B,MAAM,CAAChC,EAAE,CAAC;IAClC;IACA,IAAIgH,SAAS,GAAGjK,QAAQ,CAAC,CAAC,CAAC,EAAEkC,mBAAmB,CAACkB,OAAO,CAACH,EAAE,CAAC,EAAE0F,GAAG,CAAC;IAClE7E,MAAM,CAACgI,OAAO,CAACjI,YAAY,CAACZ,EAAE,CAAC,CAAC,CAACqG,OAAO,CAAC,CAAC,CAACpG,KAAK,EAAEe,UAAU,CAAC,KAAK;MAChE,MAAM4B,MAAM,GAAGnE,MAAM,CAAC0B,OAAO,CAAC0C,SAAS,CAAC5C,KAAK,CAAC;MAC9C;MACA;MACA,IAAI2C,MAAM,EAAEsG,WAAW,EAAE;QACvBlC,SAAS,GAAGpE,MAAM,CAACsG,WAAW,CAAClI,UAAU,CAACiF,KAAK,EAAEe,SAAS,EAAEpE,MAAM,EAAEnE,MAAM,CAAC;MAC7E,CAAC,MAAM;QACLuI,SAAS,CAAC/G,KAAK,CAAC,GAAGe,UAAU,CAACiF,KAAK;MACrC;IACF,CAAC,CAAC;IACF,OAAOe,SAAS;EAClB,CAAC,EAAE,CAACvI,MAAM,CAAC,CAAC;EACZ,MAAM0K,UAAU,GAAG;IACjB1I,UAAU;IACVwD,gBAAgB;IAChBK;EACF,CAAC;EACD,MAAM8E,iBAAiB,GAAG;IACxBpB,0BAA0B;IAC1Bf;EACF,CAAC;EACDtJ,gBAAgB,CAACc,MAAM,EAAE0K,UAAU,EAAE,QAAQ,CAAC;EAC9CxL,gBAAgB,CAACc,MAAM,EAAE2K,iBAAiB,EAAE,SAAS,CAAC;EACtDlM,KAAK,CAACgF,SAAS,CAAC,MAAM;IACpB,IAAI3C,iBAAiB,EAAE;MACrBmF,mBAAmB,CAACnF,iBAAiB,CAAC;IACxC;EACF,CAAC,EAAE,CAACA,iBAAiB,EAAEmF,mBAAmB,CAAC,CAAC;;EAE5C;EACAtH,iBAAiB,CAAC,MAAM;IACtB,MAAMiM,UAAU,GAAGnL,sBAAsB,CAACO,MAAM,CAAC;;IAEjD;IACA,MAAM6K,uBAAuB,GAAGtK,iBAAiB,CAACmB,OAAO;IACzDnB,iBAAiB,CAACmB,OAAO,GAAGhC,SAAS,CAACQ,aAAa,CAAC,CAAC,CAAC;;IAEtD,MAAM4K,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAG3I,MAAM,CAACyE,IAAI,CAAC3G,aAAa,CAAC,EAAE,GAAGkC,MAAM,CAACyE,IAAI,CAACgE,uBAAuB,CAAC,CAAC,CAAC;IAC7FG,KAAK,CAACC,IAAI,CAACH,GAAG,CAAC,CAAClD,OAAO,CAACrG,EAAE,IAAI;MAC5B,MAAME,MAAM,GAAGvB,aAAa,CAACqB,EAAE,CAAC,IAAI;QAClCQ,IAAI,EAAE9C,YAAY,CAACoE;MACrB,CAAC;MACD,MAAM6H,QAAQ,GAAGL,uBAAuB,CAACtJ,EAAE,CAAC,EAAEQ,IAAI,IAAI9C,YAAY,CAACoE,IAAI;MACvE,MAAM8H,UAAU,GAAGP,UAAU,CAACrJ,EAAE,CAAC,GAAGvB,MAAM,CAAC0B,OAAO,CAAC0J,QAAQ,CAACR,UAAU,CAACrJ,EAAE,CAAC,CAAC,GAAGA,EAAE;MAChF,IAAIE,MAAM,CAACM,IAAI,KAAK9C,YAAY,CAAC2D,IAAI,IAAIsI,QAAQ,KAAKjM,YAAY,CAACoE,IAAI,EAAE;QACvE0D,6BAA6B,CAACzI,QAAQ,CAAC;UACrCiD,EAAE,EAAE4J;QACN,CAAC,EAAE1J,MAAM,CAAC,CAAC;MACb,CAAC,MAAM,IAAIA,MAAM,CAACM,IAAI,KAAK9C,YAAY,CAACoE,IAAI,IAAI6H,QAAQ,KAAKjM,YAAY,CAAC2D,IAAI,EAAE;QAC9EsF,4BAA4B,CAAC5J,QAAQ,CAAC;UACpCiD,EAAE,EAAE4J;QACN,CAAC,EAAE1J,MAAM,CAAC,CAAC;MACb;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,MAAM,EAAEE,aAAa,EAAE6G,6BAA6B,EAAEmB,4BAA4B,CAAC,CAAC;AAC1F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}