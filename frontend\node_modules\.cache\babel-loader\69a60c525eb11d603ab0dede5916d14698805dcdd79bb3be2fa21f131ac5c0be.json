{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridCellEditing } from \"./useGridCellEditing.js\";\nimport { GridCellModes, GridEditModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridRowEditing } from \"./useGridRowEditing.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isAutogeneratedRowNode } from \"../rows/gridRowsUtils.js\";\nexport const editingStateInitializer = state => _extends({}, state, {\n  editRows: {}\n});\nexport const useGridEditing = (apiRef, props) => {\n  useGridCellEditing(apiRef, props);\n  useGridRowEditing(apiRef, props);\n  const debounceMap = React.useRef({});\n  const {\n    isCellEditable: isCellEditableProp\n  } = props;\n  const isCellEditable = React.useCallback(params => {\n    if (isAutogeneratedRowNode(params.rowNode)) {\n      return false;\n    }\n    if (!params.colDef.editable) {\n      return false;\n    }\n    if (!params.colDef.renderEditCell) {\n      return false;\n    }\n    if (isCellEditableProp) {\n      return isCellEditableProp(params);\n    }\n    return true;\n  }, [isCellEditableProp]);\n  const maybeDebounce = (id, field, debounceMs, callback) => {\n    if (!debounceMs) {\n      callback();\n      return;\n    }\n    if (!debounceMap.current[id]) {\n      debounceMap.current[id] = {};\n    }\n    if (debounceMap.current[id][field]) {\n      const [timeout] = debounceMap.current[id][field];\n      clearTimeout(timeout);\n    }\n\n    // To run the callback immediately without waiting the timeout\n    const runImmediately = () => {\n      const [timeout] = debounceMap.current[id][field];\n      clearTimeout(timeout);\n      callback();\n      delete debounceMap.current[id][field];\n    };\n    const timeout = setTimeout(() => {\n      callback();\n      delete debounceMap.current[id][field];\n    }, debounceMs);\n    debounceMap.current[id][field] = [timeout, runImmediately];\n  };\n  React.useEffect(() => {\n    const debounces = debounceMap.current;\n    return () => {\n      Object.entries(debounces).forEach(([id, fields]) => {\n        Object.keys(fields).forEach(field => {\n          const [timeout] = debounces[id][field];\n          clearTimeout(timeout);\n          delete debounces[id][field];\n        });\n      });\n    };\n  }, []);\n  const runPendingEditCellValueMutation = React.useCallback((id, field) => {\n    if (!debounceMap.current[id]) {\n      return;\n    }\n    if (!field) {\n      Object.keys(debounceMap.current[id]).forEach(debouncedField => {\n        const [, runCallback] = debounceMap.current[id][debouncedField];\n        runCallback();\n      });\n    } else if (debounceMap.current[id][field]) {\n      const [, runCallback] = debounceMap.current[id][field];\n      runCallback();\n    }\n  }, []);\n  const setEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      debounceMs\n    } = params;\n    return new Promise(resolve => {\n      maybeDebounce(id, field, debounceMs, async () => {\n        const setEditCellValueToCall = props.editMode === GridEditModes.Row ? apiRef.current.setRowEditingEditCellValue : apiRef.current.setCellEditingEditCellValue;\n\n        // Check if the cell is in edit mode\n        // By the time this callback runs the user may have cancelled the editing\n        if (apiRef.current.getCellMode(id, field) === GridCellModes.Edit) {\n          const result = await setEditCellValueToCall(params);\n          resolve(result);\n        }\n      });\n    });\n  }, [apiRef, props.editMode]);\n  const getRowWithUpdatedValues = React.useCallback((id, field) => {\n    return props.editMode === GridEditModes.Cell ? apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field) : apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n  }, [apiRef, props.editMode]);\n  const getEditCellMeta = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    return editingState[id]?.[field] ?? null;\n  }, [apiRef]);\n  const editingSharedApi = {\n    isCellEditable,\n    setEditCellValue,\n    getRowWithUpdatedValues,\n    unstable_getEditCellMeta: getEditCellMeta\n  };\n  const editingSharedPrivateApi = {\n    runPendingEditCellValueMutation\n  };\n  useGridApiMethod(apiRef, editingSharedApi, 'public');\n  useGridApiMethod(apiRef, editingSharedPrivateApi, 'private');\n};", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "useGridCellEditing", "GridCellModes", "GridEditModes", "useGridRowEditing", "gridEditRowsStateSelector", "isAutogeneratedRowNode", "editingStateInitializer", "state", "editRows", "useGridEditing", "apiRef", "props", "debounceMap", "useRef", "isCellEditable", "isCellEditableProp", "useCallback", "params", "rowNode", "colDef", "editable", "renderEditCell", "maybeDebounce", "id", "field", "debounceMs", "callback", "current", "timeout", "clearTimeout", "runImmediately", "setTimeout", "useEffect", "debounces", "Object", "entries", "for<PERSON>ach", "fields", "keys", "runPendingEditCellValueMutation", "debouncedField", "<PERSON><PERSON><PERSON><PERSON>", "setEditCellValue", "Promise", "resolve", "setEditCellValueToCall", "editMode", "Row", "setRowEditingEditCellValue", "setCellEditingEditCellValue", "getCellMode", "Edit", "result", "getRowWithUpdatedValues", "Cell", "getRowWithUpdatedValuesFromCellEditing", "getRowWithUpdatedValuesFromRowEditing", "getEditCellMeta", "editingState", "editingSharedApi", "unstable_getEditCellMeta", "editingSharedPrivateApi"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/editing/useGridEditing.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridCellEditing } from \"./useGridCellEditing.js\";\nimport { GridCellModes, GridEditModes } from \"../../../models/gridEditRowModel.js\";\nimport { useGridRowEditing } from \"./useGridRowEditing.js\";\nimport { gridEditRowsStateSelector } from \"./gridEditingSelectors.js\";\nimport { isAutogeneratedRowNode } from \"../rows/gridRowsUtils.js\";\nexport const editingStateInitializer = state => _extends({}, state, {\n  editRows: {}\n});\nexport const useGridEditing = (apiRef, props) => {\n  useGridCellEditing(apiRef, props);\n  useGridRowEditing(apiRef, props);\n  const debounceMap = React.useRef({});\n  const {\n    isCellEditable: isCellEditableProp\n  } = props;\n  const isCellEditable = React.useCallback(params => {\n    if (isAutogeneratedRowNode(params.rowNode)) {\n      return false;\n    }\n    if (!params.colDef.editable) {\n      return false;\n    }\n    if (!params.colDef.renderEditCell) {\n      return false;\n    }\n    if (isCellEditableProp) {\n      return isCellEditableProp(params);\n    }\n    return true;\n  }, [isCellEditableProp]);\n  const maybeDebounce = (id, field, debounceMs, callback) => {\n    if (!debounceMs) {\n      callback();\n      return;\n    }\n    if (!debounceMap.current[id]) {\n      debounceMap.current[id] = {};\n    }\n    if (debounceMap.current[id][field]) {\n      const [timeout] = debounceMap.current[id][field];\n      clearTimeout(timeout);\n    }\n\n    // To run the callback immediately without waiting the timeout\n    const runImmediately = () => {\n      const [timeout] = debounceMap.current[id][field];\n      clearTimeout(timeout);\n      callback();\n      delete debounceMap.current[id][field];\n    };\n    const timeout = setTimeout(() => {\n      callback();\n      delete debounceMap.current[id][field];\n    }, debounceMs);\n    debounceMap.current[id][field] = [timeout, runImmediately];\n  };\n  React.useEffect(() => {\n    const debounces = debounceMap.current;\n    return () => {\n      Object.entries(debounces).forEach(([id, fields]) => {\n        Object.keys(fields).forEach(field => {\n          const [timeout] = debounces[id][field];\n          clearTimeout(timeout);\n          delete debounces[id][field];\n        });\n      });\n    };\n  }, []);\n  const runPendingEditCellValueMutation = React.useCallback((id, field) => {\n    if (!debounceMap.current[id]) {\n      return;\n    }\n    if (!field) {\n      Object.keys(debounceMap.current[id]).forEach(debouncedField => {\n        const [, runCallback] = debounceMap.current[id][debouncedField];\n        runCallback();\n      });\n    } else if (debounceMap.current[id][field]) {\n      const [, runCallback] = debounceMap.current[id][field];\n      runCallback();\n    }\n  }, []);\n  const setEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      debounceMs\n    } = params;\n    return new Promise(resolve => {\n      maybeDebounce(id, field, debounceMs, async () => {\n        const setEditCellValueToCall = props.editMode === GridEditModes.Row ? apiRef.current.setRowEditingEditCellValue : apiRef.current.setCellEditingEditCellValue;\n\n        // Check if the cell is in edit mode\n        // By the time this callback runs the user may have cancelled the editing\n        if (apiRef.current.getCellMode(id, field) === GridCellModes.Edit) {\n          const result = await setEditCellValueToCall(params);\n          resolve(result);\n        }\n      });\n    });\n  }, [apiRef, props.editMode]);\n  const getRowWithUpdatedValues = React.useCallback((id, field) => {\n    return props.editMode === GridEditModes.Cell ? apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field) : apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n  }, [apiRef, props.editMode]);\n  const getEditCellMeta = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef);\n    return editingState[id]?.[field] ?? null;\n  }, [apiRef]);\n  const editingSharedApi = {\n    isCellEditable,\n    setEditCellValue,\n    getRowWithUpdatedValues,\n    unstable_getEditCellMeta: getEditCellMeta\n  };\n  const editingSharedPrivateApi = {\n    runPendingEditCellValueMutation\n  };\n  useGridApiMethod(apiRef, editingSharedApi, 'public');\n  useGridApiMethod(apiRef, editingSharedPrivateApi, 'private');\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,aAAa,EAAEC,aAAa,QAAQ,qCAAqC;AAClF,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,yBAAyB,QAAQ,2BAA2B;AACrE,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,OAAO,MAAMC,uBAAuB,GAAGC,KAAK,IAAIV,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAE;EAClEC,QAAQ,EAAE,CAAC;AACb,CAAC,CAAC;AACF,OAAO,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC/CX,kBAAkB,CAACU,MAAM,EAAEC,KAAK,CAAC;EACjCR,iBAAiB,CAACO,MAAM,EAAEC,KAAK,CAAC;EAChC,MAAMC,WAAW,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC;EACpC,MAAM;IACJC,cAAc,EAAEC;EAClB,CAAC,GAAGJ,KAAK;EACT,MAAMG,cAAc,GAAGhB,KAAK,CAACkB,WAAW,CAACC,MAAM,IAAI;IACjD,IAAIZ,sBAAsB,CAACY,MAAM,CAACC,OAAO,CAAC,EAAE;MAC1C,OAAO,KAAK;IACd;IACA,IAAI,CAACD,MAAM,CAACE,MAAM,CAACC,QAAQ,EAAE;MAC3B,OAAO,KAAK;IACd;IACA,IAAI,CAACH,MAAM,CAACE,MAAM,CAACE,cAAc,EAAE;MACjC,OAAO,KAAK;IACd;IACA,IAAIN,kBAAkB,EAAE;MACtB,OAAOA,kBAAkB,CAACE,MAAM,CAAC;IACnC;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACF,kBAAkB,CAAC,CAAC;EACxB,MAAMO,aAAa,GAAGA,CAACC,EAAE,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IACzD,IAAI,CAACD,UAAU,EAAE;MACfC,QAAQ,CAAC,CAAC;MACV;IACF;IACA,IAAI,CAACd,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,EAAE;MAC5BX,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,GAAG,CAAC,CAAC;IAC9B;IACA,IAAIX,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MAClC,MAAM,CAACI,OAAO,CAAC,GAAGhB,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACC,KAAK,CAAC;MAChDK,YAAY,CAACD,OAAO,CAAC;IACvB;;IAEA;IACA,MAAME,cAAc,GAAGA,CAAA,KAAM;MAC3B,MAAM,CAACF,OAAO,CAAC,GAAGhB,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACC,KAAK,CAAC;MAChDK,YAAY,CAACD,OAAO,CAAC;MACrBF,QAAQ,CAAC,CAAC;MACV,OAAOd,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACC,KAAK,CAAC;IACvC,CAAC;IACD,MAAMI,OAAO,GAAGG,UAAU,CAAC,MAAM;MAC/BL,QAAQ,CAAC,CAAC;MACV,OAAOd,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACC,KAAK,CAAC;IACvC,CAAC,EAAEC,UAAU,CAAC;IACdb,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACC,KAAK,CAAC,GAAG,CAACI,OAAO,EAAEE,cAAc,CAAC;EAC5D,CAAC;EACDhC,KAAK,CAACkC,SAAS,CAAC,MAAM;IACpB,MAAMC,SAAS,GAAGrB,WAAW,CAACe,OAAO;IACrC,OAAO,MAAM;MACXO,MAAM,CAACC,OAAO,CAACF,SAAS,CAAC,CAACG,OAAO,CAAC,CAAC,CAACb,EAAE,EAAEc,MAAM,CAAC,KAAK;QAClDH,MAAM,CAACI,IAAI,CAACD,MAAM,CAAC,CAACD,OAAO,CAACZ,KAAK,IAAI;UACnC,MAAM,CAACI,OAAO,CAAC,GAAGK,SAAS,CAACV,EAAE,CAAC,CAACC,KAAK,CAAC;UACtCK,YAAY,CAACD,OAAO,CAAC;UACrB,OAAOK,SAAS,CAACV,EAAE,CAAC,CAACC,KAAK,CAAC;QAC7B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMe,+BAA+B,GAAGzC,KAAK,CAACkB,WAAW,CAAC,CAACO,EAAE,EAAEC,KAAK,KAAK;IACvE,IAAI,CAACZ,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,EAAE;MAC5B;IACF;IACA,IAAI,CAACC,KAAK,EAAE;MACVU,MAAM,CAACI,IAAI,CAAC1B,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAAC,CAACa,OAAO,CAACI,cAAc,IAAI;QAC7D,MAAM,GAAGC,WAAW,CAAC,GAAG7B,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACiB,cAAc,CAAC;QAC/DC,WAAW,CAAC,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI7B,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACzC,MAAM,GAAGiB,WAAW,CAAC,GAAG7B,WAAW,CAACe,OAAO,CAACJ,EAAE,CAAC,CAACC,KAAK,CAAC;MACtDiB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,gBAAgB,GAAG5C,KAAK,CAACkB,WAAW,CAACC,MAAM,IAAI;IACnD,MAAM;MACJM,EAAE;MACFC,KAAK;MACLC;IACF,CAAC,GAAGR,MAAM;IACV,OAAO,IAAI0B,OAAO,CAACC,OAAO,IAAI;MAC5BtB,aAAa,CAACC,EAAE,EAAEC,KAAK,EAAEC,UAAU,EAAE,YAAY;QAC/C,MAAMoB,sBAAsB,GAAGlC,KAAK,CAACmC,QAAQ,KAAK5C,aAAa,CAAC6C,GAAG,GAAGrC,MAAM,CAACiB,OAAO,CAACqB,0BAA0B,GAAGtC,MAAM,CAACiB,OAAO,CAACsB,2BAA2B;;QAE5J;QACA;QACA,IAAIvC,MAAM,CAACiB,OAAO,CAACuB,WAAW,CAAC3B,EAAE,EAAEC,KAAK,CAAC,KAAKvB,aAAa,CAACkD,IAAI,EAAE;UAChE,MAAMC,MAAM,GAAG,MAAMP,sBAAsB,CAAC5B,MAAM,CAAC;UACnD2B,OAAO,CAACQ,MAAM,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1C,MAAM,EAAEC,KAAK,CAACmC,QAAQ,CAAC,CAAC;EAC5B,MAAMO,uBAAuB,GAAGvD,KAAK,CAACkB,WAAW,CAAC,CAACO,EAAE,EAAEC,KAAK,KAAK;IAC/D,OAAOb,KAAK,CAACmC,QAAQ,KAAK5C,aAAa,CAACoD,IAAI,GAAG5C,MAAM,CAACiB,OAAO,CAAC4B,sCAAsC,CAAChC,EAAE,EAAEC,KAAK,CAAC,GAAGd,MAAM,CAACiB,OAAO,CAAC6B,qCAAqC,CAACjC,EAAE,CAAC;EAC5K,CAAC,EAAE,CAACb,MAAM,EAAEC,KAAK,CAACmC,QAAQ,CAAC,CAAC;EAC5B,MAAMW,eAAe,GAAG3D,KAAK,CAACkB,WAAW,CAAC,CAACO,EAAE,EAAEC,KAAK,KAAK;IACvD,MAAMkC,YAAY,GAAGtD,yBAAyB,CAACM,MAAM,CAAC;IACtD,OAAOgD,YAAY,CAACnC,EAAE,CAAC,GAAGC,KAAK,CAAC,IAAI,IAAI;EAC1C,CAAC,EAAE,CAACd,MAAM,CAAC,CAAC;EACZ,MAAMiD,gBAAgB,GAAG;IACvB7C,cAAc;IACd4B,gBAAgB;IAChBW,uBAAuB;IACvBO,wBAAwB,EAAEH;EAC5B,CAAC;EACD,MAAMI,uBAAuB,GAAG;IAC9BtB;EACF,CAAC;EACDxC,gBAAgB,CAACW,MAAM,EAAEiD,gBAAgB,EAAE,QAAQ,CAAC;EACpD5D,gBAAgB,CAACW,MAAM,EAAEmD,uBAAuB,EAAE,SAAS,CAAC;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}