{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { roundToDecimalPlaces } from '@mui/x-internals/math';\nimport { lruMemoize } from '@mui/x-internals/lruMemoize';\nimport { useStoreEffect } from '@mui/x-internals/store';\nimport { useVirtualizer, Dimensions } from '@mui/x-virtualizer';\nimport { useFirstRender } from \"../utils/useFirstRender.js\";\nimport { createSelector } from \"../../utils/createSelector.js\";\nimport { useGridSelector } from \"../utils/useGridSelector.js\";\nimport { gridHasFillerSelector, gridVerticalScrollbarWidthSelector } from \"../features/dimensions/gridDimensionsSelectors.js\";\nimport { gridDensityFactorSelector } from \"../features/density/index.js\";\nimport { gridVisibleColumnDefinitionsSelector, gridVisiblePinnedColumnDefinitionsSelector, gridColumnPositionsSelector, gridHasColSpanSelector } from \"../features/columns/gridColumnsSelector.js\";\nimport { gridPinnedRowsSelector, gridRowCountSelector } from \"../features/rows/gridRowsSelector.js\";\nimport { useGridVisibleRows } from \"../utils/useGridVisibleRows.js\";\nimport { gridPaginationSelector } from \"../features/pagination/index.js\";\nimport { gridFocusedVirtualCellSelector } from \"../features/virtualization/gridFocusedVirtualCellSelector.js\";\nimport { gridRowSelectionManagerSelector } from \"../features/rowSelection/index.js\";\nimport { DATA_GRID_PROPS_DEFAULT_VALUES } from \"../../constants/dataGridPropsDefaultValues.js\";\nimport { getValidRowHeight, minimalContentHeight, rowHeightWarning } from \"../features/rows/gridRowsUtils.js\";\nimport { getTotalHeaderHeight } from \"../features/columns/gridColumnsUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction identity(x) {\n  return x;\n}\nconst columnsTotalWidthSelector = createSelector(gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector, (visibleColumns, positions) => {\n  const colCount = visibleColumns.length;\n  if (colCount === 0) {\n    return 0;\n  }\n  return roundToDecimalPlaces(positions[colCount - 1] + visibleColumns[colCount - 1].computedWidth, 1);\n});\n\n/** Translates virtualizer state to grid state */\nconst addGridDimensionsCreator = () => lruMemoize((dimensions, headerHeight, groupHeaderHeight, headerFilterHeight, headersTotalHeight) => {\n  return _extends({}, dimensions, {\n    headerHeight,\n    groupHeaderHeight,\n    headerFilterHeight,\n    headersTotalHeight\n  });\n}, {\n  maxSize: 1\n});\n\n/**\n * Virtualizer setup\n */\nexport function useGridVirtualizer(apiRef, rootProps) {\n  const isRtl = useRtl();\n  const {\n    listView\n  } = rootProps;\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const pinnedRows = useGridSelector(apiRef, gridPinnedRowsSelector);\n  const pinnedColumns = gridVisiblePinnedColumnDefinitionsSelector(apiRef);\n  const rowSelectionManager = useGridSelector(apiRef, gridRowSelectionManagerSelector);\n  const isRowSelected = id => rowSelectionManager.has(id) && apiRef.current.isRowSelectable(id);\n  const currentPage = useGridVisibleRows(apiRef);\n  const hasColSpan = useGridSelector(apiRef, gridHasColSpanSelector);\n  const verticalScrollbarWidth = useGridSelector(apiRef, gridVerticalScrollbarWidthSelector);\n  const hasFiller = useGridSelector(apiRef, gridHasFillerSelector);\n  const {\n    autoHeight\n  } = rootProps;\n  const scrollReset = listView;\n\n  // <DIMENSIONS>\n  const density = useGridSelector(apiRef, gridDensityFactorSelector);\n  const baseRowHeight = getValidRowHeight(rootProps.rowHeight, DATA_GRID_PROPS_DEFAULT_VALUES.rowHeight, rowHeightWarning);\n  const rowHeight = Math.floor(baseRowHeight * density);\n  const headerHeight = Math.floor(rootProps.columnHeaderHeight * density);\n  const groupHeaderHeight = Math.floor((rootProps.columnGroupHeaderHeight ?? rootProps.columnHeaderHeight) * density);\n  const headerFilterHeight = Math.floor((rootProps.headerFilterHeight ?? rootProps.columnHeaderHeight) * density);\n  const columnsTotalWidth = useGridSelector(apiRef, columnsTotalWidthSelector);\n  const headersTotalHeight = getTotalHeaderHeight(apiRef, rootProps);\n  const leftPinnedWidth = pinnedColumns.left.reduce((w, col) => w + col.computedWidth, 0);\n  const rightPinnedWidth = pinnedColumns.right.reduce((w, col) => w + col.computedWidth, 0);\n  const dimensionsParams = {\n    rowHeight,\n    headerHeight,\n    columnsTotalWidth,\n    leftPinnedWidth,\n    rightPinnedWidth,\n    topPinnedHeight: headersTotalHeight,\n    bottomPinnedHeight: 0,\n    scrollbarSize: rootProps.scrollbarSize\n  };\n  const addGridDimensions = useLazyRef(addGridDimensionsCreator).current;\n\n  // </DIMENSIONS>\n\n  // <ROWS_META>\n  const dataRowCount = useGridSelector(apiRef, gridRowCountSelector);\n  const pagination = useGridSelector(apiRef, gridPaginationSelector);\n  const rowCount = Math.min(pagination.enabled ? pagination.paginationModel.pageSize : dataRowCount, dataRowCount);\n  const {\n    getRowHeight,\n    getEstimatedRowHeight,\n    getRowSpacing\n  } = rootProps;\n  // </ROWS_META>\n\n  const focusedVirtualCell = useGridSelector(apiRef, gridFocusedVirtualCellSelector);\n  const virtualizer = useVirtualizer({\n    refs: {\n      container: apiRef.current.mainElementRef,\n      scroller: apiRef.current.virtualScrollerRef,\n      scrollbarVertical: apiRef.current.virtualScrollbarVerticalRef,\n      scrollbarHorizontal: apiRef.current.virtualScrollbarHorizontalRef\n    },\n    dimensions: dimensionsParams,\n    virtualization: {\n      isRtl,\n      rowBufferPx: rootProps.rowBufferPx,\n      columnBufferPx: rootProps.columnBufferPx\n    },\n    colspan: {\n      enabled: hasColSpan,\n      getColspan: (rowId, column) => {\n        if (typeof column.colSpan === 'function') {\n          const row = apiRef.current.getRow(rowId);\n          const value = apiRef.current.getRowValue(row, column);\n          return column.colSpan(value, row, column, apiRef) ?? 0;\n        }\n        return column.colSpan ?? 1;\n      }\n    },\n    initialState: {\n      scroll: rootProps.initialState?.scroll,\n      rowSpanning: apiRef.current.state.rowSpanning,\n      virtualization: apiRef.current.state.virtualization\n    },\n    rows: currentPage.rows,\n    range: currentPage.range,\n    rowCount,\n    columns: visibleColumns,\n    pinnedRows,\n    pinnedColumns,\n    autoHeight,\n    minimalContentHeight,\n    getRowHeight: React.useMemo(() => {\n      if (!getRowHeight) {\n        return undefined;\n      }\n      return rowEntry => getRowHeight(_extends({}, rowEntry, {\n        densityFactor: density\n      }));\n    }, [getRowHeight, density]),\n    getEstimatedRowHeight: React.useMemo(() => getEstimatedRowHeight ? rowEntry => getEstimatedRowHeight(_extends({}, rowEntry, {\n      densityFactor: density\n    })) : undefined, [getEstimatedRowHeight, density]),\n    getRowSpacing: React.useMemo(() => getRowSpacing ? rowEntry => {\n      const indexRelativeToCurrentPage = currentPage.rowIdToIndexMap.get(rowEntry.id) ?? -1;\n      const visibility = {\n        isFirstVisible: indexRelativeToCurrentPage === 0,\n        isLastVisible: indexRelativeToCurrentPage === currentPage.rows.length - 1,\n        indexRelativeToCurrentPage\n      };\n      return getRowSpacing(_extends({}, rowEntry, visibility, {\n        indexRelativeToCurrentPage: apiRef.current.getRowIndexRelativeToVisibleRows(rowEntry.id)\n      }));\n    } : undefined, [apiRef, getRowSpacing, currentPage.rows, currentPage.rowIdToIndexMap]),\n    applyRowHeight: useEventCallback((entry, row) => apiRef.current.unstable_applyPipeProcessors('rowHeight', entry, row)),\n    virtualizeColumnsWithAutoRowHeight: rootProps.virtualizeColumnsWithAutoRowHeight,\n    focusedVirtualCell: useEventCallback(() => focusedVirtualCell),\n    resizeThrottleMs: rootProps.resizeThrottleMs,\n    onResize: useEventCallback(size => apiRef.current.publishEvent('resize', size)),\n    onWheel: useEventCallback(event => {\n      apiRef.current.publishEvent('virtualScrollerWheel', {}, event);\n    }),\n    onTouchMove: useEventCallback(event => {\n      apiRef.current.publishEvent('virtualScrollerTouchMove', {}, event);\n    }),\n    onRenderContextChange: useEventCallback(nextRenderContext => {\n      apiRef.current.publishEvent('renderedRowsIntervalChange', nextRenderContext);\n    }),\n    onScrollChange: (scrollPosition, nextRenderContext) => {\n      apiRef.current.publishEvent('scrollPositionChange', {\n        top: scrollPosition.top,\n        left: scrollPosition.left,\n        renderContext: nextRenderContext\n      });\n    },\n    scrollReset,\n    renderRow: params => /*#__PURE__*/_jsx(rootProps.slots.row, _extends({\n      row: params.model,\n      rowId: params.id,\n      index: params.rowIndex,\n      selected: isRowSelected(params.id),\n      offsetLeft: params.offsetLeft,\n      columnsTotalWidth: columnsTotalWidth,\n      rowHeight: params.baseRowHeight,\n      pinnedColumns: pinnedColumns,\n      visibleColumns: visibleColumns,\n      firstColumnIndex: params.firstColumnIndex,\n      lastColumnIndex: params.lastColumnIndex,\n      focusedColumnIndex: params.focusedColumnIndex,\n      isFirstVisible: params.isFirstVisible,\n      isLastVisible: params.isLastVisible,\n      isNotVisible: params.isVirtualFocusRow,\n      showBottomBorder: params.showBottomBorder,\n      scrollbarWidth: verticalScrollbarWidth,\n      gridHasFiller: hasFiller\n    }, rootProps.slotProps?.row), params.id),\n    renderInfiniteLoadingTrigger: id => apiRef.current.getInfiniteLoadingTriggerElement?.({\n      lastRowId: id\n    })\n  });\n\n  // HACK: Keep the grid's store in sync with the virtualizer store. We set up the\n  // subscription in the render phase rather than in an effect because other grid\n  // initialization code runs between those two moments.\n  //\n  // TODO(v9): Remove this\n  useFirstRender(() => {\n    apiRef.current.store.state.dimensions = addGridDimensions(virtualizer.store.state.dimensions, headerHeight, groupHeaderHeight, headerFilterHeight, headersTotalHeight);\n    apiRef.current.store.state.rowsMeta = virtualizer.store.state.rowsMeta;\n    apiRef.current.store.state.virtualization = virtualizer.store.state.virtualization;\n  });\n  useStoreEffect(virtualizer.store, Dimensions.selectors.dimensions, (_, dimensions) => {\n    apiRef.current.setState(gridState => _extends({}, gridState, {\n      dimensions: addGridDimensions(dimensions, headerHeight, groupHeaderHeight, headerFilterHeight, headersTotalHeight)\n    }));\n  });\n  useStoreEffect(virtualizer.store, identity, (_, state) => {\n    if (state.rowsMeta !== apiRef.current.state.rowsMeta) {\n      apiRef.current.setState(gridState => _extends({}, gridState, {\n        rowsMeta: state.rowsMeta\n      }));\n    }\n    if (state.virtualization !== apiRef.current.state.virtualization) {\n      apiRef.current.setState(gridState => _extends({}, gridState, {\n        virtualization: state.virtualization\n      }));\n    }\n  });\n  apiRef.current.register('private', {\n    virtualizer\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useLazyRef", "useEventCallback", "useRtl", "roundToDecimalPlaces", "lruMemoize", "useStoreEffect", "useVirtualizer", "Dimensions", "useFirstRender", "createSelector", "useGridSelector", "gridHasFillerSelector", "gridVerticalScrollbarWidthSelector", "gridDensityFactorSelector", "gridVisibleColumnDefinitionsSelector", "gridVisiblePinnedColumnDefinitionsSelector", "gridColumnPositionsSelector", "gridHasColSpanSelector", "gridPinnedRowsSelector", "gridRowCountSelector", "useGridVisibleRows", "gridPaginationSelector", "gridFocusedVirtualCellSelector", "gridRowSelectionManagerSelector", "DATA_GRID_PROPS_DEFAULT_VALUES", "getValidRowHeight", "minimalContentHeight", "rowHeightWarning", "getTotalHeaderHeight", "jsx", "_jsx", "identity", "x", "columnsTotalWidthSelector", "visibleColumns", "positions", "col<PERSON>ount", "length", "computedWidth", "addGridDimensionsCreator", "dimensions", "headerHeight", "groupHeaderHeight", "headerFilterHeight", "headersTotalHeight", "maxSize", "useGridVirtualizer", "apiRef", "rootProps", "isRtl", "listView", "pinnedRows", "pinnedColumns", "rowSelectionManager", "isRowSelected", "id", "has", "current", "isRowSelectable", "currentPage", "hasColSpan", "verticalScrollbarWidth", "has<PERSON><PERSON>r", "autoHeight", "scrollReset", "density", "baseRowHeight", "rowHeight", "Math", "floor", "columnHeaderHeight", "columnGroupHeaderHeight", "columnsTotalWidth", "leftPinnedWidth", "left", "reduce", "w", "col", "right<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "right", "dimensionsParams", "topPinnedHeight", "bottomPinnedHeight", "scrollbarSize", "addGridDimensions", "dataRowCount", "pagination", "rowCount", "min", "enabled", "paginationModel", "pageSize", "getRowHeight", "getEstimatedRowHeight", "getRowSpacing", "focusedVirtualCell", "virtualizer", "refs", "container", "mainElementRef", "scroller", "virtualScrollerRef", "scrollbarVertical", "virtualScrollbarVerticalRef", "scrollbarHorizontal", "virtualScrollbarHorizontalRef", "virtualization", "rowBufferPx", "columnBufferPx", "colspan", "getColspan", "rowId", "column", "colSpan", "row", "getRow", "value", "getRowValue", "initialState", "scroll", "rowSpanning", "state", "rows", "range", "columns", "useMemo", "undefined", "rowEntry", "densityFactor", "indexRelativeToCurrentPage", "rowIdToIndexMap", "get", "visibility", "isFirstVisible", "isLastVisible", "getRowIndexRelativeToVisibleRows", "applyRowHeight", "entry", "unstable_applyPipeProcessors", "virtualizeColumnsWithAutoRowHeight", "resizeThrottleMs", "onResize", "size", "publishEvent", "onWheel", "event", "onTouchMove", "onRenderContextChange", "nextRenderContext", "onScrollChange", "scrollPosition", "top", "renderContext", "renderRow", "params", "slots", "model", "index", "rowIndex", "selected", "offsetLeft", "firstColumnIndex", "lastColumnIndex", "focusedColumnIndex", "isNotVisible", "isVirtualFocusRow", "showBottomBorder", "scrollbarWidth", "gridHasFiller", "slotProps", "renderInfiniteLoadingTrigger", "getInfiniteLoadingTriggerElement", "lastRowId", "store", "rowsMeta", "selectors", "_", "setState", "gridState", "register"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/core/useGridVirtualizer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { roundToDecimalPlaces } from '@mui/x-internals/math';\nimport { lruMemoize } from '@mui/x-internals/lruMemoize';\nimport { useStoreEffect } from '@mui/x-internals/store';\nimport { useVirtualizer, Dimensions } from '@mui/x-virtualizer';\nimport { useFirstRender } from \"../utils/useFirstRender.js\";\nimport { createSelector } from \"../../utils/createSelector.js\";\nimport { useGridSelector } from \"../utils/useGridSelector.js\";\nimport { gridHasFillerSelector, gridVerticalScrollbarWidthSelector } from \"../features/dimensions/gridDimensionsSelectors.js\";\nimport { gridDensityFactorSelector } from \"../features/density/index.js\";\nimport { gridVisibleColumnDefinitionsSelector, gridVisiblePinnedColumnDefinitionsSelector, gridColumnPositionsSelector, gridHasColSpanSelector } from \"../features/columns/gridColumnsSelector.js\";\nimport { gridPinnedRowsSelector, gridRowCountSelector } from \"../features/rows/gridRowsSelector.js\";\nimport { useGridVisibleRows } from \"../utils/useGridVisibleRows.js\";\nimport { gridPaginationSelector } from \"../features/pagination/index.js\";\nimport { gridFocusedVirtualCellSelector } from \"../features/virtualization/gridFocusedVirtualCellSelector.js\";\nimport { gridRowSelectionManagerSelector } from \"../features/rowSelection/index.js\";\nimport { DATA_GRID_PROPS_DEFAULT_VALUES } from \"../../constants/dataGridPropsDefaultValues.js\";\nimport { getValidRowHeight, minimalContentHeight, rowHeightWarning } from \"../features/rows/gridRowsUtils.js\";\nimport { getTotalHeaderHeight } from \"../features/columns/gridColumnsUtils.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction identity(x) {\n  return x;\n}\nconst columnsTotalWidthSelector = createSelector(gridVisibleColumnDefinitionsSelector, gridColumnPositionsSelector, (visibleColumns, positions) => {\n  const colCount = visibleColumns.length;\n  if (colCount === 0) {\n    return 0;\n  }\n  return roundToDecimalPlaces(positions[colCount - 1] + visibleColumns[colCount - 1].computedWidth, 1);\n});\n\n/** Translates virtualizer state to grid state */\nconst addGridDimensionsCreator = () => lruMemoize((dimensions, headerHeight, groupHeaderHeight, headerFilterHeight, headersTotalHeight) => {\n  return _extends({}, dimensions, {\n    headerHeight,\n    groupHeaderHeight,\n    headerFilterHeight,\n    headersTotalHeight\n  });\n}, {\n  maxSize: 1\n});\n\n/**\n * Virtualizer setup\n */\nexport function useGridVirtualizer(apiRef, rootProps) {\n  const isRtl = useRtl();\n  const {\n    listView\n  } = rootProps;\n  const visibleColumns = useGridSelector(apiRef, gridVisibleColumnDefinitionsSelector);\n  const pinnedRows = useGridSelector(apiRef, gridPinnedRowsSelector);\n  const pinnedColumns = gridVisiblePinnedColumnDefinitionsSelector(apiRef);\n  const rowSelectionManager = useGridSelector(apiRef, gridRowSelectionManagerSelector);\n  const isRowSelected = id => rowSelectionManager.has(id) && apiRef.current.isRowSelectable(id);\n  const currentPage = useGridVisibleRows(apiRef);\n  const hasColSpan = useGridSelector(apiRef, gridHasColSpanSelector);\n  const verticalScrollbarWidth = useGridSelector(apiRef, gridVerticalScrollbarWidthSelector);\n  const hasFiller = useGridSelector(apiRef, gridHasFillerSelector);\n  const {\n    autoHeight\n  } = rootProps;\n  const scrollReset = listView;\n\n  // <DIMENSIONS>\n  const density = useGridSelector(apiRef, gridDensityFactorSelector);\n  const baseRowHeight = getValidRowHeight(rootProps.rowHeight, DATA_GRID_PROPS_DEFAULT_VALUES.rowHeight, rowHeightWarning);\n  const rowHeight = Math.floor(baseRowHeight * density);\n  const headerHeight = Math.floor(rootProps.columnHeaderHeight * density);\n  const groupHeaderHeight = Math.floor((rootProps.columnGroupHeaderHeight ?? rootProps.columnHeaderHeight) * density);\n  const headerFilterHeight = Math.floor((rootProps.headerFilterHeight ?? rootProps.columnHeaderHeight) * density);\n  const columnsTotalWidth = useGridSelector(apiRef, columnsTotalWidthSelector);\n  const headersTotalHeight = getTotalHeaderHeight(apiRef, rootProps);\n  const leftPinnedWidth = pinnedColumns.left.reduce((w, col) => w + col.computedWidth, 0);\n  const rightPinnedWidth = pinnedColumns.right.reduce((w, col) => w + col.computedWidth, 0);\n  const dimensionsParams = {\n    rowHeight,\n    headerHeight,\n    columnsTotalWidth,\n    leftPinnedWidth,\n    rightPinnedWidth,\n    topPinnedHeight: headersTotalHeight,\n    bottomPinnedHeight: 0,\n    scrollbarSize: rootProps.scrollbarSize\n  };\n  const addGridDimensions = useLazyRef(addGridDimensionsCreator).current;\n\n  // </DIMENSIONS>\n\n  // <ROWS_META>\n  const dataRowCount = useGridSelector(apiRef, gridRowCountSelector);\n  const pagination = useGridSelector(apiRef, gridPaginationSelector);\n  const rowCount = Math.min(pagination.enabled ? pagination.paginationModel.pageSize : dataRowCount, dataRowCount);\n  const {\n    getRowHeight,\n    getEstimatedRowHeight,\n    getRowSpacing\n  } = rootProps;\n  // </ROWS_META>\n\n  const focusedVirtualCell = useGridSelector(apiRef, gridFocusedVirtualCellSelector);\n  const virtualizer = useVirtualizer({\n    refs: {\n      container: apiRef.current.mainElementRef,\n      scroller: apiRef.current.virtualScrollerRef,\n      scrollbarVertical: apiRef.current.virtualScrollbarVerticalRef,\n      scrollbarHorizontal: apiRef.current.virtualScrollbarHorizontalRef\n    },\n    dimensions: dimensionsParams,\n    virtualization: {\n      isRtl,\n      rowBufferPx: rootProps.rowBufferPx,\n      columnBufferPx: rootProps.columnBufferPx\n    },\n    colspan: {\n      enabled: hasColSpan,\n      getColspan: (rowId, column) => {\n        if (typeof column.colSpan === 'function') {\n          const row = apiRef.current.getRow(rowId);\n          const value = apiRef.current.getRowValue(row, column);\n          return column.colSpan(value, row, column, apiRef) ?? 0;\n        }\n        return column.colSpan ?? 1;\n      }\n    },\n    initialState: {\n      scroll: rootProps.initialState?.scroll,\n      rowSpanning: apiRef.current.state.rowSpanning,\n      virtualization: apiRef.current.state.virtualization\n    },\n    rows: currentPage.rows,\n    range: currentPage.range,\n    rowCount,\n    columns: visibleColumns,\n    pinnedRows,\n    pinnedColumns,\n    autoHeight,\n    minimalContentHeight,\n    getRowHeight: React.useMemo(() => {\n      if (!getRowHeight) {\n        return undefined;\n      }\n      return rowEntry => getRowHeight(_extends({}, rowEntry, {\n        densityFactor: density\n      }));\n    }, [getRowHeight, density]),\n    getEstimatedRowHeight: React.useMemo(() => getEstimatedRowHeight ? rowEntry => getEstimatedRowHeight(_extends({}, rowEntry, {\n      densityFactor: density\n    })) : undefined, [getEstimatedRowHeight, density]),\n    getRowSpacing: React.useMemo(() => getRowSpacing ? rowEntry => {\n      const indexRelativeToCurrentPage = currentPage.rowIdToIndexMap.get(rowEntry.id) ?? -1;\n      const visibility = {\n        isFirstVisible: indexRelativeToCurrentPage === 0,\n        isLastVisible: indexRelativeToCurrentPage === currentPage.rows.length - 1,\n        indexRelativeToCurrentPage\n      };\n      return getRowSpacing(_extends({}, rowEntry, visibility, {\n        indexRelativeToCurrentPage: apiRef.current.getRowIndexRelativeToVisibleRows(rowEntry.id)\n      }));\n    } : undefined, [apiRef, getRowSpacing, currentPage.rows, currentPage.rowIdToIndexMap]),\n    applyRowHeight: useEventCallback((entry, row) => apiRef.current.unstable_applyPipeProcessors('rowHeight', entry, row)),\n    virtualizeColumnsWithAutoRowHeight: rootProps.virtualizeColumnsWithAutoRowHeight,\n    focusedVirtualCell: useEventCallback(() => focusedVirtualCell),\n    resizeThrottleMs: rootProps.resizeThrottleMs,\n    onResize: useEventCallback(size => apiRef.current.publishEvent('resize', size)),\n    onWheel: useEventCallback(event => {\n      apiRef.current.publishEvent('virtualScrollerWheel', {}, event);\n    }),\n    onTouchMove: useEventCallback(event => {\n      apiRef.current.publishEvent('virtualScrollerTouchMove', {}, event);\n    }),\n    onRenderContextChange: useEventCallback(nextRenderContext => {\n      apiRef.current.publishEvent('renderedRowsIntervalChange', nextRenderContext);\n    }),\n    onScrollChange: (scrollPosition, nextRenderContext) => {\n      apiRef.current.publishEvent('scrollPositionChange', {\n        top: scrollPosition.top,\n        left: scrollPosition.left,\n        renderContext: nextRenderContext\n      });\n    },\n    scrollReset,\n    renderRow: params => /*#__PURE__*/_jsx(rootProps.slots.row, _extends({\n      row: params.model,\n      rowId: params.id,\n      index: params.rowIndex,\n      selected: isRowSelected(params.id),\n      offsetLeft: params.offsetLeft,\n      columnsTotalWidth: columnsTotalWidth,\n      rowHeight: params.baseRowHeight,\n      pinnedColumns: pinnedColumns,\n      visibleColumns: visibleColumns,\n      firstColumnIndex: params.firstColumnIndex,\n      lastColumnIndex: params.lastColumnIndex,\n      focusedColumnIndex: params.focusedColumnIndex,\n      isFirstVisible: params.isFirstVisible,\n      isLastVisible: params.isLastVisible,\n      isNotVisible: params.isVirtualFocusRow,\n      showBottomBorder: params.showBottomBorder,\n      scrollbarWidth: verticalScrollbarWidth,\n      gridHasFiller: hasFiller\n    }, rootProps.slotProps?.row), params.id),\n    renderInfiniteLoadingTrigger: id => apiRef.current.getInfiniteLoadingTriggerElement?.({\n      lastRowId: id\n    })\n  });\n\n  // HACK: Keep the grid's store in sync with the virtualizer store. We set up the\n  // subscription in the render phase rather than in an effect because other grid\n  // initialization code runs between those two moments.\n  //\n  // TODO(v9): Remove this\n  useFirstRender(() => {\n    apiRef.current.store.state.dimensions = addGridDimensions(virtualizer.store.state.dimensions, headerHeight, groupHeaderHeight, headerFilterHeight, headersTotalHeight);\n    apiRef.current.store.state.rowsMeta = virtualizer.store.state.rowsMeta;\n    apiRef.current.store.state.virtualization = virtualizer.store.state.virtualization;\n  });\n  useStoreEffect(virtualizer.store, Dimensions.selectors.dimensions, (_, dimensions) => {\n    apiRef.current.setState(gridState => _extends({}, gridState, {\n      dimensions: addGridDimensions(dimensions, headerHeight, groupHeaderHeight, headerFilterHeight, headersTotalHeight)\n    }));\n  });\n  useStoreEffect(virtualizer.store, identity, (_, state) => {\n    if (state.rowsMeta !== apiRef.current.state.rowsMeta) {\n      apiRef.current.setState(gridState => _extends({}, gridState, {\n        rowsMeta: state.rowsMeta\n      }));\n    }\n    if (state.virtualization !== apiRef.current.state.virtualization) {\n      apiRef.current.setState(gridState => _extends({}, gridState, {\n        virtualization: state.virtualization\n      }));\n    }\n  });\n  apiRef.current.register('private', {\n    virtualizer\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,cAAc,EAAEC,UAAU,QAAQ,oBAAoB;AAC/D,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,cAAc,QAAQ,+BAA+B;AAC9D,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,qBAAqB,EAAEC,kCAAkC,QAAQ,mDAAmD;AAC7H,SAASC,yBAAyB,QAAQ,8BAA8B;AACxE,SAASC,oCAAoC,EAAEC,0CAA0C,EAAEC,2BAA2B,EAAEC,sBAAsB,QAAQ,4CAA4C;AAClM,SAASC,sBAAsB,EAAEC,oBAAoB,QAAQ,sCAAsC;AACnG,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,8BAA8B,QAAQ,8DAA8D;AAC7G,SAASC,+BAA+B,QAAQ,mCAAmC;AACnF,SAASC,8BAA8B,QAAQ,+CAA+C;AAC9F,SAASC,iBAAiB,EAAEC,oBAAoB,EAAEC,gBAAgB,QAAQ,mCAAmC;AAC7G,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQA,CAACC,CAAC,EAAE;EACnB,OAAOA,CAAC;AACV;AACA,MAAMC,yBAAyB,GAAGxB,cAAc,CAACK,oCAAoC,EAAEE,2BAA2B,EAAE,CAACkB,cAAc,EAAEC,SAAS,KAAK;EACjJ,MAAMC,QAAQ,GAAGF,cAAc,CAACG,MAAM;EACtC,IAAID,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAO,CAAC;EACV;EACA,OAAOjC,oBAAoB,CAACgC,SAAS,CAACC,QAAQ,GAAG,CAAC,CAAC,GAAGF,cAAc,CAACE,QAAQ,GAAG,CAAC,CAAC,CAACE,aAAa,EAAE,CAAC,CAAC;AACtG,CAAC,CAAC;;AAEF;AACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAMnC,UAAU,CAAC,CAACoC,UAAU,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,kBAAkB,KAAK;EACzI,OAAO9C,QAAQ,CAAC,CAAC,CAAC,EAAE0C,UAAU,EAAE;IAC9BC,YAAY;IACZC,iBAAiB;IACjBC,kBAAkB;IAClBC;EACF,CAAC,CAAC;AACJ,CAAC,EAAE;EACDC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACpD,MAAMC,KAAK,GAAG/C,MAAM,CAAC,CAAC;EACtB,MAAM;IACJgD;EACF,CAAC,GAAGF,SAAS;EACb,MAAMd,cAAc,GAAGxB,eAAe,CAACqC,MAAM,EAAEjC,oCAAoC,CAAC;EACpF,MAAMqC,UAAU,GAAGzC,eAAe,CAACqC,MAAM,EAAE7B,sBAAsB,CAAC;EAClE,MAAMkC,aAAa,GAAGrC,0CAA0C,CAACgC,MAAM,CAAC;EACxE,MAAMM,mBAAmB,GAAG3C,eAAe,CAACqC,MAAM,EAAExB,+BAA+B,CAAC;EACpF,MAAM+B,aAAa,GAAGC,EAAE,IAAIF,mBAAmB,CAACG,GAAG,CAACD,EAAE,CAAC,IAAIR,MAAM,CAACU,OAAO,CAACC,eAAe,CAACH,EAAE,CAAC;EAC7F,MAAMI,WAAW,GAAGvC,kBAAkB,CAAC2B,MAAM,CAAC;EAC9C,MAAMa,UAAU,GAAGlD,eAAe,CAACqC,MAAM,EAAE9B,sBAAsB,CAAC;EAClE,MAAM4C,sBAAsB,GAAGnD,eAAe,CAACqC,MAAM,EAAEnC,kCAAkC,CAAC;EAC1F,MAAMkD,SAAS,GAAGpD,eAAe,CAACqC,MAAM,EAAEpC,qBAAqB,CAAC;EAChE,MAAM;IACJoD;EACF,CAAC,GAAGf,SAAS;EACb,MAAMgB,WAAW,GAAGd,QAAQ;;EAE5B;EACA,MAAMe,OAAO,GAAGvD,eAAe,CAACqC,MAAM,EAAElC,yBAAyB,CAAC;EAClE,MAAMqD,aAAa,GAAGzC,iBAAiB,CAACuB,SAAS,CAACmB,SAAS,EAAE3C,8BAA8B,CAAC2C,SAAS,EAAExC,gBAAgB,CAAC;EACxH,MAAMwC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACH,aAAa,GAAGD,OAAO,CAAC;EACrD,MAAMxB,YAAY,GAAG2B,IAAI,CAACC,KAAK,CAACrB,SAAS,CAACsB,kBAAkB,GAAGL,OAAO,CAAC;EACvE,MAAMvB,iBAAiB,GAAG0B,IAAI,CAACC,KAAK,CAAC,CAACrB,SAAS,CAACuB,uBAAuB,IAAIvB,SAAS,CAACsB,kBAAkB,IAAIL,OAAO,CAAC;EACnH,MAAMtB,kBAAkB,GAAGyB,IAAI,CAACC,KAAK,CAAC,CAACrB,SAAS,CAACL,kBAAkB,IAAIK,SAAS,CAACsB,kBAAkB,IAAIL,OAAO,CAAC;EAC/G,MAAMO,iBAAiB,GAAG9D,eAAe,CAACqC,MAAM,EAAEd,yBAAyB,CAAC;EAC5E,MAAMW,kBAAkB,GAAGhB,oBAAoB,CAACmB,MAAM,EAAEC,SAAS,CAAC;EAClE,MAAMyB,eAAe,GAAGrB,aAAa,CAACsB,IAAI,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKD,CAAC,GAAGC,GAAG,CAACvC,aAAa,EAAE,CAAC,CAAC;EACvF,MAAMwC,gBAAgB,GAAG1B,aAAa,CAAC2B,KAAK,CAACJ,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKD,CAAC,GAAGC,GAAG,CAACvC,aAAa,EAAE,CAAC,CAAC;EACzF,MAAM0C,gBAAgB,GAAG;IACvBb,SAAS;IACT1B,YAAY;IACZ+B,iBAAiB;IACjBC,eAAe;IACfK,gBAAgB;IAChBG,eAAe,EAAErC,kBAAkB;IACnCsC,kBAAkB,EAAE,CAAC;IACrBC,aAAa,EAAEnC,SAAS,CAACmC;EAC3B,CAAC;EACD,MAAMC,iBAAiB,GAAGpF,UAAU,CAACuC,wBAAwB,CAAC,CAACkB,OAAO;;EAEtE;;EAEA;EACA,MAAM4B,YAAY,GAAG3E,eAAe,CAACqC,MAAM,EAAE5B,oBAAoB,CAAC;EAClE,MAAMmE,UAAU,GAAG5E,eAAe,CAACqC,MAAM,EAAE1B,sBAAsB,CAAC;EAClE,MAAMkE,QAAQ,GAAGnB,IAAI,CAACoB,GAAG,CAACF,UAAU,CAACG,OAAO,GAAGH,UAAU,CAACI,eAAe,CAACC,QAAQ,GAAGN,YAAY,EAAEA,YAAY,CAAC;EAChH,MAAM;IACJO,YAAY;IACZC,qBAAqB;IACrBC;EACF,CAAC,GAAG9C,SAAS;EACb;;EAEA,MAAM+C,kBAAkB,GAAGrF,eAAe,CAACqC,MAAM,EAAEzB,8BAA8B,CAAC;EAClF,MAAM0E,WAAW,GAAG1F,cAAc,CAAC;IACjC2F,IAAI,EAAE;MACJC,SAAS,EAAEnD,MAAM,CAACU,OAAO,CAAC0C,cAAc;MACxCC,QAAQ,EAAErD,MAAM,CAACU,OAAO,CAAC4C,kBAAkB;MAC3CC,iBAAiB,EAAEvD,MAAM,CAACU,OAAO,CAAC8C,2BAA2B;MAC7DC,mBAAmB,EAAEzD,MAAM,CAACU,OAAO,CAACgD;IACtC,CAAC;IACDjE,UAAU,EAAEwC,gBAAgB;IAC5B0B,cAAc,EAAE;MACdzD,KAAK;MACL0D,WAAW,EAAE3D,SAAS,CAAC2D,WAAW;MAClCC,cAAc,EAAE5D,SAAS,CAAC4D;IAC5B,CAAC;IACDC,OAAO,EAAE;MACPpB,OAAO,EAAE7B,UAAU;MACnBkD,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;QAC7B,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,UAAU,EAAE;UACxC,MAAMC,GAAG,GAAGnE,MAAM,CAACU,OAAO,CAAC0D,MAAM,CAACJ,KAAK,CAAC;UACxC,MAAMK,KAAK,GAAGrE,MAAM,CAACU,OAAO,CAAC4D,WAAW,CAACH,GAAG,EAAEF,MAAM,CAAC;UACrD,OAAOA,MAAM,CAACC,OAAO,CAACG,KAAK,EAAEF,GAAG,EAAEF,MAAM,EAAEjE,MAAM,CAAC,IAAI,CAAC;QACxD;QACA,OAAOiE,MAAM,CAACC,OAAO,IAAI,CAAC;MAC5B;IACF,CAAC;IACDK,YAAY,EAAE;MACZC,MAAM,EAAEvE,SAAS,CAACsE,YAAY,EAAEC,MAAM;MACtCC,WAAW,EAAEzE,MAAM,CAACU,OAAO,CAACgE,KAAK,CAACD,WAAW;MAC7Cd,cAAc,EAAE3D,MAAM,CAACU,OAAO,CAACgE,KAAK,CAACf;IACvC,CAAC;IACDgB,IAAI,EAAE/D,WAAW,CAAC+D,IAAI;IACtBC,KAAK,EAAEhE,WAAW,CAACgE,KAAK;IACxBpC,QAAQ;IACRqC,OAAO,EAAE1F,cAAc;IACvBiB,UAAU;IACVC,aAAa;IACbW,UAAU;IACVrC,oBAAoB;IACpBkE,YAAY,EAAE7F,KAAK,CAAC8H,OAAO,CAAC,MAAM;MAChC,IAAI,CAACjC,YAAY,EAAE;QACjB,OAAOkC,SAAS;MAClB;MACA,OAAOC,QAAQ,IAAInC,YAAY,CAAC9F,QAAQ,CAAC,CAAC,CAAC,EAAEiI,QAAQ,EAAE;QACrDC,aAAa,EAAE/D;MACjB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC2B,YAAY,EAAE3B,OAAO,CAAC,CAAC;IAC3B4B,qBAAqB,EAAE9F,KAAK,CAAC8H,OAAO,CAAC,MAAMhC,qBAAqB,GAAGkC,QAAQ,IAAIlC,qBAAqB,CAAC/F,QAAQ,CAAC,CAAC,CAAC,EAAEiI,QAAQ,EAAE;MAC1HC,aAAa,EAAE/D;IACjB,CAAC,CAAC,CAAC,GAAG6D,SAAS,EAAE,CAACjC,qBAAqB,EAAE5B,OAAO,CAAC,CAAC;IAClD6B,aAAa,EAAE/F,KAAK,CAAC8H,OAAO,CAAC,MAAM/B,aAAa,GAAGiC,QAAQ,IAAI;MAC7D,MAAME,0BAA0B,GAAGtE,WAAW,CAACuE,eAAe,CAACC,GAAG,CAACJ,QAAQ,CAACxE,EAAE,CAAC,IAAI,CAAC,CAAC;MACrF,MAAM6E,UAAU,GAAG;QACjBC,cAAc,EAAEJ,0BAA0B,KAAK,CAAC;QAChDK,aAAa,EAAEL,0BAA0B,KAAKtE,WAAW,CAAC+D,IAAI,CAACrF,MAAM,GAAG,CAAC;QACzE4F;MACF,CAAC;MACD,OAAOnC,aAAa,CAAChG,QAAQ,CAAC,CAAC,CAAC,EAAEiI,QAAQ,EAAEK,UAAU,EAAE;QACtDH,0BAA0B,EAAElF,MAAM,CAACU,OAAO,CAAC8E,gCAAgC,CAACR,QAAQ,CAACxE,EAAE;MACzF,CAAC,CAAC,CAAC;IACL,CAAC,GAAGuE,SAAS,EAAE,CAAC/E,MAAM,EAAE+C,aAAa,EAAEnC,WAAW,CAAC+D,IAAI,EAAE/D,WAAW,CAACuE,eAAe,CAAC,CAAC;IACtFM,cAAc,EAAEvI,gBAAgB,CAAC,CAACwI,KAAK,EAAEvB,GAAG,KAAKnE,MAAM,CAACU,OAAO,CAACiF,4BAA4B,CAAC,WAAW,EAAED,KAAK,EAAEvB,GAAG,CAAC,CAAC;IACtHyB,kCAAkC,EAAE3F,SAAS,CAAC2F,kCAAkC;IAChF5C,kBAAkB,EAAE9F,gBAAgB,CAAC,MAAM8F,kBAAkB,CAAC;IAC9D6C,gBAAgB,EAAE5F,SAAS,CAAC4F,gBAAgB;IAC5CC,QAAQ,EAAE5I,gBAAgB,CAAC6I,IAAI,IAAI/F,MAAM,CAACU,OAAO,CAACsF,YAAY,CAAC,QAAQ,EAAED,IAAI,CAAC,CAAC;IAC/EE,OAAO,EAAE/I,gBAAgB,CAACgJ,KAAK,IAAI;MACjClG,MAAM,CAACU,OAAO,CAACsF,YAAY,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAEE,KAAK,CAAC;IAChE,CAAC,CAAC;IACFC,WAAW,EAAEjJ,gBAAgB,CAACgJ,KAAK,IAAI;MACrClG,MAAM,CAACU,OAAO,CAACsF,YAAY,CAAC,0BAA0B,EAAE,CAAC,CAAC,EAAEE,KAAK,CAAC;IACpE,CAAC,CAAC;IACFE,qBAAqB,EAAElJ,gBAAgB,CAACmJ,iBAAiB,IAAI;MAC3DrG,MAAM,CAACU,OAAO,CAACsF,YAAY,CAAC,4BAA4B,EAAEK,iBAAiB,CAAC;IAC9E,CAAC,CAAC;IACFC,cAAc,EAAEA,CAACC,cAAc,EAAEF,iBAAiB,KAAK;MACrDrG,MAAM,CAACU,OAAO,CAACsF,YAAY,CAAC,sBAAsB,EAAE;QAClDQ,GAAG,EAAED,cAAc,CAACC,GAAG;QACvB7E,IAAI,EAAE4E,cAAc,CAAC5E,IAAI;QACzB8E,aAAa,EAAEJ;MACjB,CAAC,CAAC;IACJ,CAAC;IACDpF,WAAW;IACXyF,SAAS,EAAEC,MAAM,IAAI,aAAa5H,IAAI,CAACkB,SAAS,CAAC2G,KAAK,CAACzC,GAAG,EAAEpH,QAAQ,CAAC;MACnEoH,GAAG,EAAEwC,MAAM,CAACE,KAAK;MACjB7C,KAAK,EAAE2C,MAAM,CAACnG,EAAE;MAChBsG,KAAK,EAAEH,MAAM,CAACI,QAAQ;MACtBC,QAAQ,EAAEzG,aAAa,CAACoG,MAAM,CAACnG,EAAE,CAAC;MAClCyG,UAAU,EAAEN,MAAM,CAACM,UAAU;MAC7BxF,iBAAiB,EAAEA,iBAAiB;MACpCL,SAAS,EAAEuF,MAAM,CAACxF,aAAa;MAC/Bd,aAAa,EAAEA,aAAa;MAC5BlB,cAAc,EAAEA,cAAc;MAC9B+H,gBAAgB,EAAEP,MAAM,CAACO,gBAAgB;MACzCC,eAAe,EAAER,MAAM,CAACQ,eAAe;MACvCC,kBAAkB,EAAET,MAAM,CAACS,kBAAkB;MAC7C9B,cAAc,EAAEqB,MAAM,CAACrB,cAAc;MACrCC,aAAa,EAAEoB,MAAM,CAACpB,aAAa;MACnC8B,YAAY,EAAEV,MAAM,CAACW,iBAAiB;MACtCC,gBAAgB,EAAEZ,MAAM,CAACY,gBAAgB;MACzCC,cAAc,EAAE1G,sBAAsB;MACtC2G,aAAa,EAAE1G;IACjB,CAAC,EAAEd,SAAS,CAACyH,SAAS,EAAEvD,GAAG,CAAC,EAAEwC,MAAM,CAACnG,EAAE,CAAC;IACxCmH,4BAA4B,EAAEnH,EAAE,IAAIR,MAAM,CAACU,OAAO,CAACkH,gCAAgC,GAAG;MACpFC,SAAS,EAAErH;IACb,CAAC;EACH,CAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA;EACA/C,cAAc,CAAC,MAAM;IACnBuC,MAAM,CAACU,OAAO,CAACoH,KAAK,CAACpD,KAAK,CAACjF,UAAU,GAAG4C,iBAAiB,CAACY,WAAW,CAAC6E,KAAK,CAACpD,KAAK,CAACjF,UAAU,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,kBAAkB,CAAC;IACtKG,MAAM,CAACU,OAAO,CAACoH,KAAK,CAACpD,KAAK,CAACqD,QAAQ,GAAG9E,WAAW,CAAC6E,KAAK,CAACpD,KAAK,CAACqD,QAAQ;IACtE/H,MAAM,CAACU,OAAO,CAACoH,KAAK,CAACpD,KAAK,CAACf,cAAc,GAAGV,WAAW,CAAC6E,KAAK,CAACpD,KAAK,CAACf,cAAc;EACpF,CAAC,CAAC;EACFrG,cAAc,CAAC2F,WAAW,CAAC6E,KAAK,EAAEtK,UAAU,CAACwK,SAAS,CAACvI,UAAU,EAAE,CAACwI,CAAC,EAAExI,UAAU,KAAK;IACpFO,MAAM,CAACU,OAAO,CAACwH,QAAQ,CAACC,SAAS,IAAIpL,QAAQ,CAAC,CAAC,CAAC,EAAEoL,SAAS,EAAE;MAC3D1I,UAAU,EAAE4C,iBAAiB,CAAC5C,UAAU,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,kBAAkB;IACnH,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACFvC,cAAc,CAAC2F,WAAW,CAAC6E,KAAK,EAAE9I,QAAQ,EAAE,CAACiJ,CAAC,EAAEvD,KAAK,KAAK;IACxD,IAAIA,KAAK,CAACqD,QAAQ,KAAK/H,MAAM,CAACU,OAAO,CAACgE,KAAK,CAACqD,QAAQ,EAAE;MACpD/H,MAAM,CAACU,OAAO,CAACwH,QAAQ,CAACC,SAAS,IAAIpL,QAAQ,CAAC,CAAC,CAAC,EAAEoL,SAAS,EAAE;QAC3DJ,QAAQ,EAAErD,KAAK,CAACqD;MAClB,CAAC,CAAC,CAAC;IACL;IACA,IAAIrD,KAAK,CAACf,cAAc,KAAK3D,MAAM,CAACU,OAAO,CAACgE,KAAK,CAACf,cAAc,EAAE;MAChE3D,MAAM,CAACU,OAAO,CAACwH,QAAQ,CAACC,SAAS,IAAIpL,QAAQ,CAAC,CAAC,CAAC,EAAEoL,SAAS,EAAE;QAC3DxE,cAAc,EAAEe,KAAK,CAACf;MACxB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;EACF3D,MAAM,CAACU,OAAO,CAAC0H,QAAQ,CAAC,SAAS,EAAE;IACjCnF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}