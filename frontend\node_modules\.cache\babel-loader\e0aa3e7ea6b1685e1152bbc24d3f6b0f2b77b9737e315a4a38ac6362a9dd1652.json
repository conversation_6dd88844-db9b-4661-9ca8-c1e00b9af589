{"ast": null, "code": "import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nexport const gridRowsStateSelector = createRootSelector(state => state.rows);\nexport const gridRowCountSelector = createSelector(gridRowsStateSelector, rows => rows.totalRowCount);\nexport const gridRowsLoadingSelector = createSelector(gridRowsStateSelector, rows => rows.loading);\nexport const gridTopLevelRowCountSelector = createSelector(gridRowsStateSelector, rows => rows.totalTopLevelRowCount);\n\n// TODO rows v6: Rename\nexport const gridRowsLookupSelector = createSelector(gridRowsStateSelector, rows => rows.dataRowIdToModelLookup);\n\n/**\n * @category Rows\n */\nexport const gridRowSelector = createSelector(gridRowsLookupSelector, (rows, id) => rows[id]);\nexport const gridRowTreeSelector = createSelector(gridRowsStateSelector, rows => rows.tree);\n\n/**\n * @category Rows\n */\nexport const gridRowNodeSelector = createSelector(gridRowTreeSelector, (rowTree, rowId) => rowTree[rowId]);\nexport const gridRowGroupsToFetchSelector = createSelector(gridRowsStateSelector, rows => rows.groupsToFetch);\nexport const gridRowGroupingNameSelector = createSelector(gridRowsStateSelector, rows => rows.groupingName);\nexport const gridRowTreeDepthsSelector = createSelector(gridRowsStateSelector, rows => rows.treeDepths);\nexport const gridRowMaximumTreeDepthSelector = createSelectorMemoized(gridRowsStateSelector, rows => {\n  const entries = Object.entries(rows.treeDepths);\n  if (entries.length === 0) {\n    return 1;\n  }\n  return (entries.filter(([, nodeCount]) => nodeCount > 0).map(([depth]) => Number(depth)).sort((a, b) => b - a)[0] ?? 0) + 1;\n});\nexport const gridDataRowIdsSelector = createSelector(gridRowsStateSelector, rows => rows.dataRowIds);\nexport const gridDataRowsSelector = createSelectorMemoized(gridDataRowIdsSelector, gridRowsLookupSelector, (dataRowIds, rowsLookup) => dataRowIds.reduce((acc, id) => {\n  if (!rowsLookup[id]) {\n    return acc;\n  }\n  acc.push(rowsLookup[id]);\n  return acc;\n}, []));\n\n/**\n * @ignore - do not document.\n */\nexport const gridAdditionalRowGroupsSelector = createSelector(gridRowsStateSelector, rows => rows?.additionalRowGroups);\n\n/**\n * @ignore - do not document.\n */\nexport const gridPinnedRowsSelector = createSelectorMemoized(gridAdditionalRowGroupsSelector, additionalRowGroups => {\n  const rawPinnedRows = additionalRowGroups?.pinnedRows;\n  return {\n    bottom: rawPinnedRows?.bottom?.map(rowEntry => ({\n      id: rowEntry.id,\n      model: rowEntry.model ?? {}\n    })) ?? [],\n    top: rawPinnedRows?.top?.map(rowEntry => ({\n      id: rowEntry.id,\n      model: rowEntry.model ?? {}\n    })) ?? []\n  };\n});\n\n/**\n * @ignore - do not document.\n */\nexport const gridPinnedRowsCountSelector = createSelector(gridPinnedRowsSelector, pinnedRows => {\n  return (pinnedRows?.top?.length || 0) + (pinnedRows?.bottom?.length || 0);\n});", "map": {"version": 3, "names": ["createRootSelector", "createSelector", "createSelectorMemoized", "gridRowsStateSelector", "state", "rows", "gridRowCountSelector", "totalRowCount", "gridRowsLoadingSelector", "loading", "gridTopLevelRowCountSelector", "totalTopLevelRowCount", "gridRowsLookupSelector", "dataRowIdToModelLookup", "gridRowSelector", "id", "gridRowTreeSelector", "tree", "gridRowNodeSelector", "rowTree", "rowId", "gridRowGroupsToFetchSelector", "groupsToFetch", "gridRowGroupingNameSelector", "groupingName", "gridRowTreeDepthsSelector", "treeDepths", "gridRowMaximumTreeDepthSelector", "entries", "Object", "length", "filter", "nodeCount", "map", "depth", "Number", "sort", "a", "b", "gridDataRowIdsSelector", "dataRowIds", "gridDataRowsSelector", "rowsLookup", "reduce", "acc", "push", "gridAdditionalRowGroupsSelector", "additionalRowGroups", "gridPinnedRowsSelector", "rawPinnedRows", "pinnedRows", "bottom", "rowEntry", "model", "top", "gridPinnedRowsCountSelector"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rows/gridRowsSelector.js"], "sourcesContent": ["import { createRootSelector, createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nexport const gridRowsStateSelector = createRootSelector(state => state.rows);\nexport const gridRowCountSelector = createSelector(gridRowsStateSelector, rows => rows.totalRowCount);\nexport const gridRowsLoadingSelector = createSelector(gridRowsStateSelector, rows => rows.loading);\nexport const gridTopLevelRowCountSelector = createSelector(gridRowsStateSelector, rows => rows.totalTopLevelRowCount);\n\n// TODO rows v6: Rename\nexport const gridRowsLookupSelector = createSelector(gridRowsStateSelector, rows => rows.dataRowIdToModelLookup);\n\n/**\n * @category Rows\n */\nexport const gridRowSelector = createSelector(gridRowsLookupSelector, (rows, id) => rows[id]);\nexport const gridRowTreeSelector = createSelector(gridRowsStateSelector, rows => rows.tree);\n\n/**\n * @category Rows\n */\nexport const gridRowNodeSelector = createSelector(gridRowTreeSelector, (rowTree, rowId) => rowTree[rowId]);\nexport const gridRowGroupsToFetchSelector = createSelector(gridRowsStateSelector, rows => rows.groupsToFetch);\nexport const gridRowGroupingNameSelector = createSelector(gridRowsStateSelector, rows => rows.groupingName);\nexport const gridRowTreeDepthsSelector = createSelector(gridRowsStateSelector, rows => rows.treeDepths);\nexport const gridRowMaximumTreeDepthSelector = createSelectorMemoized(gridRowsStateSelector, rows => {\n  const entries = Object.entries(rows.treeDepths);\n  if (entries.length === 0) {\n    return 1;\n  }\n  return (entries.filter(([, nodeCount]) => nodeCount > 0).map(([depth]) => Number(depth)).sort((a, b) => b - a)[0] ?? 0) + 1;\n});\nexport const gridDataRowIdsSelector = createSelector(gridRowsStateSelector, rows => rows.dataRowIds);\nexport const gridDataRowsSelector = createSelectorMemoized(gridDataRowIdsSelector, gridRowsLookupSelector, (dataRowIds, rowsLookup) => dataRowIds.reduce((acc, id) => {\n  if (!rowsLookup[id]) {\n    return acc;\n  }\n  acc.push(rowsLookup[id]);\n  return acc;\n}, []));\n\n/**\n * @ignore - do not document.\n */\nexport const gridAdditionalRowGroupsSelector = createSelector(gridRowsStateSelector, rows => rows?.additionalRowGroups);\n\n/**\n * @ignore - do not document.\n */\nexport const gridPinnedRowsSelector = createSelectorMemoized(gridAdditionalRowGroupsSelector, additionalRowGroups => {\n  const rawPinnedRows = additionalRowGroups?.pinnedRows;\n  return {\n    bottom: rawPinnedRows?.bottom?.map(rowEntry => ({\n      id: rowEntry.id,\n      model: rowEntry.model ?? {}\n    })) ?? [],\n    top: rawPinnedRows?.top?.map(rowEntry => ({\n      id: rowEntry.id,\n      model: rowEntry.model ?? {}\n    })) ?? []\n  };\n});\n\n/**\n * @ignore - do not document.\n */\nexport const gridPinnedRowsCountSelector = createSelector(gridPinnedRowsSelector, pinnedRows => {\n  return (pinnedRows?.top?.length || 0) + (pinnedRows?.bottom?.length || 0);\n});"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,cAAc,EAAEC,sBAAsB,QAAQ,kCAAkC;AAC7G,OAAO,MAAMC,qBAAqB,GAAGH,kBAAkB,CAACI,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;AAC5E,OAAO,MAAMC,oBAAoB,GAAGL,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACE,aAAa,CAAC;AACrG,OAAO,MAAMC,uBAAuB,GAAGP,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACI,OAAO,CAAC;AAClG,OAAO,MAAMC,4BAA4B,GAAGT,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACM,qBAAqB,CAAC;;AAErH;AACA,OAAO,MAAMC,sBAAsB,GAAGX,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACQ,sBAAsB,CAAC;;AAEhH;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGb,cAAc,CAACW,sBAAsB,EAAE,CAACP,IAAI,EAAEU,EAAE,KAAKV,IAAI,CAACU,EAAE,CAAC,CAAC;AAC7F,OAAO,MAAMC,mBAAmB,GAAGf,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACY,IAAI,CAAC;;AAE3F;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGjB,cAAc,CAACe,mBAAmB,EAAE,CAACG,OAAO,EAAEC,KAAK,KAAKD,OAAO,CAACC,KAAK,CAAC,CAAC;AAC1G,OAAO,MAAMC,4BAA4B,GAAGpB,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACiB,aAAa,CAAC;AAC7G,OAAO,MAAMC,2BAA2B,GAAGtB,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACmB,YAAY,CAAC;AAC3G,OAAO,MAAMC,yBAAyB,GAAGxB,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACqB,UAAU,CAAC;AACvG,OAAO,MAAMC,+BAA+B,GAAGzB,sBAAsB,CAACC,qBAAqB,EAAEE,IAAI,IAAI;EACnG,MAAMuB,OAAO,GAAGC,MAAM,CAACD,OAAO,CAACvB,IAAI,CAACqB,UAAU,CAAC;EAC/C,IAAIE,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,OAAO,CAAC;EACV;EACA,OAAO,CAACF,OAAO,CAACG,MAAM,CAAC,CAAC,GAAGC,SAAS,CAAC,KAAKA,SAAS,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,KAAK,CAAC,KAAKC,MAAM,CAACD,KAAK,CAAC,CAAC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAC7H,CAAC,CAAC;AACF,OAAO,MAAME,sBAAsB,GAAGtC,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,CAACmC,UAAU,CAAC;AACpG,OAAO,MAAMC,oBAAoB,GAAGvC,sBAAsB,CAACqC,sBAAsB,EAAE3B,sBAAsB,EAAE,CAAC4B,UAAU,EAAEE,UAAU,KAAKF,UAAU,CAACG,MAAM,CAAC,CAACC,GAAG,EAAE7B,EAAE,KAAK;EACpK,IAAI,CAAC2B,UAAU,CAAC3B,EAAE,CAAC,EAAE;IACnB,OAAO6B,GAAG;EACZ;EACAA,GAAG,CAACC,IAAI,CAACH,UAAU,CAAC3B,EAAE,CAAC,CAAC;EACxB,OAAO6B,GAAG;AACZ,CAAC,EAAE,EAAE,CAAC,CAAC;;AAEP;AACA;AACA;AACA,OAAO,MAAME,+BAA+B,GAAG7C,cAAc,CAACE,qBAAqB,EAAEE,IAAI,IAAIA,IAAI,EAAE0C,mBAAmB,CAAC;;AAEvH;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAG9C,sBAAsB,CAAC4C,+BAA+B,EAAEC,mBAAmB,IAAI;EACnH,MAAME,aAAa,GAAGF,mBAAmB,EAAEG,UAAU;EACrD,OAAO;IACLC,MAAM,EAAEF,aAAa,EAAEE,MAAM,EAAElB,GAAG,CAACmB,QAAQ,KAAK;MAC9CrC,EAAE,EAAEqC,QAAQ,CAACrC,EAAE;MACfsC,KAAK,EAAED,QAAQ,CAACC,KAAK,IAAI,CAAC;IAC5B,CAAC,CAAC,CAAC,IAAI,EAAE;IACTC,GAAG,EAAEL,aAAa,EAAEK,GAAG,EAAErB,GAAG,CAACmB,QAAQ,KAAK;MACxCrC,EAAE,EAAEqC,QAAQ,CAACrC,EAAE;MACfsC,KAAK,EAAED,QAAQ,CAACC,KAAK,IAAI,CAAC;IAC5B,CAAC,CAAC,CAAC,IAAI;EACT,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA,OAAO,MAAME,2BAA2B,GAAGtD,cAAc,CAAC+C,sBAAsB,EAAEE,UAAU,IAAI;EAC9F,OAAO,CAACA,UAAU,EAAEI,GAAG,EAAExB,MAAM,IAAI,CAAC,KAAKoB,UAAU,EAAEC,MAAM,EAAErB,MAAM,IAAI,CAAC,CAAC;AAC3E,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}