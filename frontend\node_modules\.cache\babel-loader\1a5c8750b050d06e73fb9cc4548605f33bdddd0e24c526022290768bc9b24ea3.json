{"ast": null, "code": "import { Rowspan } from '@mui/x-virtualizer';\nimport { gridFilteredSortedRowIdsSelector } from \"../filter/gridFilterSelector.js\";\nexport const getLeftColumnIndex = ({\n  currentColIndex,\n  firstColIndex,\n  lastColIndex,\n  isRtl\n}) => {\n  if (isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  }\n  return null;\n};\nexport const getRightColumnIndex = ({\n  currentColIndex,\n  firstColIndex,\n  lastColIndex,\n  isRtl\n}) => {\n  if (isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  }\n  return null;\n};\nexport function findNonRowSpannedCell(apiRef, rowId, colIndex, rowSpanScanDirection) {\n  const rowSpanHiddenCells = Rowspan.selectors.hiddenCells(apiRef.current.virtualizer.store.state);\n  if (!rowSpanHiddenCells[rowId]?.[colIndex]) {\n    return rowId;\n  }\n  const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(apiRef);\n  // find closest non row spanned cell in the given `rowSpanScanDirection`\n  let nextRowIndex = filteredSortedRowIds.indexOf(rowId) + (rowSpanScanDirection === 'down' ? 1 : -1);\n  while (nextRowIndex >= 0 && nextRowIndex < filteredSortedRowIds.length) {\n    const nextRowId = filteredSortedRowIds[nextRowIndex];\n    if (!rowSpanHiddenCells[nextRowId]?.[colIndex]) {\n      return nextRowId;\n    }\n    nextRowIndex += rowSpanScanDirection === 'down' ? 1 : -1;\n  }\n  return rowId;\n}", "map": {"version": 3, "names": ["Rowspan", "gridFilteredSortedRowIdsSelector", "getLeftColumnIndex", "currentColIndex", "firstColIndex", "lastColIndex", "isRtl", "getRightColumnIndex", "findNonRowSpannedCell", "apiRef", "rowId", "colIndex", "rowSpanScanDirection", "rowSpanHiddenCells", "selectors", "hidden<PERSON>ells", "current", "virtualizer", "store", "state", "filteredSortedRowIds", "nextRowIndex", "indexOf", "length", "nextRowId"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/keyboardNavigation/utils.js"], "sourcesContent": ["import { Rowspan } from '@mui/x-virtualizer';\nimport { gridFilteredSortedRowIdsSelector } from \"../filter/gridFilterSelector.js\";\nexport const getLeftColumnIndex = ({\n  currentColIndex,\n  firstColIndex,\n  lastColIndex,\n  isRtl\n}) => {\n  if (isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  }\n  return null;\n};\nexport const getRightColumnIndex = ({\n  currentColIndex,\n  firstColIndex,\n  lastColIndex,\n  isRtl\n}) => {\n  if (isRtl) {\n    if (currentColIndex > firstColIndex) {\n      return currentColIndex - 1;\n    }\n  } else if (!isRtl) {\n    if (currentColIndex < lastColIndex) {\n      return currentColIndex + 1;\n    }\n  }\n  return null;\n};\nexport function findNonRowSpannedCell(apiRef, rowId, colIndex, rowSpanScanDirection) {\n  const rowSpanHiddenCells = Rowspan.selectors.hiddenCells(apiRef.current.virtualizer.store.state);\n  if (!rowSpanHiddenCells[rowId]?.[colIndex]) {\n    return rowId;\n  }\n  const filteredSortedRowIds = gridFilteredSortedRowIdsSelector(apiRef);\n  // find closest non row spanned cell in the given `rowSpanScanDirection`\n  let nextRowIndex = filteredSortedRowIds.indexOf(rowId) + (rowSpanScanDirection === 'down' ? 1 : -1);\n  while (nextRowIndex >= 0 && nextRowIndex < filteredSortedRowIds.length) {\n    const nextRowId = filteredSortedRowIds[nextRowIndex];\n    if (!rowSpanHiddenCells[nextRowId]?.[colIndex]) {\n      return nextRowId;\n    }\n    nextRowIndex += rowSpanScanDirection === 'down' ? 1 : -1;\n  }\n  return rowId;\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,gCAAgC,QAAQ,iCAAiC;AAClF,OAAO,MAAMC,kBAAkB,GAAGA,CAAC;EACjCC,eAAe;EACfC,aAAa;EACbC,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,IAAIA,KAAK,EAAE;IACT,IAAIH,eAAe,GAAGE,YAAY,EAAE;MAClC,OAAOF,eAAe,GAAG,CAAC;IAC5B;EACF,CAAC,MAAM,IAAI,CAACG,KAAK,EAAE;IACjB,IAAIH,eAAe,GAAGC,aAAa,EAAE;MACnC,OAAOD,eAAe,GAAG,CAAC;IAC5B;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,MAAMI,mBAAmB,GAAGA,CAAC;EAClCJ,eAAe;EACfC,aAAa;EACbC,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,IAAIA,KAAK,EAAE;IACT,IAAIH,eAAe,GAAGC,aAAa,EAAE;MACnC,OAAOD,eAAe,GAAG,CAAC;IAC5B;EACF,CAAC,MAAM,IAAI,CAACG,KAAK,EAAE;IACjB,IAAIH,eAAe,GAAGE,YAAY,EAAE;MAClC,OAAOF,eAAe,GAAG,CAAC;IAC5B;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,SAASK,qBAAqBA,CAACC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,oBAAoB,EAAE;EACnF,MAAMC,kBAAkB,GAAGb,OAAO,CAACc,SAAS,CAACC,WAAW,CAACN,MAAM,CAACO,OAAO,CAACC,WAAW,CAACC,KAAK,CAACC,KAAK,CAAC;EAChG,IAAI,CAACN,kBAAkB,CAACH,KAAK,CAAC,GAAGC,QAAQ,CAAC,EAAE;IAC1C,OAAOD,KAAK;EACd;EACA,MAAMU,oBAAoB,GAAGnB,gCAAgC,CAACQ,MAAM,CAAC;EACrE;EACA,IAAIY,YAAY,GAAGD,oBAAoB,CAACE,OAAO,CAACZ,KAAK,CAAC,IAAIE,oBAAoB,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACnG,OAAOS,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAGD,oBAAoB,CAACG,MAAM,EAAE;IACtE,MAAMC,SAAS,GAAGJ,oBAAoB,CAACC,YAAY,CAAC;IACpD,IAAI,CAACR,kBAAkB,CAACW,SAAS,CAAC,GAAGb,QAAQ,CAAC,EAAE;MAC9C,OAAOa,SAAS;IAClB;IACAH,YAAY,IAAIT,oBAAoB,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1D;EACA,OAAOF,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}