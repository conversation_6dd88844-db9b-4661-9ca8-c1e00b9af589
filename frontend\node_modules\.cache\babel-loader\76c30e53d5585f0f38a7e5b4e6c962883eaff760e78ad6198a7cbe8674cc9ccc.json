{"ast": null, "code": "import { orderBy } from './orderBy.mjs';\nfunction sortBy(arr, criteria) {\n  return orderBy(arr, criteria, ['asc']);\n}\nexport { sortBy };", "map": {"version": 3, "names": ["orderBy", "sortBy", "arr", "criteria"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/sortBy.mjs"], "sourcesContent": ["import { orderBy } from './orderBy.mjs';\n\nfunction sortBy(arr, criteria) {\n    return orderBy(arr, criteria, ['asc']);\n}\n\nexport { sortBy };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AAEvC,SAASC,MAAMA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EAC3B,OAAOH,OAAO,CAACE,GAAG,EAAEC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC;AAC1C;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}