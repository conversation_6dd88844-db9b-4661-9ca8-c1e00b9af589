{"ast": null, "code": "function isPromise(value) {\n  return value instanceof Promise;\n}\nexport { isPromise };", "map": {"version": 3, "names": ["isPromise", "value", "Promise"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isPromise.mjs"], "sourcesContent": ["function isPromise(value) {\n    return value instanceof Promise;\n}\n\nexport { isPromise };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,KAAK,EAAE;EACtB,OAAOA,KAAK,YAAYC,OAAO;AACnC;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}