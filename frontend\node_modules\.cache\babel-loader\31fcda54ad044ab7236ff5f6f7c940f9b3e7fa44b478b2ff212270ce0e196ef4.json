{"ast": null, "code": "function isWeakMap(value) {\n  return value instanceof WeakMap;\n}\nexport { isWeakMap };", "map": {"version": 3, "names": ["isWeakMap", "value", "WeakMap"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isWeakMap.mjs"], "sourcesContent": ["function isWeakMap(value) {\n    return value instanceof WeakMap;\n}\n\nexport { isWeakMap };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,KAAK,EAAE;EACtB,OAAOA,KAAK,YAAYC,OAAO;AACnC;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}