{"ast": null, "code": "function isNode() {\n  return typeof process !== 'undefined' && process?.versions?.node != null;\n}\nexport { isNode };", "map": {"version": 3, "names": ["isNode", "process", "versions", "node"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isNode.mjs"], "sourcesContent": ["function isNode() {\n    return typeof process !== 'undefined' && process?.versions?.node != null;\n}\n\nexport { isNode };\n"], "mappings": "AAAA,SAASA,MAAMA,CAAA,EAAG;EACd,OAAO,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,EAAEC,QAAQ,EAAEC,IAAI,IAAI,IAAI;AAC5E;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}