{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"type\", \"align\", \"width\", \"height\", \"empty\", \"style\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { createRandomNumberGenerator } from \"../../utils/utils.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CIRCULAR_CONTENT_SIZE = '1.3em';\nconst CONTENT_HEIGHT = '1.2em';\nconst DEFAULT_CONTENT_WIDTH_RANGE = [40, 80];\nconst CONTENT_WIDTH_RANGE_BY_TYPE = {\n  number: [40, 60],\n  string: [40, 80],\n  date: [40, 60],\n  dateTime: [60, 80],\n  singleSelect: [40, 80]\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    classes,\n    empty\n  } = ownerState;\n  const slots = {\n    root: ['cell', 'cellSkeleton', `cell--text${align ? capitalize(align) : 'Left'}`, empty && 'cellEmpty']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst randomNumberGenerator = createRandomNumberGenerator(12345);\nfunction GridSkeletonCell(props) {\n  const {\n      field,\n      type,\n      align,\n      width,\n      height,\n      empty = false,\n      style,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes,\n    align,\n    empty\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // Memo prevents the non-circular skeleton widths changing to random widths on every render\n  const skeletonProps = React.useMemo(() => {\n    const isCircularContent = type === 'boolean' || type === 'actions';\n    if (isCircularContent) {\n      return {\n        variant: 'circular',\n        width: CIRCULAR_CONTENT_SIZE,\n        height: CIRCULAR_CONTENT_SIZE\n      };\n    }\n\n    // The width of the skeleton is a random number between the min and max values\n    // The min and max values are determined by the type of the column\n    const [min, max] = type ? CONTENT_WIDTH_RANGE_BY_TYPE[type] ?? DEFAULT_CONTENT_WIDTH_RANGE : DEFAULT_CONTENT_WIDTH_RANGE;\n    return {\n      variant: 'text',\n      width: `${Math.round(randomNumberGenerator(min, max))}%`,\n      height: CONTENT_HEIGHT\n    };\n  }, [type]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    \"data-field\": field,\n    className: clsx(classes.root, className),\n    style: _extends({\n      height,\n      maxWidth: width,\n      minWidth: width\n    }, style)\n  }, other, {\n    children: !empty && /*#__PURE__*/_jsx(rootProps.slots.baseSkeleton, _extends({}, skeletonProps))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridSkeletonCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  align: PropTypes.string,\n  /**\n   * If `true`, the cell will not display the skeleton but still reserve the cell space.\n   * @default false\n   */\n  empty: PropTypes.bool,\n  field: PropTypes.string,\n  height: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]),\n  type: PropTypes.oneOf(['actions', 'boolean', 'custom', 'date', 'dateTime', 'number', 'singleSelect', 'string']),\n  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nconst Memoized = fastMemo(GridSkeletonCell);\nexport { Memoized as GridSkeletonCell };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "capitalize", "fastMemo", "createRandomNumberGenerator", "useGridRootProps", "getDataGridUtilityClass", "jsx", "_jsx", "CIRCULAR_CONTENT_SIZE", "CONTENT_HEIGHT", "DEFAULT_CONTENT_WIDTH_RANGE", "CONTENT_WIDTH_RANGE_BY_TYPE", "number", "string", "date", "dateTime", "singleSelect", "useUtilityClasses", "ownerState", "align", "classes", "empty", "slots", "root", "randomNumberGenerator", "GridSkeletonCell", "props", "field", "type", "width", "height", "style", "className", "other", "rootProps", "skeletonProps", "useMemo", "isCircularContent", "variant", "min", "max", "Math", "round", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "children", "baseSkeleton", "process", "env", "NODE_ENV", "propTypes", "bool", "oneOfType", "oneOf", "Memoized"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/cell/GridSkeletonCell.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"field\", \"type\", \"align\", \"width\", \"height\", \"empty\", \"style\", \"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { createRandomNumberGenerator } from \"../../utils/utils.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CIRCULAR_CONTENT_SIZE = '1.3em';\nconst CONTENT_HEIGHT = '1.2em';\nconst DEFAULT_CONTENT_WIDTH_RANGE = [40, 80];\nconst CONTENT_WIDTH_RANGE_BY_TYPE = {\n  number: [40, 60],\n  string: [40, 80],\n  date: [40, 60],\n  dateTime: [60, 80],\n  singleSelect: [40, 80]\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    classes,\n    empty\n  } = ownerState;\n  const slots = {\n    root: ['cell', 'cellSkeleton', `cell--text${align ? capitalize(align) : 'Left'}`, empty && 'cellEmpty']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst randomNumberGenerator = createRandomNumberGenerator(12345);\nfunction GridSkeletonCell(props) {\n  const {\n      field,\n      type,\n      align,\n      width,\n      height,\n      empty = false,\n      style,\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const ownerState = {\n    classes: rootProps.classes,\n    align,\n    empty\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  // Memo prevents the non-circular skeleton widths changing to random widths on every render\n  const skeletonProps = React.useMemo(() => {\n    const isCircularContent = type === 'boolean' || type === 'actions';\n    if (isCircularContent) {\n      return {\n        variant: 'circular',\n        width: CIRCULAR_CONTENT_SIZE,\n        height: CIRCULAR_CONTENT_SIZE\n      };\n    }\n\n    // The width of the skeleton is a random number between the min and max values\n    // The min and max values are determined by the type of the column\n    const [min, max] = type ? CONTENT_WIDTH_RANGE_BY_TYPE[type] ?? DEFAULT_CONTENT_WIDTH_RANGE : DEFAULT_CONTENT_WIDTH_RANGE;\n    return {\n      variant: 'text',\n      width: `${Math.round(randomNumberGenerator(min, max))}%`,\n      height: CONTENT_HEIGHT\n    };\n  }, [type]);\n  return /*#__PURE__*/_jsx(\"div\", _extends({\n    \"data-field\": field,\n    className: clsx(classes.root, className),\n    style: _extends({\n      height,\n      maxWidth: width,\n      minWidth: width\n    }, style)\n  }, other, {\n    children: !empty && /*#__PURE__*/_jsx(rootProps.slots.baseSkeleton, _extends({}, skeletonProps))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridSkeletonCell.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  align: PropTypes.string,\n  /**\n   * If `true`, the cell will not display the skeleton but still reserve the cell space.\n   * @default false\n   */\n  empty: PropTypes.bool,\n  field: PropTypes.string,\n  height: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]),\n  type: PropTypes.oneOf(['actions', 'boolean', 'custom', 'date', 'dateTime', 'number', 'singleSelect', 'string']),\n  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nconst Memoized = fastMemo(GridSkeletonCell);\nexport { Memoized as GridSkeletonCell };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;AAC9F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,2BAA2B,QAAQ,sBAAsB;AAClE,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,qBAAqB,GAAG,OAAO;AACrC,MAAMC,cAAc,GAAG,OAAO;AAC9B,MAAMC,2BAA2B,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AAC5C,MAAMC,2BAA2B,GAAG;EAClCC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAChBC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAChBC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACdC,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAClBC,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE;AACvB,CAAC;AACD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,KAAK;IACLC,OAAO;IACPC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,aAAaJ,KAAK,GAAGlB,UAAU,CAACkB,KAAK,CAAC,GAAG,MAAM,EAAE,EAAEE,KAAK,IAAI,WAAW;EACxG,CAAC;EACD,OAAOrB,cAAc,CAACsB,KAAK,EAAEjB,uBAAuB,EAAEe,OAAO,CAAC;AAChE,CAAC;AACD,MAAMI,qBAAqB,GAAGrB,2BAA2B,CAAC,KAAK,CAAC;AAChE,SAASsB,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAM;MACFC,KAAK;MACLC,IAAI;MACJT,KAAK;MACLU,KAAK;MACLC,MAAM;MACNT,KAAK,GAAG,KAAK;MACbU,KAAK;MACLC;IACF,CAAC,GAAGN,KAAK;IACTO,KAAK,GAAGtC,6BAA6B,CAAC+B,KAAK,EAAE9B,SAAS,CAAC;EACzD,MAAMsC,SAAS,GAAG9B,gBAAgB,CAAC,CAAC;EACpC,MAAMc,UAAU,GAAG;IACjBE,OAAO,EAAEc,SAAS,CAACd,OAAO;IAC1BD,KAAK;IACLE;EACF,CAAC;EACD,MAAMD,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;;EAE7C;EACA,MAAMiB,aAAa,GAAGtC,KAAK,CAACuC,OAAO,CAAC,MAAM;IACxC,MAAMC,iBAAiB,GAAGT,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,SAAS;IAClE,IAAIS,iBAAiB,EAAE;MACrB,OAAO;QACLC,OAAO,EAAE,UAAU;QACnBT,KAAK,EAAErB,qBAAqB;QAC5BsB,MAAM,EAAEtB;MACV,CAAC;IACH;;IAEA;IACA;IACA,MAAM,CAAC+B,GAAG,EAAEC,GAAG,CAAC,GAAGZ,IAAI,GAAGjB,2BAA2B,CAACiB,IAAI,CAAC,IAAIlB,2BAA2B,GAAGA,2BAA2B;IACxH,OAAO;MACL4B,OAAO,EAAE,MAAM;MACfT,KAAK,EAAE,GAAGY,IAAI,CAACC,KAAK,CAAClB,qBAAqB,CAACe,GAAG,EAAEC,GAAG,CAAC,CAAC,GAAG;MACxDV,MAAM,EAAErB;IACV,CAAC;EACH,CAAC,EAAE,CAACmB,IAAI,CAAC,CAAC;EACV,OAAO,aAAarB,IAAI,CAAC,KAAK,EAAEb,QAAQ,CAAC;IACvC,YAAY,EAAEiC,KAAK;IACnBK,SAAS,EAAEjC,IAAI,CAACqB,OAAO,CAACG,IAAI,EAAES,SAAS,CAAC;IACxCD,KAAK,EAAErC,QAAQ,CAAC;MACdoC,MAAM;MACNa,QAAQ,EAAEd,KAAK;MACfe,QAAQ,EAAEf;IACZ,CAAC,EAAEE,KAAK;EACV,CAAC,EAAEE,KAAK,EAAE;IACRY,QAAQ,EAAE,CAACxB,KAAK,IAAI,aAAad,IAAI,CAAC2B,SAAS,CAACZ,KAAK,CAACwB,YAAY,EAAEpD,QAAQ,CAAC,CAAC,CAAC,EAAEyC,aAAa,CAAC;EACjG,CAAC,CAAC,CAAC;AACL;AACAY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,gBAAgB,CAACyB,SAAS,GAAG;EACnE;EACA;EACA;EACA;EACA/B,KAAK,EAAErB,SAAS,CAACe,MAAM;EACvB;AACF;AACA;AACA;EACEQ,KAAK,EAAEvB,SAAS,CAACqD,IAAI;EACrBxB,KAAK,EAAE7B,SAAS,CAACe,MAAM;EACvBiB,MAAM,EAAEhC,SAAS,CAACsD,SAAS,CAAC,CAACtD,SAAS,CAACuD,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEvD,SAAS,CAACc,MAAM,CAAC,CAAC;EAC1EgB,IAAI,EAAE9B,SAAS,CAACuD,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;EAC/GxB,KAAK,EAAE/B,SAAS,CAACsD,SAAS,CAAC,CAACtD,SAAS,CAACc,MAAM,EAAEd,SAAS,CAACe,MAAM,CAAC;AACjE,CAAC,GAAG,KAAK,CAAC;AACV,MAAMyC,QAAQ,GAAGpD,QAAQ,CAACuB,gBAAgB,CAAC;AAC3C,SAAS6B,QAAQ,IAAI7B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}