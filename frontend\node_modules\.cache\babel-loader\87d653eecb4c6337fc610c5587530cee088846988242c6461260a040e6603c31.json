{"ast": null, "code": "import { uniqWith } from './uniqWith.mjs';\nfunction unionWith(arr1, arr2, areItemsEqual) {\n  return uniqWith(arr1.concat(arr2), areItemsEqual);\n}\nexport { unionWith };", "map": {"version": 3, "names": ["uniqWith", "unionWith", "arr1", "arr2", "areItemsEqual", "concat"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/unionWith.mjs"], "sourcesContent": ["import { uniqWith } from './uniqWith.mjs';\n\nfunction unionWith(arr1, arr2, areItemsEqual) {\n    return uniqWith(arr1.concat(arr2), areItemsEqual);\n}\n\nexport { unionWith };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AAEzC,SAASC,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAEC,aAAa,EAAE;EAC1C,OAAOJ,QAAQ,CAACE,IAAI,CAACG,MAAM,CAACF,IAAI,CAAC,EAAEC,aAAa,CAAC;AACrD;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}