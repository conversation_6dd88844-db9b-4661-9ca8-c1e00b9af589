{"ast": null, "code": "import * as React from 'react';\nexport default parseInt(React.version, 10);", "map": {"version": 3, "names": ["React", "parseInt", "version"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/reactMajor/index.js"], "sourcesContent": ["import * as React from 'react';\nexport default parseInt(React.version, 10);"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAeC,QAAQ,CAACD,KAAK,CAACE,OAAO,EAAE,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}