{"ast": null, "code": "// Based on https://stackoverflow.com/a/59518678\nlet cachedSupportsPreventScroll;\nexport function doesSupportPreventScroll() {\n  if (cachedSupportsPreventScroll === undefined) {\n    document.createElement('div').focus({\n      get preventScroll() {\n        cachedSupportsPreventScroll = true;\n        return false;\n      }\n    });\n  }\n  return cachedSupportsPreventScroll;\n}", "map": {"version": 3, "names": ["cachedSupportsPreventScroll", "doesSupportPreventScroll", "undefined", "document", "createElement", "focus", "preventScroll"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/doesSupportPreventScroll.js"], "sourcesContent": ["// Based on https://stackoverflow.com/a/59518678\nlet cachedSupportsPreventScroll;\nexport function doesSupportPreventScroll() {\n  if (cachedSupportsPreventScroll === undefined) {\n    document.createElement('div').focus({\n      get preventScroll() {\n        cachedSupportsPreventScroll = true;\n        return false;\n      }\n    });\n  }\n  return cachedSupportsPreventScroll;\n}"], "mappings": "AAAA;AACA,IAAIA,2BAA2B;AAC/B,OAAO,SAASC,wBAAwBA,CAAA,EAAG;EACzC,IAAID,2BAA2B,KAAKE,SAAS,EAAE;IAC7CC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAACC,KAAK,CAAC;MAClC,IAAIC,aAAaA,CAAA,EAAG;QAClBN,2BAA2B,GAAG,IAAI;QAClC,OAAO,KAAK;MACd;IACF,CAAC,CAAC;EACJ;EACA,OAAOA,2BAA2B;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}