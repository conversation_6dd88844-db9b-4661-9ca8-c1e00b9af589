{"ast": null, "code": "function maxBy(items, getValue) {\n  if (items.length === 0) {\n    return undefined;\n  }\n  let maxElement = items[0];\n  let max = getValue(maxElement);\n  for (let i = 1; i < items.length; i++) {\n    const element = items[i];\n    const value = getValue(element);\n    if (value > max) {\n      max = value;\n      maxElement = element;\n    }\n  }\n  return maxElement;\n}\nexport { maxBy };", "map": {"version": 3, "names": ["maxBy", "items", "getValue", "length", "undefined", "maxElement", "max", "i", "element", "value"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/maxBy.mjs"], "sourcesContent": ["function maxBy(items, getValue) {\n    if (items.length === 0) {\n        return undefined;\n    }\n    let maxElement = items[0];\n    let max = getValue(maxElement);\n    for (let i = 1; i < items.length; i++) {\n        const element = items[i];\n        const value = getValue(element);\n        if (value > max) {\n            max = value;\n            maxElement = element;\n        }\n    }\n    return maxElement;\n}\n\nexport { maxBy };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC5B,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOC,SAAS;EACpB;EACA,IAAIC,UAAU,GAAGJ,KAAK,CAAC,CAAC,CAAC;EACzB,IAAIK,GAAG,GAAGJ,QAAQ,CAACG,UAAU,CAAC;EAC9B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAACE,MAAM,EAAEI,CAAC,EAAE,EAAE;IACnC,MAAMC,OAAO,GAAGP,KAAK,CAACM,CAAC,CAAC;IACxB,MAAME,KAAK,GAAGP,QAAQ,CAACM,OAAO,CAAC;IAC/B,IAAIC,KAAK,GAAGH,GAAG,EAAE;MACbA,GAAG,GAAGG,KAAK;MACXJ,UAAU,GAAGG,OAAO;IACxB;EACJ;EACA,OAAOH,UAAU;AACrB;AAEA,SAASL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}