{"ast": null, "code": "import { warnOnce } from '@mui/x-internals/warning';\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../../colDef/index.js\";\nfunction sanitizeCellValue(value, csvOptions) {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  const valueStr = typeof value === 'string' ? value : `${value}`;\n  if (csvOptions.shouldAppendQuotes || csvOptions.escapeFormulas) {\n    const escapedValue = valueStr.replace(/\"/g, '\"\"');\n    if (csvOptions.escapeFormulas) {\n      // See https://owasp.org/www-community/attacks/CSV_Injection\n      if (['=', '+', '-', '@', '\\t', '\\r'].includes(escapedValue[0])) {\n        return `\"'${escapedValue}\"`;\n      }\n    }\n    // Make sure value containing delimiter or line break won't be split into multiple cells\n    if ([csvOptions.delimiter, '\\n', '\\r', '\"'].some(delimiter => valueStr.includes(delimiter))) {\n      return `\"${escapedValue}\"`;\n    }\n    return escapedValue;\n  }\n  return valueStr;\n}\nexport const serializeCellValue = (cellParams, options) => {\n  const {\n    csvOptions,\n    ignoreValueFormatter\n  } = options;\n  let value;\n  if (ignoreValueFormatter) {\n    const columnType = cellParams.colDef.type;\n    if (columnType === 'number') {\n      value = String(cellParams.value);\n    } else if (columnType === 'date' || columnType === 'dateTime') {\n      value = cellParams.value?.toISOString();\n    } else if (typeof cellParams.value?.toString === 'function') {\n      value = cellParams.value.toString();\n    } else {\n      value = cellParams.value;\n    }\n  } else {\n    value = cellParams.formattedValue;\n  }\n  return sanitizeCellValue(value, csvOptions);\n};\nclass CSVRow {\n  rowString = '';\n  isEmpty = true;\n  constructor(options) {\n    this.options = options;\n  }\n  addValue(value) {\n    if (!this.isEmpty) {\n      this.rowString += this.options.csvOptions.delimiter;\n    }\n    if (typeof this.options.sanitizeCellValue === 'function') {\n      this.rowString += this.options.sanitizeCellValue(value, this.options.csvOptions);\n    } else {\n      this.rowString += value;\n    }\n    this.isEmpty = false;\n  }\n  getRowString() {\n    return this.rowString;\n  }\n}\nconst serializeRow = ({\n  id,\n  columns,\n  getCellParams,\n  csvOptions,\n  ignoreValueFormatter\n}) => {\n  const row = new CSVRow({\n    csvOptions\n  });\n  columns.forEach(column => {\n    const cellParams = getCellParams(id, column.field);\n    if (process.env.NODE_ENV !== 'production') {\n      if (String(cellParams.formattedValue) === '[object Object]') {\n        warnOnce(['MUI X: When the value of a field is an object or a `renderCell` is provided, the CSV export might not display the value correctly.', 'You can provide a `valueFormatter` with a string representation to be used.']);\n      }\n    }\n    row.addValue(serializeCellValue(cellParams, {\n      ignoreValueFormatter,\n      csvOptions\n    }));\n  });\n  return row.getRowString();\n};\nexport function buildCSV(options) {\n  const {\n    columns,\n    rowIds,\n    csvOptions,\n    ignoreValueFormatter,\n    apiRef\n  } = options;\n  const CSVBody = rowIds.reduce((acc, id) => `${acc}${serializeRow({\n    id,\n    columns,\n    getCellParams: apiRef.current.getCellParams,\n    ignoreValueFormatter,\n    csvOptions\n  })}\\r\\n`, '').trim();\n  if (!csvOptions.includeHeaders) {\n    return CSVBody;\n  }\n  const filteredColumns = columns.filter(column => column.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field);\n  const headerRows = [];\n  if (csvOptions.includeColumnGroupsHeaders) {\n    const columnGroupLookup = apiRef.current.getAllGroupDetails();\n    let maxColumnGroupsDepth = 0;\n    const columnGroupPathsLookup = filteredColumns.reduce((acc, column) => {\n      const columnGroupPath = apiRef.current.getColumnGroupPath(column.field);\n      acc[column.field] = columnGroupPath;\n      maxColumnGroupsDepth = Math.max(maxColumnGroupsDepth, columnGroupPath.length);\n      return acc;\n    }, {});\n    for (let i = 0; i < maxColumnGroupsDepth; i += 1) {\n      const headerGroupRow = new CSVRow({\n        csvOptions,\n        sanitizeCellValue\n      });\n      headerRows.push(headerGroupRow);\n      filteredColumns.forEach(column => {\n        const columnGroupId = (columnGroupPathsLookup[column.field] || [])[i];\n        const columnGroup = columnGroupLookup[columnGroupId];\n        headerGroupRow.addValue(columnGroup ? columnGroup.headerName || columnGroup.groupId : '');\n      });\n    }\n  }\n  const mainHeaderRow = new CSVRow({\n    csvOptions,\n    sanitizeCellValue\n  });\n  filteredColumns.forEach(column => {\n    mainHeaderRow.addValue(column.headerName || column.field);\n  });\n  headerRows.push(mainHeaderRow);\n  const CSVHead = `${headerRows.map(row => row.getRowString()).join('\\r\\n')}\\r\\n`;\n  return `${CSVHead}${CSVBody}`.trim();\n}", "map": {"version": 3, "names": ["warnOnce", "GRID_CHECKBOX_SELECTION_COL_DEF", "sanitizeCellValue", "value", "csvOptions", "undefined", "valueStr", "shouldAppendQuotes", "escapeFormulas", "escapedValue", "replace", "includes", "delimiter", "some", "serializeCellValue", "cellParams", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnType", "colDef", "type", "String", "toISOString", "toString", "formattedValue", "CSVRow", "rowString", "isEmpty", "constructor", "addValue", "getRowString", "serializeRow", "id", "columns", "getCellParams", "row", "for<PERSON>ach", "column", "field", "process", "env", "NODE_ENV", "buildCSV", "rowIds", "apiRef", "CSVBody", "reduce", "acc", "current", "trim", "includeHeaders", "filteredColumns", "filter", "headerRows", "includeColumnGroupsHeaders", "columnGroupLookup", "getAllGroupDetails", "maxColumnGroupsDepth", "columnGroupPathsLookup", "columnGroupPath", "getColumnGroupPath", "Math", "max", "length", "i", "headerGroupRow", "push", "columnGroupId", "columnGroup", "headerName", "groupId", "mainHeaderRow", "CSVHead", "map", "join"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/export/serializers/csvSerializer.js"], "sourcesContent": ["import { warnOnce } from '@mui/x-internals/warning';\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../../colDef/index.js\";\nfunction sanitizeCellValue(value, csvOptions) {\n  if (value === null || value === undefined) {\n    return '';\n  }\n  const valueStr = typeof value === 'string' ? value : `${value}`;\n  if (csvOptions.shouldAppendQuotes || csvOptions.escapeFormulas) {\n    const escapedValue = valueStr.replace(/\"/g, '\"\"');\n    if (csvOptions.escapeFormulas) {\n      // See https://owasp.org/www-community/attacks/CSV_Injection\n      if (['=', '+', '-', '@', '\\t', '\\r'].includes(escapedValue[0])) {\n        return `\"'${escapedValue}\"`;\n      }\n    }\n    // Make sure value containing delimiter or line break won't be split into multiple cells\n    if ([csvOptions.delimiter, '\\n', '\\r', '\"'].some(delimiter => valueStr.includes(delimiter))) {\n      return `\"${escapedValue}\"`;\n    }\n    return escapedValue;\n  }\n  return valueStr;\n}\nexport const serializeCellValue = (cellParams, options) => {\n  const {\n    csvOptions,\n    ignoreValueFormatter\n  } = options;\n  let value;\n  if (ignoreValueFormatter) {\n    const columnType = cellParams.colDef.type;\n    if (columnType === 'number') {\n      value = String(cellParams.value);\n    } else if (columnType === 'date' || columnType === 'dateTime') {\n      value = cellParams.value?.toISOString();\n    } else if (typeof cellParams.value?.toString === 'function') {\n      value = cellParams.value.toString();\n    } else {\n      value = cellParams.value;\n    }\n  } else {\n    value = cellParams.formattedValue;\n  }\n  return sanitizeCellValue(value, csvOptions);\n};\nclass CSVRow {\n  rowString = '';\n  isEmpty = true;\n  constructor(options) {\n    this.options = options;\n  }\n  addValue(value) {\n    if (!this.isEmpty) {\n      this.rowString += this.options.csvOptions.delimiter;\n    }\n    if (typeof this.options.sanitizeCellValue === 'function') {\n      this.rowString += this.options.sanitizeCellValue(value, this.options.csvOptions);\n    } else {\n      this.rowString += value;\n    }\n    this.isEmpty = false;\n  }\n  getRowString() {\n    return this.rowString;\n  }\n}\nconst serializeRow = ({\n  id,\n  columns,\n  getCellParams,\n  csvOptions,\n  ignoreValueFormatter\n}) => {\n  const row = new CSVRow({\n    csvOptions\n  });\n  columns.forEach(column => {\n    const cellParams = getCellParams(id, column.field);\n    if (process.env.NODE_ENV !== 'production') {\n      if (String(cellParams.formattedValue) === '[object Object]') {\n        warnOnce(['MUI X: When the value of a field is an object or a `renderCell` is provided, the CSV export might not display the value correctly.', 'You can provide a `valueFormatter` with a string representation to be used.']);\n      }\n    }\n    row.addValue(serializeCellValue(cellParams, {\n      ignoreValueFormatter,\n      csvOptions\n    }));\n  });\n  return row.getRowString();\n};\nexport function buildCSV(options) {\n  const {\n    columns,\n    rowIds,\n    csvOptions,\n    ignoreValueFormatter,\n    apiRef\n  } = options;\n  const CSVBody = rowIds.reduce((acc, id) => `${acc}${serializeRow({\n    id,\n    columns,\n    getCellParams: apiRef.current.getCellParams,\n    ignoreValueFormatter,\n    csvOptions\n  })}\\r\\n`, '').trim();\n  if (!csvOptions.includeHeaders) {\n    return CSVBody;\n  }\n  const filteredColumns = columns.filter(column => column.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field);\n  const headerRows = [];\n  if (csvOptions.includeColumnGroupsHeaders) {\n    const columnGroupLookup = apiRef.current.getAllGroupDetails();\n    let maxColumnGroupsDepth = 0;\n    const columnGroupPathsLookup = filteredColumns.reduce((acc, column) => {\n      const columnGroupPath = apiRef.current.getColumnGroupPath(column.field);\n      acc[column.field] = columnGroupPath;\n      maxColumnGroupsDepth = Math.max(maxColumnGroupsDepth, columnGroupPath.length);\n      return acc;\n    }, {});\n    for (let i = 0; i < maxColumnGroupsDepth; i += 1) {\n      const headerGroupRow = new CSVRow({\n        csvOptions,\n        sanitizeCellValue\n      });\n      headerRows.push(headerGroupRow);\n      filteredColumns.forEach(column => {\n        const columnGroupId = (columnGroupPathsLookup[column.field] || [])[i];\n        const columnGroup = columnGroupLookup[columnGroupId];\n        headerGroupRow.addValue(columnGroup ? columnGroup.headerName || columnGroup.groupId : '');\n      });\n    }\n  }\n  const mainHeaderRow = new CSVRow({\n    csvOptions,\n    sanitizeCellValue\n  });\n  filteredColumns.forEach(column => {\n    mainHeaderRow.addValue(column.headerName || column.field);\n  });\n  headerRows.push(mainHeaderRow);\n  const CSVHead = `${headerRows.map(row => row.getRowString()).join('\\r\\n')}\\r\\n`;\n  return `${CSVHead}${CSVBody}`.trim();\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,+BAA+B,QAAQ,6BAA6B;AAC7E,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC5C,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE;IACzC,OAAO,EAAE;EACX;EACA,MAAMC,QAAQ,GAAG,OAAOH,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,GAAGA,KAAK,EAAE;EAC/D,IAAIC,UAAU,CAACG,kBAAkB,IAAIH,UAAU,CAACI,cAAc,EAAE;IAC9D,MAAMC,YAAY,GAAGH,QAAQ,CAACI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;IACjD,IAAIN,UAAU,CAACI,cAAc,EAAE;MAC7B;MACA,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAACG,QAAQ,CAACF,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;QAC9D,OAAO,KAAKA,YAAY,GAAG;MAC7B;IACF;IACA;IACA,IAAI,CAACL,UAAU,CAACQ,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAACC,IAAI,CAACD,SAAS,IAAIN,QAAQ,CAACK,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAE;MAC3F,OAAO,IAAIH,YAAY,GAAG;IAC5B;IACA,OAAOA,YAAY;EACrB;EACA,OAAOH,QAAQ;AACjB;AACA,OAAO,MAAMQ,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,OAAO,KAAK;EACzD,MAAM;IACJZ,UAAU;IACVa;EACF,CAAC,GAAGD,OAAO;EACX,IAAIb,KAAK;EACT,IAAIc,oBAAoB,EAAE;IACxB,MAAMC,UAAU,GAAGH,UAAU,CAACI,MAAM,CAACC,IAAI;IACzC,IAAIF,UAAU,KAAK,QAAQ,EAAE;MAC3Bf,KAAK,GAAGkB,MAAM,CAACN,UAAU,CAACZ,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIe,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,UAAU,EAAE;MAC7Df,KAAK,GAAGY,UAAU,CAACZ,KAAK,EAAEmB,WAAW,CAAC,CAAC;IACzC,CAAC,MAAM,IAAI,OAAOP,UAAU,CAACZ,KAAK,EAAEoB,QAAQ,KAAK,UAAU,EAAE;MAC3DpB,KAAK,GAAGY,UAAU,CAACZ,KAAK,CAACoB,QAAQ,CAAC,CAAC;IACrC,CAAC,MAAM;MACLpB,KAAK,GAAGY,UAAU,CAACZ,KAAK;IAC1B;EACF,CAAC,MAAM;IACLA,KAAK,GAAGY,UAAU,CAACS,cAAc;EACnC;EACA,OAAOtB,iBAAiB,CAACC,KAAK,EAAEC,UAAU,CAAC;AAC7C,CAAC;AACD,MAAMqB,MAAM,CAAC;EACXC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,IAAI;EACdC,WAAWA,CAACZ,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EACAa,QAAQA,CAAC1B,KAAK,EAAE;IACd,IAAI,CAAC,IAAI,CAACwB,OAAO,EAAE;MACjB,IAAI,CAACD,SAAS,IAAI,IAAI,CAACV,OAAO,CAACZ,UAAU,CAACQ,SAAS;IACrD;IACA,IAAI,OAAO,IAAI,CAACI,OAAO,CAACd,iBAAiB,KAAK,UAAU,EAAE;MACxD,IAAI,CAACwB,SAAS,IAAI,IAAI,CAACV,OAAO,CAACd,iBAAiB,CAACC,KAAK,EAAE,IAAI,CAACa,OAAO,CAACZ,UAAU,CAAC;IAClF,CAAC,MAAM;MACL,IAAI,CAACsB,SAAS,IAAIvB,KAAK;IACzB;IACA,IAAI,CAACwB,OAAO,GAAG,KAAK;EACtB;EACAG,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,SAAS;EACvB;AACF;AACA,MAAMK,YAAY,GAAGA,CAAC;EACpBC,EAAE;EACFC,OAAO;EACPC,aAAa;EACb9B,UAAU;EACVa;AACF,CAAC,KAAK;EACJ,MAAMkB,GAAG,GAAG,IAAIV,MAAM,CAAC;IACrBrB;EACF,CAAC,CAAC;EACF6B,OAAO,CAACG,OAAO,CAACC,MAAM,IAAI;IACxB,MAAMtB,UAAU,GAAGmB,aAAa,CAACF,EAAE,EAAEK,MAAM,CAACC,KAAK,CAAC;IAClD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIpB,MAAM,CAACN,UAAU,CAACS,cAAc,CAAC,KAAK,iBAAiB,EAAE;QAC3DxB,QAAQ,CAAC,CAAC,oIAAoI,EAAE,6EAA6E,CAAC,CAAC;MACjO;IACF;IACAmC,GAAG,CAACN,QAAQ,CAACf,kBAAkB,CAACC,UAAU,EAAE;MAC1CE,oBAAoB;MACpBb;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAO+B,GAAG,CAACL,YAAY,CAAC,CAAC;AAC3B,CAAC;AACD,OAAO,SAASY,QAAQA,CAAC1B,OAAO,EAAE;EAChC,MAAM;IACJiB,OAAO;IACPU,MAAM;IACNvC,UAAU;IACVa,oBAAoB;IACpB2B;EACF,CAAC,GAAG5B,OAAO;EACX,MAAM6B,OAAO,GAAGF,MAAM,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEf,EAAE,KAAK,GAAGe,GAAG,GAAGhB,YAAY,CAAC;IAC/DC,EAAE;IACFC,OAAO;IACPC,aAAa,EAAEU,MAAM,CAACI,OAAO,CAACd,aAAa;IAC3CjB,oBAAoB;IACpBb;EACF,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC6C,IAAI,CAAC,CAAC;EACpB,IAAI,CAAC7C,UAAU,CAAC8C,cAAc,EAAE;IAC9B,OAAOL,OAAO;EAChB;EACA,MAAMM,eAAe,GAAGlB,OAAO,CAACmB,MAAM,CAACf,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKrC,+BAA+B,CAACqC,KAAK,CAAC;EACxG,MAAMe,UAAU,GAAG,EAAE;EACrB,IAAIjD,UAAU,CAACkD,0BAA0B,EAAE;IACzC,MAAMC,iBAAiB,GAAGX,MAAM,CAACI,OAAO,CAACQ,kBAAkB,CAAC,CAAC;IAC7D,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,MAAMC,sBAAsB,GAAGP,eAAe,CAACL,MAAM,CAAC,CAACC,GAAG,EAAEV,MAAM,KAAK;MACrE,MAAMsB,eAAe,GAAGf,MAAM,CAACI,OAAO,CAACY,kBAAkB,CAACvB,MAAM,CAACC,KAAK,CAAC;MACvES,GAAG,CAACV,MAAM,CAACC,KAAK,CAAC,GAAGqB,eAAe;MACnCF,oBAAoB,GAAGI,IAAI,CAACC,GAAG,CAACL,oBAAoB,EAAEE,eAAe,CAACI,MAAM,CAAC;MAC7E,OAAOhB,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,oBAAoB,EAAEO,CAAC,IAAI,CAAC,EAAE;MAChD,MAAMC,cAAc,GAAG,IAAIxC,MAAM,CAAC;QAChCrB,UAAU;QACVF;MACF,CAAC,CAAC;MACFmD,UAAU,CAACa,IAAI,CAACD,cAAc,CAAC;MAC/Bd,eAAe,CAACf,OAAO,CAACC,MAAM,IAAI;QAChC,MAAM8B,aAAa,GAAG,CAACT,sBAAsB,CAACrB,MAAM,CAACC,KAAK,CAAC,IAAI,EAAE,EAAE0B,CAAC,CAAC;QACrE,MAAMI,WAAW,GAAGb,iBAAiB,CAACY,aAAa,CAAC;QACpDF,cAAc,CAACpC,QAAQ,CAACuC,WAAW,GAAGA,WAAW,CAACC,UAAU,IAAID,WAAW,CAACE,OAAO,GAAG,EAAE,CAAC;MAC3F,CAAC,CAAC;IACJ;EACF;EACA,MAAMC,aAAa,GAAG,IAAI9C,MAAM,CAAC;IAC/BrB,UAAU;IACVF;EACF,CAAC,CAAC;EACFiD,eAAe,CAACf,OAAO,CAACC,MAAM,IAAI;IAChCkC,aAAa,CAAC1C,QAAQ,CAACQ,MAAM,CAACgC,UAAU,IAAIhC,MAAM,CAACC,KAAK,CAAC;EAC3D,CAAC,CAAC;EACFe,UAAU,CAACa,IAAI,CAACK,aAAa,CAAC;EAC9B,MAAMC,OAAO,GAAG,GAAGnB,UAAU,CAACoB,GAAG,CAACtC,GAAG,IAAIA,GAAG,CAACL,YAAY,CAAC,CAAC,CAAC,CAAC4C,IAAI,CAAC,MAAM,CAAC,MAAM;EAC/E,OAAO,GAAGF,OAAO,GAAG3B,OAAO,EAAE,CAACI,IAAI,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}