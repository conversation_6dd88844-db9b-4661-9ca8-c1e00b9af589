{"ast": null, "code": "/**\n * @mui/x-virtualizer v0.1.7\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport * from \"./useVirtualizer.js\";\nexport * from \"./features/index.js\";", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-virtualizer/esm/index.js"], "sourcesContent": ["/**\n * @mui/x-virtualizer v0.1.7\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nexport * from \"./useVirtualizer.js\";\nexport * from \"./features/index.js\";"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,qBAAqB;AACnC,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}