{"ast": null, "code": "import { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n */\nexport const useGridColumnSpanning = apiRef => {\n  const virtualizer = apiRef.current.virtualizer;\n  const resetColSpan = virtualizer.api.resetColSpan;\n  const getCellColSpanInfo = virtualizer.api.getCellColSpanInfo;\n  const calculateColSpan = virtualizer.api.calculateColSpan;\n  const columnSpanningPublicApi = {\n    unstable_getCellColSpanInfo: getCellColSpanInfo\n  };\n  const columnSpanningPrivateApi = {\n    resetColSpan,\n    calculateColSpan\n  };\n  useGridApiMethod(apiRef, columnSpanningPublicApi, 'public');\n  useGridApiMethod(apiRef, columnSpanningPrivateApi, 'private');\n  useGridEvent(apiRef, 'columnOrderChange', resetColSpan);\n};", "map": {"version": 3, "names": ["useGridApiMethod", "useGridEvent", "useGridColumnSpanning", "apiRef", "virtualizer", "current", "resetColSpan", "api", "getCellColSpanInfo", "calculateColSpan", "columnSpanningPublicApi", "unstable_getCellColSpanInfo", "columnSpanningPrivateApi"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columns/useGridColumnSpanning.js"], "sourcesContent": ["import { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\n/**\n * @requires useGridColumns (method, event)\n * @requires useGridParamsApi (method)\n */\nexport const useGridColumnSpanning = apiRef => {\n  const virtualizer = apiRef.current.virtualizer;\n  const resetColSpan = virtualizer.api.resetColSpan;\n  const getCellColSpanInfo = virtualizer.api.getCellColSpanInfo;\n  const calculateColSpan = virtualizer.api.calculateColSpan;\n  const columnSpanningPublicApi = {\n    unstable_getCellColSpanInfo: getCellColSpanInfo\n  };\n  const columnSpanningPrivateApi = {\n    resetColSpan,\n    calculateColSpan\n  };\n  useGridApiMethod(apiRef, columnSpanningPublicApi, 'public');\n  useGridApiMethod(apiRef, columnSpanningPrivateApi, 'private');\n  useGridEvent(apiRef, 'columnOrderChange', resetColSpan);\n};"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAGC,MAAM,IAAI;EAC7C,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAACD,WAAW;EAC9C,MAAME,YAAY,GAAGF,WAAW,CAACG,GAAG,CAACD,YAAY;EACjD,MAAME,kBAAkB,GAAGJ,WAAW,CAACG,GAAG,CAACC,kBAAkB;EAC7D,MAAMC,gBAAgB,GAAGL,WAAW,CAACG,GAAG,CAACE,gBAAgB;EACzD,MAAMC,uBAAuB,GAAG;IAC9BC,2BAA2B,EAAEH;EAC/B,CAAC;EACD,MAAMI,wBAAwB,GAAG;IAC/BN,YAAY;IACZG;EACF,CAAC;EACDT,gBAAgB,CAACG,MAAM,EAAEO,uBAAuB,EAAE,QAAQ,CAAC;EAC3DV,gBAAgB,CAACG,MAAM,EAAES,wBAAwB,EAAE,SAAS,CAAC;EAC7DX,YAAY,CAACE,MAAM,EAAE,mBAAmB,EAAEG,YAAY,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}