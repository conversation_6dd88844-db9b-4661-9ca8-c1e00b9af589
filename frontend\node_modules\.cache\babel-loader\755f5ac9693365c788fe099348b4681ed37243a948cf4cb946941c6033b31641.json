{"ast": null, "code": "import { differenceWith } from './differenceWith.mjs';\nimport { intersectionWith } from './intersectionWith.mjs';\nimport { unionWith } from './unionWith.mjs';\nfunction xorWith(arr1, arr2, areElementsEqual) {\n  const union = unionWith(arr1, arr2, areElementsEqual);\n  const intersection = intersectionWith(arr1, arr2, areElementsEqual);\n  return differenceWith(union, intersection, areElementsEqual);\n}\nexport { xorWith };", "map": {"version": 3, "names": ["differenceWith", "intersectionWith", "unionWith", "xorWith", "arr1", "arr2", "areElementsEqual", "union", "intersection"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/xorWith.mjs"], "sourcesContent": ["import { differenceWith } from './differenceWith.mjs';\nimport { intersectionWith } from './intersectionWith.mjs';\nimport { unionWith } from './unionWith.mjs';\n\nfunction xorWith(arr1, arr2, areElementsEqual) {\n    const union = unionWith(arr1, arr2, areElementsEqual);\n    const intersection = intersectionWith(arr1, arr2, areElementsEqual);\n    return differenceWith(union, intersection, areElementsEqual);\n}\n\nexport { xorWith };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB;AACrD,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,SAAS,QAAQ,iBAAiB;AAE3C,SAASC,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAEC,gBAAgB,EAAE;EAC3C,MAAMC,KAAK,GAAGL,SAAS,CAACE,IAAI,EAAEC,IAAI,EAAEC,gBAAgB,CAAC;EACrD,MAAME,YAAY,GAAGP,gBAAgB,CAACG,IAAI,EAAEC,IAAI,EAAEC,gBAAgB,CAAC;EACnE,OAAON,cAAc,CAACO,KAAK,EAAEC,YAAY,EAAEF,gBAAgB,CAAC;AAChE;AAEA,SAASH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}