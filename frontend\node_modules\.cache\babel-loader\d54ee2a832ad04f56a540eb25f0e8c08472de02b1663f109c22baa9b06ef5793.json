{"ast": null, "code": "import * as React from 'react';\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { useGridPrivateApiContext } from \"../../utils/useGridPrivateApiContext.js\";\nexport const useGridRowAriaAttributes = () => {\n  const apiRef = useGridPrivateApiContext();\n  const headerGroupingMaxDepth = useGridSelector(apiRef, gridColumnGroupsHeaderMaxDepthSelector);\n  return React.useCallback((rowNode, index) => {\n    const ariaAttributes = {};\n    const ariaRowIndex = index + headerGroupingMaxDepth + 2; // 1 for the header row and 1 as it's 1-based\n    ariaAttributes['aria-rowindex'] = ariaRowIndex;\n\n    // XXX: fix this properly\n    if (rowNode && apiRef.current.isRowSelectable(rowNode.id)) {\n      ariaAttributes['aria-selected'] = apiRef.current.isRowSelected(rowNode.id);\n    }\n    return ariaAttributes;\n  }, [apiRef, headerGroupingMaxDepth]);\n};", "map": {"version": 3, "names": ["React", "useGridSelector", "gridColumnGroupsHeaderMaxDepthSelector", "useGridPrivateApiContext", "useGridRowAriaAttributes", "apiRef", "headerGroupingMaxDepth", "useCallback", "rowNode", "index", "ariaAttributes", "ariaRowIndex", "current", "isRowSelectable", "id", "isRowSelected"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rows/useGridRowAriaAttributes.js"], "sourcesContent": ["import * as React from 'react';\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { useGridPrivateApiContext } from \"../../utils/useGridPrivateApiContext.js\";\nexport const useGridRowAriaAttributes = () => {\n  const apiRef = useGridPrivateApiContext();\n  const headerGroupingMaxDepth = useGridSelector(apiRef, gridColumnGroupsHeaderMaxDepthSelector);\n  return React.useCallback((rowNode, index) => {\n    const ariaAttributes = {};\n    const ariaRowIndex = index + headerGroupingMaxDepth + 2; // 1 for the header row and 1 as it's 1-based\n    ariaAttributes['aria-rowindex'] = ariaRowIndex;\n\n    // XXX: fix this properly\n    if (rowNode && apiRef.current.isRowSelectable(rowNode.id)) {\n      ariaAttributes['aria-selected'] = apiRef.current.isRowSelected(rowNode.id);\n    }\n    return ariaAttributes;\n  }, [apiRef, headerGroupingMaxDepth]);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,sCAAsC,QAAQ,+CAA+C;AACtG,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,OAAO,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;EAC5C,MAAMC,MAAM,GAAGF,wBAAwB,CAAC,CAAC;EACzC,MAAMG,sBAAsB,GAAGL,eAAe,CAACI,MAAM,EAAEH,sCAAsC,CAAC;EAC9F,OAAOF,KAAK,CAACO,WAAW,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;IAC3C,MAAMC,cAAc,GAAG,CAAC,CAAC;IACzB,MAAMC,YAAY,GAAGF,KAAK,GAAGH,sBAAsB,GAAG,CAAC,CAAC,CAAC;IACzDI,cAAc,CAAC,eAAe,CAAC,GAAGC,YAAY;;IAE9C;IACA,IAAIH,OAAO,IAAIH,MAAM,CAACO,OAAO,CAACC,eAAe,CAACL,OAAO,CAACM,EAAE,CAAC,EAAE;MACzDJ,cAAc,CAAC,eAAe,CAAC,GAAGL,MAAM,CAACO,OAAO,CAACG,aAAa,CAACP,OAAO,CAACM,EAAE,CAAC;IAC5E;IACA,OAAOJ,cAAc;EACvB,CAAC,EAAE,CAACL,MAAM,EAAEC,sBAAsB,CAAC,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}