{"ast": null, "code": "import { GridSignature } from \"../../../constants/signature.js\";\nimport { GRID_ROOT_GROUP_ID } from \"../rows/gridRowsUtils.js\";\nimport { gridFilteredRowsLookupSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridSortedRowIdsSelector } from \"../sorting/gridSortingSelector.js\";\nimport { gridRowSelectionManagerSelector } from \"./gridRowSelectionSelector.js\";\nimport { gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { createSelector } from \"../../../utils/createSelector.js\";\nexport const ROW_SELECTION_PROPAGATION_DEFAULT = {\n  parents: true,\n  descendants: true\n};\nfunction getGridRowGroupSelectableDescendants(apiRef, groupId) {\n  const rowTree = gridRowTreeSelector(apiRef);\n  const sortedRowIds = gridSortedRowIdsSelector(apiRef);\n  const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n  const groupNode = rowTree[groupId];\n  if (!groupNode || groupNode.type !== 'group') {\n    return [];\n  }\n  const descendants = [];\n  const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;\n  for (let index = startIndex; index < sortedRowIds.length && rowTree[sortedRowIds[index]]?.depth > groupNode.depth; index += 1) {\n    const id = sortedRowIds[index];\n    if (filteredRowsLookup[id] !== false && apiRef.current.isRowSelectable(id)) {\n      descendants.push(id);\n    }\n  }\n  return descendants;\n}\nexport const checkboxPropsSelector = createSelector(gridRowTreeSelector, gridFilteredRowsLookupSelector, gridRowSelectionManagerSelector, (rowTree, filteredRowsLookup, rowSelectionManager, {\n  groupId,\n  autoSelectParents\n}) => {\n  const groupNode = rowTree[groupId];\n  if (!groupNode || groupNode.type !== 'group' || rowSelectionManager.has(groupId)) {\n    return {\n      isIndeterminate: false,\n      isChecked: rowSelectionManager.has(groupId)\n    };\n  }\n  let hasSelectedDescendant = false;\n  let hasUnSelectedDescendant = false;\n  const traverseDescendants = itemToTraverseId => {\n    if (filteredRowsLookup[itemToTraverseId] === false ||\n    // Perf: Skip checking the rest of the descendants if we already\n    // know that there is a selected and an unselected descendant\n    hasSelectedDescendant && hasUnSelectedDescendant) {\n      return;\n    }\n    const node = rowTree[itemToTraverseId];\n    if (node?.type === 'group') {\n      node.children.forEach(traverseDescendants);\n    }\n    if (rowSelectionManager.has(itemToTraverseId)) {\n      hasSelectedDescendant = true;\n    } else {\n      hasUnSelectedDescendant = true;\n    }\n  };\n  traverseDescendants(groupId);\n  return {\n    isIndeterminate: hasSelectedDescendant && hasUnSelectedDescendant,\n    isChecked: autoSelectParents ? hasSelectedDescendant && !hasUnSelectedDescendant : false\n  };\n});\nexport function isMultipleRowSelectionEnabled(props) {\n  if (props.signature === GridSignature.DataGrid) {\n    // DataGrid Community has multiple row selection enabled only if checkbox selection is enabled.\n    return props.checkboxSelection && props.disableMultipleRowSelection !== true;\n  }\n  return !props.disableMultipleRowSelection;\n}\nconst getRowNodeParents = (tree, id) => {\n  const parents = [];\n  let parent = id;\n  while (parent != null && parent !== GRID_ROOT_GROUP_ID) {\n    const node = tree[parent];\n    if (!node) {\n      return parents;\n    }\n    parents.push(parent);\n    parent = node.parent;\n  }\n  return parents;\n};\nconst getFilteredRowNodeSiblings = (tree, filteredRows, id) => {\n  const node = tree[id];\n  if (!node) {\n    return [];\n  }\n  const parent = node.parent;\n  if (parent == null) {\n    return [];\n  }\n  const parentNode = tree[parent];\n  return parentNode.children.filter(childId => childId !== id && filteredRows[childId] !== false);\n};\nexport const findRowsToSelect = (apiRef, tree, selectedRow, autoSelectDescendants, autoSelectParents, addRow, rowSelectionManager = gridRowSelectionManagerSelector(apiRef)) => {\n  const filteredRows = gridFilteredRowsLookupSelector(apiRef);\n  const selectedDescendants = new Set([]);\n  if (!autoSelectDescendants && !autoSelectParents || filteredRows[selectedRow] === false) {\n    return;\n  }\n  if (autoSelectDescendants) {\n    const rowNode = tree[selectedRow];\n    if (rowNode?.type === 'group') {\n      const descendants = getGridRowGroupSelectableDescendants(apiRef, selectedRow);\n      descendants.forEach(rowId => {\n        addRow(rowId);\n        selectedDescendants.add(rowId);\n      });\n    }\n  }\n  if (autoSelectParents) {\n    const checkAllDescendantsSelected = rowId => {\n      if (!rowSelectionManager.has(rowId) && !selectedDescendants.has(rowId)) {\n        return false;\n      }\n      const node = tree[rowId];\n      if (!node) {\n        return false;\n      }\n      if (node.type !== 'group') {\n        return true;\n      }\n      return node.children.every(checkAllDescendantsSelected);\n    };\n    const traverseParents = rowId => {\n      const siblings = getFilteredRowNodeSiblings(tree, filteredRows, rowId);\n      if (siblings.length === 0 || siblings.every(checkAllDescendantsSelected)) {\n        const rowNode = tree[rowId];\n        const parent = rowNode?.parent;\n        if (parent != null && parent !== GRID_ROOT_GROUP_ID && apiRef.current.isRowSelectable(parent)) {\n          addRow(parent);\n          selectedDescendants.add(parent);\n          traverseParents(parent);\n        }\n      }\n    };\n    // For root level rows, we don't need to traverse parents\n    const rowNode = tree[selectedRow];\n    if (!rowNode || rowNode.parent === GRID_ROOT_GROUP_ID) {\n      return;\n    }\n    traverseParents(selectedRow);\n  }\n};\nexport const findRowsToDeselect = (apiRef, tree, deselectedRow, autoSelectDescendants, autoSelectParents, removeRow) => {\n  const rowSelectionManager = gridRowSelectionManagerSelector(apiRef);\n  if (!autoSelectParents && !autoSelectDescendants) {\n    return;\n  }\n  if (autoSelectParents) {\n    const allParents = getRowNodeParents(tree, deselectedRow);\n    allParents.forEach(parent => {\n      const isSelected = rowSelectionManager.has(parent);\n      if (isSelected) {\n        removeRow(parent);\n      }\n    });\n  }\n  if (autoSelectDescendants) {\n    const rowNode = tree[deselectedRow];\n    if (rowNode?.type === 'group') {\n      const descendants = getGridRowGroupSelectableDescendants(apiRef, deselectedRow);\n      descendants.forEach(descendant => {\n        removeRow(descendant);\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["GridSignature", "GRID_ROOT_GROUP_ID", "gridFilteredRowsLookupSelector", "gridSortedRowIdsSelector", "gridRowSelectionManagerSelector", "gridRowTreeSelector", "createSelector", "ROW_SELECTION_PROPAGATION_DEFAULT", "parents", "descendants", "getGridRowGroupSelectableDescendants", "apiRef", "groupId", "rowTree", "sortedRowIds", "filteredRowsLookup", "groupNode", "type", "startIndex", "findIndex", "id", "index", "length", "depth", "current", "isRowSelectable", "push", "checkboxPropsSelector", "rowSelectionManager", "autoSelectParents", "has", "isIndeterminate", "isChecked", "hasSelectedDescendant", "hasUnSelectedDescendant", "traverseDescendants", "itemToTraverseId", "node", "children", "for<PERSON>ach", "isMultipleRowSelectionEnabled", "props", "signature", "DataGrid", "checkboxSelection", "disableMultipleRowSelection", "getRowNodeParents", "tree", "parent", "getFilteredRowNodeSiblings", "filteredRows", "parentNode", "filter", "childId", "findRowsToSelect", "selectedRow", "autoSelectDescendants", "addRow", "selectedDescendants", "Set", "rowNode", "rowId", "add", "checkAllDescendantsSelected", "every", "traverseParents", "siblings", "findRowsToDeselect", "deselectedRow", "removeRow", "allParents", "isSelected", "descendant"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rowSelection/utils.js"], "sourcesContent": ["import { GridSignature } from \"../../../constants/signature.js\";\nimport { GRID_ROOT_GROUP_ID } from \"../rows/gridRowsUtils.js\";\nimport { gridFilteredRowsLookupSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridSortedRowIdsSelector } from \"../sorting/gridSortingSelector.js\";\nimport { gridRowSelectionManagerSelector } from \"./gridRowSelectionSelector.js\";\nimport { gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { createSelector } from \"../../../utils/createSelector.js\";\nexport const ROW_SELECTION_PROPAGATION_DEFAULT = {\n  parents: true,\n  descendants: true\n};\nfunction getGridRowGroupSelectableDescendants(apiRef, groupId) {\n  const rowTree = gridRowTreeSelector(apiRef);\n  const sortedRowIds = gridSortedRowIdsSelector(apiRef);\n  const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n  const groupNode = rowTree[groupId];\n  if (!groupNode || groupNode.type !== 'group') {\n    return [];\n  }\n  const descendants = [];\n  const startIndex = sortedRowIds.findIndex(id => id === groupId) + 1;\n  for (let index = startIndex; index < sortedRowIds.length && rowTree[sortedRowIds[index]]?.depth > groupNode.depth; index += 1) {\n    const id = sortedRowIds[index];\n    if (filteredRowsLookup[id] !== false && apiRef.current.isRowSelectable(id)) {\n      descendants.push(id);\n    }\n  }\n  return descendants;\n}\nexport const checkboxPropsSelector = createSelector(gridRowTreeSelector, gridFilteredRowsLookupSelector, gridRowSelectionManagerSelector, (rowTree, filteredRowsLookup, rowSelectionManager, {\n  groupId,\n  autoSelectParents\n}) => {\n  const groupNode = rowTree[groupId];\n  if (!groupNode || groupNode.type !== 'group' || rowSelectionManager.has(groupId)) {\n    return {\n      isIndeterminate: false,\n      isChecked: rowSelectionManager.has(groupId)\n    };\n  }\n  let hasSelectedDescendant = false;\n  let hasUnSelectedDescendant = false;\n  const traverseDescendants = itemToTraverseId => {\n    if (filteredRowsLookup[itemToTraverseId] === false ||\n    // Perf: Skip checking the rest of the descendants if we already\n    // know that there is a selected and an unselected descendant\n    hasSelectedDescendant && hasUnSelectedDescendant) {\n      return;\n    }\n    const node = rowTree[itemToTraverseId];\n    if (node?.type === 'group') {\n      node.children.forEach(traverseDescendants);\n    }\n    if (rowSelectionManager.has(itemToTraverseId)) {\n      hasSelectedDescendant = true;\n    } else {\n      hasUnSelectedDescendant = true;\n    }\n  };\n  traverseDescendants(groupId);\n  return {\n    isIndeterminate: hasSelectedDescendant && hasUnSelectedDescendant,\n    isChecked: autoSelectParents ? hasSelectedDescendant && !hasUnSelectedDescendant : false\n  };\n});\nexport function isMultipleRowSelectionEnabled(props) {\n  if (props.signature === GridSignature.DataGrid) {\n    // DataGrid Community has multiple row selection enabled only if checkbox selection is enabled.\n    return props.checkboxSelection && props.disableMultipleRowSelection !== true;\n  }\n  return !props.disableMultipleRowSelection;\n}\nconst getRowNodeParents = (tree, id) => {\n  const parents = [];\n  let parent = id;\n  while (parent != null && parent !== GRID_ROOT_GROUP_ID) {\n    const node = tree[parent];\n    if (!node) {\n      return parents;\n    }\n    parents.push(parent);\n    parent = node.parent;\n  }\n  return parents;\n};\nconst getFilteredRowNodeSiblings = (tree, filteredRows, id) => {\n  const node = tree[id];\n  if (!node) {\n    return [];\n  }\n  const parent = node.parent;\n  if (parent == null) {\n    return [];\n  }\n  const parentNode = tree[parent];\n  return parentNode.children.filter(childId => childId !== id && filteredRows[childId] !== false);\n};\nexport const findRowsToSelect = (apiRef, tree, selectedRow, autoSelectDescendants, autoSelectParents, addRow, rowSelectionManager = gridRowSelectionManagerSelector(apiRef)) => {\n  const filteredRows = gridFilteredRowsLookupSelector(apiRef);\n  const selectedDescendants = new Set([]);\n  if (!autoSelectDescendants && !autoSelectParents || filteredRows[selectedRow] === false) {\n    return;\n  }\n  if (autoSelectDescendants) {\n    const rowNode = tree[selectedRow];\n    if (rowNode?.type === 'group') {\n      const descendants = getGridRowGroupSelectableDescendants(apiRef, selectedRow);\n      descendants.forEach(rowId => {\n        addRow(rowId);\n        selectedDescendants.add(rowId);\n      });\n    }\n  }\n  if (autoSelectParents) {\n    const checkAllDescendantsSelected = rowId => {\n      if (!rowSelectionManager.has(rowId) && !selectedDescendants.has(rowId)) {\n        return false;\n      }\n      const node = tree[rowId];\n      if (!node) {\n        return false;\n      }\n      if (node.type !== 'group') {\n        return true;\n      }\n      return node.children.every(checkAllDescendantsSelected);\n    };\n    const traverseParents = rowId => {\n      const siblings = getFilteredRowNodeSiblings(tree, filteredRows, rowId);\n      if (siblings.length === 0 || siblings.every(checkAllDescendantsSelected)) {\n        const rowNode = tree[rowId];\n        const parent = rowNode?.parent;\n        if (parent != null && parent !== GRID_ROOT_GROUP_ID && apiRef.current.isRowSelectable(parent)) {\n          addRow(parent);\n          selectedDescendants.add(parent);\n          traverseParents(parent);\n        }\n      }\n    };\n    // For root level rows, we don't need to traverse parents\n    const rowNode = tree[selectedRow];\n    if (!rowNode || rowNode.parent === GRID_ROOT_GROUP_ID) {\n      return;\n    }\n    traverseParents(selectedRow);\n  }\n};\nexport const findRowsToDeselect = (apiRef, tree, deselectedRow, autoSelectDescendants, autoSelectParents, removeRow) => {\n  const rowSelectionManager = gridRowSelectionManagerSelector(apiRef);\n  if (!autoSelectParents && !autoSelectDescendants) {\n    return;\n  }\n  if (autoSelectParents) {\n    const allParents = getRowNodeParents(tree, deselectedRow);\n    allParents.forEach(parent => {\n      const isSelected = rowSelectionManager.has(parent);\n      if (isSelected) {\n        removeRow(parent);\n      }\n    });\n  }\n  if (autoSelectDescendants) {\n    const rowNode = tree[deselectedRow];\n    if (rowNode?.type === 'group') {\n      const descendants = getGridRowGroupSelectableDescendants(apiRef, deselectedRow);\n      descendants.forEach(descendant => {\n        removeRow(descendant);\n      });\n    }\n  }\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,8BAA8B,QAAQ,iCAAiC;AAChF,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,+BAA+B,QAAQ,+BAA+B;AAC/E,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAO,MAAMC,iCAAiC,GAAG;EAC/CC,OAAO,EAAE,IAAI;EACbC,WAAW,EAAE;AACf,CAAC;AACD,SAASC,oCAAoCA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC7D,MAAMC,OAAO,GAAGR,mBAAmB,CAACM,MAAM,CAAC;EAC3C,MAAMG,YAAY,GAAGX,wBAAwB,CAACQ,MAAM,CAAC;EACrD,MAAMI,kBAAkB,GAAGb,8BAA8B,CAACS,MAAM,CAAC;EACjE,MAAMK,SAAS,GAAGH,OAAO,CAACD,OAAO,CAAC;EAClC,IAAI,CAACI,SAAS,IAAIA,SAAS,CAACC,IAAI,KAAK,OAAO,EAAE;IAC5C,OAAO,EAAE;EACX;EACA,MAAMR,WAAW,GAAG,EAAE;EACtB,MAAMS,UAAU,GAAGJ,YAAY,CAACK,SAAS,CAACC,EAAE,IAAIA,EAAE,KAAKR,OAAO,CAAC,GAAG,CAAC;EACnE,KAAK,IAAIS,KAAK,GAAGH,UAAU,EAAEG,KAAK,GAAGP,YAAY,CAACQ,MAAM,IAAIT,OAAO,CAACC,YAAY,CAACO,KAAK,CAAC,CAAC,EAAEE,KAAK,GAAGP,SAAS,CAACO,KAAK,EAAEF,KAAK,IAAI,CAAC,EAAE;IAC7H,MAAMD,EAAE,GAAGN,YAAY,CAACO,KAAK,CAAC;IAC9B,IAAIN,kBAAkB,CAACK,EAAE,CAAC,KAAK,KAAK,IAAIT,MAAM,CAACa,OAAO,CAACC,eAAe,CAACL,EAAE,CAAC,EAAE;MAC1EX,WAAW,CAACiB,IAAI,CAACN,EAAE,CAAC;IACtB;EACF;EACA,OAAOX,WAAW;AACpB;AACA,OAAO,MAAMkB,qBAAqB,GAAGrB,cAAc,CAACD,mBAAmB,EAAEH,8BAA8B,EAAEE,+BAA+B,EAAE,CAACS,OAAO,EAAEE,kBAAkB,EAAEa,mBAAmB,EAAE;EAC3LhB,OAAO;EACPiB;AACF,CAAC,KAAK;EACJ,MAAMb,SAAS,GAAGH,OAAO,CAACD,OAAO,CAAC;EAClC,IAAI,CAACI,SAAS,IAAIA,SAAS,CAACC,IAAI,KAAK,OAAO,IAAIW,mBAAmB,CAACE,GAAG,CAAClB,OAAO,CAAC,EAAE;IAChF,OAAO;MACLmB,eAAe,EAAE,KAAK;MACtBC,SAAS,EAAEJ,mBAAmB,CAACE,GAAG,CAAClB,OAAO;IAC5C,CAAC;EACH;EACA,IAAIqB,qBAAqB,GAAG,KAAK;EACjC,IAAIC,uBAAuB,GAAG,KAAK;EACnC,MAAMC,mBAAmB,GAAGC,gBAAgB,IAAI;IAC9C,IAAIrB,kBAAkB,CAACqB,gBAAgB,CAAC,KAAK,KAAK;IAClD;IACA;IACAH,qBAAqB,IAAIC,uBAAuB,EAAE;MAChD;IACF;IACA,MAAMG,IAAI,GAAGxB,OAAO,CAACuB,gBAAgB,CAAC;IACtC,IAAIC,IAAI,EAAEpB,IAAI,KAAK,OAAO,EAAE;MAC1BoB,IAAI,CAACC,QAAQ,CAACC,OAAO,CAACJ,mBAAmB,CAAC;IAC5C;IACA,IAAIP,mBAAmB,CAACE,GAAG,CAACM,gBAAgB,CAAC,EAAE;MAC7CH,qBAAqB,GAAG,IAAI;IAC9B,CAAC,MAAM;MACLC,uBAAuB,GAAG,IAAI;IAChC;EACF,CAAC;EACDC,mBAAmB,CAACvB,OAAO,CAAC;EAC5B,OAAO;IACLmB,eAAe,EAAEE,qBAAqB,IAAIC,uBAAuB;IACjEF,SAAS,EAAEH,iBAAiB,GAAGI,qBAAqB,IAAI,CAACC,uBAAuB,GAAG;EACrF,CAAC;AACH,CAAC,CAAC;AACF,OAAO,SAASM,6BAA6BA,CAACC,KAAK,EAAE;EACnD,IAAIA,KAAK,CAACC,SAAS,KAAK1C,aAAa,CAAC2C,QAAQ,EAAE;IAC9C;IACA,OAAOF,KAAK,CAACG,iBAAiB,IAAIH,KAAK,CAACI,2BAA2B,KAAK,IAAI;EAC9E;EACA,OAAO,CAACJ,KAAK,CAACI,2BAA2B;AAC3C;AACA,MAAMC,iBAAiB,GAAGA,CAACC,IAAI,EAAE3B,EAAE,KAAK;EACtC,MAAMZ,OAAO,GAAG,EAAE;EAClB,IAAIwC,MAAM,GAAG5B,EAAE;EACf,OAAO4B,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK/C,kBAAkB,EAAE;IACtD,MAAMoC,IAAI,GAAGU,IAAI,CAACC,MAAM,CAAC;IACzB,IAAI,CAACX,IAAI,EAAE;MACT,OAAO7B,OAAO;IAChB;IACAA,OAAO,CAACkB,IAAI,CAACsB,MAAM,CAAC;IACpBA,MAAM,GAAGX,IAAI,CAACW,MAAM;EACtB;EACA,OAAOxC,OAAO;AAChB,CAAC;AACD,MAAMyC,0BAA0B,GAAGA,CAACF,IAAI,EAAEG,YAAY,EAAE9B,EAAE,KAAK;EAC7D,MAAMiB,IAAI,GAAGU,IAAI,CAAC3B,EAAE,CAAC;EACrB,IAAI,CAACiB,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EACA,MAAMW,MAAM,GAAGX,IAAI,CAACW,MAAM;EAC1B,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,EAAE;EACX;EACA,MAAMG,UAAU,GAAGJ,IAAI,CAACC,MAAM,CAAC;EAC/B,OAAOG,UAAU,CAACb,QAAQ,CAACc,MAAM,CAACC,OAAO,IAAIA,OAAO,KAAKjC,EAAE,IAAI8B,YAAY,CAACG,OAAO,CAAC,KAAK,KAAK,CAAC;AACjG,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGA,CAAC3C,MAAM,EAAEoC,IAAI,EAAEQ,WAAW,EAAEC,qBAAqB,EAAE3B,iBAAiB,EAAE4B,MAAM,EAAE7B,mBAAmB,GAAGxB,+BAA+B,CAACO,MAAM,CAAC,KAAK;EAC9K,MAAMuC,YAAY,GAAGhD,8BAA8B,CAACS,MAAM,CAAC;EAC3D,MAAM+C,mBAAmB,GAAG,IAAIC,GAAG,CAAC,EAAE,CAAC;EACvC,IAAI,CAACH,qBAAqB,IAAI,CAAC3B,iBAAiB,IAAIqB,YAAY,CAACK,WAAW,CAAC,KAAK,KAAK,EAAE;IACvF;EACF;EACA,IAAIC,qBAAqB,EAAE;IACzB,MAAMI,OAAO,GAAGb,IAAI,CAACQ,WAAW,CAAC;IACjC,IAAIK,OAAO,EAAE3C,IAAI,KAAK,OAAO,EAAE;MAC7B,MAAMR,WAAW,GAAGC,oCAAoC,CAACC,MAAM,EAAE4C,WAAW,CAAC;MAC7E9C,WAAW,CAAC8B,OAAO,CAACsB,KAAK,IAAI;QAC3BJ,MAAM,CAACI,KAAK,CAAC;QACbH,mBAAmB,CAACI,GAAG,CAACD,KAAK,CAAC;MAChC,CAAC,CAAC;IACJ;EACF;EACA,IAAIhC,iBAAiB,EAAE;IACrB,MAAMkC,2BAA2B,GAAGF,KAAK,IAAI;MAC3C,IAAI,CAACjC,mBAAmB,CAACE,GAAG,CAAC+B,KAAK,CAAC,IAAI,CAACH,mBAAmB,CAAC5B,GAAG,CAAC+B,KAAK,CAAC,EAAE;QACtE,OAAO,KAAK;MACd;MACA,MAAMxB,IAAI,GAAGU,IAAI,CAACc,KAAK,CAAC;MACxB,IAAI,CAACxB,IAAI,EAAE;QACT,OAAO,KAAK;MACd;MACA,IAAIA,IAAI,CAACpB,IAAI,KAAK,OAAO,EAAE;QACzB,OAAO,IAAI;MACb;MACA,OAAOoB,IAAI,CAACC,QAAQ,CAAC0B,KAAK,CAACD,2BAA2B,CAAC;IACzD,CAAC;IACD,MAAME,eAAe,GAAGJ,KAAK,IAAI;MAC/B,MAAMK,QAAQ,GAAGjB,0BAA0B,CAACF,IAAI,EAAEG,YAAY,EAAEW,KAAK,CAAC;MACtE,IAAIK,QAAQ,CAAC5C,MAAM,KAAK,CAAC,IAAI4C,QAAQ,CAACF,KAAK,CAACD,2BAA2B,CAAC,EAAE;QACxE,MAAMH,OAAO,GAAGb,IAAI,CAACc,KAAK,CAAC;QAC3B,MAAMb,MAAM,GAAGY,OAAO,EAAEZ,MAAM;QAC9B,IAAIA,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK/C,kBAAkB,IAAIU,MAAM,CAACa,OAAO,CAACC,eAAe,CAACuB,MAAM,CAAC,EAAE;UAC7FS,MAAM,CAACT,MAAM,CAAC;UACdU,mBAAmB,CAACI,GAAG,CAACd,MAAM,CAAC;UAC/BiB,eAAe,CAACjB,MAAM,CAAC;QACzB;MACF;IACF,CAAC;IACD;IACA,MAAMY,OAAO,GAAGb,IAAI,CAACQ,WAAW,CAAC;IACjC,IAAI,CAACK,OAAO,IAAIA,OAAO,CAACZ,MAAM,KAAK/C,kBAAkB,EAAE;MACrD;IACF;IACAgE,eAAe,CAACV,WAAW,CAAC;EAC9B;AACF,CAAC;AACD,OAAO,MAAMY,kBAAkB,GAAGA,CAACxD,MAAM,EAAEoC,IAAI,EAAEqB,aAAa,EAAEZ,qBAAqB,EAAE3B,iBAAiB,EAAEwC,SAAS,KAAK;EACtH,MAAMzC,mBAAmB,GAAGxB,+BAA+B,CAACO,MAAM,CAAC;EACnE,IAAI,CAACkB,iBAAiB,IAAI,CAAC2B,qBAAqB,EAAE;IAChD;EACF;EACA,IAAI3B,iBAAiB,EAAE;IACrB,MAAMyC,UAAU,GAAGxB,iBAAiB,CAACC,IAAI,EAAEqB,aAAa,CAAC;IACzDE,UAAU,CAAC/B,OAAO,CAACS,MAAM,IAAI;MAC3B,MAAMuB,UAAU,GAAG3C,mBAAmB,CAACE,GAAG,CAACkB,MAAM,CAAC;MAClD,IAAIuB,UAAU,EAAE;QACdF,SAAS,CAACrB,MAAM,CAAC;MACnB;IACF,CAAC,CAAC;EACJ;EACA,IAAIQ,qBAAqB,EAAE;IACzB,MAAMI,OAAO,GAAGb,IAAI,CAACqB,aAAa,CAAC;IACnC,IAAIR,OAAO,EAAE3C,IAAI,KAAK,OAAO,EAAE;MAC7B,MAAMR,WAAW,GAAGC,oCAAoC,CAACC,MAAM,EAAEyD,aAAa,CAAC;MAC/E3D,WAAW,CAAC8B,OAAO,CAACiC,UAAU,IAAI;QAChCH,SAAS,CAACG,UAAU,CAAC;MACvB,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}