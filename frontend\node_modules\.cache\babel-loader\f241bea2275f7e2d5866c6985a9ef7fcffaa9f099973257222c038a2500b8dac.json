{"ast": null, "code": "function dropRight(arr, itemsCount) {\n  itemsCount = Math.min(-itemsCount, 0);\n  if (itemsCount === 0) {\n    return arr.slice();\n  }\n  return arr.slice(0, itemsCount);\n}\nexport { dropRight };", "map": {"version": 3, "names": ["dropRight", "arr", "itemsCount", "Math", "min", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/dropRight.mjs"], "sourcesContent": ["function dropRight(arr, itemsCount) {\n    itemsCount = Math.min(-itemsCount, 0);\n    if (itemsCount === 0) {\n        return arr.slice();\n    }\n    return arr.slice(0, itemsCount);\n}\n\nexport { dropRight };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,GAAG,EAAEC,UAAU,EAAE;EAChCA,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACF,UAAU,EAAE,CAAC,CAAC;EACrC,IAAIA,UAAU,KAAK,CAAC,EAAE;IAClB,OAAOD,GAAG,CAACI,KAAK,CAAC,CAAC;EACtB;EACA,OAAOJ,GAAG,CAACI,KAAK,CAAC,CAAC,EAAEH,UAAU,CAAC;AACnC;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}