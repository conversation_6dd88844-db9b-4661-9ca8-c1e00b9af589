{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nexport const ToolbarContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") ToolbarContext.displayName = \"ToolbarContext\";\nexport function useToolbarContext() {\n  const context = React.useContext(ToolbarContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context. Toolbar subcomponents must be placed within a <Toolbar /> component.');\n  }\n  return context;\n}", "map": {"version": 3, "names": ["React", "ToolbarContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName", "useToolbarContext", "context", "useContext", "Error"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/toolbarV8/ToolbarContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nexport const ToolbarContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") ToolbarContext.displayName = \"ToolbarContext\";\nexport function useToolbarContext() {\n  const context = React.useContext(ToolbarContext);\n  if (context === undefined) {\n    throw new Error('MUI X: Missing context. Toolbar subcomponents must be placed within a <Toolbar /> component.');\n  }\n  return context;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,cAAc,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACzE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEL,cAAc,CAACM,WAAW,GAAG,gBAAgB;AACxF,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,MAAMC,OAAO,GAAGT,KAAK,CAACU,UAAU,CAACT,cAAc,CAAC;EAChD,IAAIQ,OAAO,KAAKN,SAAS,EAAE;IACzB,MAAM,IAAIQ,KAAK,CAAC,8FAA8F,CAAC;EACjH;EACA,OAAOF,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}