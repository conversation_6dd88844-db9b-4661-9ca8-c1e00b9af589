{"ast": null, "code": "const htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\nfunction escape(str) {\n  return str.replace(/[&<>\"']/g, match => htmlEscapes[match]);\n}\nexport { escape };", "map": {"version": 3, "names": ["htmlEscapes", "escape", "str", "replace", "match"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/string/escape.mjs"], "sourcesContent": ["const htmlEscapes = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;',\n};\nfunction escape(str) {\n    return str.replace(/[&<>\"']/g, match => htmlEscapes[match]);\n}\n\nexport { escape };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChB,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE;AACT,CAAC;AACD,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjB,OAAOA,GAAG,CAACC,OAAO,CAAC,UAAU,EAAEC,KAAK,IAAIJ,WAAW,CAACI,KAAK,CAAC,CAAC;AAC/D;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}