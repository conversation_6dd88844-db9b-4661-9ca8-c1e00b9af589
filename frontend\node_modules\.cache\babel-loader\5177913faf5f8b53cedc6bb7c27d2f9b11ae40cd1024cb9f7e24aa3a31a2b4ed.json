{"ast": null, "code": "function sumBy(items, getValue) {\n  let result = 0;\n  for (let i = 0; i < items.length; i++) {\n    result += getValue(items[i]);\n  }\n  return result;\n}\nexport { sumBy };", "map": {"version": 3, "names": ["sumBy", "items", "getValue", "result", "i", "length"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/math/sumBy.mjs"], "sourcesContent": ["function sumBy(items, getValue) {\n    let result = 0;\n    for (let i = 0; i < items.length; i++) {\n        result += getValue(items[i]);\n    }\n    return result;\n}\n\nexport { sumBy };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC5B,IAAIC,MAAM,GAAG,CAAC;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCD,MAAM,IAAID,QAAQ,CAACD,KAAK,CAACG,CAAC,CAAC,CAAC;EAChC;EACA,OAAOD,MAAM;AACjB;AAEA,SAASH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}