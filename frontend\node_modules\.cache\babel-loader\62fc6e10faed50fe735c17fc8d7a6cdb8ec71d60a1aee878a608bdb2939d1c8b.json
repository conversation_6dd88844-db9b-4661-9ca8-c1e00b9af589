{"ast": null, "code": "function chunk(arr, size) {\n  if (!Number.isInteger(size) || size <= 0) {\n    throw new Error('Size must be an integer greater than zero.');\n  }\n  const chunkLength = Math.ceil(arr.length / size);\n  const result = Array(chunkLength);\n  for (let index = 0; index < chunkLength; index++) {\n    const start = index * size;\n    const end = start + size;\n    result[index] = arr.slice(start, end);\n  }\n  return result;\n}\nexport { chunk };", "map": {"version": 3, "names": ["chunk", "arr", "size", "Number", "isInteger", "Error", "chunkLength", "Math", "ceil", "length", "result", "Array", "index", "start", "end", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/chunk.mjs"], "sourcesContent": ["function chunk(arr, size) {\n    if (!Number.isInteger(size) || size <= 0) {\n        throw new Error('Size must be an integer greater than zero.');\n    }\n    const chunkLength = Math.ceil(arr.length / size);\n    const result = Array(chunkLength);\n    for (let index = 0; index < chunkLength; index++) {\n        const start = index * size;\n        const end = start + size;\n        result[index] = arr.slice(start, end);\n    }\n    return result;\n}\n\nexport { chunk };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACtB,IAAI,CAACC,MAAM,CAACC,SAAS,CAACF,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,EAAE;IACtC,MAAM,IAAIG,KAAK,CAAC,4CAA4C,CAAC;EACjE;EACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,IAAI,CAACP,GAAG,CAACQ,MAAM,GAAGP,IAAI,CAAC;EAChD,MAAMQ,MAAM,GAAGC,KAAK,CAACL,WAAW,CAAC;EACjC,KAAK,IAAIM,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,WAAW,EAAEM,KAAK,EAAE,EAAE;IAC9C,MAAMC,KAAK,GAAGD,KAAK,GAAGV,IAAI;IAC1B,MAAMY,GAAG,GAAGD,KAAK,GAAGX,IAAI;IACxBQ,MAAM,CAACE,KAAK,CAAC,GAAGX,GAAG,CAACc,KAAK,CAACF,KAAK,EAAEC,GAAG,CAAC;EACzC;EACA,OAAOJ,MAAM;AACjB;AAEA,SAASV,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}