{"ast": null, "code": "function isMap(value) {\n  return value instanceof Map;\n}\nexport { isMap };", "map": {"version": 3, "names": ["isMap", "value", "Map"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isMap.mjs"], "sourcesContent": ["function isMap(value) {\n    return value instanceof Map;\n}\n\nexport { isMap };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,KAAK,EAAE;EAClB,OAAOA,KAAK,YAAYC,GAAG;AAC/B;AAEA,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}