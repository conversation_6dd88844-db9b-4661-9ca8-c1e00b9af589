{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nconst gridPivotingStateSelector = createRootSelector(\n// @ts-ignore\nstate => state.pivoting);\nexport const gridPivotActiveSelector = createSelector(gridPivotingStateSelector, pivoting => pivoting?.active);\nconst emptyColumns = new Map();\nexport const gridPivotInitialColumnsSelector = createSelector(gridPivotingStateSelector, pivoting => pivoting?.initialColumns || emptyColumns);", "map": {"version": 3, "names": ["createSelector", "createRootSelector", "gridPivotingStateSelector", "state", "pivoting", "gridPivotActiveSelector", "active", "emptyColumns", "Map", "gridPivotInitialColumnsSelector", "initialColumns"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/pivoting/gridPivotingSelectors.js"], "sourcesContent": ["import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nconst gridPivotingStateSelector = createRootSelector(\n// @ts-ignore\nstate => state.pivoting);\nexport const gridPivotActiveSelector = createSelector(gridPivotingStateSelector, pivoting => pivoting?.active);\nconst emptyColumns = new Map();\nexport const gridPivotInitialColumnsSelector = createSelector(gridPivotingStateSelector, pivoting => pivoting?.initialColumns || emptyColumns);"], "mappings": "AAAA,SAASA,cAAc,EAAEC,kBAAkB,QAAQ,kCAAkC;AACrF,MAAMC,yBAAyB,GAAGD,kBAAkB;AACpD;AACAE,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;AACxB,OAAO,MAAMC,uBAAuB,GAAGL,cAAc,CAACE,yBAAyB,EAAEE,QAAQ,IAAIA,QAAQ,EAAEE,MAAM,CAAC;AAC9G,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC9B,OAAO,MAAMC,+BAA+B,GAAGT,cAAc,CAACE,yBAAyB,EAAEE,QAAQ,IAAIA,QAAQ,EAAEM,cAAc,IAAIH,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}