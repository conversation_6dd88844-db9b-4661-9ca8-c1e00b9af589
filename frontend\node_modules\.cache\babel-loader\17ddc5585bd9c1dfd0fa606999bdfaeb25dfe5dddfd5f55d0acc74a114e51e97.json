{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"aria-label\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { isOverflown } from \"../../utils/domUtils.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['columnHeaderTitle']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridColumnHeaderTitleRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaderTitle'\n})({\n  textOverflow: 'ellipsis',\n  overflow: 'hidden',\n  whiteSpace: 'nowrap',\n  fontWeight: 'var(--unstable_DataGrid-headWeight)',\n  lineHeight: 'normal'\n});\nconst ColumnHeaderInnerTitle = forwardRef(function ColumnHeaderInnerTitle(props, ref) {\n  // Tooltip adds aria-label to the props, which is not needed since the children prop is a string\n  // See https://github.com/mui/mui-x/pull/14482\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridColumnHeaderTitleRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") ColumnHeaderInnerTitle.displayName = \"ColumnHeaderInnerTitle\";\n// No React.memo here as if we display the sort icon, we need to recalculate the isOver\nfunction GridColumnHeaderTitle(props) {\n  const {\n    label,\n    description\n  } = props;\n  const rootProps = useGridRootProps();\n  const titleRef = React.useRef(null);\n  const [tooltip, setTooltip] = React.useState('');\n  const handleMouseOver = React.useCallback(() => {\n    if (!description && titleRef?.current) {\n      const isOver = isOverflown(titleRef.current);\n      if (isOver) {\n        setTooltip(label);\n      } else {\n        setTooltip('');\n      }\n    }\n  }, [description, label]);\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: description || tooltip\n  }, rootProps.slotProps?.baseTooltip, {\n    children: /*#__PURE__*/_jsx(ColumnHeaderInnerTitle, {\n      onMouseOver: handleMouseOver,\n      ref: titleRef,\n      children: label\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderTitle.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnWidth: PropTypes.number.isRequired,\n  description: PropTypes.node,\n  label: PropTypes.string.isRequired\n} : void 0;\nexport { GridColumnHeaderTitle };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "forwardRef", "isOverflown", "getDataGridUtilityClass", "useGridRootProps", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "GridColumnHeaderTitleRoot", "name", "slot", "textOverflow", "overflow", "whiteSpace", "fontWeight", "lineHeight", "ColumnHeaderInnerTitle", "props", "ref", "className", "other", "rootProps", "process", "env", "NODE_ENV", "displayName", "GridColumnHeaderTitle", "label", "description", "titleRef", "useRef", "tooltip", "setTooltip", "useState", "handleMouseOver", "useCallback", "current", "isOver", "baseTooltip", "title", "slotProps", "children", "onMouseOver", "propTypes", "columnWidth", "number", "isRequired", "node", "string"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/columnHeaders/GridColumnHeaderTitle.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"aria-label\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '@mui/system';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { isOverflown } from \"../../utils/domUtils.js\";\nimport { getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['columnHeaderTitle']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridColumnHeaderTitleRoot = styled('div', {\n  name: 'MuiDataGrid',\n  slot: 'ColumnHeaderTitle'\n})({\n  textOverflow: 'ellipsis',\n  overflow: 'hidden',\n  whiteSpace: 'nowrap',\n  fontWeight: 'var(--unstable_DataGrid-headWeight)',\n  lineHeight: 'normal'\n});\nconst ColumnHeaderInnerTitle = forwardRef(function ColumnHeaderInnerTitle(props, ref) {\n  // Tooltip adds aria-label to the props, which is not needed since the children prop is a string\n  // See https://github.com/mui/mui-x/pull/14482\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  return /*#__PURE__*/_jsx(GridColumnHeaderTitleRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: rootProps\n  }, other, {\n    ref: ref\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") ColumnHeaderInnerTitle.displayName = \"ColumnHeaderInnerTitle\";\n// No React.memo here as if we display the sort icon, we need to recalculate the isOver\nfunction GridColumnHeaderTitle(props) {\n  const {\n    label,\n    description\n  } = props;\n  const rootProps = useGridRootProps();\n  const titleRef = React.useRef(null);\n  const [tooltip, setTooltip] = React.useState('');\n  const handleMouseOver = React.useCallback(() => {\n    if (!description && titleRef?.current) {\n      const isOver = isOverflown(titleRef.current);\n      if (isOver) {\n        setTooltip(label);\n      } else {\n        setTooltip('');\n      }\n    }\n  }, [description, label]);\n  return /*#__PURE__*/_jsx(rootProps.slots.baseTooltip, _extends({\n    title: description || tooltip\n  }, rootProps.slotProps?.baseTooltip, {\n    children: /*#__PURE__*/_jsx(ColumnHeaderInnerTitle, {\n      onMouseOver: handleMouseOver,\n      ref: titleRef,\n      children: label\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderTitle.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnWidth: PropTypes.number.isRequired,\n  description: PropTypes.node,\n  label: PropTypes.string.isRequired\n} : void 0;\nexport { GridColumnHeaderTitle };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;AAC7C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,uBAAuB,QAAQ,gCAAgC;AACxE,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,mBAAmB;EAC5B,CAAC;EACD,OAAOZ,cAAc,CAACW,KAAK,EAAEP,uBAAuB,EAAEM,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,yBAAyB,GAAGZ,MAAM,CAAC,KAAK,EAAE;EAC9Ca,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,YAAY,EAAE,UAAU;EACxBC,QAAQ,EAAE,QAAQ;EAClBC,UAAU,EAAE,QAAQ;EACpBC,UAAU,EAAE,qCAAqC;EACjDC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAGnB,UAAU,CAAC,SAASmB,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACpF;EACA;EACA,MAAM;MACFC;IACF,CAAC,GAAGF,KAAK;IACTG,KAAK,GAAG9B,6BAA6B,CAAC2B,KAAK,EAAE1B,SAAS,CAAC;EACzD,MAAM8B,SAAS,GAAGrB,gBAAgB,CAAC,CAAC;EACpC,MAAMK,OAAO,GAAGF,iBAAiB,CAACkB,SAAS,CAAC;EAC5C,OAAO,aAAanB,IAAI,CAACM,yBAAyB,EAAEnB,QAAQ,CAAC;IAC3D8B,SAAS,EAAEzB,IAAI,CAACW,OAAO,CAACE,IAAI,EAAEY,SAAS,CAAC;IACxCf,UAAU,EAAEiB;EACd,CAAC,EAAED,KAAK,EAAE;IACRF,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAER,sBAAsB,CAACS,WAAW,GAAG,wBAAwB;AACxG;AACA,SAASC,qBAAqBA,CAACT,KAAK,EAAE;EACpC,MAAM;IACJU,KAAK;IACLC;EACF,CAAC,GAAGX,KAAK;EACT,MAAMI,SAAS,GAAGrB,gBAAgB,CAAC,CAAC;EACpC,MAAM6B,QAAQ,GAAGrC,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,KAAK,CAACyC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMC,eAAe,GAAG1C,KAAK,CAAC2C,WAAW,CAAC,MAAM;IAC9C,IAAI,CAACP,WAAW,IAAIC,QAAQ,EAAEO,OAAO,EAAE;MACrC,MAAMC,MAAM,GAAGvC,WAAW,CAAC+B,QAAQ,CAACO,OAAO,CAAC;MAC5C,IAAIC,MAAM,EAAE;QACVL,UAAU,CAACL,KAAK,CAAC;MACnB,CAAC,MAAM;QACLK,UAAU,CAAC,EAAE,CAAC;MAChB;IACF;EACF,CAAC,EAAE,CAACJ,WAAW,EAAED,KAAK,CAAC,CAAC;EACxB,OAAO,aAAazB,IAAI,CAACmB,SAAS,CAACf,KAAK,CAACgC,WAAW,EAAEjD,QAAQ,CAAC;IAC7DkD,KAAK,EAAEX,WAAW,IAAIG;EACxB,CAAC,EAAEV,SAAS,CAACmB,SAAS,EAAEF,WAAW,EAAE;IACnCG,QAAQ,EAAE,aAAavC,IAAI,CAACc,sBAAsB,EAAE;MAClD0B,WAAW,EAAER,eAAe;MAC5BhB,GAAG,EAAEW,QAAQ;MACbY,QAAQ,EAAEd;IACZ,CAAC;EACH,CAAC,CAAC,CAAC;AACL;AACAL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGE,qBAAqB,CAACiB,SAAS,GAAG;EACxE;EACA;EACA;EACA;EACAC,WAAW,EAAEnD,SAAS,CAACoD,MAAM,CAACC,UAAU;EACxClB,WAAW,EAAEnC,SAAS,CAACsD,IAAI;EAC3BpB,KAAK,EAAElC,SAAS,CAACuD,MAAM,CAACF;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASpB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}