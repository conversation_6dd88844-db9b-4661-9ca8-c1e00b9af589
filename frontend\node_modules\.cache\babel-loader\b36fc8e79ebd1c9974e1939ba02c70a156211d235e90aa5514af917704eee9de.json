{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { gridDimensionsSelector } from \"../dimensions/index.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nexport const listViewStateInitializer = (state, props, apiRef) => _extends({}, state, {\n  listViewColumn: props.listViewColumn ? _extends({}, props.listViewColumn, {\n    computedWidth: getListColumnWidth(apiRef)\n  }) : undefined\n});\nexport function useGridListView(apiRef, props) {\n  /*\n   * EVENTS\n   */\n  const updateListColumnWidth = () => {\n    apiRef.current.setState(state => {\n      if (!state.listViewColumn) {\n        return state;\n      }\n      return _extends({}, state, {\n        listViewColumn: _extends({}, state.listViewColumn, {\n          computedWidth: getListColumnWidth(apiRef)\n        })\n      });\n    });\n  };\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      updateListColumnWidth();\n    }\n  };\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n  useGridEvent(apiRef, 'columnVisibilityModelChange', updateListColumnWidth);\n\n  /*\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    const listColumn = props.listViewColumn;\n    if (listColumn) {\n      apiRef.current.setState(state => {\n        return _extends({}, state, {\n          listViewColumn: _extends({}, listColumn, {\n            computedWidth: getListColumnWidth(apiRef)\n          })\n        });\n      });\n    }\n  }, [apiRef, props.listViewColumn]);\n  React.useEffect(() => {\n    if (props.listView && !props.listViewColumn) {\n      warnOnce(['MUI X: The `listViewColumn` prop must be set if `listView` is enabled.', 'To fix, pass a column definition to the `listViewColumn` prop, e.g. `{ field: \"example\", renderCell: (params) => <div>{params.row.id}</div> }`.', 'For more details, see https://mui.com/x/react-data-grid/list-view/']);\n    }\n  }, [props.listView, props.listViewColumn]);\n}\nfunction getListColumnWidth(apiRef) {\n  return gridDimensionsSelector(apiRef).viewportInnerSize.width;\n}", "map": {"version": 3, "names": ["_extends", "React", "useEnhancedEffect", "warnOnce", "gridDimensionsSelector", "useGridEvent", "listViewStateInitializer", "state", "props", "apiRef", "listViewColumn", "computedWidth", "getListColumnWidth", "undefined", "useGridListView", "updateListColumnWidth", "current", "setState", "prevInnerWidth", "useRef", "handleGridSizeChange", "viewportInnerSize", "width", "listColumn", "useEffect", "listView"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/listView/useGridListView.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { gridDimensionsSelector } from \"../dimensions/index.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nexport const listViewStateInitializer = (state, props, apiRef) => _extends({}, state, {\n  listViewColumn: props.listViewColumn ? _extends({}, props.listViewColumn, {\n    computedWidth: getListColumnWidth(apiRef)\n  }) : undefined\n});\nexport function useGridListView(apiRef, props) {\n  /*\n   * EVENTS\n   */\n  const updateListColumnWidth = () => {\n    apiRef.current.setState(state => {\n      if (!state.listViewColumn) {\n        return state;\n      }\n      return _extends({}, state, {\n        listViewColumn: _extends({}, state.listViewColumn, {\n          computedWidth: getListColumnWidth(apiRef)\n        })\n      });\n    });\n  };\n  const prevInnerWidth = React.useRef(null);\n  const handleGridSizeChange = viewportInnerSize => {\n    if (prevInnerWidth.current !== viewportInnerSize.width) {\n      prevInnerWidth.current = viewportInnerSize.width;\n      updateListColumnWidth();\n    }\n  };\n  useGridEvent(apiRef, 'viewportInnerSizeChange', handleGridSizeChange);\n  useGridEvent(apiRef, 'columnVisibilityModelChange', updateListColumnWidth);\n\n  /*\n   * EFFECTS\n   */\n  useEnhancedEffect(() => {\n    const listColumn = props.listViewColumn;\n    if (listColumn) {\n      apiRef.current.setState(state => {\n        return _extends({}, state, {\n          listViewColumn: _extends({}, listColumn, {\n            computedWidth: getListColumnWidth(apiRef)\n          })\n        });\n      });\n    }\n  }, [apiRef, props.listViewColumn]);\n  React.useEffect(() => {\n    if (props.listView && !props.listViewColumn) {\n      warnOnce(['MUI X: The `listViewColumn` prop must be set if `listView` is enabled.', 'To fix, pass a column definition to the `listViewColumn` prop, e.g. `{ field: \"example\", renderCell: (params) => <div>{params.row.id}</div> }`.', 'For more details, see https://mui.com/x/react-data-grid/list-view/']);\n    }\n  }, [props.listView, props.listViewColumn]);\n}\nfunction getListColumnWidth(apiRef) {\n  return gridDimensionsSelector(apiRef).viewportInnerSize.width;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,sBAAsB,QAAQ,wBAAwB;AAC/D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,OAAO,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAKT,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;EACpFG,cAAc,EAAEF,KAAK,CAACE,cAAc,GAAGV,QAAQ,CAAC,CAAC,CAAC,EAAEQ,KAAK,CAACE,cAAc,EAAE;IACxEC,aAAa,EAAEC,kBAAkB,CAACH,MAAM;EAC1C,CAAC,CAAC,GAAGI;AACP,CAAC,CAAC;AACF,OAAO,SAASC,eAAeA,CAACL,MAAM,EAAED,KAAK,EAAE;EAC7C;AACF;AACA;EACE,MAAMO,qBAAqB,GAAGA,CAAA,KAAM;IAClCN,MAAM,CAACO,OAAO,CAACC,QAAQ,CAACV,KAAK,IAAI;MAC/B,IAAI,CAACA,KAAK,CAACG,cAAc,EAAE;QACzB,OAAOH,KAAK;MACd;MACA,OAAOP,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;QACzBG,cAAc,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,CAACG,cAAc,EAAE;UACjDC,aAAa,EAAEC,kBAAkB,CAACH,MAAM;QAC1C,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAMS,cAAc,GAAGjB,KAAK,CAACkB,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMC,oBAAoB,GAAGC,iBAAiB,IAAI;IAChD,IAAIH,cAAc,CAACF,OAAO,KAAKK,iBAAiB,CAACC,KAAK,EAAE;MACtDJ,cAAc,CAACF,OAAO,GAAGK,iBAAiB,CAACC,KAAK;MAChDP,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC;EACDV,YAAY,CAACI,MAAM,EAAE,yBAAyB,EAAEW,oBAAoB,CAAC;EACrEf,YAAY,CAACI,MAAM,EAAE,6BAA6B,EAAEM,qBAAqB,CAAC;;EAE1E;AACF;AACA;EACEb,iBAAiB,CAAC,MAAM;IACtB,MAAMqB,UAAU,GAAGf,KAAK,CAACE,cAAc;IACvC,IAAIa,UAAU,EAAE;MACdd,MAAM,CAACO,OAAO,CAACC,QAAQ,CAACV,KAAK,IAAI;QAC/B,OAAOP,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;UACzBG,cAAc,EAAEV,QAAQ,CAAC,CAAC,CAAC,EAAEuB,UAAU,EAAE;YACvCZ,aAAa,EAAEC,kBAAkB,CAACH,MAAM;UAC1C,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,MAAM,EAAED,KAAK,CAACE,cAAc,CAAC,CAAC;EAClCT,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpB,IAAIhB,KAAK,CAACiB,QAAQ,IAAI,CAACjB,KAAK,CAACE,cAAc,EAAE;MAC3CP,QAAQ,CAAC,CAAC,wEAAwE,EAAE,iJAAiJ,EAAE,oEAAoE,CAAC,CAAC;IAC/S;EACF,CAAC,EAAE,CAACK,KAAK,CAACiB,QAAQ,EAAEjB,KAAK,CAACE,cAAc,CAAC,CAAC;AAC5C;AACA,SAASE,kBAAkBA,CAACH,MAAM,EAAE;EAClC,OAAOL,sBAAsB,CAACK,MAAM,CAAC,CAACY,iBAAiB,CAACC,KAAK;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}