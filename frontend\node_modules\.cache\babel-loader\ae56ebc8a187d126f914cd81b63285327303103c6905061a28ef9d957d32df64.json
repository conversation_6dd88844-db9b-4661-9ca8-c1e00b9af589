{"ast": null, "code": "function zipObject(keys, values) {\n  const result = {};\n  for (let i = 0; i < keys.length; i++) {\n    result[keys[i]] = values[i];\n  }\n  return result;\n}\nexport { zipObject };", "map": {"version": 3, "names": ["zipObject", "keys", "values", "result", "i", "length"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/zipObject.mjs"], "sourcesContent": ["function zipObject(keys, values) {\n    const result = {};\n    for (let i = 0; i < keys.length; i++) {\n        result[keys[i]] = values[i];\n    }\n    return result;\n}\n\nexport { zipObject };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC7B,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IAClCD,MAAM,CAACF,IAAI,CAACG,CAAC,CAAC,CAAC,GAAGF,MAAM,CAACE,CAAC,CAAC;EAC/B;EACA,OAAOD,MAAM;AACjB;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}