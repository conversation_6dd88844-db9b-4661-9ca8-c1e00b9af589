{"ast": null, "code": "import { useSyncExternalStore } from 'use-sync-external-store/shim';\nconst emptySubscribe = () => () => {};\nconst clientSnapshot = () => false;\nconst serverSnapshot = () => true;\nexport const useIsSSR = () => useSyncExternalStore(emptySubscribe, clientSnapshot, serverSnapshot);", "map": {"version": 3, "names": ["useSyncExternalStore", "emptySubscribe", "clientSnapshot", "serverSnapshot", "useIsSSR"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useIsSSR.js"], "sourcesContent": ["import { useSyncExternalStore } from 'use-sync-external-store/shim';\nconst emptySubscribe = () => () => {};\nconst clientSnapshot = () => false;\nconst serverSnapshot = () => true;\nexport const useIsSSR = () => useSyncExternalStore(emptySubscribe, clientSnapshot, serverSnapshot);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,8BAA8B;AACnE,MAAMC,cAAc,GAAGA,CAAA,KAAM,MAAM,CAAC,CAAC;AACrC,MAAMC,cAAc,GAAGA,CAAA,KAAM,KAAK;AAClC,MAAMC,cAAc,GAAGA,CAAA,KAAM,IAAI;AACjC,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAMJ,oBAAoB,CAACC,cAAc,EAAEC,cAAc,EAAEC,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}