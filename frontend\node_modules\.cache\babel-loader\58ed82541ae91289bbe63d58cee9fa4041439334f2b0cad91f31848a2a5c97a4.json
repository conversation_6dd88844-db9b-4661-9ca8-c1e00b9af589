{"ast": null, "code": "import { createSelector as baseCreateSelector, createSelectorMemoized as baseCreateSelectorMemoized } from '@mui/x-internals/store';\nexport const createSelector = (...args) => {\n  const baseSelector = baseCreateSelector(...args);\n  const selector = (apiRef, a1, a2, a3) => baseSelector(unwrapIfNeeded(apiRef), a1, a2, a3);\n  return selector;\n};\nexport const createSelectorMemoized = (...args) => {\n  const baseSelector = baseCreateSelectorMemoized(...args);\n  const selector = (apiRef, a1, a2, a3) => baseSelector(unwrapIfNeeded(apiRef), a1, a2, a3);\n  return selector;\n};\n\n/**\n * Used to create the root selector for a feature. It assumes that the state is already initialized\n * and strips from the types the possibility of `apiRef` being `null`.\n * Users are warned about this in our documentation https://mui.com/x/react-data-grid/state/#direct-selector-access\n */\nexport const createRootSelector = fn => (apiRef, args) => fn(unwrapIfNeeded(apiRef), args);\nfunction unwrapIfNeeded(refOrState) {\n  if ('current' in refOrState) {\n    return refOrState.current.state;\n  }\n  return refOrState;\n}", "map": {"version": 3, "names": ["createSelector", "baseCreateSelector", "createSelectorMemoized", "baseCreateSelectorMemoized", "args", "baseSelector", "selector", "apiRef", "a1", "a2", "a3", "unwrapIfNeeded", "createRootSelector", "fn", "refOrState", "current", "state"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/createSelector.js"], "sourcesContent": ["import { createSelector as baseCreateSelector, createSelectorMemoized as baseCreateSelectorMemoized } from '@mui/x-internals/store';\nexport const createSelector = (...args) => {\n  const baseSelector = baseCreateSelector(...args);\n  const selector = (apiRef, a1, a2, a3) => baseSelector(unwrapIfNeeded(apiRef), a1, a2, a3);\n  return selector;\n};\nexport const createSelectorMemoized = (...args) => {\n  const baseSelector = baseCreateSelectorMemoized(...args);\n  const selector = (apiRef, a1, a2, a3) => baseSelector(unwrapIfNeeded(apiRef), a1, a2, a3);\n  return selector;\n};\n\n/**\n * Used to create the root selector for a feature. It assumes that the state is already initialized\n * and strips from the types the possibility of `apiRef` being `null`.\n * Users are warned about this in our documentation https://mui.com/x/react-data-grid/state/#direct-selector-access\n */\nexport const createRootSelector = fn => (apiRef, args) => fn(unwrapIfNeeded(apiRef), args);\nfunction unwrapIfNeeded(refOrState) {\n  if ('current' in refOrState) {\n    return refOrState.current.state;\n  }\n  return refOrState;\n}"], "mappings": "AAAA,SAASA,cAAc,IAAIC,kBAAkB,EAAEC,sBAAsB,IAAIC,0BAA0B,QAAQ,wBAAwB;AACnI,OAAO,MAAMH,cAAc,GAAGA,CAAC,GAAGI,IAAI,KAAK;EACzC,MAAMC,YAAY,GAAGJ,kBAAkB,CAAC,GAAGG,IAAI,CAAC;EAChD,MAAME,QAAQ,GAAGA,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAKL,YAAY,CAACM,cAAc,CAACJ,MAAM,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACzF,OAAOJ,QAAQ;AACjB,CAAC;AACD,OAAO,MAAMJ,sBAAsB,GAAGA,CAAC,GAAGE,IAAI,KAAK;EACjD,MAAMC,YAAY,GAAGF,0BAA0B,CAAC,GAAGC,IAAI,CAAC;EACxD,MAAME,QAAQ,GAAGA,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAKL,YAAY,CAACM,cAAc,CAACJ,MAAM,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACzF,OAAOJ,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,kBAAkB,GAAGC,EAAE,IAAI,CAACN,MAAM,EAAEH,IAAI,KAAKS,EAAE,CAACF,cAAc,CAACJ,MAAM,CAAC,EAAEH,IAAI,CAAC;AAC1F,SAASO,cAAcA,CAACG,UAAU,EAAE;EAClC,IAAI,SAAS,IAAIA,UAAU,EAAE;IAC3B,OAAOA,UAAU,CAACC,OAAO,CAACC,KAAK;EACjC;EACA,OAAOF,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}