{"ast": null, "code": "import { GridVirtualScroller } from \"../virtualization/GridVirtualScroller.js\";\nexport { GridVirtualScroller as GridBody };", "map": {"version": 3, "names": ["GridVirtualScroller", "GridBody"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/base/GridBody.js"], "sourcesContent": ["import { GridVirtualScroller } from \"../virtualization/GridVirtualScroller.js\";\nexport { GridVirtualScroller as GridBody };"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0CAA0C;AAC9E,SAASA,mBAAmB,IAAIC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}