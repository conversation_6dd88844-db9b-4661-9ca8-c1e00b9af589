{"ast": null, "code": "import { isUnsafeProperty } from '../_internal/isUnsafeProperty.mjs';\nimport { isObjectLike } from '../compat/predicate/isObjectLike.mjs';\nfunction mergeWith(target, source, merge) {\n  const sourceKeys = Object.keys(source);\n  for (let i = 0; i < sourceKeys.length; i++) {\n    const key = sourceKeys[i];\n    if (isUnsafeProperty(key)) {\n      continue;\n    }\n    const sourceValue = source[key];\n    const targetValue = target[key];\n    const merged = merge(targetValue, sourceValue, key, target, source);\n    if (merged !== undefined) {\n      target[key] = merged;\n    } else if (Array.isArray(sourceValue)) {\n      target[key] = mergeWith(targetValue ?? [], sourceValue, merge);\n    } else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {\n      target[key] = mergeWith(targetValue ?? {}, sourceValue, merge);\n    } else if (targetValue === undefined || sourceValue !== undefined) {\n      target[key] = sourceValue;\n    }\n  }\n  return target;\n}\nexport { mergeWith };", "map": {"version": 3, "names": ["isUnsafeProperty", "isObjectLike", "mergeWith", "target", "source", "merge", "sourceKeys", "Object", "keys", "i", "length", "key", "sourceValue", "targetValue", "merged", "undefined", "Array", "isArray"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/object/mergeWith.mjs"], "sourcesContent": ["import { isUnsafeProperty } from '../_internal/isUnsafeProperty.mjs';\nimport { isObjectLike } from '../compat/predicate/isObjectLike.mjs';\n\nfunction mergeWith(target, source, merge) {\n    const sourceKeys = Object.keys(source);\n    for (let i = 0; i < sourceKeys.length; i++) {\n        const key = sourceKeys[i];\n        if (isUnsafeProperty(key)) {\n            continue;\n        }\n        const sourceValue = source[key];\n        const targetValue = target[key];\n        const merged = merge(targetValue, sourceValue, key, target, source);\n        if (merged !== undefined) {\n            target[key] = merged;\n        }\n        else if (Array.isArray(sourceValue)) {\n            target[key] = mergeWith(targetValue ?? [], sourceValue, merge);\n        }\n        else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {\n            target[key] = mergeWith(targetValue ?? {}, sourceValue, merge);\n        }\n        else if (targetValue === undefined || sourceValue !== undefined) {\n            target[key] = sourceValue;\n        }\n    }\n    return target;\n}\n\nexport { mergeWith };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,YAAY,QAAQ,sCAAsC;AAEnE,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACtC,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC;EACtC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,MAAME,GAAG,GAAGL,UAAU,CAACG,CAAC,CAAC;IACzB,IAAIT,gBAAgB,CAACW,GAAG,CAAC,EAAE;MACvB;IACJ;IACA,MAAMC,WAAW,GAAGR,MAAM,CAACO,GAAG,CAAC;IAC/B,MAAME,WAAW,GAAGV,MAAM,CAACQ,GAAG,CAAC;IAC/B,MAAMG,MAAM,GAAGT,KAAK,CAACQ,WAAW,EAAED,WAAW,EAAED,GAAG,EAAER,MAAM,EAAEC,MAAM,CAAC;IACnE,IAAIU,MAAM,KAAKC,SAAS,EAAE;MACtBZ,MAAM,CAACQ,GAAG,CAAC,GAAGG,MAAM;IACxB,CAAC,MACI,IAAIE,KAAK,CAACC,OAAO,CAACL,WAAW,CAAC,EAAE;MACjCT,MAAM,CAACQ,GAAG,CAAC,GAAGT,SAAS,CAACW,WAAW,IAAI,EAAE,EAAED,WAAW,EAAEP,KAAK,CAAC;IAClE,CAAC,MACI,IAAIJ,YAAY,CAACY,WAAW,CAAC,IAAIZ,YAAY,CAACW,WAAW,CAAC,EAAE;MAC7DT,MAAM,CAACQ,GAAG,CAAC,GAAGT,SAAS,CAACW,WAAW,IAAI,CAAC,CAAC,EAAED,WAAW,EAAEP,KAAK,CAAC;IAClE,CAAC,MACI,IAAIQ,WAAW,KAAKE,SAAS,IAAIH,WAAW,KAAKG,SAAS,EAAE;MAC7DZ,MAAM,CAACQ,GAAG,CAAC,GAAGC,WAAW;IAC7B;EACJ;EACA,OAAOT,MAAM;AACjB;AAEA,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}