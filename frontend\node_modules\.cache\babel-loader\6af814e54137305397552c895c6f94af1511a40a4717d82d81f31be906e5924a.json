{"ast": null, "code": "export { EMPTY_RENDER_CONTEXT } from '@mui/x-virtualizer';\nexport * from \"./useGridVirtualization.js\";\nexport * from \"./gridVirtualizationSelectors.js\";", "map": {"version": 3, "names": ["EMPTY_RENDER_CONTEXT"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/virtualization/index.js"], "sourcesContent": ["export { EMPTY_RENDER_CONTEXT } from '@mui/x-virtualizer';\nexport * from \"./useGridVirtualization.js\";\nexport * from \"./gridVirtualizationSelectors.js\";"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,oBAAoB;AACzD,cAAc,4BAA4B;AAC1C,cAAc,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}