{"ast": null, "code": "import { words } from './words.mjs';\nfunction snakeCase(str) {\n  const words$1 = words(str);\n  return words$1.map(word => word.toLowerCase()).join('_');\n}\nexport { snakeCase };", "map": {"version": 3, "names": ["words", "snakeCase", "str", "words$1", "map", "word", "toLowerCase", "join"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/string/snakeCase.mjs"], "sourcesContent": ["import { words } from './words.mjs';\n\nfunction snakeCase(str) {\n    const words$1 = words(str);\n    return words$1.map(word => word.toLowerCase()).join('_');\n}\n\nexport { snakeCase };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AAEnC,SAASC,SAASA,CAACC,GAAG,EAAE;EACpB,MAAMC,OAAO,GAAGH,KAAK,CAACE,GAAG,CAAC;EAC1B,OAAOC,OAAO,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAC5D;AAEA,SAASN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}