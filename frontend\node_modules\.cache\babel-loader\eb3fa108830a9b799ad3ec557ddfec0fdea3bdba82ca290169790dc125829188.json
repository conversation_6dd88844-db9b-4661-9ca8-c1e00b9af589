{"ast": null, "code": "function remove(arr, shouldRemoveElement) {\n  const originalArr = arr.slice();\n  const removed = [];\n  let resultIndex = 0;\n  for (let i = 0; i < arr.length; i++) {\n    if (shouldRemoveElement(arr[i], i, originalArr)) {\n      removed.push(arr[i]);\n      continue;\n    }\n    if (!Object.hasOwn(arr, i)) {\n      delete arr[resultIndex++];\n      continue;\n    }\n    arr[resultIndex++] = arr[i];\n  }\n  arr.length = resultIndex;\n  return removed;\n}\nexport { remove };", "map": {"version": 3, "names": ["remove", "arr", "shouldRemoveElement", "originalArr", "slice", "removed", "resultIndex", "i", "length", "push", "Object", "hasOwn"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/remove.mjs"], "sourcesContent": ["function remove(arr, shouldRemoveElement) {\n    const originalArr = arr.slice();\n    const removed = [];\n    let resultIndex = 0;\n    for (let i = 0; i < arr.length; i++) {\n        if (shouldRemoveElement(arr[i], i, originalArr)) {\n            removed.push(arr[i]);\n            continue;\n        }\n        if (!Object.hasOwn(arr, i)) {\n            delete arr[resultIndex++];\n            continue;\n        }\n        arr[resultIndex++] = arr[i];\n    }\n    arr.length = resultIndex;\n    return removed;\n}\n\nexport { remove };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,GAAG,EAAEC,mBAAmB,EAAE;EACtC,MAAMC,WAAW,GAAGF,GAAG,CAACG,KAAK,CAAC,CAAC;EAC/B,MAAMC,OAAO,GAAG,EAAE;EAClB,IAAIC,WAAW,GAAG,CAAC;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,GAAG,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,IAAIL,mBAAmB,CAACD,GAAG,CAACM,CAAC,CAAC,EAAEA,CAAC,EAAEJ,WAAW,CAAC,EAAE;MAC7CE,OAAO,CAACI,IAAI,CAACR,GAAG,CAACM,CAAC,CAAC,CAAC;MACpB;IACJ;IACA,IAAI,CAACG,MAAM,CAACC,MAAM,CAACV,GAAG,EAAEM,CAAC,CAAC,EAAE;MACxB,OAAON,GAAG,CAACK,WAAW,EAAE,CAAC;MACzB;IACJ;IACAL,GAAG,CAACK,WAAW,EAAE,CAAC,GAAGL,GAAG,CAACM,CAAC,CAAC;EAC/B;EACAN,GAAG,CAACO,MAAM,GAAGF,WAAW;EACxB,OAAOD,OAAO;AAClB;AAEA,SAASL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}