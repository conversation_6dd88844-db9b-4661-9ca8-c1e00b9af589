{"ast": null, "code": "import * as React from 'react';\nimport clsx from 'clsx';\nimport { gridClasses } from \"../constants/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst classes = {\n  root: gridClasses.scrollbarFiller,\n  header: gridClasses['scrollbarFiller--header'],\n  borderTop: gridClasses['scrollbarFiller--borderTop'],\n  borderBottom: gridClasses['scrollbarFiller--borderBottom'],\n  pinnedRight: gridClasses['scrollbarFiller--pinnedRight']\n};\nfunction GridScrollbarFillerCell({\n  header,\n  borderTop = true,\n  borderBottom,\n  pinnedRight\n}) {\n  return /*#__PURE__*/_jsx(\"div\", {\n    role: \"presentation\",\n    className: clsx(classes.root, header && classes.header, borderTop && classes.borderTop, borderBottom && classes.borderBottom, pinnedRight && classes.pinnedRight)\n  });\n}\nexport { GridScrollbarFillerCell };", "map": {"version": 3, "names": ["React", "clsx", "gridClasses", "jsx", "_jsx", "classes", "root", "scrollbarFiller", "header", "borderTop", "borderBottom", "pinnedRight", "GridScrollbarFillerCell", "role", "className"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/GridScrollbarFillerCell.js"], "sourcesContent": ["import * as React from 'react';\nimport clsx from 'clsx';\nimport { gridClasses } from \"../constants/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst classes = {\n  root: gridClasses.scrollbarFiller,\n  header: gridClasses['scrollbarFiller--header'],\n  borderTop: gridClasses['scrollbarFiller--borderTop'],\n  borderBottom: gridClasses['scrollbarFiller--borderBottom'],\n  pinnedRight: gridClasses['scrollbarFiller--pinnedRight']\n};\nfunction GridScrollbarFillerCell({\n  header,\n  borderTop = true,\n  borderBottom,\n  pinnedRight\n}) {\n  return /*#__PURE__*/_jsx(\"div\", {\n    role: \"presentation\",\n    className: clsx(classes.root, header && classes.header, borderTop && classes.borderTop, borderBottom && classes.borderBottom, pinnedRight && classes.pinnedRight)\n  });\n}\nexport { GridScrollbarFillerCell };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG;EACdC,IAAI,EAAEJ,WAAW,CAACK,eAAe;EACjCC,MAAM,EAAEN,WAAW,CAAC,yBAAyB,CAAC;EAC9CO,SAAS,EAAEP,WAAW,CAAC,4BAA4B,CAAC;EACpDQ,YAAY,EAAER,WAAW,CAAC,+BAA+B,CAAC;EAC1DS,WAAW,EAAET,WAAW,CAAC,8BAA8B;AACzD,CAAC;AACD,SAASU,uBAAuBA,CAAC;EAC/BJ,MAAM;EACNC,SAAS,GAAG,IAAI;EAChBC,YAAY;EACZC;AACF,CAAC,EAAE;EACD,OAAO,aAAaP,IAAI,CAAC,KAAK,EAAE;IAC9BS,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEb,IAAI,CAACI,OAAO,CAACC,IAAI,EAAEE,MAAM,IAAIH,OAAO,CAACG,MAAM,EAAEC,SAAS,IAAIJ,OAAO,CAACI,SAAS,EAAEC,YAAY,IAAIL,OAAO,CAACK,YAAY,EAAEC,WAAW,IAAIN,OAAO,CAACM,WAAW;EAClK,CAAC,CAAC;AACJ;AACA,SAASC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}