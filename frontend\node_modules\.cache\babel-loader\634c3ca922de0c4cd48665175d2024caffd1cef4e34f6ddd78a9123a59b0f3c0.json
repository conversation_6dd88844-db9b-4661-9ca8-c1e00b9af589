{"ast": null, "code": "function round(value, precision = 0) {\n  if (!Number.isInteger(precision)) {\n    throw new Error('Precision must be an integer.');\n  }\n  const multiplier = Math.pow(10, precision);\n  return Math.round(value * multiplier) / multiplier;\n}\nexport { round };", "map": {"version": 3, "names": ["round", "value", "precision", "Number", "isInteger", "Error", "multiplier", "Math", "pow"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/math/round.mjs"], "sourcesContent": ["function round(value, precision = 0) {\n    if (!Number.isInteger(precision)) {\n        throw new Error('Precision must be an integer.');\n    }\n    const multiplier = Math.pow(10, precision);\n    return Math.round(value * multiplier) / multiplier;\n}\n\nexport { round };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,KAAK,EAAEC,SAAS,GAAG,CAAC,EAAE;EACjC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACF,SAAS,CAAC,EAAE;IAC9B,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;EACpD;EACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEN,SAAS,CAAC;EAC1C,OAAOK,IAAI,CAACP,KAAK,CAACC,KAAK,GAAGK,UAAU,CAAC,GAAGA,UAAU;AACtD;AAEA,SAASN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}