{"ast": null, "code": "function tail(arr) {\n  return arr.slice(1);\n}\nexport { tail };", "map": {"version": 3, "names": ["tail", "arr", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/tail.mjs"], "sourcesContent": ["function tail(arr) {\n    return arr.slice(1);\n}\n\nexport { tail };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,GAAG,EAAE;EACf,OAAOA,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC;AACvB;AAEA,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}