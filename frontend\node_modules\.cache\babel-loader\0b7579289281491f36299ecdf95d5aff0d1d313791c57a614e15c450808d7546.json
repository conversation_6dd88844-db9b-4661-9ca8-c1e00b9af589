{"ast": null, "code": "import { isBlob } from './isBlob.mjs';\nfunction isFile(x) {\n  if (typeof File === 'undefined') {\n    return false;\n  }\n  return isBlob(x) && x instanceof File;\n}\nexport { isFile };", "map": {"version": 3, "names": ["isBlob", "isFile", "x", "File"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isFile.mjs"], "sourcesContent": ["import { isBlob } from './isBlob.mjs';\n\nfunction isFile(x) {\n    if (typeof File === 'undefined') {\n        return false;\n    }\n    return isBlob(x) && x instanceof File;\n}\n\nexport { isFile };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,SAASC,MAAMA,CAACC,CAAC,EAAE;EACf,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAO,KAAK;EAChB;EACA,OAAOH,MAAM,CAACE,CAAC,CAAC,IAAIA,CAAC,YAAYC,IAAI;AACzC;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}