{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"skipCache\"];\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport debounce from '@mui/utils/debounce';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { GRID_ROOT_GROUP_ID } from \"../rows/gridRowsUtils.js\";\nimport { runIf } from \"../../../utils/utils.js\";\nimport { GridStrategyGroup } from \"../../core/strategyProcessing/index.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridPaginationModelSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridGetRowsParamsSelector } from \"./gridDataSourceSelector.js\";\nimport { CacheChunkManager, DataSourceRowsUpdateStrategy } from \"./utils.js\";\nimport { GridDataSourceCacheDefault } from \"./cache.js\";\nimport { GridGetRowsError, GridUpdateRowError } from \"./gridDataSourceError.js\";\nconst noopCache = {\n  clear: () => {},\n  get: () => undefined,\n  set: () => {}\n};\nfunction getCache(cacheProp, options = {}) {\n  if (cacheProp === null) {\n    return noopCache;\n  }\n  return cacheProp ?? new GridDataSourceCacheDefault(options);\n}\nexport const useGridDataSourceBase = (apiRef, props, options = {}) => {\n  const setStrategyAvailability = React.useCallback(() => {\n    apiRef.current.setStrategyAvailability(GridStrategyGroup.DataSource, DataSourceRowsUpdateStrategy.Default, props.dataSource ? () => true : () => false);\n  }, [apiRef, props.dataSource]);\n  const [defaultRowsUpdateStrategyActive, setDefaultRowsUpdateStrategyActive] = React.useState(false);\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const lastRequestId = React.useRef(0);\n  const onDataSourceErrorProp = props.onDataSourceError;\n  const cacheChunkManager = useLazyRef(() => {\n    if (!props.pagination) {\n      return new CacheChunkManager(paginationModel.pageSize);\n    }\n    const sortedPageSizeOptions = props.pageSizeOptions.map(option => typeof option === 'number' ? option : option.value).sort((a, b) => a - b);\n    const cacheChunkSize = Math.min(paginationModel.pageSize, sortedPageSizeOptions[0]);\n    return new CacheChunkManager(cacheChunkSize);\n  }).current;\n  const [cache, setCache] = React.useState(() => getCache(props.dataSourceCache, options.cacheOptions));\n  const fetchRows = React.useCallback(async (parentId, params) => {\n    const getRows = props.dataSource?.getRows;\n    if (!getRows) {\n      return;\n    }\n    if (parentId && parentId !== GRID_ROOT_GROUP_ID && props.signature !== 'DataGrid') {\n      options.fetchRowChildren?.([parentId]);\n      return;\n    }\n    options.clearDataSourceState?.();\n    const _ref = params || {},\n      {\n        skipCache\n      } = _ref,\n      getRowsParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const fetchParams = _extends({}, gridGetRowsParamsSelector(apiRef), apiRef.current.unstable_applyPipeProcessors('getRowsParams', {}), getRowsParams);\n    const cacheKeys = cacheChunkManager.getCacheKeys(fetchParams);\n    const responses = cacheKeys.map(cacheKey => cache.get(cacheKey));\n    if (!skipCache && responses.every(response => response !== undefined)) {\n      apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n        response: CacheChunkManager.mergeResponses(responses),\n        fetchParams\n      });\n      return;\n    }\n\n    // Manage loading state only for the default strategy\n    if (defaultRowsUpdateStrategyActive || apiRef.current.getRowsCount() === 0) {\n      apiRef.current.setLoading(true);\n    }\n    const requestId = lastRequestId.current + 1;\n    lastRequestId.current = requestId;\n    try {\n      const getRowsResponse = await getRows(fetchParams);\n      const cacheResponses = cacheChunkManager.splitResponse(fetchParams, getRowsResponse);\n      cacheResponses.forEach((response, key) => cache.set(key, response));\n      if (lastRequestId.current === requestId) {\n        apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n          response: getRowsResponse,\n          fetchParams\n        });\n      }\n    } catch (originalError) {\n      if (lastRequestId.current === requestId) {\n        apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n          error: originalError,\n          fetchParams\n        });\n        if (typeof onDataSourceErrorProp === 'function') {\n          onDataSourceErrorProp(new GridGetRowsError({\n            message: originalError?.message,\n            params: fetchParams,\n            cause: originalError\n          }));\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `dataSource.getRows()` threw an error which was not handled because `onDataSourceError()` is missing.', 'To handle the error pass a callback to the `onDataSourceError` prop, for example `<DataGrid onDataSourceError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/server-side-data/#error-handling.'], 'error');\n        }\n      }\n    } finally {\n      if (defaultRowsUpdateStrategyActive && lastRequestId.current === requestId) {\n        apiRef.current.setLoading(false);\n      }\n    }\n  }, [cacheChunkManager, cache, apiRef, defaultRowsUpdateStrategyActive, props.dataSource?.getRows, onDataSourceErrorProp, options, props.signature]);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    setDefaultRowsUpdateStrategyActive(apiRef.current.getActiveStrategy(GridStrategyGroup.DataSource) === DataSourceRowsUpdateStrategy.Default);\n  }, [apiRef]);\n  const handleDataUpdate = React.useCallback(params => {\n    if ('error' in params) {\n      apiRef.current.setRows([]);\n      return;\n    }\n    const {\n      response\n    } = params;\n    if (response.rowCount !== undefined) {\n      apiRef.current.setRowCount(response.rowCount);\n    }\n    apiRef.current.setRows(response.rows);\n    apiRef.current.unstable_applyPipeProcessors('processDataSourceRows', {\n      params: params.fetchParams,\n      response\n    }, true);\n  }, [apiRef]);\n  const dataSourceUpdateRow = props.dataSource?.updateRow;\n  const handleEditRowOption = options.handleEditRow;\n  const editRow = React.useCallback(async params => {\n    if (!dataSourceUpdateRow) {\n      return undefined;\n    }\n    try {\n      const finalRowUpdate = await dataSourceUpdateRow(params);\n      if (typeof handleEditRowOption === 'function') {\n        handleEditRowOption(params, finalRowUpdate);\n        return finalRowUpdate;\n      }\n      apiRef.current.updateNestedRows([finalRowUpdate], []);\n      if (finalRowUpdate && !isDeepEqual(finalRowUpdate, params.previousRow)) {\n        // Reset the outdated cache, only if the row is _actually_ updated\n        apiRef.current.dataSource.cache.clear();\n      }\n      return finalRowUpdate;\n    } catch (errorThrown) {\n      if (typeof onDataSourceErrorProp === 'function') {\n        onDataSourceErrorProp(new GridUpdateRowError({\n          message: errorThrown?.message,\n          params,\n          cause: errorThrown\n        }));\n      } else if (process.env.NODE_ENV !== 'production') {\n        warnOnce(['MUI X: A call to `dataSource.updateRow()` threw an error which was not handled because `onDataSourceError()` is missing.', 'To handle the error pass a callback to the `onDataSourceError` prop, for example `<DataGrid onDataSourceError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/server-side-data/#error-handling.'], 'error');\n      }\n      throw errorThrown; // Let the caller handle the error further\n    }\n  }, [apiRef, dataSourceUpdateRow, onDataSourceErrorProp, handleEditRowOption]);\n  const dataSourceApi = {\n    dataSource: {\n      fetchRows,\n      cache,\n      editRow\n    }\n  };\n  const debouncedFetchRows = React.useMemo(() => debounce(fetchRows, 0), [fetchRows]);\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    if (props.dataSourceCache === undefined) {\n      return;\n    }\n    const newCache = getCache(props.dataSourceCache, options.cacheOptions);\n    setCache(prevCache => prevCache !== newCache ? newCache : prevCache);\n  }, [props.dataSourceCache, options.cacheOptions]);\n  React.useEffect(() => {\n    if (props.dataSource) {\n      apiRef.current.dataSource.cache.clear();\n      apiRef.current.dataSource.fetchRows();\n    }\n    return () => {\n      // ignore the current request on unmount\n      lastRequestId.current += 1;\n    };\n  }, [apiRef, props.dataSource]);\n  return {\n    api: {\n      public: dataSourceApi\n    },\n    debouncedFetchRows,\n    strategyProcessor: {\n      strategyName: DataSourceRowsUpdateStrategy.Default,\n      group: 'dataSourceRowsUpdate',\n      processor: handleDataUpdate\n    },\n    setStrategyAvailability,\n    cacheChunkManager,\n    cache,\n    events: {\n      strategyAvailabilityChange: handleStrategyActivityChange,\n      sortModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows()),\n      filterModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows()),\n      paginationModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows())\n    }\n  };\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useLazyRef", "debounce", "warnOnce", "isDeepEqual", "GRID_ROOT_GROUP_ID", "runIf", "GridStrategyGroup", "useGridSelector", "gridPaginationModelSelector", "gridGetRowsParamsSelector", "CacheChunkManager", "DataSourceRowsUpdateStrategy", "GridDataSourceCacheDefault", "GridGetRowsError", "GridUpdateRowError", "noop<PERSON><PERSON>", "clear", "get", "undefined", "set", "getCache", "cacheProp", "options", "useGridDataSourceBase", "apiRef", "props", "setStrategyAvailability", "useCallback", "current", "DataSource", "<PERSON><PERSON><PERSON>", "dataSource", "defaultRowsUpdateStrategyActive", "setDefaultRowsUpdateStrategyActive", "useState", "paginationModel", "lastRequestId", "useRef", "onDataSourceErrorProp", "onDataSourceError", "cacheChunkManager", "pagination", "pageSize", "sortedPageSizeOptions", "pageSizeOptions", "map", "option", "value", "sort", "a", "b", "cacheChunkSize", "Math", "min", "cache", "setCache", "dataSourceCache", "cacheOptions", "fetchRows", "parentId", "params", "getRows", "signature", "fetchRowChildren", "clearDataSourceState", "_ref", "<PERSON><PERSON><PERSON>", "getRowsParams", "fetchParams", "unstable_applyPipeProcessors", "cacheKeys", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "responses", "cache<PERSON>ey", "every", "response", "applyStrategyProcessor", "mergeResponses", "getRowsCount", "setLoading", "requestId", "getRowsResponse", "cacheResponses", "splitResponse", "for<PERSON>ach", "key", "originalError", "error", "message", "cause", "process", "env", "NODE_ENV", "handleStrategyActivityChange", "getActiveStrategy", "handleDataUpdate", "setRows", "rowCount", "setRowCount", "rows", "dataSourceUpdateRow", "updateRow", "handleEditRowOption", "handleEditRow", "editRow", "finalRowUpdate", "updateNestedRows", "previousRow", "errorThrown", "dataSourceApi", "debouncedFetchRows", "useMemo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "newCache", "prevCache", "api", "public", "strategyProcessor", "strategyName", "group", "processor", "events", "strategyAvailabilityChange", "sortModelChange", "filterModelChange", "paginationModelChange"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/dataSource/useGridDataSourceBase.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"skipCache\"];\nimport * as React from 'react';\nimport useLazyRef from '@mui/utils/useLazyRef';\nimport debounce from '@mui/utils/debounce';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { isDeepEqual } from '@mui/x-internals/isDeepEqual';\nimport { GRID_ROOT_GROUP_ID } from \"../rows/gridRowsUtils.js\";\nimport { runIf } from \"../../../utils/utils.js\";\nimport { GridStrategyGroup } from \"../../core/strategyProcessing/index.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridPaginationModelSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridGetRowsParamsSelector } from \"./gridDataSourceSelector.js\";\nimport { CacheChunkManager, DataSourceRowsUpdateStrategy } from \"./utils.js\";\nimport { GridDataSourceCacheDefault } from \"./cache.js\";\nimport { GridGetRowsError, GridUpdateRowError } from \"./gridDataSourceError.js\";\nconst noopCache = {\n  clear: () => {},\n  get: () => undefined,\n  set: () => {}\n};\nfunction getCache(cacheProp, options = {}) {\n  if (cacheProp === null) {\n    return noopCache;\n  }\n  return cacheProp ?? new GridDataSourceCacheDefault(options);\n}\nexport const useGridDataSourceBase = (apiRef, props, options = {}) => {\n  const setStrategyAvailability = React.useCallback(() => {\n    apiRef.current.setStrategyAvailability(GridStrategyGroup.DataSource, DataSourceRowsUpdateStrategy.Default, props.dataSource ? () => true : () => false);\n  }, [apiRef, props.dataSource]);\n  const [defaultRowsUpdateStrategyActive, setDefaultRowsUpdateStrategyActive] = React.useState(false);\n  const paginationModel = useGridSelector(apiRef, gridPaginationModelSelector);\n  const lastRequestId = React.useRef(0);\n  const onDataSourceErrorProp = props.onDataSourceError;\n  const cacheChunkManager = useLazyRef(() => {\n    if (!props.pagination) {\n      return new CacheChunkManager(paginationModel.pageSize);\n    }\n    const sortedPageSizeOptions = props.pageSizeOptions.map(option => typeof option === 'number' ? option : option.value).sort((a, b) => a - b);\n    const cacheChunkSize = Math.min(paginationModel.pageSize, sortedPageSizeOptions[0]);\n    return new CacheChunkManager(cacheChunkSize);\n  }).current;\n  const [cache, setCache] = React.useState(() => getCache(props.dataSourceCache, options.cacheOptions));\n  const fetchRows = React.useCallback(async (parentId, params) => {\n    const getRows = props.dataSource?.getRows;\n    if (!getRows) {\n      return;\n    }\n    if (parentId && parentId !== GRID_ROOT_GROUP_ID && props.signature !== 'DataGrid') {\n      options.fetchRowChildren?.([parentId]);\n      return;\n    }\n    options.clearDataSourceState?.();\n    const _ref = params || {},\n      {\n        skipCache\n      } = _ref,\n      getRowsParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n    const fetchParams = _extends({}, gridGetRowsParamsSelector(apiRef), apiRef.current.unstable_applyPipeProcessors('getRowsParams', {}), getRowsParams);\n    const cacheKeys = cacheChunkManager.getCacheKeys(fetchParams);\n    const responses = cacheKeys.map(cacheKey => cache.get(cacheKey));\n    if (!skipCache && responses.every(response => response !== undefined)) {\n      apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n        response: CacheChunkManager.mergeResponses(responses),\n        fetchParams\n      });\n      return;\n    }\n\n    // Manage loading state only for the default strategy\n    if (defaultRowsUpdateStrategyActive || apiRef.current.getRowsCount() === 0) {\n      apiRef.current.setLoading(true);\n    }\n    const requestId = lastRequestId.current + 1;\n    lastRequestId.current = requestId;\n    try {\n      const getRowsResponse = await getRows(fetchParams);\n      const cacheResponses = cacheChunkManager.splitResponse(fetchParams, getRowsResponse);\n      cacheResponses.forEach((response, key) => cache.set(key, response));\n      if (lastRequestId.current === requestId) {\n        apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n          response: getRowsResponse,\n          fetchParams\n        });\n      }\n    } catch (originalError) {\n      if (lastRequestId.current === requestId) {\n        apiRef.current.applyStrategyProcessor('dataSourceRowsUpdate', {\n          error: originalError,\n          fetchParams\n        });\n        if (typeof onDataSourceErrorProp === 'function') {\n          onDataSourceErrorProp(new GridGetRowsError({\n            message: originalError?.message,\n            params: fetchParams,\n            cause: originalError\n          }));\n        } else if (process.env.NODE_ENV !== 'production') {\n          warnOnce(['MUI X: A call to `dataSource.getRows()` threw an error which was not handled because `onDataSourceError()` is missing.', 'To handle the error pass a callback to the `onDataSourceError` prop, for example `<DataGrid onDataSourceError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/server-side-data/#error-handling.'], 'error');\n        }\n      }\n    } finally {\n      if (defaultRowsUpdateStrategyActive && lastRequestId.current === requestId) {\n        apiRef.current.setLoading(false);\n      }\n    }\n  }, [cacheChunkManager, cache, apiRef, defaultRowsUpdateStrategyActive, props.dataSource?.getRows, onDataSourceErrorProp, options, props.signature]);\n  const handleStrategyActivityChange = React.useCallback(() => {\n    setDefaultRowsUpdateStrategyActive(apiRef.current.getActiveStrategy(GridStrategyGroup.DataSource) === DataSourceRowsUpdateStrategy.Default);\n  }, [apiRef]);\n  const handleDataUpdate = React.useCallback(params => {\n    if ('error' in params) {\n      apiRef.current.setRows([]);\n      return;\n    }\n    const {\n      response\n    } = params;\n    if (response.rowCount !== undefined) {\n      apiRef.current.setRowCount(response.rowCount);\n    }\n    apiRef.current.setRows(response.rows);\n    apiRef.current.unstable_applyPipeProcessors('processDataSourceRows', {\n      params: params.fetchParams,\n      response\n    }, true);\n  }, [apiRef]);\n  const dataSourceUpdateRow = props.dataSource?.updateRow;\n  const handleEditRowOption = options.handleEditRow;\n  const editRow = React.useCallback(async params => {\n    if (!dataSourceUpdateRow) {\n      return undefined;\n    }\n    try {\n      const finalRowUpdate = await dataSourceUpdateRow(params);\n      if (typeof handleEditRowOption === 'function') {\n        handleEditRowOption(params, finalRowUpdate);\n        return finalRowUpdate;\n      }\n      apiRef.current.updateNestedRows([finalRowUpdate], []);\n      if (finalRowUpdate && !isDeepEqual(finalRowUpdate, params.previousRow)) {\n        // Reset the outdated cache, only if the row is _actually_ updated\n        apiRef.current.dataSource.cache.clear();\n      }\n      return finalRowUpdate;\n    } catch (errorThrown) {\n      if (typeof onDataSourceErrorProp === 'function') {\n        onDataSourceErrorProp(new GridUpdateRowError({\n          message: errorThrown?.message,\n          params,\n          cause: errorThrown\n        }));\n      } else if (process.env.NODE_ENV !== 'production') {\n        warnOnce(['MUI X: A call to `dataSource.updateRow()` threw an error which was not handled because `onDataSourceError()` is missing.', 'To handle the error pass a callback to the `onDataSourceError` prop, for example `<DataGrid onDataSourceError={(error) => ...} />`.', 'For more detail, see https://mui.com/x/react-data-grid/server-side-data/#error-handling.'], 'error');\n      }\n      throw errorThrown; // Let the caller handle the error further\n    }\n  }, [apiRef, dataSourceUpdateRow, onDataSourceErrorProp, handleEditRowOption]);\n  const dataSourceApi = {\n    dataSource: {\n      fetchRows,\n      cache,\n      editRow\n    }\n  };\n  const debouncedFetchRows = React.useMemo(() => debounce(fetchRows, 0), [fetchRows]);\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n      return;\n    }\n    if (props.dataSourceCache === undefined) {\n      return;\n    }\n    const newCache = getCache(props.dataSourceCache, options.cacheOptions);\n    setCache(prevCache => prevCache !== newCache ? newCache : prevCache);\n  }, [props.dataSourceCache, options.cacheOptions]);\n  React.useEffect(() => {\n    if (props.dataSource) {\n      apiRef.current.dataSource.cache.clear();\n      apiRef.current.dataSource.fetchRows();\n    }\n    return () => {\n      // ignore the current request on unmount\n      lastRequestId.current += 1;\n    };\n  }, [apiRef, props.dataSource]);\n  return {\n    api: {\n      public: dataSourceApi\n    },\n    debouncedFetchRows,\n    strategyProcessor: {\n      strategyName: DataSourceRowsUpdateStrategy.Default,\n      group: 'dataSourceRowsUpdate',\n      processor: handleDataUpdate\n    },\n    setStrategyAvailability,\n    cacheChunkManager,\n    cache,\n    events: {\n      strategyAvailabilityChange: handleStrategyActivityChange,\n      sortModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows()),\n      filterModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows()),\n      paginationModelChange: runIf(defaultRowsUpdateStrategyActive, () => debouncedFetchRows())\n    }\n  };\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,2BAA2B,QAAQ,yCAAyC;AACrF,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,iBAAiB,EAAEC,4BAA4B,QAAQ,YAAY;AAC5E,SAASC,0BAA0B,QAAQ,YAAY;AACvD,SAASC,gBAAgB,EAAEC,kBAAkB,QAAQ,0BAA0B;AAC/E,MAAMC,SAAS,GAAG;EAChBC,KAAK,EAAEA,CAAA,KAAM,CAAC,CAAC;EACfC,GAAG,EAAEA,CAAA,KAAMC,SAAS;EACpBC,GAAG,EAAEA,CAAA,KAAM,CAAC;AACd,CAAC;AACD,SAASC,QAAQA,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACzC,IAAID,SAAS,KAAK,IAAI,EAAE;IACtB,OAAON,SAAS;EAClB;EACA,OAAOM,SAAS,IAAI,IAAIT,0BAA0B,CAACU,OAAO,CAAC;AAC7D;AACA,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEH,OAAO,GAAG,CAAC,CAAC,KAAK;EACpE,MAAMI,uBAAuB,GAAG3B,KAAK,CAAC4B,WAAW,CAAC,MAAM;IACtDH,MAAM,CAACI,OAAO,CAACF,uBAAuB,CAACpB,iBAAiB,CAACuB,UAAU,EAAElB,4BAA4B,CAACmB,OAAO,EAAEL,KAAK,CAACM,UAAU,GAAG,MAAM,IAAI,GAAG,MAAM,KAAK,CAAC;EACzJ,CAAC,EAAE,CAACP,MAAM,EAAEC,KAAK,CAACM,UAAU,CAAC,CAAC;EAC9B,MAAM,CAACC,+BAA+B,EAAEC,kCAAkC,CAAC,GAAGlC,KAAK,CAACmC,QAAQ,CAAC,KAAK,CAAC;EACnG,MAAMC,eAAe,GAAG5B,eAAe,CAACiB,MAAM,EAAEhB,2BAA2B,CAAC;EAC5E,MAAM4B,aAAa,GAAGrC,KAAK,CAACsC,MAAM,CAAC,CAAC,CAAC;EACrC,MAAMC,qBAAqB,GAAGb,KAAK,CAACc,iBAAiB;EACrD,MAAMC,iBAAiB,GAAGxC,UAAU,CAAC,MAAM;IACzC,IAAI,CAACyB,KAAK,CAACgB,UAAU,EAAE;MACrB,OAAO,IAAI/B,iBAAiB,CAACyB,eAAe,CAACO,QAAQ,CAAC;IACxD;IACA,MAAMC,qBAAqB,GAAGlB,KAAK,CAACmB,eAAe,CAACC,GAAG,CAACC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGA,MAAM,CAACC,KAAK,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;IAC3I,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAClB,eAAe,CAACO,QAAQ,EAAEC,qBAAqB,CAAC,CAAC,CAAC,CAAC;IACnF,OAAO,IAAIjC,iBAAiB,CAACyC,cAAc,CAAC;EAC9C,CAAC,CAAC,CAACvB,OAAO;EACV,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,KAAK,CAACmC,QAAQ,CAAC,MAAMd,QAAQ,CAACK,KAAK,CAAC+B,eAAe,EAAElC,OAAO,CAACmC,YAAY,CAAC,CAAC;EACrG,MAAMC,SAAS,GAAG3D,KAAK,CAAC4B,WAAW,CAAC,OAAOgC,QAAQ,EAAEC,MAAM,KAAK;IAC9D,MAAMC,OAAO,GAAGpC,KAAK,CAACM,UAAU,EAAE8B,OAAO;IACzC,IAAI,CAACA,OAAO,EAAE;MACZ;IACF;IACA,IAAIF,QAAQ,IAAIA,QAAQ,KAAKvD,kBAAkB,IAAIqB,KAAK,CAACqC,SAAS,KAAK,UAAU,EAAE;MACjFxC,OAAO,CAACyC,gBAAgB,GAAG,CAACJ,QAAQ,CAAC,CAAC;MACtC;IACF;IACArC,OAAO,CAAC0C,oBAAoB,GAAG,CAAC;IAChC,MAAMC,IAAI,GAAGL,MAAM,IAAI,CAAC,CAAC;MACvB;QACEM;MACF,CAAC,GAAGD,IAAI;MACRE,aAAa,GAAGtE,6BAA6B,CAACoE,IAAI,EAAEnE,SAAS,CAAC;IAChE,MAAMsE,WAAW,GAAGxE,QAAQ,CAAC,CAAC,CAAC,EAAEa,yBAAyB,CAACe,MAAM,CAAC,EAAEA,MAAM,CAACI,OAAO,CAACyC,4BAA4B,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC;IACpJ,MAAMG,SAAS,GAAG9B,iBAAiB,CAAC+B,YAAY,CAACH,WAAW,CAAC;IAC7D,MAAMI,SAAS,GAAGF,SAAS,CAACzB,GAAG,CAAC4B,QAAQ,IAAInB,KAAK,CAACrC,GAAG,CAACwD,QAAQ,CAAC,CAAC;IAChE,IAAI,CAACP,SAAS,IAAIM,SAAS,CAACE,KAAK,CAACC,QAAQ,IAAIA,QAAQ,KAAKzD,SAAS,CAAC,EAAE;MACrEM,MAAM,CAACI,OAAO,CAACgD,sBAAsB,CAAC,sBAAsB,EAAE;QAC5DD,QAAQ,EAAEjE,iBAAiB,CAACmE,cAAc,CAACL,SAAS,CAAC;QACrDJ;MACF,CAAC,CAAC;MACF;IACF;;IAEA;IACA,IAAIpC,+BAA+B,IAAIR,MAAM,CAACI,OAAO,CAACkD,YAAY,CAAC,CAAC,KAAK,CAAC,EAAE;MAC1EtD,MAAM,CAACI,OAAO,CAACmD,UAAU,CAAC,IAAI,CAAC;IACjC;IACA,MAAMC,SAAS,GAAG5C,aAAa,CAACR,OAAO,GAAG,CAAC;IAC3CQ,aAAa,CAACR,OAAO,GAAGoD,SAAS;IACjC,IAAI;MACF,MAAMC,eAAe,GAAG,MAAMpB,OAAO,CAACO,WAAW,CAAC;MAClD,MAAMc,cAAc,GAAG1C,iBAAiB,CAAC2C,aAAa,CAACf,WAAW,EAAEa,eAAe,CAAC;MACpFC,cAAc,CAACE,OAAO,CAAC,CAACT,QAAQ,EAAEU,GAAG,KAAK/B,KAAK,CAACnC,GAAG,CAACkE,GAAG,EAAEV,QAAQ,CAAC,CAAC;MACnE,IAAIvC,aAAa,CAACR,OAAO,KAAKoD,SAAS,EAAE;QACvCxD,MAAM,CAACI,OAAO,CAACgD,sBAAsB,CAAC,sBAAsB,EAAE;UAC5DD,QAAQ,EAAEM,eAAe;UACzBb;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOkB,aAAa,EAAE;MACtB,IAAIlD,aAAa,CAACR,OAAO,KAAKoD,SAAS,EAAE;QACvCxD,MAAM,CAACI,OAAO,CAACgD,sBAAsB,CAAC,sBAAsB,EAAE;UAC5DW,KAAK,EAAED,aAAa;UACpBlB;QACF,CAAC,CAAC;QACF,IAAI,OAAO9B,qBAAqB,KAAK,UAAU,EAAE;UAC/CA,qBAAqB,CAAC,IAAIzB,gBAAgB,CAAC;YACzC2E,OAAO,EAAEF,aAAa,EAAEE,OAAO;YAC/B5B,MAAM,EAAEQ,WAAW;YACnBqB,KAAK,EAAEH;UACT,CAAC,CAAC,CAAC;QACL,CAAC,MAAM,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UAChD1F,QAAQ,CAAC,CAAC,wHAAwH,EAAE,qIAAqI,EAAE,0FAA0F,CAAC,EAAE,OAAO,CAAC;QAClX;MACF;IACF,CAAC,SAAS;MACR,IAAI8B,+BAA+B,IAAII,aAAa,CAACR,OAAO,KAAKoD,SAAS,EAAE;QAC1ExD,MAAM,CAACI,OAAO,CAACmD,UAAU,CAAC,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAACvC,iBAAiB,EAAEc,KAAK,EAAE9B,MAAM,EAAEQ,+BAA+B,EAAEP,KAAK,CAACM,UAAU,EAAE8B,OAAO,EAAEvB,qBAAqB,EAAEhB,OAAO,EAAEG,KAAK,CAACqC,SAAS,CAAC,CAAC;EACnJ,MAAM+B,4BAA4B,GAAG9F,KAAK,CAAC4B,WAAW,CAAC,MAAM;IAC3DM,kCAAkC,CAACT,MAAM,CAACI,OAAO,CAACkE,iBAAiB,CAACxF,iBAAiB,CAACuB,UAAU,CAAC,KAAKlB,4BAA4B,CAACmB,OAAO,CAAC;EAC7I,CAAC,EAAE,CAACN,MAAM,CAAC,CAAC;EACZ,MAAMuE,gBAAgB,GAAGhG,KAAK,CAAC4B,WAAW,CAACiC,MAAM,IAAI;IACnD,IAAI,OAAO,IAAIA,MAAM,EAAE;MACrBpC,MAAM,CAACI,OAAO,CAACoE,OAAO,CAAC,EAAE,CAAC;MAC1B;IACF;IACA,MAAM;MACJrB;IACF,CAAC,GAAGf,MAAM;IACV,IAAIe,QAAQ,CAACsB,QAAQ,KAAK/E,SAAS,EAAE;MACnCM,MAAM,CAACI,OAAO,CAACsE,WAAW,CAACvB,QAAQ,CAACsB,QAAQ,CAAC;IAC/C;IACAzE,MAAM,CAACI,OAAO,CAACoE,OAAO,CAACrB,QAAQ,CAACwB,IAAI,CAAC;IACrC3E,MAAM,CAACI,OAAO,CAACyC,4BAA4B,CAAC,uBAAuB,EAAE;MACnET,MAAM,EAAEA,MAAM,CAACQ,WAAW;MAC1BO;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,CAACnD,MAAM,CAAC,CAAC;EACZ,MAAM4E,mBAAmB,GAAG3E,KAAK,CAACM,UAAU,EAAEsE,SAAS;EACvD,MAAMC,mBAAmB,GAAGhF,OAAO,CAACiF,aAAa;EACjD,MAAMC,OAAO,GAAGzG,KAAK,CAAC4B,WAAW,CAAC,MAAMiC,MAAM,IAAI;IAChD,IAAI,CAACwC,mBAAmB,EAAE;MACxB,OAAOlF,SAAS;IAClB;IACA,IAAI;MACF,MAAMuF,cAAc,GAAG,MAAML,mBAAmB,CAACxC,MAAM,CAAC;MACxD,IAAI,OAAO0C,mBAAmB,KAAK,UAAU,EAAE;QAC7CA,mBAAmB,CAAC1C,MAAM,EAAE6C,cAAc,CAAC;QAC3C,OAAOA,cAAc;MACvB;MACAjF,MAAM,CAACI,OAAO,CAAC8E,gBAAgB,CAAC,CAACD,cAAc,CAAC,EAAE,EAAE,CAAC;MACrD,IAAIA,cAAc,IAAI,CAACtG,WAAW,CAACsG,cAAc,EAAE7C,MAAM,CAAC+C,WAAW,CAAC,EAAE;QACtE;QACAnF,MAAM,CAACI,OAAO,CAACG,UAAU,CAACuB,KAAK,CAACtC,KAAK,CAAC,CAAC;MACzC;MACA,OAAOyF,cAAc;IACvB,CAAC,CAAC,OAAOG,WAAW,EAAE;MACpB,IAAI,OAAOtE,qBAAqB,KAAK,UAAU,EAAE;QAC/CA,qBAAqB,CAAC,IAAIxB,kBAAkB,CAAC;UAC3C0E,OAAO,EAAEoB,WAAW,EAAEpB,OAAO;UAC7B5B,MAAM;UACN6B,KAAK,EAAEmB;QACT,CAAC,CAAC,CAAC;MACL,CAAC,MAAM,IAAIlB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QAChD1F,QAAQ,CAAC,CAAC,0HAA0H,EAAE,qIAAqI,EAAE,0FAA0F,CAAC,EAAE,OAAO,CAAC;MACpX;MACA,MAAM0G,WAAW,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACpF,MAAM,EAAE4E,mBAAmB,EAAE9D,qBAAqB,EAAEgE,mBAAmB,CAAC,CAAC;EAC7E,MAAMO,aAAa,GAAG;IACpB9E,UAAU,EAAE;MACV2B,SAAS;MACTJ,KAAK;MACLkD;IACF;EACF,CAAC;EACD,MAAMM,kBAAkB,GAAG/G,KAAK,CAACgH,OAAO,CAAC,MAAM9G,QAAQ,CAACyD,SAAS,EAAE,CAAC,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACnF,MAAMsD,aAAa,GAAGjH,KAAK,CAACsC,MAAM,CAAC,IAAI,CAAC;EACxCtC,KAAK,CAACkH,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,CAACpF,OAAO,EAAE;MACzBoF,aAAa,CAACpF,OAAO,GAAG,KAAK;MAC7B;IACF;IACA,IAAIH,KAAK,CAAC+B,eAAe,KAAKtC,SAAS,EAAE;MACvC;IACF;IACA,MAAMgG,QAAQ,GAAG9F,QAAQ,CAACK,KAAK,CAAC+B,eAAe,EAAElC,OAAO,CAACmC,YAAY,CAAC;IACtEF,QAAQ,CAAC4D,SAAS,IAAIA,SAAS,KAAKD,QAAQ,GAAGA,QAAQ,GAAGC,SAAS,CAAC;EACtE,CAAC,EAAE,CAAC1F,KAAK,CAAC+B,eAAe,EAAElC,OAAO,CAACmC,YAAY,CAAC,CAAC;EACjD1D,KAAK,CAACkH,SAAS,CAAC,MAAM;IACpB,IAAIxF,KAAK,CAACM,UAAU,EAAE;MACpBP,MAAM,CAACI,OAAO,CAACG,UAAU,CAACuB,KAAK,CAACtC,KAAK,CAAC,CAAC;MACvCQ,MAAM,CAACI,OAAO,CAACG,UAAU,CAAC2B,SAAS,CAAC,CAAC;IACvC;IACA,OAAO,MAAM;MACX;MACAtB,aAAa,CAACR,OAAO,IAAI,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAACJ,MAAM,EAAEC,KAAK,CAACM,UAAU,CAAC,CAAC;EAC9B,OAAO;IACLqF,GAAG,EAAE;MACHC,MAAM,EAAER;IACV,CAAC;IACDC,kBAAkB;IAClBQ,iBAAiB,EAAE;MACjBC,YAAY,EAAE5G,4BAA4B,CAACmB,OAAO;MAClD0F,KAAK,EAAE,sBAAsB;MAC7BC,SAAS,EAAE1B;IACb,CAAC;IACDrE,uBAAuB;IACvBc,iBAAiB;IACjBc,KAAK;IACLoE,MAAM,EAAE;MACNC,0BAA0B,EAAE9B,4BAA4B;MACxD+B,eAAe,EAAEvH,KAAK,CAAC2B,+BAA+B,EAAE,MAAM8E,kBAAkB,CAAC,CAAC,CAAC;MACnFe,iBAAiB,EAAExH,KAAK,CAAC2B,+BAA+B,EAAE,MAAM8E,kBAAkB,CAAC,CAAC,CAAC;MACrFgB,qBAAqB,EAAEzH,KAAK,CAAC2B,+BAA+B,EAAE,MAAM8E,kBAAkB,CAAC,CAAC;IAC1F;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}