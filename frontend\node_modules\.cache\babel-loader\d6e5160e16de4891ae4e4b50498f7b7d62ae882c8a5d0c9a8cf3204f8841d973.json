{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridRowTreeSelector, gridRowNodeSelector } from \"./gridRowsSelector.js\";\nimport { GRID_ROOT_GROUP_ID } from \"./gridRowsUtils.js\";\nexport const useGridRowsOverridableMethods = apiRef => {\n  const setRowIndex = React.useCallback((rowId, targetIndex) => {\n    const node = gridRowNodeSelector(apiRef, rowId);\n    if (!node) {\n      throw new Error(`MUI X: No row with id #${rowId} found.`);\n    }\n\n    // TODO: Remove irrelevant checks\n    if (node.parent !== GRID_ROOT_GROUP_ID) {\n      throw new Error(`MUI X: The row reordering do not support reordering of grouped rows yet.`);\n    }\n    if (node.type !== 'leaf') {\n      throw new Error(`MUI X: The row reordering do not support reordering of footer or grouping rows.`);\n    }\n    apiRef.current.setState(state => {\n      const group = gridRowTreeSelector(apiRef)[GRID_ROOT_GROUP_ID];\n      const allRows = group.children;\n      const oldIndex = allRows.findIndex(row => row === rowId);\n      if (oldIndex === -1 || oldIndex === targetIndex) {\n        return state;\n      }\n      const updatedRows = [...allRows];\n      updatedRows.splice(targetIndex, 0, updatedRows.splice(oldIndex, 1)[0]);\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [GRID_ROOT_GROUP_ID]: _extends({}, group, {\n              children: updatedRows\n            })\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef]);\n  return {\n    setRowIndex\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "gridRowTreeSelector", "gridRowNodeSelector", "GRID_ROOT_GROUP_ID", "useGridRowsOverridableMethods", "apiRef", "setRowIndex", "useCallback", "rowId", "targetIndex", "node", "Error", "parent", "type", "current", "setState", "state", "group", "allRows", "children", "oldIndex", "findIndex", "row", "updatedRows", "splice", "rows", "tree", "publishEvent"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rows/useGridRowsOverridableMethods.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridRowTreeSelector, gridRowNodeSelector } from \"./gridRowsSelector.js\";\nimport { GRID_ROOT_GROUP_ID } from \"./gridRowsUtils.js\";\nexport const useGridRowsOverridableMethods = apiRef => {\n  const setRowIndex = React.useCallback((rowId, targetIndex) => {\n    const node = gridRowNodeSelector(apiRef, rowId);\n    if (!node) {\n      throw new Error(`MUI X: No row with id #${rowId} found.`);\n    }\n\n    // TODO: Remove irrelevant checks\n    if (node.parent !== GRID_ROOT_GROUP_ID) {\n      throw new Error(`MUI X: The row reordering do not support reordering of grouped rows yet.`);\n    }\n    if (node.type !== 'leaf') {\n      throw new Error(`MUI X: The row reordering do not support reordering of footer or grouping rows.`);\n    }\n    apiRef.current.setState(state => {\n      const group = gridRowTreeSelector(apiRef)[GRID_ROOT_GROUP_ID];\n      const allRows = group.children;\n      const oldIndex = allRows.findIndex(row => row === rowId);\n      if (oldIndex === -1 || oldIndex === targetIndex) {\n        return state;\n      }\n      const updatedRows = [...allRows];\n      updatedRows.splice(targetIndex, 0, updatedRows.splice(oldIndex, 1)[0]);\n      return _extends({}, state, {\n        rows: _extends({}, state.rows, {\n          tree: _extends({}, state.rows.tree, {\n            [GRID_ROOT_GROUP_ID]: _extends({}, group, {\n              children: updatedRows\n            })\n          })\n        })\n      });\n    });\n    apiRef.current.publishEvent('rowsSet');\n  }, [apiRef]);\n  return {\n    setRowIndex\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,EAAEC,mBAAmB,QAAQ,uBAAuB;AAChF,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,OAAO,MAAMC,6BAA6B,GAAGC,MAAM,IAAI;EACrD,MAAMC,WAAW,GAAGN,KAAK,CAACO,WAAW,CAAC,CAACC,KAAK,EAAEC,WAAW,KAAK;IAC5D,MAAMC,IAAI,GAAGR,mBAAmB,CAACG,MAAM,EAAEG,KAAK,CAAC;IAC/C,IAAI,CAACE,IAAI,EAAE;MACT,MAAM,IAAIC,KAAK,CAAC,0BAA0BH,KAAK,SAAS,CAAC;IAC3D;;IAEA;IACA,IAAIE,IAAI,CAACE,MAAM,KAAKT,kBAAkB,EAAE;MACtC,MAAM,IAAIQ,KAAK,CAAC,0EAA0E,CAAC;IAC7F;IACA,IAAID,IAAI,CAACG,IAAI,KAAK,MAAM,EAAE;MACxB,MAAM,IAAIF,KAAK,CAAC,iFAAiF,CAAC;IACpG;IACAN,MAAM,CAACS,OAAO,CAACC,QAAQ,CAACC,KAAK,IAAI;MAC/B,MAAMC,KAAK,GAAGhB,mBAAmB,CAACI,MAAM,CAAC,CAACF,kBAAkB,CAAC;MAC7D,MAAMe,OAAO,GAAGD,KAAK,CAACE,QAAQ;MAC9B,MAAMC,QAAQ,GAAGF,OAAO,CAACG,SAAS,CAACC,GAAG,IAAIA,GAAG,KAAKd,KAAK,CAAC;MACxD,IAAIY,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,KAAKX,WAAW,EAAE;QAC/C,OAAOO,KAAK;MACd;MACA,MAAMO,WAAW,GAAG,CAAC,GAAGL,OAAO,CAAC;MAChCK,WAAW,CAACC,MAAM,CAACf,WAAW,EAAE,CAAC,EAAEc,WAAW,CAACC,MAAM,CAACJ,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE,OAAOrB,QAAQ,CAAC,CAAC,CAAC,EAAEiB,KAAK,EAAE;QACzBS,IAAI,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAEiB,KAAK,CAACS,IAAI,EAAE;UAC7BC,IAAI,EAAE3B,QAAQ,CAAC,CAAC,CAAC,EAAEiB,KAAK,CAACS,IAAI,CAACC,IAAI,EAAE;YAClC,CAACvB,kBAAkB,GAAGJ,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;cACxCE,QAAQ,EAAEI;YACZ,CAAC;UACH,CAAC;QACH,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IACFlB,MAAM,CAACS,OAAO,CAACa,YAAY,CAAC,SAAS,CAAC;EACxC,CAAC,EAAE,CAACtB,MAAM,CAAC,CAAC;EACZ,OAAO;IACLC;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}