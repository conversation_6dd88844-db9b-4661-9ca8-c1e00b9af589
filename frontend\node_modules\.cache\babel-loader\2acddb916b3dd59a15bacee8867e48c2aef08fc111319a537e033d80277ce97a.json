{"ast": null, "code": "import * as React from 'react';\nexport const useGridRefs = apiRef => {\n  const rootElementRef = React.useRef(null);\n  const mainElementRef = React.useRef(null);\n  const virtualScrollerRef = React.useRef(null);\n  const virtualScrollbarVerticalRef = React.useRef(null);\n  const virtualScrollbarHorizontalRef = React.useRef(null);\n  const columnHeadersContainerRef = React.useRef(null);\n  apiRef.current.register('public', {\n    rootElementRef\n  });\n  apiRef.current.register('private', {\n    mainElementRef,\n    virtualScrollerRef,\n    virtualScrollbarVerticalRef,\n    virtualScrollbarHorizontalRef,\n    columnHeadersContainerRef\n  });\n};", "map": {"version": 3, "names": ["React", "useGridRefs", "apiRef", "rootElementRef", "useRef", "mainElementRef", "virtualScrollerRef", "virtualScrollbarVerticalRef", "virtualScrollbarHorizontalRef", "columnHeadersContainerRef", "current", "register"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/core/useGridRefs.js"], "sourcesContent": ["import * as React from 'react';\nexport const useGridRefs = apiRef => {\n  const rootElementRef = React.useRef(null);\n  const mainElementRef = React.useRef(null);\n  const virtualScrollerRef = React.useRef(null);\n  const virtualScrollbarVerticalRef = React.useRef(null);\n  const virtualScrollbarHorizontalRef = React.useRef(null);\n  const columnHeadersContainerRef = React.useRef(null);\n  apiRef.current.register('public', {\n    rootElementRef\n  });\n  apiRef.current.register('private', {\n    mainElementRef,\n    virtualScrollerRef,\n    virtualScrollbarVerticalRef,\n    virtualScrollbarHorizontalRef,\n    columnHeadersContainerRef\n  });\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,WAAW,GAAGC,MAAM,IAAI;EACnC,MAAMC,cAAc,GAAGH,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMC,cAAc,GAAGL,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EACzC,MAAME,kBAAkB,GAAGN,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EAC7C,MAAMG,2BAA2B,GAAGP,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EACtD,MAAMI,6BAA6B,GAAGR,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EACxD,MAAMK,yBAAyB,GAAGT,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EACpDF,MAAM,CAACQ,OAAO,CAACC,QAAQ,CAAC,QAAQ,EAAE;IAChCR;EACF,CAAC,CAAC;EACFD,MAAM,CAACQ,OAAO,CAACC,QAAQ,CAAC,SAAS,EAAE;IACjCN,cAAc;IACdC,kBAAkB;IAClBC,2BAA2B;IAC3BC,6BAA6B;IAC7BC;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}