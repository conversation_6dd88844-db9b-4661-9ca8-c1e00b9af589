{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function computeSlots({\n  defaultSlots,\n  slots\n}) {\n  const overrides = slots;\n  if (!overrides || Object.keys(overrides).length === 0) {\n    return defaultSlots;\n  }\n  const result = _extends({}, defaultSlots);\n  Object.keys(overrides).forEach(key => {\n    const k = key;\n    if (overrides[k] !== undefined) {\n      result[k] = overrides[k];\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["_extends", "computeSlots", "defaultSlots", "slots", "overrides", "Object", "keys", "length", "result", "for<PERSON>ach", "key", "k", "undefined"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/internals/utils/computeSlots.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport function computeSlots({\n  defaultSlots,\n  slots\n}) {\n  const overrides = slots;\n  if (!overrides || Object.keys(overrides).length === 0) {\n    return defaultSlots;\n  }\n  const result = _extends({}, defaultSlots);\n  Object.keys(overrides).forEach(key => {\n    const k = key;\n    if (overrides[k] !== undefined) {\n      result[k] = overrides[k];\n    }\n  });\n  return result;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,SAASC,YAAYA,CAAC;EAC3BC,YAAY;EACZC;AACF,CAAC,EAAE;EACD,MAAMC,SAAS,GAAGD,KAAK;EACvB,IAAI,CAACC,SAAS,IAAIC,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IACrD,OAAOL,YAAY;EACrB;EACA,MAAMM,MAAM,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAEE,YAAY,CAAC;EACzCG,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;IACpC,MAAMC,CAAC,GAAGD,GAAG;IACb,IAAIN,SAAS,CAACO,CAAC,CAAC,KAAKC,SAAS,EAAE;MAC9BJ,MAAM,CAACG,CAAC,CAAC,GAAGP,SAAS,CAACO,CAAC,CAAC;IAC1B;EACF,CAAC,CAAC;EACF,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}