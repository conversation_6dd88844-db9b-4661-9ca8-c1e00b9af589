{"ast": null, "code": "import { createSelector, createRootSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridDataRowIdsSelector, gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridFilteredRowCountSelector } from \"../filter/gridFilterSelector.js\";\nimport { createRowSelectionManager } from \"../../../models/gridRowSelectionManager.js\";\nexport const gridRowSelectionStateSelector = createRootSelector(state => state.rowSelection);\nexport const gridRowSelectionManagerSelector = createSelectorMemoized(gridRowSelectionStateSelector, createRowSelectionManager);\nexport const gridRowSelectionCountSelector = createSelector(gridRowSelectionStateSelector, gridFilteredRowCountSelector, (selection, filteredRowCount) => {\n  if (selection.type === 'include') {\n    return selection.ids.size;\n  }\n  // In exclude selection, all rows are selectable.\n  return filteredRowCount - selection.ids.size;\n});\nexport const gridRowSelectionIdsSelector = createSelectorMemoized(gridRowSelectionStateSelector, gridRowsLookupSelector, gridDataRowIdsSelector, (selectionModel, rowsLookup, rowIds) => {\n  const map = new Map();\n  if (selectionModel.type === 'include') {\n    for (const id of selectionModel.ids) {\n      map.set(id, rowsLookup[id]);\n    }\n  } else {\n    for (let i = 0; i < rowIds.length; i += 1) {\n      const id = rowIds[i];\n      if (!selectionModel.ids.has(id)) {\n        map.set(id, rowsLookup[id]);\n      }\n    }\n  }\n  return map;\n});", "map": {"version": 3, "names": ["createSelector", "createRootSelector", "createSelectorMemoized", "gridDataRowIdsSelector", "gridRowsLookupSelector", "gridFilteredRowCountSelector", "createRowSelectionManager", "gridRowSelectionStateSelector", "state", "rowSelection", "gridRowSelectionManagerSelector", "gridRowSelectionCountSelector", "selection", "filteredRowCount", "type", "ids", "size", "gridRowSelectionIdsSelector", "selectionModel", "rowsLookup", "rowIds", "map", "Map", "id", "set", "i", "length", "has"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rowSelection/gridRowSelectionSelector.js"], "sourcesContent": ["import { createSelector, createRootSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridDataRowIdsSelector, gridRowsLookupSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridFilteredRowCountSelector } from \"../filter/gridFilterSelector.js\";\nimport { createRowSelectionManager } from \"../../../models/gridRowSelectionManager.js\";\nexport const gridRowSelectionStateSelector = createRootSelector(state => state.rowSelection);\nexport const gridRowSelectionManagerSelector = createSelectorMemoized(gridRowSelectionStateSelector, createRowSelectionManager);\nexport const gridRowSelectionCountSelector = createSelector(gridRowSelectionStateSelector, gridFilteredRowCountSelector, (selection, filteredRowCount) => {\n  if (selection.type === 'include') {\n    return selection.ids.size;\n  }\n  // In exclude selection, all rows are selectable.\n  return filteredRowCount - selection.ids.size;\n});\nexport const gridRowSelectionIdsSelector = createSelectorMemoized(gridRowSelectionStateSelector, gridRowsLookupSelector, gridDataRowIdsSelector, (selectionModel, rowsLookup, rowIds) => {\n  const map = new Map();\n  if (selectionModel.type === 'include') {\n    for (const id of selectionModel.ids) {\n      map.set(id, rowsLookup[id]);\n    }\n  } else {\n    for (let i = 0; i < rowIds.length; i += 1) {\n      const id = rowIds[i];\n      if (!selectionModel.ids.has(id)) {\n        map.set(id, rowsLookup[id]);\n      }\n    }\n  }\n  return map;\n});"], "mappings": "AAAA,SAASA,cAAc,EAAEC,kBAAkB,EAAEC,sBAAsB,QAAQ,kCAAkC;AAC7G,SAASC,sBAAsB,EAAEC,sBAAsB,QAAQ,6BAA6B;AAC5F,SAASC,4BAA4B,QAAQ,iCAAiC;AAC9E,SAASC,yBAAyB,QAAQ,4CAA4C;AACtF,OAAO,MAAMC,6BAA6B,GAAGN,kBAAkB,CAACO,KAAK,IAAIA,KAAK,CAACC,YAAY,CAAC;AAC5F,OAAO,MAAMC,+BAA+B,GAAGR,sBAAsB,CAACK,6BAA6B,EAAED,yBAAyB,CAAC;AAC/H,OAAO,MAAMK,6BAA6B,GAAGX,cAAc,CAACO,6BAA6B,EAAEF,4BAA4B,EAAE,CAACO,SAAS,EAAEC,gBAAgB,KAAK;EACxJ,IAAID,SAAS,CAACE,IAAI,KAAK,SAAS,EAAE;IAChC,OAAOF,SAAS,CAACG,GAAG,CAACC,IAAI;EAC3B;EACA;EACA,OAAOH,gBAAgB,GAAGD,SAAS,CAACG,GAAG,CAACC,IAAI;AAC9C,CAAC,CAAC;AACF,OAAO,MAAMC,2BAA2B,GAAGf,sBAAsB,CAACK,6BAA6B,EAAEH,sBAAsB,EAAED,sBAAsB,EAAE,CAACe,cAAc,EAAEC,UAAU,EAAEC,MAAM,KAAK;EACvL,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrB,IAAIJ,cAAc,CAACJ,IAAI,KAAK,SAAS,EAAE;IACrC,KAAK,MAAMS,EAAE,IAAIL,cAAc,CAACH,GAAG,EAAE;MACnCM,GAAG,CAACG,GAAG,CAACD,EAAE,EAAEJ,UAAU,CAACI,EAAE,CAAC,CAAC;IAC7B;EACF,CAAC,MAAM;IACL,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACM,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAMF,EAAE,GAAGH,MAAM,CAACK,CAAC,CAAC;MACpB,IAAI,CAACP,cAAc,CAACH,GAAG,CAACY,GAAG,CAACJ,EAAE,CAAC,EAAE;QAC/BF,GAAG,CAACG,GAAG,CAACD,EAAE,EAAEJ,UAAU,CAACI,EAAE,CAAC,CAAC;MAC7B;IACF;EACF;EACA,OAAOF,GAAG;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}