{"ast": null, "code": "function partialRight(func, ...partialArgs) {\n  return partialRightImpl(func, placeholderSymbol, ...partialArgs);\n}\nfunction partialRightImpl(func, placeholder, ...partialArgs) {\n  const partialedRight = function (...providedArgs) {\n    const placeholderLength = partialArgs.filter(arg => arg === placeholder).length;\n    const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);\n    const remainingArgs = providedArgs.slice(0, rangeLength);\n    let providedArgsIndex = rangeLength;\n    const substitutedArgs = partialArgs.slice().map(arg => arg === placeholder ? providedArgs[providedArgsIndex++] : arg);\n    return func.apply(this, remainingArgs.concat(substitutedArgs));\n  };\n  if (func.prototype) {\n    partialedRight.prototype = Object.create(func.prototype);\n  }\n  return partialedRight;\n}\nconst placeholderSymbol = Symbol('partialRight.placeholder');\npartialRight.placeholder = placeholderSymbol;\nexport { partialRight, partialRightImpl };", "map": {"version": 3, "names": ["partialRight", "func", "partialArgs", "partialRightImpl", "placeholderSymbol", "placeholder", "partialedRight", "provided<PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON>", "filter", "arg", "length", "rangeLength", "Math", "max", "remainingArgs", "slice", "providedArgsIndex", "<PERSON><PERSON><PERSON><PERSON>", "map", "apply", "concat", "prototype", "Object", "create", "Symbol"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/partialRight.mjs"], "sourcesContent": ["function partialRight(func, ...partialArgs) {\n    return partialRightImpl(func, placeholderSymbol, ...partialArgs);\n}\nfunction partialRightImpl(func, placeholder, ...partialArgs) {\n    const partialedRight = function (...providedArgs) {\n        const placeholderLength = partialArgs.filter(arg => arg === placeholder).length;\n        const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);\n        const remainingArgs = providedArgs.slice(0, rangeLength);\n        let providedArgsIndex = rangeLength;\n        const substitutedArgs = partialArgs\n            .slice()\n            .map(arg => (arg === placeholder ? providedArgs[providedArgsIndex++] : arg));\n        return func.apply(this, remainingArgs.concat(substitutedArgs));\n    };\n    if (func.prototype) {\n        partialedRight.prototype = Object.create(func.prototype);\n    }\n    return partialedRight;\n}\nconst placeholderSymbol = Symbol('partialRight.placeholder');\npartialRight.placeholder = placeholderSymbol;\n\nexport { partialRight, partialRightImpl };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,IAAI,EAAE,GAAGC,WAAW,EAAE;EACxC,OAAOC,gBAAgB,CAACF,IAAI,EAAEG,iBAAiB,EAAE,GAAGF,WAAW,CAAC;AACpE;AACA,SAASC,gBAAgBA,CAACF,IAAI,EAAEI,WAAW,EAAE,GAAGH,WAAW,EAAE;EACzD,MAAMI,cAAc,GAAG,SAAAA,CAAU,GAAGC,YAAY,EAAE;IAC9C,MAAMC,iBAAiB,GAAGN,WAAW,CAACO,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKL,WAAW,CAAC,CAACM,MAAM;IAC/E,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACP,YAAY,CAACI,MAAM,GAAGH,iBAAiB,EAAE,CAAC,CAAC;IACxE,MAAMO,aAAa,GAAGR,YAAY,CAACS,KAAK,CAAC,CAAC,EAAEJ,WAAW,CAAC;IACxD,IAAIK,iBAAiB,GAAGL,WAAW;IACnC,MAAMM,eAAe,GAAGhB,WAAW,CAC9Bc,KAAK,CAAC,CAAC,CACPG,GAAG,CAACT,GAAG,IAAKA,GAAG,KAAKL,WAAW,GAAGE,YAAY,CAACU,iBAAiB,EAAE,CAAC,GAAGP,GAAI,CAAC;IAChF,OAAOT,IAAI,CAACmB,KAAK,CAAC,IAAI,EAAEL,aAAa,CAACM,MAAM,CAACH,eAAe,CAAC,CAAC;EAClE,CAAC;EACD,IAAIjB,IAAI,CAACqB,SAAS,EAAE;IAChBhB,cAAc,CAACgB,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACvB,IAAI,CAACqB,SAAS,CAAC;EAC5D;EACA,OAAOhB,cAAc;AACzB;AACA,MAAMF,iBAAiB,GAAGqB,MAAM,CAAC,0BAA0B,CAAC;AAC5DzB,YAAY,CAACK,WAAW,GAAGD,iBAAiB;AAE5C,SAASJ,YAAY,EAAEG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}