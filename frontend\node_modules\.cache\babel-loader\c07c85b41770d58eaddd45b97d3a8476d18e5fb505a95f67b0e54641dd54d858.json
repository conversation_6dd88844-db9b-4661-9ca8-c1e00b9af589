{"ast": null, "code": "function clamp(value, bound1, bound2) {\n  if (bound2 == null) {\n    return Math.min(value, bound1);\n  }\n  return Math.min(Math.max(value, bound1), bound2);\n}\nexport { clamp };", "map": {"version": 3, "names": ["clamp", "value", "bound1", "bound2", "Math", "min", "max"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/math/clamp.mjs"], "sourcesContent": ["function clamp(value, bound1, bound2) {\n    if (bound2 == null) {\n        return Math.min(value, bound1);\n    }\n    return Math.min(Math.max(value, bound1), bound2);\n}\n\nexport { clamp };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAClC,IAAIA,MAAM,IAAI,IAAI,EAAE;IAChB,OAAOC,IAAI,CAACC,GAAG,CAACJ,KAAK,EAAEC,MAAM,CAAC;EAClC;EACA,OAAOE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,EAAEC,MAAM,CAAC,EAAEC,MAAM,CAAC;AACpD;AAEA,SAASH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}