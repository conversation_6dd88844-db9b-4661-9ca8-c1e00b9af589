{"ast": null, "code": "export { default as useOnMount } from '@mui/utils/useOnMount';", "map": {"version": 3, "names": ["default", "useOnMount"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useOnMount.js"], "sourcesContent": ["export { default as useOnMount } from '@mui/utils/useOnMount';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,UAAU,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}