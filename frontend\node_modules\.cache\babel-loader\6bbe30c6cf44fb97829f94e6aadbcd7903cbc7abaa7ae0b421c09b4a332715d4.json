{"ast": null, "code": "import { isUnsafeProperty } from '../_internal/isUnsafeProperty.mjs';\nimport { isPlainObject } from '../predicate/isPlainObject.mjs';\nfunction merge(target, source) {\n  const sourceKeys = Object.keys(source);\n  for (let i = 0; i < sourceKeys.length; i++) {\n    const key = sourceKeys[i];\n    if (isUnsafeProperty(key)) {\n      continue;\n    }\n    const sourceValue = source[key];\n    const targetValue = target[key];\n    if (Array.isArray(sourceValue)) {\n      if (Array.isArray(targetValue)) {\n        target[key] = merge(targetValue, sourceValue);\n      } else {\n        target[key] = merge([], sourceValue);\n      }\n    } else if (isPlainObject(sourceValue)) {\n      if (isPlainObject(targetValue)) {\n        target[key] = merge(targetValue, sourceValue);\n      } else {\n        target[key] = merge({}, sourceValue);\n      }\n    } else if (targetValue === undefined || sourceValue !== undefined) {\n      target[key] = sourceValue;\n    }\n  }\n  return target;\n}\nexport { merge };", "map": {"version": 3, "names": ["isUnsafeProperty", "isPlainObject", "merge", "target", "source", "sourceKeys", "Object", "keys", "i", "length", "key", "sourceValue", "targetValue", "Array", "isArray", "undefined"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/object/merge.mjs"], "sourcesContent": ["import { isUnsafeProperty } from '../_internal/isUnsafeProperty.mjs';\nimport { isPlainObject } from '../predicate/isPlainObject.mjs';\n\nfunction merge(target, source) {\n    const sourceKeys = Object.keys(source);\n    for (let i = 0; i < sourceKeys.length; i++) {\n        const key = sourceKeys[i];\n        if (isUnsafeProperty(key)) {\n            continue;\n        }\n        const sourceValue = source[key];\n        const targetValue = target[key];\n        if (Array.isArray(sourceValue)) {\n            if (Array.isArray(targetValue)) {\n                target[key] = merge(targetValue, sourceValue);\n            }\n            else {\n                target[key] = merge([], sourceValue);\n            }\n        }\n        else if (isPlainObject(sourceValue)) {\n            if (isPlainObject(targetValue)) {\n                target[key] = merge(targetValue, sourceValue);\n            }\n            else {\n                target[key] = merge({}, sourceValue);\n            }\n        }\n        else if (targetValue === undefined || sourceValue !== undefined) {\n            target[key] = sourceValue;\n        }\n    }\n    return target;\n}\n\nexport { merge };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,aAAa,QAAQ,gCAAgC;AAE9D,SAASC,KAAKA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC3B,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC;EACtC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,MAAME,GAAG,GAAGL,UAAU,CAACG,CAAC,CAAC;IACzB,IAAIR,gBAAgB,CAACU,GAAG,CAAC,EAAE;MACvB;IACJ;IACA,MAAMC,WAAW,GAAGP,MAAM,CAACM,GAAG,CAAC;IAC/B,MAAME,WAAW,GAAGT,MAAM,CAACO,GAAG,CAAC;IAC/B,IAAIG,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;MAC5B,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;QAC5BT,MAAM,CAACO,GAAG,CAAC,GAAGR,KAAK,CAACU,WAAW,EAAED,WAAW,CAAC;MACjD,CAAC,MACI;QACDR,MAAM,CAACO,GAAG,CAAC,GAAGR,KAAK,CAAC,EAAE,EAAES,WAAW,CAAC;MACxC;IACJ,CAAC,MACI,IAAIV,aAAa,CAACU,WAAW,CAAC,EAAE;MACjC,IAAIV,aAAa,CAACW,WAAW,CAAC,EAAE;QAC5BT,MAAM,CAACO,GAAG,CAAC,GAAGR,KAAK,CAACU,WAAW,EAAED,WAAW,CAAC;MACjD,CAAC,MACI;QACDR,MAAM,CAACO,GAAG,CAAC,GAAGR,KAAK,CAAC,CAAC,CAAC,EAAES,WAAW,CAAC;MACxC;IACJ,CAAC,MACI,IAAIC,WAAW,KAAKG,SAAS,IAAIJ,WAAW,KAAKI,SAAS,EAAE;MAC7DZ,MAAM,CAACO,GAAG,CAAC,GAAGC,WAAW;IAC7B;EACJ;EACA,OAAOR,MAAM;AACjB;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}