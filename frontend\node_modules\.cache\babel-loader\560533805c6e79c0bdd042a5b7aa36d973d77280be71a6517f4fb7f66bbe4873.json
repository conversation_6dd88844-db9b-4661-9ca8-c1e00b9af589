{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isSymbol = require('../predicate/isSymbol.js');\nfunction toNumber(value) {\n  if (isSymbol.isSymbol(value)) {\n    return NaN;\n  }\n  return Number(value);\n}\nexports.toNumber = toNumber;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isSymbol", "require", "toNumber", "NaN", "Number"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/compat/util/toNumber.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isSymbol = require('../predicate/isSymbol.js');\n\nfunction toNumber(value) {\n    if (isSymbol.isSymbol(value)) {\n        return NaN;\n    }\n    return Number(value);\n}\n\nexports.toNumber = toNumber;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AAEpD,SAASC,QAAQA,CAACH,KAAK,EAAE;EACrB,IAAIC,QAAQ,CAACA,QAAQ,CAACD,KAAK,CAAC,EAAE;IAC1B,OAAOI,GAAG;EACd;EACA,OAAOC,MAAM,CAACL,KAAK,CAAC;AACxB;AAEAH,OAAO,CAACM,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}