{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridRowNodeSelector } from \"./gridRowsSelector.js\";\nexport const GRID_ROOT_GROUP_ID = `auto-generated-group-node-root`;\nexport const GRID_ID_AUTOGENERATED = Symbol('mui.id_autogenerated');\nexport const buildRootGroup = () => ({\n  type: 'group',\n  id: GRID_ROOT_GROUP_ID,\n  depth: -1,\n  groupingField: null,\n  groupingKey: null,\n  isAutoGenerated: true,\n  children: [],\n  childrenFromPath: {},\n  childrenExpanded: true,\n  parent: null\n});\n\n/**\n * A helper function to check if the id provided is valid.\n * @param {GridRowId} id Id as [[GridRowId]].\n * @param {GridRowModel | Partial<GridRowModel>} row Row as [[GridRowModel]].\n * @param {string} detailErrorMessage A custom error message to display for invalid IDs\n */\nexport function checkGridRowIdIsValid(id, row, detailErrorMessage = 'A row was provided without id in the rows prop:') {\n  if (id == null) {\n    throw new Error(['MUI X: The Data Grid component requires all rows to have a unique `id` property.', 'Alternatively, you can use the `getRowId` prop to specify a custom id for each row.', detailErrorMessage, JSON.stringify(row)].join('\\n'));\n  }\n}\nexport const getRowIdFromRowModel = (rowModel, getRowId, detailErrorMessage) => {\n  const id = getRowId ? getRowId(rowModel) : rowModel.id;\n  checkGridRowIdIsValid(id, rowModel, detailErrorMessage);\n  return id;\n};\nexport const getRowValue = (row, colDef, apiRef) => {\n  const field = colDef.field;\n  if (!colDef || !colDef.valueGetter) {\n    return row[field];\n  }\n  const value = row[colDef.field];\n  return colDef.valueGetter(value, row, colDef, apiRef);\n};\nexport const createRowsInternalCache = ({\n  rows,\n  getRowId,\n  loading,\n  rowCount\n}) => {\n  const updates = {\n    type: 'full',\n    rows: []\n  };\n  const dataRowIdToModelLookup = {};\n  for (let i = 0; i < rows.length; i += 1) {\n    const model = rows[i];\n    const id = getRowIdFromRowModel(model, getRowId);\n    dataRowIdToModelLookup[id] = model;\n    updates.rows.push(id);\n  }\n  return {\n    rowsBeforePartialUpdates: rows,\n    loadingPropBeforePartialUpdates: loading,\n    rowCountPropBeforePartialUpdates: rowCount,\n    updates,\n    dataRowIdToModelLookup\n  };\n};\nexport const getTopLevelRowCount = ({\n  tree,\n  rowCountProp = 0\n}) => {\n  const rootGroupNode = tree[GRID_ROOT_GROUP_ID];\n  return Math.max(rowCountProp, rootGroupNode.children.length + (rootGroupNode.footerId == null ? 0 : 1));\n};\nexport const getRowsStateFromCache = ({\n  apiRef,\n  rowCountProp = 0,\n  loadingProp,\n  previousTree,\n  previousTreeDepths,\n  previousGroupsToFetch\n}) => {\n  const cache = apiRef.current.caches.rows;\n\n  // 1. Apply the \"rowTreeCreation\" family processing.\n  const {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    groupingName,\n    groupsToFetch = []\n  } = apiRef.current.applyStrategyProcessor('rowTreeCreation', {\n    previousTree,\n    previousTreeDepths,\n    updates: cache.updates,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup,\n    previousGroupsToFetch\n  });\n\n  // 2. Apply the \"hydrateRows\" pipe-processing.\n  const groupingParamsWithHydrateRows = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 3. Reset the cache updates\n  apiRef.current.caches.rows.updates = {\n    type: 'partial',\n    actions: {\n      insert: [],\n      modify: [],\n      remove: []\n    },\n    idToActionLookup: {}\n  };\n  return _extends({}, groupingParamsWithHydrateRows, {\n    totalRowCount: Math.max(rowCountProp, groupingParamsWithHydrateRows.dataRowIds.length),\n    totalTopLevelRowCount: getTopLevelRowCount({\n      tree: groupingParamsWithHydrateRows.tree,\n      rowCountProp\n    }),\n    groupingName,\n    loading: loadingProp,\n    groupsToFetch\n  });\n};\nexport const isAutogeneratedRow = row => GRID_ID_AUTOGENERATED in row;\nexport const isAutogeneratedRowNode = rowNode => rowNode.type === 'skeletonRow' || rowNode.type === 'footer' || rowNode.type === 'group' && rowNode.isAutoGenerated || rowNode.type === 'pinnedRow' && rowNode.isAutoGenerated;\nexport const getTreeNodeDescendants = (tree, parentId, skipAutoGeneratedRows, directChildrenOnly) => {\n  const node = tree[parentId];\n  if (node.type !== 'group') {\n    return [];\n  }\n  const validDescendants = [];\n  for (let i = 0; i < node.children.length; i += 1) {\n    const child = node.children[i];\n    if (!skipAutoGeneratedRows || !isAutogeneratedRowNode(tree[child])) {\n      validDescendants.push(child);\n    }\n    if (directChildrenOnly) {\n      continue;\n    }\n    const childDescendants = getTreeNodeDescendants(tree, child, skipAutoGeneratedRows, directChildrenOnly);\n    for (let j = 0; j < childDescendants.length; j += 1) {\n      validDescendants.push(childDescendants[j]);\n    }\n  }\n  if (!skipAutoGeneratedRows && node.footerId != null) {\n    validDescendants.push(node.footerId);\n  }\n  return validDescendants;\n};\nexport const updateCacheWithNewRows = ({\n  previousCache,\n  getRowId,\n  updates,\n  groupKeys\n}) => {\n  if (previousCache.updates.type === 'full') {\n    throw new Error('MUI X: Unable to prepare a partial update if a full update is not applied yet.');\n  }\n\n  // Remove duplicate updates.\n  // A server can batch updates, and send several updates for the same row in one fn call.\n  const uniqueUpdates = new Map();\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    if (uniqueUpdates.has(id)) {\n      uniqueUpdates.set(id, _extends({}, uniqueUpdates.get(id), update));\n    } else {\n      uniqueUpdates.set(id, update);\n    }\n  });\n  const partialUpdates = {\n    type: 'partial',\n    actions: {\n      insert: [...(previousCache.updates.actions.insert ?? [])],\n      modify: [...(previousCache.updates.actions.modify ?? [])],\n      remove: [...(previousCache.updates.actions.remove ?? [])]\n    },\n    idToActionLookup: _extends({}, previousCache.updates.idToActionLookup),\n    groupKeys\n  };\n  const dataRowIdToModelLookup = _extends({}, previousCache.dataRowIdToModelLookup);\n  const alreadyAppliedActionsToRemove = {\n    insert: {},\n    modify: {},\n    remove: {}\n  };\n\n  // Depending on the action already applied to the data row,\n  // We might want drop the already-applied-update.\n  // For instance:\n  // - if you delete then insert, then you don't want to apply the deletion in the tree.\n  // - if you insert, then modify, then you just want to apply the insertion in the tree.\n  uniqueUpdates.forEach((partialRow, id) => {\n    const actionAlreadyAppliedToRow = partialUpdates.idToActionLookup[id];\n\n    // Action === \"delete\"\n    // eslint-disable-next-line no-underscore-dangle\n    if (partialRow._action === 'delete') {\n      // If the data row has been removed since the last state update,\n      // Then do nothing.\n      if (actionAlreadyAppliedToRow === 'remove' || !dataRowIdToModelLookup[id]) {\n        return;\n      }\n\n      // If the data row has been inserted / modified since the last state update,\n      // Then drop this \"insert\" / \"modify\" update.\n      if (actionAlreadyAppliedToRow != null) {\n        alreadyAppliedActionsToRemove[actionAlreadyAppliedToRow][id] = true;\n      }\n\n      // Remove the data row from the lookups and add it to the \"delete\" update.\n      partialUpdates.actions.remove.push(id);\n      delete dataRowIdToModelLookup[id];\n      return;\n    }\n    const oldRow = dataRowIdToModelLookup[id];\n\n    // Action === \"modify\"\n    if (oldRow) {\n      // If the data row has been removed since the last state update,\n      // Then drop this \"remove\" update and add it to the \"modify\" update instead.\n      if (actionAlreadyAppliedToRow === 'remove') {\n        alreadyAppliedActionsToRemove.remove[id] = true;\n        partialUpdates.actions.modify.push(id);\n      }\n      // If the date has not been inserted / modified since the last state update,\n      // Then add it to the \"modify\" update (if it has been inserted it should just remain \"inserted\").\n      else if (actionAlreadyAppliedToRow == null) {\n        partialUpdates.actions.modify.push(id);\n      }\n\n      // Update the data row lookups.\n      dataRowIdToModelLookup[id] = _extends({}, oldRow, partialRow);\n      return;\n    }\n\n    // Action === \"insert\"\n    // If the data row has been removed since the last state update,\n    // Then drop the \"remove\" update and add it to the \"insert\" update instead.\n    if (actionAlreadyAppliedToRow === 'remove') {\n      alreadyAppliedActionsToRemove.remove[id] = true;\n      partialUpdates.actions.insert.push(id);\n    }\n    // If the data row has not been inserted since the last state update,\n    // Then add it to the \"insert\" update.\n    // `actionAlreadyAppliedToRow` can't be equal to \"modify\", otherwise we would have an `oldRow` above.\n    else if (actionAlreadyAppliedToRow == null) {\n      partialUpdates.actions.insert.push(id);\n    }\n\n    // Update the data row lookups.\n    dataRowIdToModelLookup[id] = partialRow;\n  });\n  const actionTypeWithActionsToRemove = Object.keys(alreadyAppliedActionsToRemove);\n  for (let i = 0; i < actionTypeWithActionsToRemove.length; i += 1) {\n    const actionType = actionTypeWithActionsToRemove[i];\n    const idsToRemove = alreadyAppliedActionsToRemove[actionType];\n    if (Object.keys(idsToRemove).length > 0) {\n      partialUpdates.actions[actionType] = partialUpdates.actions[actionType].filter(id => !idsToRemove[id]);\n    }\n  }\n  return {\n    dataRowIdToModelLookup,\n    updates: partialUpdates,\n    rowsBeforePartialUpdates: previousCache.rowsBeforePartialUpdates,\n    loadingPropBeforePartialUpdates: previousCache.loadingPropBeforePartialUpdates,\n    rowCountPropBeforePartialUpdates: previousCache.rowCountPropBeforePartialUpdates\n  };\n};\nexport const minimalContentHeight = 'var(--DataGrid-overlayHeight, calc(var(--height) * 2))';\nexport function computeRowsUpdates(apiRef, updates, getRowId) {\n  const nonPinnedRowsUpdates = [];\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (rowNode?.type === 'pinnedRow') {\n      // @ts-ignore because otherwise `release:build` doesn't work\n      const pinnedRowsCache = apiRef.current.caches.pinnedRows;\n      const prevModel = pinnedRowsCache.idLookup[id];\n      if (prevModel) {\n        pinnedRowsCache.idLookup[id] = _extends({}, prevModel, update);\n      }\n    } else {\n      nonPinnedRowsUpdates.push(update);\n    }\n  });\n  return nonPinnedRowsUpdates;\n}\nlet warnedOnceInvalidRowHeight = false;\nexport const getValidRowHeight = (rowHeightProp, defaultRowHeight, warningMessage) => {\n  if (typeof rowHeightProp === 'number' && rowHeightProp > 0) {\n    return rowHeightProp;\n  }\n  if (process.env.NODE_ENV !== 'production' && !warnedOnceInvalidRowHeight && typeof rowHeightProp !== 'undefined' && rowHeightProp !== null) {\n    console.warn(warningMessage);\n    warnedOnceInvalidRowHeight = true;\n  }\n  return defaultRowHeight;\n};\nexport const rowHeightWarning = [`MUI X: The \\`rowHeight\\` prop should be a number greater than 0.`, `The default value will be used instead.`].join('\\n');\nexport const getRowHeightWarning = [`MUI X: The \\`getRowHeight\\` prop should return a number greater than 0 or 'auto'.`, `The default value will be used instead.`].join('\\n');", "map": {"version": 3, "names": ["_extends", "gridRowNodeSelector", "GRID_ROOT_GROUP_ID", "GRID_ID_AUTOGENERATED", "Symbol", "buildRootGroup", "type", "id", "depth", "groupingField", "grouping<PERSON>ey", "isAutoGenerated", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childrenExpanded", "parent", "checkGridRowIdIsValid", "row", "detailErrorMessage", "Error", "JSON", "stringify", "join", "getRowIdFromRowModel", "rowModel", "getRowId", "getRowValue", "colDef", "apiRef", "field", "valueGetter", "value", "createRowsInternalCache", "rows", "loading", "rowCount", "updates", "dataRowIdToModelLookup", "i", "length", "model", "push", "rowsBeforePartialUpdates", "loadingPropBeforePartialUpdates", "rowCountPropBeforePartialUpdates", "getTopLevelRowCount", "tree", "rowCountProp", "rootGroupNode", "Math", "max", "footerId", "getRowsStateFromCache", "loadingProp", "previousTree", "previousTreeDepths", "previousGroupsToFetch", "cache", "current", "caches", "unProcessedTree", "treeDepths", "unProcessedTreeDepths", "dataRowIds", "unProcessedDataRowIds", "groupingName", "groupsToFetch", "applyStrategyProcessor", "groupingParamsWithHydrateRows", "unstable_applyPipeProcessors", "actions", "insert", "modify", "remove", "idToActionLookup", "totalRowCount", "totalTopLevelRowCount", "isAutogeneratedRow", "isAutogeneratedRowNode", "rowNode", "getTreeNodeDescendants", "parentId", "skipAutoGeneratedRows", "directChildrenOnly", "node", "validDescendants", "child", "childDescendants", "j", "updateCacheWithNewRows", "previousCache", "groupKeys", "uniqueUpdates", "Map", "for<PERSON>ach", "update", "has", "set", "get", "partialUpdates", "alreadyAppliedActionsToRemove", "partialRow", "actionAlreadyAppliedToRow", "_action", "oldRow", "actionTypeWithActionsToRemove", "Object", "keys", "actionType", "idsToRemove", "filter", "minimalContentHeight", "computeRowsUpdates", "nonPinnedRowsUpdates", "pinnedRowsCache", "pinnedRows", "prevModel", "idLookup", "warnedOnceInvalidRowHeight", "getValidRowHeight", "rowHeightProp", "defaultRowHeight", "warningMessage", "process", "env", "NODE_ENV", "console", "warn", "rowHeightWarning", "getRowHeightWarning"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rows/gridRowsUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { gridRowNodeSelector } from \"./gridRowsSelector.js\";\nexport const GRID_ROOT_GROUP_ID = `auto-generated-group-node-root`;\nexport const GRID_ID_AUTOGENERATED = Symbol('mui.id_autogenerated');\nexport const buildRootGroup = () => ({\n  type: 'group',\n  id: GRID_ROOT_GROUP_ID,\n  depth: -1,\n  groupingField: null,\n  groupingKey: null,\n  isAutoGenerated: true,\n  children: [],\n  childrenFromPath: {},\n  childrenExpanded: true,\n  parent: null\n});\n\n/**\n * A helper function to check if the id provided is valid.\n * @param {GridRowId} id Id as [[GridRowId]].\n * @param {GridRowModel | Partial<GridRowModel>} row Row as [[GridRowModel]].\n * @param {string} detailErrorMessage A custom error message to display for invalid IDs\n */\nexport function checkGridRowIdIsValid(id, row, detailErrorMessage = 'A row was provided without id in the rows prop:') {\n  if (id == null) {\n    throw new Error(['MUI X: The Data Grid component requires all rows to have a unique `id` property.', 'Alternatively, you can use the `getRowId` prop to specify a custom id for each row.', detailErrorMessage, JSON.stringify(row)].join('\\n'));\n  }\n}\nexport const getRowIdFromRowModel = (rowModel, getRowId, detailErrorMessage) => {\n  const id = getRowId ? getRowId(rowModel) : rowModel.id;\n  checkGridRowIdIsValid(id, rowModel, detailErrorMessage);\n  return id;\n};\nexport const getRowValue = (row, colDef, apiRef) => {\n  const field = colDef.field;\n  if (!colDef || !colDef.valueGetter) {\n    return row[field];\n  }\n  const value = row[colDef.field];\n  return colDef.valueGetter(value, row, colDef, apiRef);\n};\nexport const createRowsInternalCache = ({\n  rows,\n  getRowId,\n  loading,\n  rowCount\n}) => {\n  const updates = {\n    type: 'full',\n    rows: []\n  };\n  const dataRowIdToModelLookup = {};\n  for (let i = 0; i < rows.length; i += 1) {\n    const model = rows[i];\n    const id = getRowIdFromRowModel(model, getRowId);\n    dataRowIdToModelLookup[id] = model;\n    updates.rows.push(id);\n  }\n  return {\n    rowsBeforePartialUpdates: rows,\n    loadingPropBeforePartialUpdates: loading,\n    rowCountPropBeforePartialUpdates: rowCount,\n    updates,\n    dataRowIdToModelLookup\n  };\n};\nexport const getTopLevelRowCount = ({\n  tree,\n  rowCountProp = 0\n}) => {\n  const rootGroupNode = tree[GRID_ROOT_GROUP_ID];\n  return Math.max(rowCountProp, rootGroupNode.children.length + (rootGroupNode.footerId == null ? 0 : 1));\n};\nexport const getRowsStateFromCache = ({\n  apiRef,\n  rowCountProp = 0,\n  loadingProp,\n  previousTree,\n  previousTreeDepths,\n  previousGroupsToFetch\n}) => {\n  const cache = apiRef.current.caches.rows;\n\n  // 1. Apply the \"rowTreeCreation\" family processing.\n  const {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    groupingName,\n    groupsToFetch = []\n  } = apiRef.current.applyStrategyProcessor('rowTreeCreation', {\n    previousTree,\n    previousTreeDepths,\n    updates: cache.updates,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup,\n    previousGroupsToFetch\n  });\n\n  // 2. Apply the \"hydrateRows\" pipe-processing.\n  const groupingParamsWithHydrateRows = apiRef.current.unstable_applyPipeProcessors('hydrateRows', {\n    tree: unProcessedTree,\n    treeDepths: unProcessedTreeDepths,\n    dataRowIds: unProcessedDataRowIds,\n    dataRowIdToModelLookup: cache.dataRowIdToModelLookup\n  });\n\n  // 3. Reset the cache updates\n  apiRef.current.caches.rows.updates = {\n    type: 'partial',\n    actions: {\n      insert: [],\n      modify: [],\n      remove: []\n    },\n    idToActionLookup: {}\n  };\n  return _extends({}, groupingParamsWithHydrateRows, {\n    totalRowCount: Math.max(rowCountProp, groupingParamsWithHydrateRows.dataRowIds.length),\n    totalTopLevelRowCount: getTopLevelRowCount({\n      tree: groupingParamsWithHydrateRows.tree,\n      rowCountProp\n    }),\n    groupingName,\n    loading: loadingProp,\n    groupsToFetch\n  });\n};\nexport const isAutogeneratedRow = row => GRID_ID_AUTOGENERATED in row;\nexport const isAutogeneratedRowNode = rowNode => rowNode.type === 'skeletonRow' || rowNode.type === 'footer' || rowNode.type === 'group' && rowNode.isAutoGenerated || rowNode.type === 'pinnedRow' && rowNode.isAutoGenerated;\nexport const getTreeNodeDescendants = (tree, parentId, skipAutoGeneratedRows, directChildrenOnly) => {\n  const node = tree[parentId];\n  if (node.type !== 'group') {\n    return [];\n  }\n  const validDescendants = [];\n  for (let i = 0; i < node.children.length; i += 1) {\n    const child = node.children[i];\n    if (!skipAutoGeneratedRows || !isAutogeneratedRowNode(tree[child])) {\n      validDescendants.push(child);\n    }\n    if (directChildrenOnly) {\n      continue;\n    }\n    const childDescendants = getTreeNodeDescendants(tree, child, skipAutoGeneratedRows, directChildrenOnly);\n    for (let j = 0; j < childDescendants.length; j += 1) {\n      validDescendants.push(childDescendants[j]);\n    }\n  }\n  if (!skipAutoGeneratedRows && node.footerId != null) {\n    validDescendants.push(node.footerId);\n  }\n  return validDescendants;\n};\nexport const updateCacheWithNewRows = ({\n  previousCache,\n  getRowId,\n  updates,\n  groupKeys\n}) => {\n  if (previousCache.updates.type === 'full') {\n    throw new Error('MUI X: Unable to prepare a partial update if a full update is not applied yet.');\n  }\n\n  // Remove duplicate updates.\n  // A server can batch updates, and send several updates for the same row in one fn call.\n  const uniqueUpdates = new Map();\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    if (uniqueUpdates.has(id)) {\n      uniqueUpdates.set(id, _extends({}, uniqueUpdates.get(id), update));\n    } else {\n      uniqueUpdates.set(id, update);\n    }\n  });\n  const partialUpdates = {\n    type: 'partial',\n    actions: {\n      insert: [...(previousCache.updates.actions.insert ?? [])],\n      modify: [...(previousCache.updates.actions.modify ?? [])],\n      remove: [...(previousCache.updates.actions.remove ?? [])]\n    },\n    idToActionLookup: _extends({}, previousCache.updates.idToActionLookup),\n    groupKeys\n  };\n  const dataRowIdToModelLookup = _extends({}, previousCache.dataRowIdToModelLookup);\n  const alreadyAppliedActionsToRemove = {\n    insert: {},\n    modify: {},\n    remove: {}\n  };\n\n  // Depending on the action already applied to the data row,\n  // We might want drop the already-applied-update.\n  // For instance:\n  // - if you delete then insert, then you don't want to apply the deletion in the tree.\n  // - if you insert, then modify, then you just want to apply the insertion in the tree.\n  uniqueUpdates.forEach((partialRow, id) => {\n    const actionAlreadyAppliedToRow = partialUpdates.idToActionLookup[id];\n\n    // Action === \"delete\"\n    // eslint-disable-next-line no-underscore-dangle\n    if (partialRow._action === 'delete') {\n      // If the data row has been removed since the last state update,\n      // Then do nothing.\n      if (actionAlreadyAppliedToRow === 'remove' || !dataRowIdToModelLookup[id]) {\n        return;\n      }\n\n      // If the data row has been inserted / modified since the last state update,\n      // Then drop this \"insert\" / \"modify\" update.\n      if (actionAlreadyAppliedToRow != null) {\n        alreadyAppliedActionsToRemove[actionAlreadyAppliedToRow][id] = true;\n      }\n\n      // Remove the data row from the lookups and add it to the \"delete\" update.\n      partialUpdates.actions.remove.push(id);\n      delete dataRowIdToModelLookup[id];\n      return;\n    }\n    const oldRow = dataRowIdToModelLookup[id];\n\n    // Action === \"modify\"\n    if (oldRow) {\n      // If the data row has been removed since the last state update,\n      // Then drop this \"remove\" update and add it to the \"modify\" update instead.\n      if (actionAlreadyAppliedToRow === 'remove') {\n        alreadyAppliedActionsToRemove.remove[id] = true;\n        partialUpdates.actions.modify.push(id);\n      }\n      // If the date has not been inserted / modified since the last state update,\n      // Then add it to the \"modify\" update (if it has been inserted it should just remain \"inserted\").\n      else if (actionAlreadyAppliedToRow == null) {\n        partialUpdates.actions.modify.push(id);\n      }\n\n      // Update the data row lookups.\n      dataRowIdToModelLookup[id] = _extends({}, oldRow, partialRow);\n      return;\n    }\n\n    // Action === \"insert\"\n    // If the data row has been removed since the last state update,\n    // Then drop the \"remove\" update and add it to the \"insert\" update instead.\n    if (actionAlreadyAppliedToRow === 'remove') {\n      alreadyAppliedActionsToRemove.remove[id] = true;\n      partialUpdates.actions.insert.push(id);\n    }\n    // If the data row has not been inserted since the last state update,\n    // Then add it to the \"insert\" update.\n    // `actionAlreadyAppliedToRow` can't be equal to \"modify\", otherwise we would have an `oldRow` above.\n    else if (actionAlreadyAppliedToRow == null) {\n      partialUpdates.actions.insert.push(id);\n    }\n\n    // Update the data row lookups.\n    dataRowIdToModelLookup[id] = partialRow;\n  });\n  const actionTypeWithActionsToRemove = Object.keys(alreadyAppliedActionsToRemove);\n  for (let i = 0; i < actionTypeWithActionsToRemove.length; i += 1) {\n    const actionType = actionTypeWithActionsToRemove[i];\n    const idsToRemove = alreadyAppliedActionsToRemove[actionType];\n    if (Object.keys(idsToRemove).length > 0) {\n      partialUpdates.actions[actionType] = partialUpdates.actions[actionType].filter(id => !idsToRemove[id]);\n    }\n  }\n  return {\n    dataRowIdToModelLookup,\n    updates: partialUpdates,\n    rowsBeforePartialUpdates: previousCache.rowsBeforePartialUpdates,\n    loadingPropBeforePartialUpdates: previousCache.loadingPropBeforePartialUpdates,\n    rowCountPropBeforePartialUpdates: previousCache.rowCountPropBeforePartialUpdates\n  };\n};\nexport const minimalContentHeight = 'var(--DataGrid-overlayHeight, calc(var(--height) * 2))';\nexport function computeRowsUpdates(apiRef, updates, getRowId) {\n  const nonPinnedRowsUpdates = [];\n  updates.forEach(update => {\n    const id = getRowIdFromRowModel(update, getRowId, 'A row was provided without id when calling updateRows():');\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (rowNode?.type === 'pinnedRow') {\n      // @ts-ignore because otherwise `release:build` doesn't work\n      const pinnedRowsCache = apiRef.current.caches.pinnedRows;\n      const prevModel = pinnedRowsCache.idLookup[id];\n      if (prevModel) {\n        pinnedRowsCache.idLookup[id] = _extends({}, prevModel, update);\n      }\n    } else {\n      nonPinnedRowsUpdates.push(update);\n    }\n  });\n  return nonPinnedRowsUpdates;\n}\nlet warnedOnceInvalidRowHeight = false;\nexport const getValidRowHeight = (rowHeightProp, defaultRowHeight, warningMessage) => {\n  if (typeof rowHeightProp === 'number' && rowHeightProp > 0) {\n    return rowHeightProp;\n  }\n  if (process.env.NODE_ENV !== 'production' && !warnedOnceInvalidRowHeight && typeof rowHeightProp !== 'undefined' && rowHeightProp !== null) {\n    console.warn(warningMessage);\n    warnedOnceInvalidRowHeight = true;\n  }\n  return defaultRowHeight;\n};\nexport const rowHeightWarning = [`MUI X: The \\`rowHeight\\` prop should be a number greater than 0.`, `The default value will be used instead.`].join('\\n');\nexport const getRowHeightWarning = [`MUI X: The \\`getRowHeight\\` prop should return a number greater than 0 or 'auto'.`, `The default value will be used instead.`].join('\\n');"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,OAAO,MAAMC,kBAAkB,GAAG,gCAAgC;AAClE,OAAO,MAAMC,qBAAqB,GAAGC,MAAM,CAAC,sBAAsB,CAAC;AACnE,OAAO,MAAMC,cAAc,GAAGA,CAAA,MAAO;EACnCC,IAAI,EAAE,OAAO;EACbC,EAAE,EAAEL,kBAAkB;EACtBM,KAAK,EAAE,CAAC,CAAC;EACTC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE,IAAI;EACjBC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,EAAE;EACZC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,MAAM,EAAE;AACV,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACT,EAAE,EAAEU,GAAG,EAAEC,kBAAkB,GAAG,iDAAiD,EAAE;EACrH,IAAIX,EAAE,IAAI,IAAI,EAAE;IACd,MAAM,IAAIY,KAAK,CAAC,CAAC,kFAAkF,EAAE,qFAAqF,EAAED,kBAAkB,EAAEE,IAAI,CAACC,SAAS,CAACJ,GAAG,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,CAAC;EAClP;AACF;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEP,kBAAkB,KAAK;EAC9E,MAAMX,EAAE,GAAGkB,QAAQ,GAAGA,QAAQ,CAACD,QAAQ,CAAC,GAAGA,QAAQ,CAACjB,EAAE;EACtDS,qBAAqB,CAACT,EAAE,EAAEiB,QAAQ,EAAEN,kBAAkB,CAAC;EACvD,OAAOX,EAAE;AACX,CAAC;AACD,OAAO,MAAMmB,WAAW,GAAGA,CAACT,GAAG,EAAEU,MAAM,EAAEC,MAAM,KAAK;EAClD,MAAMC,KAAK,GAAGF,MAAM,CAACE,KAAK;EAC1B,IAAI,CAACF,MAAM,IAAI,CAACA,MAAM,CAACG,WAAW,EAAE;IAClC,OAAOb,GAAG,CAACY,KAAK,CAAC;EACnB;EACA,MAAME,KAAK,GAAGd,GAAG,CAACU,MAAM,CAACE,KAAK,CAAC;EAC/B,OAAOF,MAAM,CAACG,WAAW,CAACC,KAAK,EAAEd,GAAG,EAAEU,MAAM,EAAEC,MAAM,CAAC;AACvD,CAAC;AACD,OAAO,MAAMI,uBAAuB,GAAGA,CAAC;EACtCC,IAAI;EACJR,QAAQ;EACRS,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,MAAMC,OAAO,GAAG;IACd9B,IAAI,EAAE,MAAM;IACZ2B,IAAI,EAAE;EACR,CAAC;EACD,MAAMI,sBAAsB,GAAG,CAAC,CAAC;EACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACM,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACvC,MAAME,KAAK,GAAGP,IAAI,CAACK,CAAC,CAAC;IACrB,MAAM/B,EAAE,GAAGgB,oBAAoB,CAACiB,KAAK,EAAEf,QAAQ,CAAC;IAChDY,sBAAsB,CAAC9B,EAAE,CAAC,GAAGiC,KAAK;IAClCJ,OAAO,CAACH,IAAI,CAACQ,IAAI,CAAClC,EAAE,CAAC;EACvB;EACA,OAAO;IACLmC,wBAAwB,EAAET,IAAI;IAC9BU,+BAA+B,EAAET,OAAO;IACxCU,gCAAgC,EAAET,QAAQ;IAC1CC,OAAO;IACPC;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMQ,mBAAmB,GAAGA,CAAC;EAClCC,IAAI;EACJC,YAAY,GAAG;AACjB,CAAC,KAAK;EACJ,MAAMC,aAAa,GAAGF,IAAI,CAAC5C,kBAAkB,CAAC;EAC9C,OAAO+C,IAAI,CAACC,GAAG,CAACH,YAAY,EAAEC,aAAa,CAACpC,QAAQ,CAAC2B,MAAM,IAAIS,aAAa,CAACG,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACzG,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGA,CAAC;EACpCxB,MAAM;EACNmB,YAAY,GAAG,CAAC;EAChBM,WAAW;EACXC,YAAY;EACZC,kBAAkB;EAClBC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAG7B,MAAM,CAAC8B,OAAO,CAACC,MAAM,CAAC1B,IAAI;;EAExC;EACA,MAAM;IACJa,IAAI,EAAEc,eAAe;IACrBC,UAAU,EAAEC,qBAAqB;IACjCC,UAAU,EAAEC,qBAAqB;IACjCC,YAAY;IACZC,aAAa,GAAG;EAClB,CAAC,GAAGtC,MAAM,CAAC8B,OAAO,CAACS,sBAAsB,CAAC,iBAAiB,EAAE;IAC3Db,YAAY;IACZC,kBAAkB;IAClBnB,OAAO,EAAEqB,KAAK,CAACrB,OAAO;IACtBC,sBAAsB,EAAEoB,KAAK,CAACpB,sBAAsB;IACpDmB;EACF,CAAC,CAAC;;EAEF;EACA,MAAMY,6BAA6B,GAAGxC,MAAM,CAAC8B,OAAO,CAACW,4BAA4B,CAAC,aAAa,EAAE;IAC/FvB,IAAI,EAAEc,eAAe;IACrBC,UAAU,EAAEC,qBAAqB;IACjCC,UAAU,EAAEC,qBAAqB;IACjC3B,sBAAsB,EAAEoB,KAAK,CAACpB;EAChC,CAAC,CAAC;;EAEF;EACAT,MAAM,CAAC8B,OAAO,CAACC,MAAM,CAAC1B,IAAI,CAACG,OAAO,GAAG;IACnC9B,IAAI,EAAE,SAAS;IACfgE,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;IACV,CAAC;IACDC,gBAAgB,EAAE,CAAC;EACrB,CAAC;EACD,OAAO1E,QAAQ,CAAC,CAAC,CAAC,EAAEoE,6BAA6B,EAAE;IACjDO,aAAa,EAAE1B,IAAI,CAACC,GAAG,CAACH,YAAY,EAAEqB,6BAA6B,CAACL,UAAU,CAACxB,MAAM,CAAC;IACtFqC,qBAAqB,EAAE/B,mBAAmB,CAAC;MACzCC,IAAI,EAAEsB,6BAA6B,CAACtB,IAAI;MACxCC;IACF,CAAC,CAAC;IACFkB,YAAY;IACZ/B,OAAO,EAAEmB,WAAW;IACpBa;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMW,kBAAkB,GAAG5D,GAAG,IAAId,qBAAqB,IAAIc,GAAG;AACrE,OAAO,MAAM6D,sBAAsB,GAAGC,OAAO,IAAIA,OAAO,CAACzE,IAAI,KAAK,aAAa,IAAIyE,OAAO,CAACzE,IAAI,KAAK,QAAQ,IAAIyE,OAAO,CAACzE,IAAI,KAAK,OAAO,IAAIyE,OAAO,CAACpE,eAAe,IAAIoE,OAAO,CAACzE,IAAI,KAAK,WAAW,IAAIyE,OAAO,CAACpE,eAAe;AAC9N,OAAO,MAAMqE,sBAAsB,GAAGA,CAAClC,IAAI,EAAEmC,QAAQ,EAAEC,qBAAqB,EAAEC,kBAAkB,KAAK;EACnG,MAAMC,IAAI,GAAGtC,IAAI,CAACmC,QAAQ,CAAC;EAC3B,IAAIG,IAAI,CAAC9E,IAAI,KAAK,OAAO,EAAE;IACzB,OAAO,EAAE;EACX;EACA,MAAM+E,gBAAgB,GAAG,EAAE;EAC3B,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,IAAI,CAACxE,QAAQ,CAAC2B,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAChD,MAAMgD,KAAK,GAAGF,IAAI,CAACxE,QAAQ,CAAC0B,CAAC,CAAC;IAC9B,IAAI,CAAC4C,qBAAqB,IAAI,CAACJ,sBAAsB,CAAChC,IAAI,CAACwC,KAAK,CAAC,CAAC,EAAE;MAClED,gBAAgB,CAAC5C,IAAI,CAAC6C,KAAK,CAAC;IAC9B;IACA,IAAIH,kBAAkB,EAAE;MACtB;IACF;IACA,MAAMI,gBAAgB,GAAGP,sBAAsB,CAAClC,IAAI,EAAEwC,KAAK,EAAEJ,qBAAqB,EAAEC,kBAAkB,CAAC;IACvG,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,gBAAgB,CAAChD,MAAM,EAAEiD,CAAC,IAAI,CAAC,EAAE;MACnDH,gBAAgB,CAAC5C,IAAI,CAAC8C,gBAAgB,CAACC,CAAC,CAAC,CAAC;IAC5C;EACF;EACA,IAAI,CAACN,qBAAqB,IAAIE,IAAI,CAACjC,QAAQ,IAAI,IAAI,EAAE;IACnDkC,gBAAgB,CAAC5C,IAAI,CAAC2C,IAAI,CAACjC,QAAQ,CAAC;EACtC;EACA,OAAOkC,gBAAgB;AACzB,CAAC;AACD,OAAO,MAAMI,sBAAsB,GAAGA,CAAC;EACrCC,aAAa;EACbjE,QAAQ;EACRW,OAAO;EACPuD;AACF,CAAC,KAAK;EACJ,IAAID,aAAa,CAACtD,OAAO,CAAC9B,IAAI,KAAK,MAAM,EAAE;IACzC,MAAM,IAAIa,KAAK,CAAC,gFAAgF,CAAC;EACnG;;EAEA;EACA;EACA,MAAMyE,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/BzD,OAAO,CAAC0D,OAAO,CAACC,MAAM,IAAI;IACxB,MAAMxF,EAAE,GAAGgB,oBAAoB,CAACwE,MAAM,EAAEtE,QAAQ,EAAE,0DAA0D,CAAC;IAC7G,IAAImE,aAAa,CAACI,GAAG,CAACzF,EAAE,CAAC,EAAE;MACzBqF,aAAa,CAACK,GAAG,CAAC1F,EAAE,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAE4F,aAAa,CAACM,GAAG,CAAC3F,EAAE,CAAC,EAAEwF,MAAM,CAAC,CAAC;IACpE,CAAC,MAAM;MACLH,aAAa,CAACK,GAAG,CAAC1F,EAAE,EAAEwF,MAAM,CAAC;IAC/B;EACF,CAAC,CAAC;EACF,MAAMI,cAAc,GAAG;IACrB7F,IAAI,EAAE,SAAS;IACfgE,OAAO,EAAE;MACPC,MAAM,EAAE,CAAC,IAAImB,aAAa,CAACtD,OAAO,CAACkC,OAAO,CAACC,MAAM,IAAI,EAAE,CAAC,CAAC;MACzDC,MAAM,EAAE,CAAC,IAAIkB,aAAa,CAACtD,OAAO,CAACkC,OAAO,CAACE,MAAM,IAAI,EAAE,CAAC,CAAC;MACzDC,MAAM,EAAE,CAAC,IAAIiB,aAAa,CAACtD,OAAO,CAACkC,OAAO,CAACG,MAAM,IAAI,EAAE,CAAC;IAC1D,CAAC;IACDC,gBAAgB,EAAE1E,QAAQ,CAAC,CAAC,CAAC,EAAE0F,aAAa,CAACtD,OAAO,CAACsC,gBAAgB,CAAC;IACtEiB;EACF,CAAC;EACD,MAAMtD,sBAAsB,GAAGrC,QAAQ,CAAC,CAAC,CAAC,EAAE0F,aAAa,CAACrD,sBAAsB,CAAC;EACjF,MAAM+D,6BAA6B,GAAG;IACpC7B,MAAM,EAAE,CAAC,CAAC;IACVC,MAAM,EAAE,CAAC,CAAC;IACVC,MAAM,EAAE,CAAC;EACX,CAAC;;EAED;EACA;EACA;EACA;EACA;EACAmB,aAAa,CAACE,OAAO,CAAC,CAACO,UAAU,EAAE9F,EAAE,KAAK;IACxC,MAAM+F,yBAAyB,GAAGH,cAAc,CAACzB,gBAAgB,CAACnE,EAAE,CAAC;;IAErE;IACA;IACA,IAAI8F,UAAU,CAACE,OAAO,KAAK,QAAQ,EAAE;MACnC;MACA;MACA,IAAID,yBAAyB,KAAK,QAAQ,IAAI,CAACjE,sBAAsB,CAAC9B,EAAE,CAAC,EAAE;QACzE;MACF;;MAEA;MACA;MACA,IAAI+F,yBAAyB,IAAI,IAAI,EAAE;QACrCF,6BAA6B,CAACE,yBAAyB,CAAC,CAAC/F,EAAE,CAAC,GAAG,IAAI;MACrE;;MAEA;MACA4F,cAAc,CAAC7B,OAAO,CAACG,MAAM,CAAChC,IAAI,CAAClC,EAAE,CAAC;MACtC,OAAO8B,sBAAsB,CAAC9B,EAAE,CAAC;MACjC;IACF;IACA,MAAMiG,MAAM,GAAGnE,sBAAsB,CAAC9B,EAAE,CAAC;;IAEzC;IACA,IAAIiG,MAAM,EAAE;MACV;MACA;MACA,IAAIF,yBAAyB,KAAK,QAAQ,EAAE;QAC1CF,6BAA6B,CAAC3B,MAAM,CAAClE,EAAE,CAAC,GAAG,IAAI;QAC/C4F,cAAc,CAAC7B,OAAO,CAACE,MAAM,CAAC/B,IAAI,CAAClC,EAAE,CAAC;MACxC;MACA;MACA;MAAA,KACK,IAAI+F,yBAAyB,IAAI,IAAI,EAAE;QAC1CH,cAAc,CAAC7B,OAAO,CAACE,MAAM,CAAC/B,IAAI,CAAClC,EAAE,CAAC;MACxC;;MAEA;MACA8B,sBAAsB,CAAC9B,EAAE,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC,EAAEwG,MAAM,EAAEH,UAAU,CAAC;MAC7D;IACF;;IAEA;IACA;IACA;IACA,IAAIC,yBAAyB,KAAK,QAAQ,EAAE;MAC1CF,6BAA6B,CAAC3B,MAAM,CAAClE,EAAE,CAAC,GAAG,IAAI;MAC/C4F,cAAc,CAAC7B,OAAO,CAACC,MAAM,CAAC9B,IAAI,CAAClC,EAAE,CAAC;IACxC;IACA;IACA;IACA;IAAA,KACK,IAAI+F,yBAAyB,IAAI,IAAI,EAAE;MAC1CH,cAAc,CAAC7B,OAAO,CAACC,MAAM,CAAC9B,IAAI,CAAClC,EAAE,CAAC;IACxC;;IAEA;IACA8B,sBAAsB,CAAC9B,EAAE,CAAC,GAAG8F,UAAU;EACzC,CAAC,CAAC;EACF,MAAMI,6BAA6B,GAAGC,MAAM,CAACC,IAAI,CAACP,6BAA6B,CAAC;EAChF,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,6BAA6B,CAAClE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAChE,MAAMsE,UAAU,GAAGH,6BAA6B,CAACnE,CAAC,CAAC;IACnD,MAAMuE,WAAW,GAAGT,6BAA6B,CAACQ,UAAU,CAAC;IAC7D,IAAIF,MAAM,CAACC,IAAI,CAACE,WAAW,CAAC,CAACtE,MAAM,GAAG,CAAC,EAAE;MACvC4D,cAAc,CAAC7B,OAAO,CAACsC,UAAU,CAAC,GAAGT,cAAc,CAAC7B,OAAO,CAACsC,UAAU,CAAC,CAACE,MAAM,CAACvG,EAAE,IAAI,CAACsG,WAAW,CAACtG,EAAE,CAAC,CAAC;IACxG;EACF;EACA,OAAO;IACL8B,sBAAsB;IACtBD,OAAO,EAAE+D,cAAc;IACvBzD,wBAAwB,EAAEgD,aAAa,CAAChD,wBAAwB;IAChEC,+BAA+B,EAAE+C,aAAa,CAAC/C,+BAA+B;IAC9EC,gCAAgC,EAAE8C,aAAa,CAAC9C;EAClD,CAAC;AACH,CAAC;AACD,OAAO,MAAMmE,oBAAoB,GAAG,wDAAwD;AAC5F,OAAO,SAASC,kBAAkBA,CAACpF,MAAM,EAAEQ,OAAO,EAAEX,QAAQ,EAAE;EAC5D,MAAMwF,oBAAoB,GAAG,EAAE;EAC/B7E,OAAO,CAAC0D,OAAO,CAACC,MAAM,IAAI;IACxB,MAAMxF,EAAE,GAAGgB,oBAAoB,CAACwE,MAAM,EAAEtE,QAAQ,EAAE,0DAA0D,CAAC;IAC7G,MAAMsD,OAAO,GAAG9E,mBAAmB,CAAC2B,MAAM,EAAErB,EAAE,CAAC;IAC/C,IAAIwE,OAAO,EAAEzE,IAAI,KAAK,WAAW,EAAE;MACjC;MACA,MAAM4G,eAAe,GAAGtF,MAAM,CAAC8B,OAAO,CAACC,MAAM,CAACwD,UAAU;MACxD,MAAMC,SAAS,GAAGF,eAAe,CAACG,QAAQ,CAAC9G,EAAE,CAAC;MAC9C,IAAI6G,SAAS,EAAE;QACbF,eAAe,CAACG,QAAQ,CAAC9G,EAAE,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC,EAAEoH,SAAS,EAAErB,MAAM,CAAC;MAChE;IACF,CAAC,MAAM;MACLkB,oBAAoB,CAACxE,IAAI,CAACsD,MAAM,CAAC;IACnC;EACF,CAAC,CAAC;EACF,OAAOkB,oBAAoB;AAC7B;AACA,IAAIK,0BAA0B,GAAG,KAAK;AACtC,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,KAAK;EACpF,IAAI,OAAOF,aAAa,KAAK,QAAQ,IAAIA,aAAa,GAAG,CAAC,EAAE;IAC1D,OAAOA,aAAa;EACtB;EACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACP,0BAA0B,IAAI,OAAOE,aAAa,KAAK,WAAW,IAAIA,aAAa,KAAK,IAAI,EAAE;IAC1IM,OAAO,CAACC,IAAI,CAACL,cAAc,CAAC;IAC5BJ,0BAA0B,GAAG,IAAI;EACnC;EACA,OAAOG,gBAAgB;AACzB,CAAC;AACD,OAAO,MAAMO,gBAAgB,GAAG,CAAC,kEAAkE,EAAE,yCAAyC,CAAC,CAAC1G,IAAI,CAAC,IAAI,CAAC;AAC1J,OAAO,MAAM2G,mBAAmB,GAAG,CAAC,mFAAmF,EAAE,yCAAyC,CAAC,CAAC3G,IAAI,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}