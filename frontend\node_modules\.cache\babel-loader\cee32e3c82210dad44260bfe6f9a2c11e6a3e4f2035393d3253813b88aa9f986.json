{"ast": null, "code": "import { createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridColumnMenuSelector = createRootSelector(state => state.columnMenu);", "map": {"version": 3, "names": ["createRootSelector", "gridColumnMenuSelector", "state", "columnMenu"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columnMenu/columnMenuSelector.js"], "sourcesContent": ["import { createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridColumnMenuSelector = createRootSelector(state => state.columnMenu);"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,kCAAkC;AACrE,OAAO,MAAMC,sBAAsB,GAAGD,kBAAkB,CAACE,KAAK,IAAIA,KAAK,CAACC,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}