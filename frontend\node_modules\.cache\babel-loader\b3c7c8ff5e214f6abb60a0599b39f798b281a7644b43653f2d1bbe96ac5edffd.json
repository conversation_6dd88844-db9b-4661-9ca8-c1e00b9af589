{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { TimerBasedCleanupTracking } from \"../../utils/cleanupTracking/TimerBasedCleanupTracking.js\";\nimport { FinalizationRegistryBasedCleanupTracking } from \"../../utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking.js\";\n// Based on https://github.com/Bnaya/use-dispose-uncommitted/blob/main/src/finalization-registry-based-impl.ts\n// Check https://github.com/facebook/react/issues/15317 to get more information\n\n// We use class to make it easier to detect in heap snapshots by name\nclass ObjectToBeRetainedByReact {\n  static create() {\n    return new ObjectToBeRetainedByReact();\n  }\n}\nconst registryContainer = {\n  current: createRegistry()\n};\nlet cleanupTokensCounter = 0;\nexport function useGridEvent(apiRef, eventName, handler, options) {\n  const objectRetainedByReact = React.useState(ObjectToBeRetainedByReact.create)[0];\n  const subscription = React.useRef(null);\n  const handlerRef = React.useRef(null);\n  handlerRef.current = handler;\n  const cleanupTokenRef = React.useRef(null);\n  if (!subscription.current && handlerRef.current) {\n    const enhancedHandler = (params, event, details) => {\n      if (!event.defaultMuiPrevented) {\n        handlerRef.current?.(params, event, details);\n      }\n    };\n    subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n    cleanupTokensCounter += 1;\n    cleanupTokenRef.current = {\n      cleanupToken: cleanupTokensCounter\n    };\n    registryContainer.current.register(objectRetainedByReact,\n    // The callback below will be called once this reference stops being retained\n    () => {\n      subscription.current?.();\n      subscription.current = null;\n      cleanupTokenRef.current = null;\n    }, cleanupTokenRef.current);\n  } else if (!handlerRef.current && subscription.current) {\n    subscription.current();\n    subscription.current = null;\n    if (cleanupTokenRef.current) {\n      registryContainer.current.unregister(cleanupTokenRef.current);\n      cleanupTokenRef.current = null;\n    }\n  }\n  React.useEffect(() => {\n    if (!subscription.current && handlerRef.current) {\n      const enhancedHandler = (params, event, details) => {\n        if (!event.defaultMuiPrevented) {\n          handlerRef.current?.(params, event, details);\n        }\n      };\n      subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n    }\n    if (cleanupTokenRef.current && registryContainer.current) {\n      // If the effect was called, it means that this render was committed\n      // so we can trust the cleanup function to remove the listener.\n      registryContainer.current.unregister(cleanupTokenRef.current);\n      cleanupTokenRef.current = null;\n    }\n    return () => {\n      subscription.current?.();\n      subscription.current = null;\n    };\n  }, [apiRef, eventName, options]);\n}\nconst OPTIONS_IS_FIRST = {\n  isFirst: true\n};\nexport function useGridEventPriority(apiRef, eventName, handler) {\n  useGridEvent(apiRef, eventName, handler, OPTIONS_IS_FIRST);\n}\n\n// TODO: move to @mui/x-data-grid/internals\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_resetCleanupTracking() {\n  registryContainer.current?.reset();\n  registryContainer.current = createRegistry();\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const internal_registryContainer = registryContainer;\nfunction createRegistry() {\n  return typeof FinalizationRegistry !== 'undefined' ? new FinalizationRegistryBasedCleanupTracking() : new TimerBasedCleanupTracking();\n}", "map": {"version": 3, "names": ["React", "TimerBasedCleanupTracking", "FinalizationRegistryBasedCleanupTracking", "ObjectToBeRetainedByReact", "create", "registryContainer", "current", "createRegistry", "cleanupTokensCounter", "useGridEvent", "apiRef", "eventName", "handler", "options", "objectRetainedByReact", "useState", "subscription", "useRef", "handler<PERSON>ef", "cleanupTokenRef", "enhancedHandler", "params", "event", "details", "defaultMuiPrevented", "subscribeEvent", "cleanupToken", "register", "unregister", "useEffect", "OPTIONS_IS_FIRST", "<PERSON><PERSON><PERSON><PERSON>", "useGridEventPriority", "unstable_resetCleanupTracking", "reset", "internal_registryContainer", "FinalizationRegistry"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridEvent.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { TimerBasedCleanupTracking } from \"../../utils/cleanupTracking/TimerBasedCleanupTracking.js\";\nimport { FinalizationRegistryBasedCleanupTracking } from \"../../utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking.js\";\n// Based on https://github.com/Bnaya/use-dispose-uncommitted/blob/main/src/finalization-registry-based-impl.ts\n// Check https://github.com/facebook/react/issues/15317 to get more information\n\n// We use class to make it easier to detect in heap snapshots by name\nclass ObjectToBeRetainedByReact {\n  static create() {\n    return new ObjectToBeRetainedByReact();\n  }\n}\nconst registryContainer = {\n  current: createRegistry()\n};\nlet cleanupTokensCounter = 0;\nexport function useGridEvent(apiRef, eventName, handler, options) {\n  const objectRetainedByReact = React.useState(ObjectToBeRetainedByReact.create)[0];\n  const subscription = React.useRef(null);\n  const handlerRef = React.useRef(null);\n  handlerRef.current = handler;\n  const cleanupTokenRef = React.useRef(null);\n  if (!subscription.current && handlerRef.current) {\n    const enhancedHandler = (params, event, details) => {\n      if (!event.defaultMuiPrevented) {\n        handlerRef.current?.(params, event, details);\n      }\n    };\n    subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n    cleanupTokensCounter += 1;\n    cleanupTokenRef.current = {\n      cleanupToken: cleanupTokensCounter\n    };\n    registryContainer.current.register(objectRetainedByReact,\n    // The callback below will be called once this reference stops being retained\n    () => {\n      subscription.current?.();\n      subscription.current = null;\n      cleanupTokenRef.current = null;\n    }, cleanupTokenRef.current);\n  } else if (!handlerRef.current && subscription.current) {\n    subscription.current();\n    subscription.current = null;\n    if (cleanupTokenRef.current) {\n      registryContainer.current.unregister(cleanupTokenRef.current);\n      cleanupTokenRef.current = null;\n    }\n  }\n  React.useEffect(() => {\n    if (!subscription.current && handlerRef.current) {\n      const enhancedHandler = (params, event, details) => {\n        if (!event.defaultMuiPrevented) {\n          handlerRef.current?.(params, event, details);\n        }\n      };\n      subscription.current = apiRef.current.subscribeEvent(eventName, enhancedHandler, options);\n    }\n    if (cleanupTokenRef.current && registryContainer.current) {\n      // If the effect was called, it means that this render was committed\n      // so we can trust the cleanup function to remove the listener.\n      registryContainer.current.unregister(cleanupTokenRef.current);\n      cleanupTokenRef.current = null;\n    }\n    return () => {\n      subscription.current?.();\n      subscription.current = null;\n    };\n  }, [apiRef, eventName, options]);\n}\nconst OPTIONS_IS_FIRST = {\n  isFirst: true\n};\nexport function useGridEventPriority(apiRef, eventName, handler) {\n  useGridEvent(apiRef, eventName, handler, OPTIONS_IS_FIRST);\n}\n\n// TODO: move to @mui/x-data-grid/internals\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_resetCleanupTracking() {\n  registryContainer.current?.reset();\n  registryContainer.current = createRegistry();\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const internal_registryContainer = registryContainer;\nfunction createRegistry() {\n  return typeof FinalizationRegistry !== 'undefined' ? new FinalizationRegistryBasedCleanupTracking() : new TimerBasedCleanupTracking();\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,QAAQ,0DAA0D;AACpG,SAASC,wCAAwC,QAAQ,yEAAyE;AAClI;AACA;;AAEA;AACA,MAAMC,yBAAyB,CAAC;EAC9B,OAAOC,MAAMA,CAAA,EAAG;IACd,OAAO,IAAID,yBAAyB,CAAC,CAAC;EACxC;AACF;AACA,MAAME,iBAAiB,GAAG;EACxBC,OAAO,EAAEC,cAAc,CAAC;AAC1B,CAAC;AACD,IAAIC,oBAAoB,GAAG,CAAC;AAC5B,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAChE,MAAMC,qBAAqB,GAAGd,KAAK,CAACe,QAAQ,CAACZ,yBAAyB,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;EACjF,MAAMY,YAAY,GAAGhB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMC,UAAU,GAAGlB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EACrCC,UAAU,CAACZ,OAAO,GAAGM,OAAO;EAC5B,MAAMO,eAAe,GAAGnB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EAC1C,IAAI,CAACD,YAAY,CAACV,OAAO,IAAIY,UAAU,CAACZ,OAAO,EAAE;IAC/C,MAAMc,eAAe,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,OAAO,KAAK;MAClD,IAAI,CAACD,KAAK,CAACE,mBAAmB,EAAE;QAC9BN,UAAU,CAACZ,OAAO,GAAGe,MAAM,EAAEC,KAAK,EAAEC,OAAO,CAAC;MAC9C;IACF,CAAC;IACDP,YAAY,CAACV,OAAO,GAAGI,MAAM,CAACJ,OAAO,CAACmB,cAAc,CAACd,SAAS,EAAES,eAAe,EAAEP,OAAO,CAAC;IACzFL,oBAAoB,IAAI,CAAC;IACzBW,eAAe,CAACb,OAAO,GAAG;MACxBoB,YAAY,EAAElB;IAChB,CAAC;IACDH,iBAAiB,CAACC,OAAO,CAACqB,QAAQ,CAACb,qBAAqB;IACxD;IACA,MAAM;MACJE,YAAY,CAACV,OAAO,GAAG,CAAC;MACxBU,YAAY,CAACV,OAAO,GAAG,IAAI;MAC3Ba,eAAe,CAACb,OAAO,GAAG,IAAI;IAChC,CAAC,EAAEa,eAAe,CAACb,OAAO,CAAC;EAC7B,CAAC,MAAM,IAAI,CAACY,UAAU,CAACZ,OAAO,IAAIU,YAAY,CAACV,OAAO,EAAE;IACtDU,YAAY,CAACV,OAAO,CAAC,CAAC;IACtBU,YAAY,CAACV,OAAO,GAAG,IAAI;IAC3B,IAAIa,eAAe,CAACb,OAAO,EAAE;MAC3BD,iBAAiB,CAACC,OAAO,CAACsB,UAAU,CAACT,eAAe,CAACb,OAAO,CAAC;MAC7Da,eAAe,CAACb,OAAO,GAAG,IAAI;IAChC;EACF;EACAN,KAAK,CAAC6B,SAAS,CAAC,MAAM;IACpB,IAAI,CAACb,YAAY,CAACV,OAAO,IAAIY,UAAU,CAACZ,OAAO,EAAE;MAC/C,MAAMc,eAAe,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,OAAO,KAAK;QAClD,IAAI,CAACD,KAAK,CAACE,mBAAmB,EAAE;UAC9BN,UAAU,CAACZ,OAAO,GAAGe,MAAM,EAAEC,KAAK,EAAEC,OAAO,CAAC;QAC9C;MACF,CAAC;MACDP,YAAY,CAACV,OAAO,GAAGI,MAAM,CAACJ,OAAO,CAACmB,cAAc,CAACd,SAAS,EAAES,eAAe,EAAEP,OAAO,CAAC;IAC3F;IACA,IAAIM,eAAe,CAACb,OAAO,IAAID,iBAAiB,CAACC,OAAO,EAAE;MACxD;MACA;MACAD,iBAAiB,CAACC,OAAO,CAACsB,UAAU,CAACT,eAAe,CAACb,OAAO,CAAC;MAC7Da,eAAe,CAACb,OAAO,GAAG,IAAI;IAChC;IACA,OAAO,MAAM;MACXU,YAAY,CAACV,OAAO,GAAG,CAAC;MACxBU,YAAY,CAACV,OAAO,GAAG,IAAI;IAC7B,CAAC;EACH,CAAC,EAAE,CAACI,MAAM,EAAEC,SAAS,EAAEE,OAAO,CAAC,CAAC;AAClC;AACA,MAAMiB,gBAAgB,GAAG;EACvBC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,SAASC,oBAAoBA,CAACtB,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAC/DH,YAAY,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEkB,gBAAgB,CAAC;AAC5D;;AAEA;AACA;AACA,OAAO,SAASG,6BAA6BA,CAAA,EAAG;EAC9C5B,iBAAiB,CAACC,OAAO,EAAE4B,KAAK,CAAC,CAAC;EAClC7B,iBAAiB,CAACC,OAAO,GAAGC,cAAc,CAAC,CAAC;AAC9C;;AAEA;AACA,OAAO,MAAM4B,0BAA0B,GAAG9B,iBAAiB;AAC3D,SAASE,cAAcA,CAAA,EAAG;EACxB,OAAO,OAAO6B,oBAAoB,KAAK,WAAW,GAAG,IAAIlC,wCAAwC,CAAC,CAAC,GAAG,IAAID,yBAAyB,CAAC,CAAC;AACvI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}