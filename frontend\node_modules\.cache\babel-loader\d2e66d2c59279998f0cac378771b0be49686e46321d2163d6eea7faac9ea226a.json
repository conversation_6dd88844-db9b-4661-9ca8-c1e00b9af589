{"ast": null, "code": "const encoder = new TextEncoder();\n\n// bufferLength must be a multiple of 4 to satisfy Int32Array constraints\nlet bufferLength = 2 * 1024;\nlet buffer = new ArrayBuffer(bufferLength);\nlet uint8View = new Uint8Array(buffer);\nlet int32View = new Int32Array(buffer);\nexport const hash = xxh;\n\n/**\n * Returns an xxh hash of `input` formatted as a decimal string.\n */\n// prettier-ignore\nfunction xxh(input) {\n  /* eslint-disable no-bitwise */\n\n  // Worst-case scenario: full string of 2-byte characters\n  const requiredLength = input.length * 2;\n  if (requiredLength > bufferLength) {\n    // buffer.resize() is only available in recent browsers, so we re-allocate\n    // a new and views\n    bufferLength = requiredLength + (4 - requiredLength % 4);\n    buffer = new ArrayBuffer(bufferLength);\n    uint8View = new Uint8Array(buffer);\n    int32View = new Int32Array(buffer);\n  }\n  const length8 = encoder.encodeInto(input, uint8View).written;\n  const seed = 0;\n  const len = length8 | 0;\n  let i = 0;\n  let h = (seed + len | 0) + 0x165667B1 | 0;\n  if (len < 16) {\n    for (; (i + 3 | 0) < len; i = i + 4 | 0) {\n      h = Math.imul(rotl32(h + Math.imul(int32View[i] | 0, 0xC2B2AE3D) | 0, 17) | 0, 0x27D4EB2F);\n    }\n  } else {\n    let v0 = seed + 0x24234428 | 0;\n    let v1 = seed + 0x85EBCA77 | 0;\n    let v2 = seed;\n    let v3 = seed - 0x9E3779B1 | 0;\n    for (; (i + 15 | 0) < len; i = i + 16 | 0) {\n      v0 = Math.imul(rotl32(v0 + Math.imul(int32View[i + 0 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v1 = Math.imul(rotl32(v1 + Math.imul(int32View[i + 4 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v2 = Math.imul(rotl32(v2 + Math.imul(int32View[i + 8 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v3 = Math.imul(rotl32(v3 + Math.imul(int32View[i + 12 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n    }\n    h = (((rotl32(v0, 1) | 0 + rotl32(v1, 7) | 0) + rotl32(v2, 12) | 0) + rotl32(v3, 18) | 0) + len | 0;\n    for (; (i + 3 | 0) < len; i = i + 4 | 0) {\n      h = Math.imul(rotl32(h + Math.imul(int32View[i] | 0, 0xC2B2AE3D) | 0, 17) | 0, 0x27D4EB2F);\n    }\n  }\n  for (; i < len; i = i + 1 | 0) {\n    h = Math.imul(rotl32(h + Math.imul(uint8View[i] | 0, 0x165667B1) | 0, 11) | 0, 0x9E3779B1);\n  }\n  h = Math.imul(h ^ h >>> 15, 0x85EBCA77);\n  h = Math.imul(h ^ h >>> 13, 0xC2B2AE3D);\n  return ((h ^ h >>> 16) >>> 0).toString();\n}\nfunction rotl32(x, r) {\n  return x << r | x >>> 32 - r;\n}", "map": {"version": 3, "names": ["encoder", "TextEncoder", "bufferLength", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uint8View", "Uint8Array", "int32View", "Int32Array", "hash", "xxh", "input", "<PERSON><PERSON><PERSON><PERSON>", "length", "length8", "encodeInto", "written", "seed", "len", "i", "h", "Math", "imul", "rotl32", "v0", "v1", "v2", "v3", "toString", "x", "r"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/hash/hash.js"], "sourcesContent": ["const encoder = new TextEncoder();\n\n// bufferLength must be a multiple of 4 to satisfy Int32Array constraints\nlet bufferLength = 2 * 1024;\nlet buffer = new ArrayBuffer(bufferLength);\nlet uint8View = new Uint8Array(buffer);\nlet int32View = new Int32Array(buffer);\nexport const hash = xxh;\n\n/**\n * Returns an xxh hash of `input` formatted as a decimal string.\n */\n// prettier-ignore\nfunction xxh(input) {\n  /* eslint-disable no-bitwise */\n\n  // Worst-case scenario: full string of 2-byte characters\n  const requiredLength = input.length * 2;\n  if (requiredLength > bufferLength) {\n    // buffer.resize() is only available in recent browsers, so we re-allocate\n    // a new and views\n    bufferLength = requiredLength + (4 - requiredLength % 4);\n    buffer = new ArrayBuffer(bufferLength);\n    uint8View = new Uint8Array(buffer);\n    int32View = new Int32Array(buffer);\n  }\n  const length8 = encoder.encodeInto(input, uint8View).written;\n  const seed = 0;\n  const len = length8 | 0;\n  let i = 0;\n  let h = (seed + len | 0) + 0x165667B1 | 0;\n  if (len < 16) {\n    for (; (i + 3 | 0) < len; i = i + 4 | 0) {\n      h = Math.imul(rotl32(h + Math.imul(int32View[i] | 0, 0xC2B2AE3D) | 0, 17) | 0, 0x27D4EB2F);\n    }\n  } else {\n    let v0 = seed + 0x24234428 | 0;\n    let v1 = seed + 0x85EBCA77 | 0;\n    let v2 = seed;\n    let v3 = seed - 0x9E3779B1 | 0;\n    for (; (i + 15 | 0) < len; i = i + 16 | 0) {\n      v0 = Math.imul(rotl32(v0 + Math.imul(int32View[i + 0 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v1 = Math.imul(rotl32(v1 + Math.imul(int32View[i + 4 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v2 = Math.imul(rotl32(v2 + Math.imul(int32View[i + 8 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n      v3 = Math.imul(rotl32(v3 + Math.imul(int32View[i + 12 | 0] | 0, 0x85EBCA77) | 0, 13) | 0, 0x9E3779B1);\n    }\n    h = (((rotl32(v0, 1) | 0 + rotl32(v1, 7) | 0) + rotl32(v2, 12) | 0) + rotl32(v3, 18) | 0) + len | 0;\n    for (; (i + 3 | 0) < len; i = i + 4 | 0) {\n      h = Math.imul(rotl32(h + Math.imul(int32View[i] | 0, 0xC2B2AE3D) | 0, 17) | 0, 0x27D4EB2F);\n    }\n  }\n  for (; i < len; i = i + 1 | 0) {\n    h = Math.imul(rotl32(h + Math.imul(uint8View[i] | 0, 0x165667B1) | 0, 11) | 0, 0x9E3779B1);\n  }\n  h = Math.imul(h ^ h >>> 15, 0x85EBCA77);\n  h = Math.imul(h ^ h >>> 13, 0xC2B2AE3D);\n  return ((h ^ h >>> 16) >>> 0).toString();\n}\nfunction rotl32(x, r) {\n  return x << r | x >>> 32 - r;\n}"], "mappings": "AAAA,MAAMA,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;;AAEjC;AACA,IAAIC,YAAY,GAAG,CAAC,GAAG,IAAI;AAC3B,IAAIC,MAAM,GAAG,IAAIC,WAAW,CAACF,YAAY,CAAC;AAC1C,IAAIG,SAAS,GAAG,IAAIC,UAAU,CAACH,MAAM,CAAC;AACtC,IAAII,SAAS,GAAG,IAAIC,UAAU,CAACL,MAAM,CAAC;AACtC,OAAO,MAAMM,IAAI,GAAGC,GAAG;;AAEvB;AACA;AACA;AACA;AACA,SAASA,GAAGA,CAACC,KAAK,EAAE;EAClB;;EAEA;EACA,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAM,GAAG,CAAC;EACvC,IAAID,cAAc,GAAGV,YAAY,EAAE;IACjC;IACA;IACAA,YAAY,GAAGU,cAAc,IAAI,CAAC,GAAGA,cAAc,GAAG,CAAC,CAAC;IACxDT,MAAM,GAAG,IAAIC,WAAW,CAACF,YAAY,CAAC;IACtCG,SAAS,GAAG,IAAIC,UAAU,CAACH,MAAM,CAAC;IAClCI,SAAS,GAAG,IAAIC,UAAU,CAACL,MAAM,CAAC;EACpC;EACA,MAAMW,OAAO,GAAGd,OAAO,CAACe,UAAU,CAACJ,KAAK,EAAEN,SAAS,CAAC,CAACW,OAAO;EAC5D,MAAMC,IAAI,GAAG,CAAC;EACd,MAAMC,GAAG,GAAGJ,OAAO,GAAG,CAAC;EACvB,IAAIK,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAACH,IAAI,GAAGC,GAAG,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC;EACzC,IAAIA,GAAG,GAAG,EAAE,EAAE;IACZ,OAAO,CAACC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAID,GAAG,EAAEC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACvCC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACC,MAAM,CAACH,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACf,SAAS,CAACY,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC;IAC5F;EACF,CAAC,MAAM;IACL,IAAIK,EAAE,GAAGP,IAAI,GAAG,UAAU,GAAG,CAAC;IAC9B,IAAIQ,EAAE,GAAGR,IAAI,GAAG,UAAU,GAAG,CAAC;IAC9B,IAAIS,EAAE,GAAGT,IAAI;IACb,IAAIU,EAAE,GAAGV,IAAI,GAAG,UAAU,GAAG,CAAC;IAC9B,OAAO,CAACE,CAAC,GAAG,EAAE,GAAG,CAAC,IAAID,GAAG,EAAEC,CAAC,GAAGA,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;MACzCK,EAAE,GAAGH,IAAI,CAACC,IAAI,CAACC,MAAM,CAACC,EAAE,GAAGH,IAAI,CAACC,IAAI,CAACf,SAAS,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC;MACpGM,EAAE,GAAGJ,IAAI,CAACC,IAAI,CAACC,MAAM,CAACE,EAAE,GAAGJ,IAAI,CAACC,IAAI,CAACf,SAAS,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC;MACpGO,EAAE,GAAGL,IAAI,CAACC,IAAI,CAACC,MAAM,CAACG,EAAE,GAAGL,IAAI,CAACC,IAAI,CAACf,SAAS,CAACY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC;MACpGQ,EAAE,GAAGN,IAAI,CAACC,IAAI,CAACC,MAAM,CAACI,EAAE,GAAGN,IAAI,CAACC,IAAI,CAACf,SAAS,CAACY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC;IACvG;IACAC,CAAC,GAAG,CAAC,CAAC,CAACG,MAAM,CAACC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,GAAGD,MAAM,CAACE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,IAAIF,MAAM,CAACG,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,IAAIH,MAAM,CAACI,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,IAAIT,GAAG,GAAG,CAAC;IACnG,OAAO,CAACC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAID,GAAG,EAAEC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACvCC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACC,MAAM,CAACH,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACf,SAAS,CAACY,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC;IAC5F;EACF;EACA,OAAOA,CAAC,GAAGD,GAAG,EAAEC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IAC7BC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACC,MAAM,CAACH,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACjB,SAAS,CAACc,CAAC,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC;EAC5F;EACAC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACF,CAAC,GAAGA,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC;EACvCA,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACF,CAAC,GAAGA,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC;EACvC,OAAO,CAAC,CAACA,CAAC,GAAGA,CAAC,KAAK,EAAE,MAAM,CAAC,EAAEQ,QAAQ,CAAC,CAAC;AAC1C;AACA,SAASL,MAAMA,CAACM,CAAC,EAAEC,CAAC,EAAE;EACpB,OAAOD,CAAC,IAAIC,CAAC,GAAGD,CAAC,KAAK,EAAE,GAAGC,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}