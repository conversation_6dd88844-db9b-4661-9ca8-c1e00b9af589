{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"children\", \"sidePanel\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useForkRef from '@mui/utils/useForkRef';\nimport capitalize from '@mui/utils/capitalize';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { GridRootStyles } from \"./GridRootStyles.js\";\nimport { useCSSVariablesContext } from \"../../utils/css/context.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../../constants/gridClasses.js\";\nimport { gridDensitySelector } from \"../../hooks/features/density/densitySelector.js\";\nimport { useIsSSR } from \"../../hooks/utils/useIsSSR.js\";\nimport { GridHeader } from \"../GridHeader.js\";\nimport { GridBody, GridFooterPlaceholder } from \"../base/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = (ownerState, density) => {\n  const {\n    autoHeight,\n    classes,\n    showCellVerticalBorder\n  } = ownerState;\n  const slots = {\n    root: ['root', autoHeight && 'autoHeight', `root--density${capitalize(density)}`, ownerState.slots.toolbar === null && 'root--noToolbar', 'withBorderColor', showCellVerticalBorder && 'withVerticalBorder']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridRoot = forwardRef(function GridRoot(props, ref) {\n  const rootProps = useGridRootProps();\n  const {\n      className,\n      children,\n      sidePanel\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const density = useGridSelector(apiRef, gridDensitySelector);\n  const rootElementRef = apiRef.current.rootElementRef;\n  const rootMountCallback = React.useCallback(node => {\n    if (node === null) {\n      return;\n    }\n    apiRef.current.publishEvent('rootMount', node);\n  }, [apiRef]);\n  const handleRef = useForkRef(rootElementRef, ref, rootMountCallback);\n  const ownerState = rootProps;\n  const classes = useUtilityClasses(ownerState, density);\n  const cssVariables = useCSSVariablesContext();\n  const isSSR = useIsSSR();\n  if (isSSR) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(GridRootStyles, _extends({\n    className: clsx(classes.root, className, cssVariables.className, sidePanel && gridClasses.withSidePanel),\n    ownerState: ownerState\n  }, other, {\n    ref: handleRef,\n    children: [/*#__PURE__*/_jsxs(\"div\", {\n      className: gridClasses.mainContent,\n      role: \"presentation\",\n      children: [/*#__PURE__*/_jsx(GridHeader, {}), /*#__PURE__*/_jsx(GridBody, {\n        children: children\n      }), /*#__PURE__*/_jsx(GridFooterPlaceholder, {})]\n    }), sidePanel, cssVariables.tag]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridRoot.displayName = \"GridRoot\";\nprocess.env.NODE_ENV !== \"production\" ? GridRoot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sidePanel: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nconst MemoizedGridRoot = fastMemo(GridRoot);\nexport { MemoizedGridRoot as GridRoot };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "useForkRef", "capitalize", "composeClasses", "fastMemo", "forwardRef", "GridRootStyles", "useCSSVariablesContext", "useGridSelector", "useGridPrivateApiContext", "useGridRootProps", "getDataGridUtilityClass", "gridClasses", "gridDensitySelector", "useIsSSR", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GridBody", "GridFooterPlaceholder", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "density", "autoHeight", "classes", "showCellVerticalBorder", "slots", "root", "toolbar", "GridRoot", "props", "ref", "rootProps", "className", "children", "sidePanel", "other", "apiRef", "rootElementRef", "current", "rootMountCallback", "useCallback", "node", "publishEvent", "handleRef", "cssVariables", "isSSR", "withSidePanel", "mainContent", "role", "tag", "process", "env", "NODE_ENV", "displayName", "propTypes", "sx", "oneOfType", "arrayOf", "func", "object", "bool", "MemoizedGridRoot"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/containers/GridRoot.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"children\", \"sidePanel\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useForkRef from '@mui/utils/useForkRef';\nimport capitalize from '@mui/utils/capitalize';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { GridRootStyles } from \"./GridRootStyles.js\";\nimport { useCSSVariablesContext } from \"../../utils/css/context.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { useGridPrivateApiContext } from \"../../hooks/utils/useGridPrivateApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../../constants/gridClasses.js\";\nimport { gridDensitySelector } from \"../../hooks/features/density/densitySelector.js\";\nimport { useIsSSR } from \"../../hooks/utils/useIsSSR.js\";\nimport { GridHeader } from \"../GridHeader.js\";\nimport { GridBody, GridFooterPlaceholder } from \"../base/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = (ownerState, density) => {\n  const {\n    autoHeight,\n    classes,\n    showCellVerticalBorder\n  } = ownerState;\n  const slots = {\n    root: ['root', autoHeight && 'autoHeight', `root--density${capitalize(density)}`, ownerState.slots.toolbar === null && 'root--noToolbar', 'withBorderColor', showCellVerticalBorder && 'withVerticalBorder']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridRoot = forwardRef(function GridRoot(props, ref) {\n  const rootProps = useGridRootProps();\n  const {\n      className,\n      children,\n      sidePanel\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridPrivateApiContext();\n  const density = useGridSelector(apiRef, gridDensitySelector);\n  const rootElementRef = apiRef.current.rootElementRef;\n  const rootMountCallback = React.useCallback(node => {\n    if (node === null) {\n      return;\n    }\n    apiRef.current.publishEvent('rootMount', node);\n  }, [apiRef]);\n  const handleRef = useForkRef(rootElementRef, ref, rootMountCallback);\n  const ownerState = rootProps;\n  const classes = useUtilityClasses(ownerState, density);\n  const cssVariables = useCSSVariablesContext();\n  const isSSR = useIsSSR();\n  if (isSSR) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(GridRootStyles, _extends({\n    className: clsx(classes.root, className, cssVariables.className, sidePanel && gridClasses.withSidePanel),\n    ownerState: ownerState\n  }, other, {\n    ref: handleRef,\n    children: [/*#__PURE__*/_jsxs(\"div\", {\n      className: gridClasses.mainContent,\n      role: \"presentation\",\n      children: [/*#__PURE__*/_jsx(GridHeader, {}), /*#__PURE__*/_jsx(GridBody, {\n        children: children\n      }), /*#__PURE__*/_jsx(GridFooterPlaceholder, {})]\n    }), sidePanel, cssVariables.tag]\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") GridRoot.displayName = \"GridRoot\";\nprocess.env.NODE_ENV !== \"production\" ? GridRoot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  sidePanel: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nconst MemoizedGridRoot = fastMemo(GridRoot);\nexport { MemoizedGridRoot as GridRoot };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC;AACxD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,uBAAuB,EAAEC,WAAW,QAAQ,gCAAgC;AACrF,SAASC,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,QAAQ,QAAQ,+BAA+B;AACxD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,QAAQ,EAAEC,qBAAqB,QAAQ,kBAAkB;AAClE,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGA,CAACC,UAAU,EAAEC,OAAO,KAAK;EACjD,MAAM;IACJC,UAAU;IACVC,OAAO;IACPC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEJ,UAAU,IAAI,YAAY,EAAE,gBAAgBvB,UAAU,CAACsB,OAAO,CAAC,EAAE,EAAED,UAAU,CAACK,KAAK,CAACE,OAAO,KAAK,IAAI,IAAI,iBAAiB,EAAE,iBAAiB,EAAEH,sBAAsB,IAAI,oBAAoB;EAC7M,CAAC;EACD,OAAOxB,cAAc,CAACyB,KAAK,EAAEjB,uBAAuB,EAAEe,OAAO,CAAC;AAChE,CAAC;AACD,MAAMK,QAAQ,GAAG1B,UAAU,CAAC,SAAS0B,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxD,MAAMC,SAAS,GAAGxB,gBAAgB,CAAC,CAAC;EACpC,MAAM;MACFyB,SAAS;MACTC,QAAQ;MACRC;IACF,CAAC,GAAGL,KAAK;IACTM,KAAK,GAAG1C,6BAA6B,CAACoC,KAAK,EAAEnC,SAAS,CAAC;EACzD,MAAM0C,MAAM,GAAG9B,wBAAwB,CAAC,CAAC;EACzC,MAAMe,OAAO,GAAGhB,eAAe,CAAC+B,MAAM,EAAE1B,mBAAmB,CAAC;EAC5D,MAAM2B,cAAc,GAAGD,MAAM,CAACE,OAAO,CAACD,cAAc;EACpD,MAAME,iBAAiB,GAAG5C,KAAK,CAAC6C,WAAW,CAACC,IAAI,IAAI;IAClD,IAAIA,IAAI,KAAK,IAAI,EAAE;MACjB;IACF;IACAL,MAAM,CAACE,OAAO,CAACI,YAAY,CAAC,WAAW,EAAED,IAAI,CAAC;EAChD,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;EACZ,MAAMO,SAAS,GAAG7C,UAAU,CAACuC,cAAc,EAAEP,GAAG,EAAES,iBAAiB,CAAC;EACpE,MAAMnB,UAAU,GAAGW,SAAS;EAC5B,MAAMR,OAAO,GAAGJ,iBAAiB,CAACC,UAAU,EAAEC,OAAO,CAAC;EACtD,MAAMuB,YAAY,GAAGxC,sBAAsB,CAAC,CAAC;EAC7C,MAAMyC,KAAK,GAAGlC,QAAQ,CAAC,CAAC;EACxB,IAAIkC,KAAK,EAAE;IACT,OAAO,IAAI;EACb;EACA,OAAO,aAAa3B,KAAK,CAACf,cAAc,EAAEX,QAAQ,CAAC;IACjDwC,SAAS,EAAEnC,IAAI,CAAC0B,OAAO,CAACG,IAAI,EAAEM,SAAS,EAAEY,YAAY,CAACZ,SAAS,EAAEE,SAAS,IAAIzB,WAAW,CAACqC,aAAa,CAAC;IACxG1B,UAAU,EAAEA;EACd,CAAC,EAAEe,KAAK,EAAE;IACRL,GAAG,EAAEa,SAAS;IACdV,QAAQ,EAAE,CAAC,aAAaf,KAAK,CAAC,KAAK,EAAE;MACnCc,SAAS,EAAEvB,WAAW,CAACsC,WAAW;MAClCC,IAAI,EAAE,cAAc;MACpBf,QAAQ,EAAE,CAAC,aAAajB,IAAI,CAACJ,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,aAAaI,IAAI,CAACH,QAAQ,EAAE;QACxEoB,QAAQ,EAAEA;MACZ,CAAC,CAAC,EAAE,aAAajB,IAAI,CAACF,qBAAqB,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,EAAEoB,SAAS,EAAEU,YAAY,CAACK,GAAG;EACjC,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAExB,QAAQ,CAACyB,WAAW,GAAG,UAAU;AAC5EH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,QAAQ,CAAC0B,SAAS,GAAG;EAC3D;EACA;EACA;EACA;EACApB,SAAS,EAAEtC,SAAS,CAAC6C,IAAI;EACzB;AACF;AACA;EACEc,EAAE,EAAE3D,SAAS,CAAC4D,SAAS,CAAC,CAAC5D,SAAS,CAAC6D,OAAO,CAAC7D,SAAS,CAAC4D,SAAS,CAAC,CAAC5D,SAAS,CAAC8D,IAAI,EAAE9D,SAAS,CAAC+D,MAAM,EAAE/D,SAAS,CAACgE,IAAI,CAAC,CAAC,CAAC,EAAEhE,SAAS,CAAC8D,IAAI,EAAE9D,SAAS,CAAC+D,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,MAAME,gBAAgB,GAAG5D,QAAQ,CAAC2B,QAAQ,CAAC;AAC3C,SAASiC,gBAAgB,IAAIjC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}