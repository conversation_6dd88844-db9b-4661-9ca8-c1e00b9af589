{"ast": null, "code": "import * as React from 'react';\nimport { useGridEventPriority, useGridNativeEventListener } from \"../../utils/index.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { serializeCellValue } from \"../export/serializers/csvSerializer.js\";\nimport { isCopyShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridRowSelectionCountSelector } from \"../rowSelection/index.js\";\nfunction writeToClipboardPolyfill(data) {\n  const span = document.createElement('span');\n  span.style.whiteSpace = 'pre';\n  span.style.userSelect = 'all';\n  span.style.opacity = '0px';\n  span.textContent = data;\n  document.body.appendChild(span);\n  const range = document.createRange();\n  range.selectNode(span);\n  const selection = window.getSelection();\n  selection.removeAllRanges();\n  selection.addRange(range);\n  try {\n    document.execCommand('copy');\n  } finally {\n    document.body.removeChild(span);\n  }\n}\nfunction copyToClipboard(data) {\n  if (navigator.clipboard) {\n    navigator.clipboard.writeText(data).catch(() => {\n      writeToClipboardPolyfill(data);\n    });\n  } else {\n    writeToClipboardPolyfill(data);\n  }\n}\nfunction hasNativeSelection(element) {\n  // When getSelection is called on an <iframe> that is not displayed Firefox will return null.\n  if (window.getSelection()?.toString()) {\n    return true;\n  }\n\n  // window.getSelection() returns an empty string in Firefox for selections inside a form element.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=85686.\n  // Instead, we can use element.selectionStart that is only defined on form elements.\n  if (element && (element.selectionEnd || 0) - (element.selectionStart || 0) > 0) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * @requires useGridCsvExport (method)\n * @requires useGridSelection (method)\n */\nexport const useGridClipboard = (apiRef, props) => {\n  const ignoreValueFormatterProp = props.ignoreValueFormatterDuringExport;\n  const ignoreValueFormatter = (typeof ignoreValueFormatterProp === 'object' ? ignoreValueFormatterProp?.clipboardExport : ignoreValueFormatterProp) || false;\n  const clipboardCopyCellDelimiter = props.clipboardCopyCellDelimiter;\n  const handleCopy = React.useCallback(event => {\n    if (!isCopyShortcut(event)) {\n      return;\n    }\n\n    // Do nothing if there's a native selection\n    if (hasNativeSelection(event.target)) {\n      return;\n    }\n    let textToCopy = '';\n    const selectedRowsCount = gridRowSelectionCountSelector(apiRef);\n    if (selectedRowsCount > 0) {\n      textToCopy = apiRef.current.getDataAsCsv({\n        includeHeaders: false,\n        delimiter: clipboardCopyCellDelimiter,\n        shouldAppendQuotes: false,\n        escapeFormulas: false\n      });\n    } else {\n      const focusedCell = gridFocusCellSelector(apiRef);\n      if (focusedCell) {\n        const cellParams = apiRef.current.getCellParams(focusedCell.id, focusedCell.field);\n        textToCopy = serializeCellValue(cellParams, {\n          csvOptions: {\n            delimiter: clipboardCopyCellDelimiter,\n            shouldAppendQuotes: false,\n            escapeFormulas: false\n          },\n          ignoreValueFormatter\n        });\n      }\n    }\n    textToCopy = apiRef.current.unstable_applyPipeProcessors('clipboardCopy', textToCopy);\n    if (textToCopy) {\n      copyToClipboard(textToCopy);\n      apiRef.current.publishEvent('clipboardCopy', textToCopy);\n    }\n  }, [apiRef, ignoreValueFormatter, clipboardCopyCellDelimiter]);\n  useGridNativeEventListener(apiRef, () => apiRef.current.rootElementRef.current, 'keydown', handleCopy);\n  useGridEventPriority(apiRef, 'clipboardCopy', props.onClipboardCopy);\n};", "map": {"version": 3, "names": ["React", "useGridEventPriority", "useGridNativeEventListener", "gridFocusCellSelector", "serializeCellValue", "isCopyShortcut", "gridRowSelectionCountSelector", "writeToClipboardPolyfill", "data", "span", "document", "createElement", "style", "whiteSpace", "userSelect", "opacity", "textContent", "body", "append<PERSON><PERSON><PERSON>", "range", "createRange", "selectNode", "selection", "window", "getSelection", "removeAllRanges", "addRange", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "copyToClipboard", "navigator", "clipboard", "writeText", "catch", "hasNativeSelection", "element", "toString", "selectionEnd", "selectionStart", "useGridClipboard", "apiRef", "props", "ignoreValueFormatterProp", "ignoreValueFormatterDuringExport", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clipboardExport", "clipboardCopyCellDelimiter", "handleCopy", "useCallback", "event", "target", "textToCopy", "selectedRowsCount", "current", "getDataAsCsv", "includeHeaders", "delimiter", "shouldAppendQuotes", "escapeFormulas", "focusedCell", "cellParams", "getCellParams", "id", "field", "csvOptions", "unstable_applyPipeProcessors", "publishEvent", "rootElementRef", "onClipboardCopy"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/clipboard/useGridClipboard.js"], "sourcesContent": ["import * as React from 'react';\nimport { useGridEventPriority, useGridNativeEventListener } from \"../../utils/index.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { serializeCellValue } from \"../export/serializers/csvSerializer.js\";\nimport { isCopyShortcut } from \"../../../utils/keyboardUtils.js\";\nimport { gridRowSelectionCountSelector } from \"../rowSelection/index.js\";\nfunction writeToClipboardPolyfill(data) {\n  const span = document.createElement('span');\n  span.style.whiteSpace = 'pre';\n  span.style.userSelect = 'all';\n  span.style.opacity = '0px';\n  span.textContent = data;\n  document.body.appendChild(span);\n  const range = document.createRange();\n  range.selectNode(span);\n  const selection = window.getSelection();\n  selection.removeAllRanges();\n  selection.addRange(range);\n  try {\n    document.execCommand('copy');\n  } finally {\n    document.body.removeChild(span);\n  }\n}\nfunction copyToClipboard(data) {\n  if (navigator.clipboard) {\n    navigator.clipboard.writeText(data).catch(() => {\n      writeToClipboardPolyfill(data);\n    });\n  } else {\n    writeToClipboardPolyfill(data);\n  }\n}\nfunction hasNativeSelection(element) {\n  // When getSelection is called on an <iframe> that is not displayed Firefox will return null.\n  if (window.getSelection()?.toString()) {\n    return true;\n  }\n\n  // window.getSelection() returns an empty string in Firefox for selections inside a form element.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=85686.\n  // Instead, we can use element.selectionStart that is only defined on form elements.\n  if (element && (element.selectionEnd || 0) - (element.selectionStart || 0) > 0) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * @requires useGridCsvExport (method)\n * @requires useGridSelection (method)\n */\nexport const useGridClipboard = (apiRef, props) => {\n  const ignoreValueFormatterProp = props.ignoreValueFormatterDuringExport;\n  const ignoreValueFormatter = (typeof ignoreValueFormatterProp === 'object' ? ignoreValueFormatterProp?.clipboardExport : ignoreValueFormatterProp) || false;\n  const clipboardCopyCellDelimiter = props.clipboardCopyCellDelimiter;\n  const handleCopy = React.useCallback(event => {\n    if (!isCopyShortcut(event)) {\n      return;\n    }\n\n    // Do nothing if there's a native selection\n    if (hasNativeSelection(event.target)) {\n      return;\n    }\n    let textToCopy = '';\n    const selectedRowsCount = gridRowSelectionCountSelector(apiRef);\n    if (selectedRowsCount > 0) {\n      textToCopy = apiRef.current.getDataAsCsv({\n        includeHeaders: false,\n        delimiter: clipboardCopyCellDelimiter,\n        shouldAppendQuotes: false,\n        escapeFormulas: false\n      });\n    } else {\n      const focusedCell = gridFocusCellSelector(apiRef);\n      if (focusedCell) {\n        const cellParams = apiRef.current.getCellParams(focusedCell.id, focusedCell.field);\n        textToCopy = serializeCellValue(cellParams, {\n          csvOptions: {\n            delimiter: clipboardCopyCellDelimiter,\n            shouldAppendQuotes: false,\n            escapeFormulas: false\n          },\n          ignoreValueFormatter\n        });\n      }\n    }\n    textToCopy = apiRef.current.unstable_applyPipeProcessors('clipboardCopy', textToCopy);\n    if (textToCopy) {\n      copyToClipboard(textToCopy);\n      apiRef.current.publishEvent('clipboardCopy', textToCopy);\n    }\n  }, [apiRef, ignoreValueFormatter, clipboardCopyCellDelimiter]);\n  useGridNativeEventListener(apiRef, () => apiRef.current.rootElementRef.current, 'keydown', handleCopy);\n  useGridEventPriority(apiRef, 'clipboardCopy', props.onClipboardCopy);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,EAAEC,0BAA0B,QAAQ,sBAAsB;AACvF,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,6BAA6B,QAAQ,0BAA0B;AACxE,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EACtC,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EAC3CF,IAAI,CAACG,KAAK,CAACC,UAAU,GAAG,KAAK;EAC7BJ,IAAI,CAACG,KAAK,CAACE,UAAU,GAAG,KAAK;EAC7BL,IAAI,CAACG,KAAK,CAACG,OAAO,GAAG,KAAK;EAC1BN,IAAI,CAACO,WAAW,GAAGR,IAAI;EACvBE,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;EAC/B,MAAMU,KAAK,GAAGT,QAAQ,CAACU,WAAW,CAAC,CAAC;EACpCD,KAAK,CAACE,UAAU,CAACZ,IAAI,CAAC;EACtB,MAAMa,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;EACvCF,SAAS,CAACG,eAAe,CAAC,CAAC;EAC3BH,SAAS,CAACI,QAAQ,CAACP,KAAK,CAAC;EACzB,IAAI;IACFT,QAAQ,CAACiB,WAAW,CAAC,MAAM,CAAC;EAC9B,CAAC,SAAS;IACRjB,QAAQ,CAACO,IAAI,CAACW,WAAW,CAACnB,IAAI,CAAC;EACjC;AACF;AACA,SAASoB,eAAeA,CAACrB,IAAI,EAAE;EAC7B,IAAIsB,SAAS,CAACC,SAAS,EAAE;IACvBD,SAAS,CAACC,SAAS,CAACC,SAAS,CAACxB,IAAI,CAAC,CAACyB,KAAK,CAAC,MAAM;MAC9C1B,wBAAwB,CAACC,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,MAAM;IACLD,wBAAwB,CAACC,IAAI,CAAC;EAChC;AACF;AACA,SAAS0B,kBAAkBA,CAACC,OAAO,EAAE;EACnC;EACA,IAAIZ,MAAM,CAACC,YAAY,CAAC,CAAC,EAAEY,QAAQ,CAAC,CAAC,EAAE;IACrC,OAAO,IAAI;EACb;;EAEA;EACA;EACA;EACA,IAAID,OAAO,IAAI,CAACA,OAAO,CAACE,YAAY,IAAI,CAAC,KAAKF,OAAO,CAACG,cAAc,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IAC9E,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACjD,MAAMC,wBAAwB,GAAGD,KAAK,CAACE,gCAAgC;EACvE,MAAMC,oBAAoB,GAAG,CAAC,OAAOF,wBAAwB,KAAK,QAAQ,GAAGA,wBAAwB,EAAEG,eAAe,GAAGH,wBAAwB,KAAK,KAAK;EAC3J,MAAMI,0BAA0B,GAAGL,KAAK,CAACK,0BAA0B;EACnE,MAAMC,UAAU,GAAG/C,KAAK,CAACgD,WAAW,CAACC,KAAK,IAAI;IAC5C,IAAI,CAAC5C,cAAc,CAAC4C,KAAK,CAAC,EAAE;MAC1B;IACF;;IAEA;IACA,IAAIf,kBAAkB,CAACe,KAAK,CAACC,MAAM,CAAC,EAAE;MACpC;IACF;IACA,IAAIC,UAAU,GAAG,EAAE;IACnB,MAAMC,iBAAiB,GAAG9C,6BAA6B,CAACkC,MAAM,CAAC;IAC/D,IAAIY,iBAAiB,GAAG,CAAC,EAAE;MACzBD,UAAU,GAAGX,MAAM,CAACa,OAAO,CAACC,YAAY,CAAC;QACvCC,cAAc,EAAE,KAAK;QACrBC,SAAS,EAAEV,0BAA0B;QACrCW,kBAAkB,EAAE,KAAK;QACzBC,cAAc,EAAE;MAClB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMC,WAAW,GAAGxD,qBAAqB,CAACqC,MAAM,CAAC;MACjD,IAAImB,WAAW,EAAE;QACf,MAAMC,UAAU,GAAGpB,MAAM,CAACa,OAAO,CAACQ,aAAa,CAACF,WAAW,CAACG,EAAE,EAAEH,WAAW,CAACI,KAAK,CAAC;QAClFZ,UAAU,GAAG/C,kBAAkB,CAACwD,UAAU,EAAE;UAC1CI,UAAU,EAAE;YACVR,SAAS,EAAEV,0BAA0B;YACrCW,kBAAkB,EAAE,KAAK;YACzBC,cAAc,EAAE;UAClB,CAAC;UACDd;QACF,CAAC,CAAC;MACJ;IACF;IACAO,UAAU,GAAGX,MAAM,CAACa,OAAO,CAACY,4BAA4B,CAAC,eAAe,EAAEd,UAAU,CAAC;IACrF,IAAIA,UAAU,EAAE;MACdtB,eAAe,CAACsB,UAAU,CAAC;MAC3BX,MAAM,CAACa,OAAO,CAACa,YAAY,CAAC,eAAe,EAAEf,UAAU,CAAC;IAC1D;EACF,CAAC,EAAE,CAACX,MAAM,EAAEI,oBAAoB,EAAEE,0BAA0B,CAAC,CAAC;EAC9D5C,0BAA0B,CAACsC,MAAM,EAAE,MAAMA,MAAM,CAACa,OAAO,CAACc,cAAc,CAACd,OAAO,EAAE,SAAS,EAAEN,UAAU,CAAC;EACtG9C,oBAAoB,CAACuC,MAAM,EAAE,eAAe,EAAEC,KAAK,CAAC2B,eAAe,CAAC;AACtE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}