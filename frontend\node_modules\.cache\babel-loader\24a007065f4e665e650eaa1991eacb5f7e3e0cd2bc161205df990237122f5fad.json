{"ast": null, "code": "import { capitalize } from './capitalize.mjs';\nimport { words } from './words.mjs';\nfunction pascalCase(str) {\n  const words$1 = words(str);\n  return words$1.map(word => capitalize(word)).join('');\n}\nexport { pascalCase };", "map": {"version": 3, "names": ["capitalize", "words", "pascalCase", "str", "words$1", "map", "word", "join"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/string/pascalCase.mjs"], "sourcesContent": ["import { capitalize } from './capitalize.mjs';\nimport { words } from './words.mjs';\n\nfunction pascalCase(str) {\n    const words$1 = words(str);\n    return words$1.map(word => capitalize(word)).join('');\n}\n\nexport { pascalCase };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,KAAK,QAAQ,aAAa;AAEnC,SAASC,UAAUA,CAACC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,KAAK,CAACE,GAAG,CAAC;EAC1B,OAAOC,OAAO,CAACC,GAAG,CAACC,IAAI,IAAIN,UAAU,CAACM,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;AACzD;AAEA,SAASL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}