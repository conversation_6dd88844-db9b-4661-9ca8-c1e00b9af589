{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRenderContextSelector } from \"./gridVirtualizationSelectors.js\";\nimport { gridFocusCellSelector } from \"../focus/index.js\";\nimport { gridVisibleRowsSelector } from \"../pagination/index.js\";\nconst gridIsFocusedCellOutOfContext = createSelector(gridFocusCellSelector, gridRenderContextSelector, gridVisibleRowsSelector, gridVisibleColumnDefinitionsSelector, (focusedCell, renderContext, currentPage, visibleColumns) => {\n  if (!focusedCell) {\n    return false;\n  }\n  const rowIndex = currentPage.rowIdToIndexMap.get(focusedCell.id);\n  const columnIndex = visibleColumns.slice(renderContext.firstColumnIndex, renderContext.lastColumnIndex).findIndex(column => column.field === focusedCell.field);\n  const isInRenderContext = rowIndex !== undefined && columnIndex !== -1 && rowIndex >= renderContext.firstRowIndex && rowIndex <= renderContext.lastRowIndex;\n  return !isInRenderContext;\n});\nexport const gridFocusedVirtualCellSelector = createSelectorMemoized(gridIsFocusedCellOutOfContext, gridVisibleColumnDefinitionsSelector, gridVisibleRowsSelector, gridFocusCellSelector, (isFocusedCellOutOfRenderContext, visibleColumns, currentPage, focusedCell) => {\n  if (!isFocusedCellOutOfRenderContext) {\n    return null;\n  }\n  const rowIndex = currentPage.rowIdToIndexMap.get(focusedCell.id);\n  if (rowIndex === undefined) {\n    return null;\n  }\n  const columnIndex = visibleColumns.findIndex(column => column.field === focusedCell.field);\n  if (columnIndex === -1) {\n    return null;\n  }\n  return _extends({}, focusedCell, {\n    rowIndex,\n    columnIndex\n  });\n});", "map": {"version": 3, "names": ["_extends", "createSelector", "createSelectorMemoized", "gridVisibleColumnDefinitionsSelector", "gridRenderContextSelector", "gridFocusCellSelector", "gridVisibleRowsSelector", "gridIsFocusedCellOutOfContext", "focusedCell", "renderContext", "currentPage", "visibleColumns", "rowIndex", "rowIdToIndexMap", "get", "id", "columnIndex", "slice", "firstColumnIndex", "lastColumnIndex", "findIndex", "column", "field", "isInRenderContext", "undefined", "firstRowIndex", "lastRowIndex", "gridFocusedVirtualCellSelector", "isFocusedCellOutOfRenderContext"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/virtualization/gridFocusedVirtualCellSelector.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridRenderContextSelector } from \"./gridVirtualizationSelectors.js\";\nimport { gridFocusCellSelector } from \"../focus/index.js\";\nimport { gridVisibleRowsSelector } from \"../pagination/index.js\";\nconst gridIsFocusedCellOutOfContext = createSelector(gridFocusCellSelector, gridRenderContextSelector, gridVisibleRowsSelector, gridVisibleColumnDefinitionsSelector, (focusedCell, renderContext, currentPage, visibleColumns) => {\n  if (!focusedCell) {\n    return false;\n  }\n  const rowIndex = currentPage.rowIdToIndexMap.get(focusedCell.id);\n  const columnIndex = visibleColumns.slice(renderContext.firstColumnIndex, renderContext.lastColumnIndex).findIndex(column => column.field === focusedCell.field);\n  const isInRenderContext = rowIndex !== undefined && columnIndex !== -1 && rowIndex >= renderContext.firstRowIndex && rowIndex <= renderContext.lastRowIndex;\n  return !isInRenderContext;\n});\nexport const gridFocusedVirtualCellSelector = createSelectorMemoized(gridIsFocusedCellOutOfContext, gridVisibleColumnDefinitionsSelector, gridVisibleRowsSelector, gridFocusCellSelector, (isFocusedCellOutOfRenderContext, visibleColumns, currentPage, focusedCell) => {\n  if (!isFocusedCellOutOfRenderContext) {\n    return null;\n  }\n  const rowIndex = currentPage.rowIdToIndexMap.get(focusedCell.id);\n  if (rowIndex === undefined) {\n    return null;\n  }\n  const columnIndex = visibleColumns.findIndex(column => column.field === focusedCell.field);\n  if (columnIndex === -1) {\n    return null;\n  }\n  return _extends({}, focusedCell, {\n    rowIndex,\n    columnIndex\n  });\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,cAAc,EAAEC,sBAAsB,QAAQ,kCAAkC;AACzF,SAASC,oCAAoC,QAAQ,mCAAmC;AACxF,SAASC,yBAAyB,QAAQ,kCAAkC;AAC5E,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,MAAMC,6BAA6B,GAAGN,cAAc,CAACI,qBAAqB,EAAED,yBAAyB,EAAEE,uBAAuB,EAAEH,oCAAoC,EAAE,CAACK,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,cAAc,KAAK;EACjO,IAAI,CAACH,WAAW,EAAE;IAChB,OAAO,KAAK;EACd;EACA,MAAMI,QAAQ,GAAGF,WAAW,CAACG,eAAe,CAACC,GAAG,CAACN,WAAW,CAACO,EAAE,CAAC;EAChE,MAAMC,WAAW,GAAGL,cAAc,CAACM,KAAK,CAACR,aAAa,CAACS,gBAAgB,EAAET,aAAa,CAACU,eAAe,CAAC,CAACC,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKd,WAAW,CAACc,KAAK,CAAC;EAC/J,MAAMC,iBAAiB,GAAGX,QAAQ,KAAKY,SAAS,IAAIR,WAAW,KAAK,CAAC,CAAC,IAAIJ,QAAQ,IAAIH,aAAa,CAACgB,aAAa,IAAIb,QAAQ,IAAIH,aAAa,CAACiB,YAAY;EAC3J,OAAO,CAACH,iBAAiB;AAC3B,CAAC,CAAC;AACF,OAAO,MAAMI,8BAA8B,GAAGzB,sBAAsB,CAACK,6BAA6B,EAAEJ,oCAAoC,EAAEG,uBAAuB,EAAED,qBAAqB,EAAE,CAACuB,+BAA+B,EAAEjB,cAAc,EAAED,WAAW,EAAEF,WAAW,KAAK;EACvQ,IAAI,CAACoB,+BAA+B,EAAE;IACpC,OAAO,IAAI;EACb;EACA,MAAMhB,QAAQ,GAAGF,WAAW,CAACG,eAAe,CAACC,GAAG,CAACN,WAAW,CAACO,EAAE,CAAC;EAChE,IAAIH,QAAQ,KAAKY,SAAS,EAAE;IAC1B,OAAO,IAAI;EACb;EACA,MAAMR,WAAW,GAAGL,cAAc,CAACS,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKd,WAAW,CAACc,KAAK,CAAC;EAC1F,IAAIN,WAAW,KAAK,CAAC,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EACA,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEQ,WAAW,EAAE;IAC/BI,QAAQ;IACRI;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}