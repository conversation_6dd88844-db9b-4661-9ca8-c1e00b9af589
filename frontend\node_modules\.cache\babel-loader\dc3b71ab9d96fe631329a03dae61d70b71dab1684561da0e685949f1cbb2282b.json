{"ast": null, "code": "/**\n * I have hesitated to use https://github.com/eligrey/FileSaver.js.\n * If we get bug reports that this project solves, we should consider using it.\n *\n * Related resources.\n * https://blog.logrocket.com/programmatic-file-downloads-in-the-browser-9a5186298d5c/\n * https://github.com/mbrn/filefy/blob/ec4ed0b7415d93be7158c23029f2ea1fa0b8e2d9/src/core/BaseBuilder.ts\n * https://unpkg.com/browse/@progress/kendo-file-saver@1.0.7/dist/es/save-as.js\n * https://github.com/ag-grid/ag-grid/blob/9565c219b6210aa85fa833c929d0728f9d163a91/community-modules/csv-export/src/csvExport/downloader.ts\n */\n\nexport function exportAs(blob, extension = 'csv', filename = document.title || 'untitled') {\n  const fullName = `${filename}.${extension}`;\n\n  // Test download attribute first\n  // https://github.com/eligrey/FileSaver.js/issues/193\n  if ('download' in HTMLAnchorElement.prototype) {\n    // Create an object URL for the blob object\n    const url = URL.createObjectURL(blob);\n\n    // Create a new anchor element\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = fullName;\n\n    // Programmatically trigger a click on the anchor element\n    // Useful if you want the download to happen automatically\n    // Without attaching the anchor element to the DOM\n    a.click();\n\n    // https://github.com/eligrey/FileSaver.js/issues/205\n    setTimeout(() => {\n      URL.revokeObjectURL(url);\n    });\n    return;\n  }\n  throw new Error('MUI X: exportAs not supported.');\n}", "map": {"version": 3, "names": ["exportAs", "blob", "extension", "filename", "document", "title", "fullName", "HTMLAnchorElement", "prototype", "url", "URL", "createObjectURL", "a", "createElement", "href", "download", "click", "setTimeout", "revokeObjectURL", "Error"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/exportAs.js"], "sourcesContent": ["/**\n * I have hesitated to use https://github.com/eligrey/FileSaver.js.\n * If we get bug reports that this project solves, we should consider using it.\n *\n * Related resources.\n * https://blog.logrocket.com/programmatic-file-downloads-in-the-browser-9a5186298d5c/\n * https://github.com/mbrn/filefy/blob/ec4ed0b7415d93be7158c23029f2ea1fa0b8e2d9/src/core/BaseBuilder.ts\n * https://unpkg.com/browse/@progress/kendo-file-saver@1.0.7/dist/es/save-as.js\n * https://github.com/ag-grid/ag-grid/blob/9565c219b6210aa85fa833c929d0728f9d163a91/community-modules/csv-export/src/csvExport/downloader.ts\n */\n\nexport function exportAs(blob, extension = 'csv', filename = document.title || 'untitled') {\n  const fullName = `${filename}.${extension}`;\n\n  // Test download attribute first\n  // https://github.com/eligrey/FileSaver.js/issues/193\n  if ('download' in HTMLAnchorElement.prototype) {\n    // Create an object URL for the blob object\n    const url = URL.createObjectURL(blob);\n\n    // Create a new anchor element\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = fullName;\n\n    // Programmatically trigger a click on the anchor element\n    // Useful if you want the download to happen automatically\n    // Without attaching the anchor element to the DOM\n    a.click();\n\n    // https://github.com/eligrey/FileSaver.js/issues/205\n    setTimeout(() => {\n      URL.revokeObjectURL(url);\n    });\n    return;\n  }\n  throw new Error('MUI X: exportAs not supported.');\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASA,QAAQA,CAACC,IAAI,EAAEC,SAAS,GAAG,KAAK,EAAEC,QAAQ,GAAGC,QAAQ,CAACC,KAAK,IAAI,UAAU,EAAE;EACzF,MAAMC,QAAQ,GAAG,GAAGH,QAAQ,IAAID,SAAS,EAAE;;EAE3C;EACA;EACA,IAAI,UAAU,IAAIK,iBAAiB,CAACC,SAAS,EAAE;IAC7C;IACA,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACV,IAAI,CAAC;;IAErC;IACA,MAAMW,CAAC,GAAGR,QAAQ,CAACS,aAAa,CAAC,GAAG,CAAC;IACrCD,CAAC,CAACE,IAAI,GAAGL,GAAG;IACZG,CAAC,CAACG,QAAQ,GAAGT,QAAQ;;IAErB;IACA;IACA;IACAM,CAAC,CAACI,KAAK,CAAC,CAAC;;IAET;IACAC,UAAU,CAAC,MAAM;MACfP,GAAG,CAACQ,eAAe,CAACT,GAAG,CAAC;IAC1B,CAAC,CAAC;IACF;EACF;EACA,MAAM,IAAIU,KAAK,CAAC,gCAAgC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}