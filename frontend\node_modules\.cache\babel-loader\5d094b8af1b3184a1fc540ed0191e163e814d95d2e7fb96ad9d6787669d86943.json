{"ast": null, "code": "export function isObjectEmpty(object) {\n  // eslint-disable-next-line\n  for (const _ in object) {\n    return false;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["isObjectEmpty", "object", "_"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/isObjectEmpty/isObjectEmpty.js"], "sourcesContent": ["export function isObjectEmpty(object) {\n  // eslint-disable-next-line\n  for (const _ in object) {\n    return false;\n  }\n  return true;\n}"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAACC,MAAM,EAAE;EACpC;EACA,KAAK,MAAMC,CAAC,IAAID,MAAM,EAAE;IACtB,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}