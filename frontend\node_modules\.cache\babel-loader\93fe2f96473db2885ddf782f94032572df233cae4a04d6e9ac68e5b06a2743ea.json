{"ast": null, "code": "// Non printable keys have a name, for example \"ArrowR<PERSON>\", see the whole list:\n// https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_key_values\n// So event.key.length === 1 is often enough.\n//\n// However, we also need to ignore shortcuts, for example: select all:\n// - Windows: Ctrl+A, event.ctrlKey is true\n// - macOS: ⌘ Command+A, event.metaKey is true\nexport function isPrintableKey(event) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey;\n}\nexport const GRID_MULTIPLE_SELECTION_KEYS = ['Meta', 'Control', 'Shift'];\nexport const GRID_CELL_EXIT_EDIT_MODE_KEYS = ['Enter', 'Escape', 'Tab'];\nexport const GRID_CELL_EDIT_COMMIT_KEYS = ['Enter', 'Tab'];\nexport const isMultipleKey = key => GRID_MULTIPLE_SELECTION_KEYS.indexOf(key) > -1;\nexport const isCellEnterEditModeKeys = event => isPrintableKey(event) || event.key === 'Enter' || event.key === 'Backspace' || event.key === 'Delete';\nexport const isCellExitEditModeKeys = key => GRID_CELL_EXIT_EDIT_MODE_KEYS.indexOf(key) > -1;\nexport const isCellEditCommitKeys = key => GRID_CELL_EDIT_COMMIT_KEYS.indexOf(key) > -1;\nexport const isNavigationKey = key => key.indexOf('Arrow') === 0 || key.indexOf('Page') === 0 || key === ' ' || key === 'Home' || key === 'End';\nexport const isKeyboardEvent = event => !!event.key;\nexport const isHideMenuKey = key => key === 'Tab' || key === 'Escape';\n\n// In theory, on macOS, ctrl + v doesn't trigger a paste, so the function should return false.\n// However, maybe it's overkill to fix, so let's be lazy.\nexport function isPasteShortcut(event) {\n  return (event.ctrlKey || event.metaKey) &&\n  // We can't use event.code === 'KeyV' as event.code assumes a QWERTY keyboard layout,\n  // for example, it would be another letter on a Dvorak physical keyboard.\n  // We can't use event.key === 'v' as event.key is not stable with key modifiers and keyboard layouts,\n  // for example, it would be ה on a Hebrew keyboard layout.\n  // https://github.com/w3c/uievents/issues/377 could be a long-term solution\n  String.fromCharCode(event.keyCode) === 'V' && !event.shiftKey && !event.altKey;\n}\n\n// Checks if the keyboard event corresponds to the copy shortcut (CTRL+C or CMD+C) across different localization keyboards.\nexport function isCopyShortcut(event) {\n  return (event.ctrlKey || event.metaKey) && String.fromCharCode(event.keyCode) === 'C' && !event.shiftKey && !event.altKey;\n}", "map": {"version": 3, "names": ["isPrintableKey", "event", "key", "length", "ctrl<PERSON>ey", "metaKey", "GRID_MULTIPLE_SELECTION_KEYS", "GRID_CELL_EXIT_EDIT_MODE_KEYS", "GRID_CELL_EDIT_COMMIT_KEYS", "isMultipleKey", "indexOf", "isCellEnterEditModeKeys", "isCellExitEditModeKeys", "isCellEditCommitKeys", "isNavigationKey", "isKeyboardEvent", "isHideMenuKey", "isPasteShortcut", "String", "fromCharCode", "keyCode", "shift<PERSON>ey", "altKey", "isCopyShortcut"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/keyboardUtils.js"], "sourcesContent": ["// Non printable keys have a name, for example \"ArrowR<PERSON>\", see the whole list:\n// https://developer.mozilla.org/en-US/docs/Web/API/UI_Events/Keyboard_event_key_values\n// So event.key.length === 1 is often enough.\n//\n// However, we also need to ignore shortcuts, for example: select all:\n// - Windows: Ctrl+A, event.ctrlKey is true\n// - macOS: ⌘ Command+A, event.metaKey is true\nexport function isPrintableKey(event) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey;\n}\nexport const GRID_MULTIPLE_SELECTION_KEYS = ['Meta', 'Control', 'Shift'];\nexport const GRID_CELL_EXIT_EDIT_MODE_KEYS = ['Enter', 'Escape', 'Tab'];\nexport const GRID_CELL_EDIT_COMMIT_KEYS = ['Enter', 'Tab'];\nexport const isMultipleKey = key => GRID_MULTIPLE_SELECTION_KEYS.indexOf(key) > -1;\nexport const isCellEnterEditModeKeys = event => isPrintableKey(event) || event.key === 'Enter' || event.key === 'Backspace' || event.key === 'Delete';\nexport const isCellExitEditModeKeys = key => GRID_CELL_EXIT_EDIT_MODE_KEYS.indexOf(key) > -1;\nexport const isCellEditCommitKeys = key => GRID_CELL_EDIT_COMMIT_KEYS.indexOf(key) > -1;\nexport const isNavigationKey = key => key.indexOf('Arrow') === 0 || key.indexOf('Page') === 0 || key === ' ' || key === 'Home' || key === 'End';\nexport const isKeyboardEvent = event => !!event.key;\nexport const isHideMenuKey = key => key === 'Tab' || key === 'Escape';\n\n// In theory, on macOS, ctrl + v doesn't trigger a paste, so the function should return false.\n// However, maybe it's overkill to fix, so let's be lazy.\nexport function isPasteShortcut(event) {\n  return (event.ctrlKey || event.metaKey) &&\n  // We can't use event.code === 'KeyV' as event.code assumes a QWERTY keyboard layout,\n  // for example, it would be another letter on a Dvorak physical keyboard.\n  // We can't use event.key === 'v' as event.key is not stable with key modifiers and keyboard layouts,\n  // for example, it would be ה on a Hebrew keyboard layout.\n  // https://github.com/w3c/uievents/issues/377 could be a long-term solution\n  String.fromCharCode(event.keyCode) === 'V' && !event.shiftKey && !event.altKey;\n}\n\n// Checks if the keyboard event corresponds to the copy shortcut (CTRL+C or CMD+C) across different localization keyboards.\nexport function isCopyShortcut(event) {\n  return (event.ctrlKey || event.metaKey) && String.fromCharCode(event.keyCode) === 'C' && !event.shiftKey && !event.altKey;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAOA,KAAK,CAACC,GAAG,CAACC,MAAM,KAAK,CAAC,IAAI,CAACF,KAAK,CAACG,OAAO,IAAI,CAACH,KAAK,CAACI,OAAO;AACnE;AACA,OAAO,MAAMC,4BAA4B,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;AACxE,OAAO,MAAMC,6BAA6B,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;AACvE,OAAO,MAAMC,0BAA0B,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AAC1D,OAAO,MAAMC,aAAa,GAAGP,GAAG,IAAII,4BAA4B,CAACI,OAAO,CAACR,GAAG,CAAC,GAAG,CAAC,CAAC;AAClF,OAAO,MAAMS,uBAAuB,GAAGV,KAAK,IAAID,cAAc,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAID,KAAK,CAACC,GAAG,KAAK,WAAW,IAAID,KAAK,CAACC,GAAG,KAAK,QAAQ;AACrJ,OAAO,MAAMU,sBAAsB,GAAGV,GAAG,IAAIK,6BAA6B,CAACG,OAAO,CAACR,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5F,OAAO,MAAMW,oBAAoB,GAAGX,GAAG,IAAIM,0BAA0B,CAACE,OAAO,CAACR,GAAG,CAAC,GAAG,CAAC,CAAC;AACvF,OAAO,MAAMY,eAAe,GAAGZ,GAAG,IAAIA,GAAG,CAACQ,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAIR,GAAG,CAACQ,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAIR,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,KAAK;AAC/I,OAAO,MAAMa,eAAe,GAAGd,KAAK,IAAI,CAAC,CAACA,KAAK,CAACC,GAAG;AACnD,OAAO,MAAMc,aAAa,GAAGd,GAAG,IAAIA,GAAG,KAAK,KAAK,IAAIA,GAAG,KAAK,QAAQ;;AAErE;AACA;AACA,OAAO,SAASe,eAAeA,CAAChB,KAAK,EAAE;EACrC,OAAO,CAACA,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,OAAO;EACtC;EACA;EACA;EACA;EACA;EACAa,MAAM,CAACC,YAAY,CAAClB,KAAK,CAACmB,OAAO,CAAC,KAAK,GAAG,IAAI,CAACnB,KAAK,CAACoB,QAAQ,IAAI,CAACpB,KAAK,CAACqB,MAAM;AAChF;;AAEA;AACA,OAAO,SAASC,cAAcA,CAACtB,KAAK,EAAE;EACpC,OAAO,CAACA,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,OAAO,KAAKa,MAAM,CAACC,YAAY,CAAClB,KAAK,CAACmB,OAAO,CAAC,KAAK,GAAG,IAAI,CAACnB,KAAK,CAACoB,QAAQ,IAAI,CAACpB,KAAK,CAACqB,MAAM;AAC3H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}