{"ast": null, "code": "export function createControllablePromise() {\n  let resolve;\n  let reject;\n  const promise = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  promise.resolve = resolve;\n  promise.reject = reject;\n  return promise;\n}", "map": {"version": 3, "names": ["createControllablePromise", "resolve", "reject", "promise", "Promise", "_resolve", "_reject"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/createControllablePromise.js"], "sourcesContent": ["export function createControllablePromise() {\n  let resolve;\n  let reject;\n  const promise = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  promise.resolve = resolve;\n  promise.reject = reject;\n  return promise;\n}"], "mappings": "AAAA,OAAO,SAASA,yBAAyBA,CAAA,EAAG;EAC1C,IAAIC,OAAO;EACX,IAAIC,MAAM;EACV,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,QAAQ,EAAEC,OAAO,KAAK;IACjDL,OAAO,GAAGI,QAAQ;IAClBH,MAAM,GAAGI,OAAO;EAClB,CAAC,CAAC;EACFH,OAAO,CAACF,OAAO,GAAGA,OAAO;EACzBE,OAAO,CAACD,MAAM,GAAGA,MAAM;EACvB,OAAOC,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}