{"ast": null, "code": "class Semaphore {\n  capacity;\n  available;\n  deferredTasks = [];\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.available = capacity;\n  }\n  async acquire() {\n    if (this.available > 0) {\n      this.available--;\n      return;\n    }\n    return new Promise(resolve => {\n      this.deferredTasks.push(resolve);\n    });\n  }\n  release() {\n    const deferredTask = this.deferredTasks.shift();\n    if (deferredTask != null) {\n      deferredTask();\n      return;\n    }\n    if (this.available < this.capacity) {\n      this.available++;\n    }\n  }\n}\nexport { Semaphore };", "map": {"version": 3, "names": ["Semaphore", "capacity", "available", "deferredTasks", "constructor", "acquire", "Promise", "resolve", "push", "release", "deferredTask", "shift"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/promise/semaphore.mjs"], "sourcesContent": ["class Semaphore {\n    capacity;\n    available;\n    deferredTasks = [];\n    constructor(capacity) {\n        this.capacity = capacity;\n        this.available = capacity;\n    }\n    async acquire() {\n        if (this.available > 0) {\n            this.available--;\n            return;\n        }\n        return new Promise(resolve => {\n            this.deferredTasks.push(resolve);\n        });\n    }\n    release() {\n        const deferredTask = this.deferredTasks.shift();\n        if (deferredTask != null) {\n            deferredTask();\n            return;\n        }\n        if (this.available < this.capacity) {\n            this.available++;\n        }\n    }\n}\n\nexport { Semaphore };\n"], "mappings": "AAAA,MAAMA,SAAS,CAAC;EACZC,QAAQ;EACRC,SAAS;EACTC,aAAa,GAAG,EAAE;EAClBC,WAAWA,CAACH,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGD,QAAQ;EAC7B;EACA,MAAMI,OAAOA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACH,SAAS,GAAG,CAAC,EAAE;MACpB,IAAI,CAACA,SAAS,EAAE;MAChB;IACJ;IACA,OAAO,IAAII,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACJ,aAAa,CAACK,IAAI,CAACD,OAAO,CAAC;IACpC,CAAC,CAAC;EACN;EACAE,OAAOA,CAAA,EAAG;IACN,MAAMC,YAAY,GAAG,IAAI,CAACP,aAAa,CAACQ,KAAK,CAAC,CAAC;IAC/C,IAAID,YAAY,IAAI,IAAI,EAAE;MACtBA,YAAY,CAAC,CAAC;MACd;IACJ;IACA,IAAI,IAAI,CAACR,SAAS,GAAG,IAAI,CAACD,QAAQ,EAAE;MAChC,IAAI,CAACC,SAAS,EAAE;IACpB;EACJ;AACJ;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}