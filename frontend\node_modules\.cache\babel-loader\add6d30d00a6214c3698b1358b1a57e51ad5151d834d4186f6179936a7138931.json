{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GridColumnMenuHideItem } from \"./GridColumnMenuHideItem.js\";\nimport { GridColumnMenuManageItem } from \"./GridColumnMenuManageItem.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuColumnsItem(props) {\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GridColumnMenuHideItem, _extends({}, props)), /*#__PURE__*/_jsx(GridColumnMenuManageItem, _extends({}, props))]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuColumnsItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuColumnsItem };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "GridColumnMenuHideItem", "GridColumnMenuManageItem", "jsx", "_jsx", "jsxs", "_jsxs", "GridColumnMenuColumnsItem", "props", "Fragment", "children", "process", "env", "NODE_ENV", "propTypes", "colDef", "object", "isRequired", "onClick", "func"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/menu/columnMenu/menuItems/GridColumnMenuColumnsItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GridColumnMenuHideItem } from \"./GridColumnMenuHideItem.js\";\nimport { GridColumnMenuManageItem } from \"./GridColumnMenuManageItem.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction GridColumnMenuColumnsItem(props) {\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GridColumnMenuHideItem, _extends({}, props)), /*#__PURE__*/_jsx(GridColumnMenuManageItem, _extends({}, props))]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuColumnsItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuColumnsItem };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,SAASC,yBAAyBA,CAACC,KAAK,EAAE;EACxC,OAAO,aAAaF,KAAK,CAACP,KAAK,CAACU,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAaN,IAAI,CAACH,sBAAsB,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,CAAC,CAAC,EAAE,aAAaJ,IAAI,CAACF,wBAAwB,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,CAAC,CAAC;EAC7I,CAAC,CAAC;AACJ;AACAG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGN,yBAAyB,CAACO,SAAS,GAAG;EAC5E;EACA;EACA;EACA;EACAC,MAAM,EAAEf,SAAS,CAACgB,MAAM,CAACC,UAAU;EACnCC,OAAO,EAAElB,SAAS,CAACmB,IAAI,CAACF;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASV,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}