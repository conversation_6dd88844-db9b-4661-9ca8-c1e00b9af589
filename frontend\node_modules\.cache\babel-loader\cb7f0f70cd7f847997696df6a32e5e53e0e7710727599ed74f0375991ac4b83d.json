{"ast": null, "code": "function takeRightWhile(arr, shouldContinueTaking) {\n  for (let i = arr.length - 1; i >= 0; i--) {\n    if (!shouldContinueTaking(arr[i])) {\n      return arr.slice(i + 1);\n    }\n  }\n  return arr.slice();\n}\nexport { takeRightWhile };", "map": {"version": 3, "names": ["takeR<PERSON>While", "arr", "shouldContinueTaking", "i", "length", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/takeRightWhile.mjs"], "sourcesContent": ["function takeRightWhile(arr, shouldContinueTaking) {\n    for (let i = arr.length - 1; i >= 0; i--) {\n        if (!shouldContinueTaking(arr[i])) {\n            return arr.slice(i + 1);\n        }\n    }\n    return arr.slice();\n}\n\nexport { takeRightWhile };\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,oBAAoB,EAAE;EAC/C,KAAK,IAAIC,CAAC,GAAGF,GAAG,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACtC,IAAI,CAACD,oBAAoB,CAACD,GAAG,CAACE,CAAC,CAAC,CAAC,EAAE;MAC/B,OAAOF,GAAG,CAACI,KAAK,CAACF,CAAC,GAAG,CAAC,CAAC;IAC3B;EACJ;EACA,OAAOF,GAAG,CAACI,KAAK,CAAC,CAAC;AACtB;AAEA,SAASL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}