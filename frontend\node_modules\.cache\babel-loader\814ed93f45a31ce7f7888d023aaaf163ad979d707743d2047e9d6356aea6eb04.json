{"ast": null, "code": "function countBy(arr, mapper) {\n  const result = {};\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    const key = mapper(item);\n    result[key] = (result[key] ?? 0) + 1;\n  }\n  return result;\n}\nexport { countBy };", "map": {"version": 3, "names": ["countBy", "arr", "mapper", "result", "i", "length", "item", "key"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/countBy.mjs"], "sourcesContent": ["function countBy(arr, mapper) {\n    const result = {};\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        const key = mapper(item);\n        result[key] = (result[key] ?? 0) + 1;\n    }\n    return result;\n}\n\nexport { countBy };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC1B,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGL,GAAG,CAACG,CAAC,CAAC;IACnB,MAAMG,GAAG,GAAGL,MAAM,CAACI,IAAI,CAAC;IACxBH,MAAM,CAACI,GAAG,CAAC,GAAG,CAACJ,MAAM,CAACI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;EACxC;EACA,OAAOJ,MAAM;AACjB;AAEA,SAASH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}