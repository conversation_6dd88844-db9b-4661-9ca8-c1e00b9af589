{"ast": null, "code": "import { flatten } from './flatten.mjs';\nfunction flatMap(arr, iteratee, depth = 1) {\n  return flatten(arr.map(item => iteratee(item)), depth);\n}\nexport { flatMap };", "map": {"version": 3, "names": ["flatten", "flatMap", "arr", "iteratee", "depth", "map", "item"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/flatMap.mjs"], "sourcesContent": ["import { flatten } from './flatten.mjs';\n\nfunction flatMap(arr, iteratee, depth = 1) {\n    return flatten(arr.map(item => iteratee(item)), depth);\n}\n\nexport { flatMap };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AAEvC,SAASC,OAAOA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,GAAG,CAAC,EAAE;EACvC,OAAOJ,OAAO,CAACE,GAAG,CAACG,GAAG,CAACC,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAAC,CAAC,EAAEF,KAAK,CAAC;AAC1D;AAEA,SAASH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}