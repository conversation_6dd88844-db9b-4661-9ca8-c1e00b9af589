{"ast": null, "code": "import { useGridRefs } from \"./useGridRefs.js\";\nimport { useGridIsRtl } from \"./useGridIsRtl.js\";\nimport { useGridLoggerFactory } from \"./useGridLoggerFactory.js\";\nimport { useGridLocaleText } from \"./useGridLocaleText.js\";\nimport { useGridPipeProcessing } from \"./pipeProcessing/index.js\";\nimport { useGridStrategyProcessing } from \"./strategyProcessing/index.js\";\nimport { useGridStateInitialization } from \"./useGridStateInitialization.js\";\nimport { useGridProps } from \"./useGridProps.js\";\n\n/**\n * Initialize the technical pieces of the DataGrid (logger, state, ...) that any DataGrid implementation needs\n */\nexport const useGridInitialization = (privateApiRef, props) => {\n  useGridRefs(privateApiRef);\n  useGridProps(privateApiRef, props);\n  useGridIsRtl(privateApiRef);\n  useGridLoggerFactory(privateApiRef, props);\n  useGridStateInitialization(privateApiRef);\n  useGridPipeProcessing(privateApiRef);\n  useGridStrategyProcessing(privateApiRef);\n  useGridLocaleText(privateApiRef, props);\n  privateApiRef.current.register('private', {\n    rootProps: props\n  });\n};", "map": {"version": 3, "names": ["useGridRefs", "useGridIsRtl", "useGridLoggerFactory", "useGridLocaleText", "useGridPipeProcessing", "useGridStrategyProcessing", "useGridStateInitialization", "useGridProps", "useGridInitialization", "privateApiRef", "props", "current", "register", "rootProps"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/core/useGridInitialization.js"], "sourcesContent": ["import { useGridRefs } from \"./useGridRefs.js\";\nimport { useGridIsRtl } from \"./useGridIsRtl.js\";\nimport { useGridLoggerFactory } from \"./useGridLoggerFactory.js\";\nimport { useGridLocaleText } from \"./useGridLocaleText.js\";\nimport { useGridPipeProcessing } from \"./pipeProcessing/index.js\";\nimport { useGridStrategyProcessing } from \"./strategyProcessing/index.js\";\nimport { useGridStateInitialization } from \"./useGridStateInitialization.js\";\nimport { useGridProps } from \"./useGridProps.js\";\n\n/**\n * Initialize the technical pieces of the DataGrid (logger, state, ...) that any DataGrid implementation needs\n */\nexport const useGridInitialization = (privateApiRef, props) => {\n  useGridRefs(privateApiRef);\n  useGridProps(privateApiRef, props);\n  useGridIsRtl(privateApiRef);\n  useGridLoggerFactory(privateApiRef, props);\n  useGridStateInitialization(privateApiRef);\n  useGridPipeProcessing(privateApiRef);\n  useGridStrategyProcessing(privateApiRef);\n  useGridLocaleText(privateApiRef, props);\n  privateApiRef.current.register('private', {\n    rootProps: props\n  });\n};"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,YAAY,QAAQ,mBAAmB;;AAEhD;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,aAAa,EAAEC,KAAK,KAAK;EAC7DV,WAAW,CAACS,aAAa,CAAC;EAC1BF,YAAY,CAACE,aAAa,EAAEC,KAAK,CAAC;EAClCT,YAAY,CAACQ,aAAa,CAAC;EAC3BP,oBAAoB,CAACO,aAAa,EAAEC,KAAK,CAAC;EAC1CJ,0BAA0B,CAACG,aAAa,CAAC;EACzCL,qBAAqB,CAACK,aAAa,CAAC;EACpCJ,yBAAyB,CAACI,aAAa,CAAC;EACxCN,iBAAiB,CAACM,aAAa,EAAEC,KAAK,CAAC;EACvCD,aAAa,CAACE,OAAO,CAACC,QAAQ,CAAC,SAAS,EAAE;IACxCC,SAAS,EAAEH;EACb,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}