{"ast": null, "code": "export { default } from \"./useOnMount.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/utils/esm/useOnMount/index.js"], "sourcesContent": ["export { default } from \"./useOnMount.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}