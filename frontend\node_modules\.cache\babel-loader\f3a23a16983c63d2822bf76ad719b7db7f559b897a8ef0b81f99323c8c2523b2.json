{"ast": null, "code": "import { timeout } from './timeout.mjs';\nasync function withTimeout(run, ms) {\n  return Promise.race([run(), timeout(ms)]);\n}\nexport { withTimeout };", "map": {"version": 3, "names": ["timeout", "withTimeout", "run", "ms", "Promise", "race"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/promise/withTimeout.mjs"], "sourcesContent": ["import { timeout } from './timeout.mjs';\n\nasync function withTimeout(run, ms) {\n    return Promise.race([run(), timeout(ms)]);\n}\n\nexport { withTimeout };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AAEvC,eAAeC,WAAWA,CAACC,GAAG,EAAEC,EAAE,EAAE;EAChC,OAAOC,OAAO,CAACC,IAAI,CAAC,CAACH,GAAG,CAAC,CAAC,EAAEF,OAAO,CAACG,EAAE,CAAC,CAAC,CAAC;AAC7C;AAEA,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}