{"ast": null, "code": "/* We need to import the shim because React 17 does not support the `useSyncExternalStore` API.\n * More info: https://github.com/mui/mui-x/issues/18303#issuecomment-2958392341 */\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector';\nexport function useStore(store, selector, a1, a2, a3) {\n  const selectorWithArgs = state => selector(state, a1, a2, a3);\n  return useSyncExternalStoreWithSelector(store.subscribe, store.getSnapshot, store.getSnapshot, selectorWithArgs);\n}", "map": {"version": 3, "names": ["useSyncExternalStoreWithSelector", "useStore", "store", "selector", "a1", "a2", "a3", "selectorWithArgs", "state", "subscribe", "getSnapshot"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/store/useStore.js"], "sourcesContent": ["/* We need to import the shim because React 17 does not support the `useSyncExternalStore` API.\n * More info: https://github.com/mui/mui-x/issues/18303#issuecomment-2958392341 */\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector';\nexport function useStore(store, selector, a1, a2, a3) {\n  const selectorWithArgs = state => selector(state, a1, a2, a3);\n  return useSyncExternalStoreWithSelector(store.subscribe, store.getSnapshot, store.getSnapshot, selectorWithArgs);\n}"], "mappings": "AAAA;AACA;AACA,SAASA,gCAAgC,QAAQ,4CAA4C;AAC7F,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACpD,MAAMC,gBAAgB,GAAGC,KAAK,IAAIL,QAAQ,CAACK,KAAK,EAAEJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC7D,OAAON,gCAAgC,CAACE,KAAK,CAACO,SAAS,EAAEP,KAAK,CAACQ,WAAW,EAAER,KAAK,CAACQ,WAAW,EAAEH,gBAAgB,CAAC;AAClH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}