{"ast": null, "code": "import { useGridEventPriority } from \"../../utils/useGridEvent.js\";\n/**\n * @requires useGridFocus (event) - can be after, async only\n * @requires useGridColumns (event) - can be after, async only\n */\nexport function useGridEvents(apiRef, props) {\n  useGridEventPriority(apiRef, 'columnHeaderClick', props.onColumnHeaderClick);\n  useGridEventPriority(apiRef, 'columnHeaderContextMenu', props.onColumnHeaderContextMenu);\n  useGridEventPriority(apiRef, 'columnHeaderDoubleClick', props.onColumnHeaderDoubleClick);\n  useGridEventPriority(apiRef, 'columnHeaderOver', props.onColumnHeaderOver);\n  useGridEventPriority(apiRef, 'columnHeaderOut', props.onColumnHeaderOut);\n  useGridEventPriority(apiRef, 'columnHeaderEnter', props.onColumnHeaderEnter);\n  useGridEventPriority(apiRef, 'columnHeaderLeave', props.onColumnHeaderLeave);\n  useGridEventPriority(apiRef, 'cellClick', props.onCellClick);\n  useGridEventPriority(apiRef, 'cellDoubleClick', props.onCellDoubleClick);\n  useGridEventPriority(apiRef, 'cellKeyDown', props.onCellKeyDown);\n  useGridEventPriority(apiRef, 'preferencePanelClose', props.onPreferencePanelClose);\n  useGridEventPriority(apiRef, 'preferencePanelOpen', props.onPreferencePanelOpen);\n  useGridEventPriority(apiRef, 'menuOpen', props.onMenuOpen);\n  useGridEventPriority(apiRef, 'menuClose', props.onMenuClose);\n  useGridEventPriority(apiRef, 'rowDoubleClick', props.onRowDoubleClick);\n  useGridEventPriority(apiRef, 'rowClick', props.onRowClick);\n  useGridEventPriority(apiRef, 'stateChange', props.onStateChange);\n}", "map": {"version": 3, "names": ["useGridEventPriority", "useGridEvents", "apiRef", "props", "onColumnHeaderClick", "onColumnHeaderContextMenu", "onColumnHeaderDoubleClick", "onColumnHeaderOver", "onColumnHeaderOut", "onColumnHeaderEnter", "onColumnHeaderLeave", "onCellClick", "onCellDoubleClick", "onCellKeyDown", "onPreferencePanelClose", "onPreferencePanelOpen", "onMenuOpen", "onMenuClose", "onRowDoubleClick", "onRowClick", "onStateChange"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/events/useGridEvents.js"], "sourcesContent": ["import { useGridEventPriority } from \"../../utils/useGridEvent.js\";\n/**\n * @requires useGridFocus (event) - can be after, async only\n * @requires useGridColumns (event) - can be after, async only\n */\nexport function useGridEvents(apiRef, props) {\n  useGridEventPriority(apiRef, 'columnHeaderClick', props.onColumnHeaderClick);\n  useGridEventPriority(apiRef, 'columnHeaderContextMenu', props.onColumnHeaderContextMenu);\n  useGridEventPriority(apiRef, 'columnHeaderDoubleClick', props.onColumnHeaderDoubleClick);\n  useGridEventPriority(apiRef, 'columnHeaderOver', props.onColumnHeaderOver);\n  useGridEventPriority(apiRef, 'columnHeaderOut', props.onColumnHeaderOut);\n  useGridEventPriority(apiRef, 'columnHeaderEnter', props.onColumnHeaderEnter);\n  useGridEventPriority(apiRef, 'columnHeaderLeave', props.onColumnHeaderLeave);\n  useGridEventPriority(apiRef, 'cellClick', props.onCellClick);\n  useGridEventPriority(apiRef, 'cellDoubleClick', props.onCellDoubleClick);\n  useGridEventPriority(apiRef, 'cellKeyDown', props.onCellKeyDown);\n  useGridEventPriority(apiRef, 'preferencePanelClose', props.onPreferencePanelClose);\n  useGridEventPriority(apiRef, 'preferencePanelOpen', props.onPreferencePanelOpen);\n  useGridEventPriority(apiRef, 'menuOpen', props.onMenuOpen);\n  useGridEventPriority(apiRef, 'menuClose', props.onMenuClose);\n  useGridEventPriority(apiRef, 'rowDoubleClick', props.onRowDoubleClick);\n  useGridEventPriority(apiRef, 'rowClick', props.onRowClick);\n  useGridEventPriority(apiRef, 'stateChange', props.onStateChange);\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,6BAA6B;AAClE;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC3CH,oBAAoB,CAACE,MAAM,EAAE,mBAAmB,EAAEC,KAAK,CAACC,mBAAmB,CAAC;EAC5EJ,oBAAoB,CAACE,MAAM,EAAE,yBAAyB,EAAEC,KAAK,CAACE,yBAAyB,CAAC;EACxFL,oBAAoB,CAACE,MAAM,EAAE,yBAAyB,EAAEC,KAAK,CAACG,yBAAyB,CAAC;EACxFN,oBAAoB,CAACE,MAAM,EAAE,kBAAkB,EAAEC,KAAK,CAACI,kBAAkB,CAAC;EAC1EP,oBAAoB,CAACE,MAAM,EAAE,iBAAiB,EAAEC,KAAK,CAACK,iBAAiB,CAAC;EACxER,oBAAoB,CAACE,MAAM,EAAE,mBAAmB,EAAEC,KAAK,CAACM,mBAAmB,CAAC;EAC5ET,oBAAoB,CAACE,MAAM,EAAE,mBAAmB,EAAEC,KAAK,CAACO,mBAAmB,CAAC;EAC5EV,oBAAoB,CAACE,MAAM,EAAE,WAAW,EAAEC,KAAK,CAACQ,WAAW,CAAC;EAC5DX,oBAAoB,CAACE,MAAM,EAAE,iBAAiB,EAAEC,KAAK,CAACS,iBAAiB,CAAC;EACxEZ,oBAAoB,CAACE,MAAM,EAAE,aAAa,EAAEC,KAAK,CAACU,aAAa,CAAC;EAChEb,oBAAoB,CAACE,MAAM,EAAE,sBAAsB,EAAEC,KAAK,CAACW,sBAAsB,CAAC;EAClFd,oBAAoB,CAACE,MAAM,EAAE,qBAAqB,EAAEC,KAAK,CAACY,qBAAqB,CAAC;EAChFf,oBAAoB,CAACE,MAAM,EAAE,UAAU,EAAEC,KAAK,CAACa,UAAU,CAAC;EAC1DhB,oBAAoB,CAACE,MAAM,EAAE,WAAW,EAAEC,KAAK,CAACc,WAAW,CAAC;EAC5DjB,oBAAoB,CAACE,MAAM,EAAE,gBAAgB,EAAEC,KAAK,CAACe,gBAAgB,CAAC;EACtElB,oBAAoB,CAACE,MAAM,EAAE,UAAU,EAAEC,KAAK,CAACgB,UAAU,CAAC;EAC1DnB,oBAAoB,CAACE,MAAM,EAAE,aAAa,EAAEC,KAAK,CAACiB,aAAa,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}