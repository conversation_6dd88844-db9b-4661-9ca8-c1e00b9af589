{"ast": null, "code": "/* eslint-disable no-bitwise */\nexport function sortByDocumentPosition(a, b) {\n  if (!a.ref.current || !b.ref.current) {\n    return 0;\n  }\n  const position = a.ref.current.compareDocumentPosition(b.ref.current);\n  if (!position) {\n    return 0;\n  }\n  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n    return -1;\n  }\n  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {\n    return 1;\n  }\n  return 0;\n}", "map": {"version": 3, "names": ["sortByDocumentPosition", "a", "b", "ref", "current", "position", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "DOCUMENT_POSITION_CONTAINED_BY", "DOCUMENT_POSITION_PRECEDING", "DOCUMENT_POSITION_CONTAINS"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/toolbarV8/utils.js"], "sourcesContent": ["/* eslint-disable no-bitwise */\nexport function sortByDocumentPosition(a, b) {\n  if (!a.ref.current || !b.ref.current) {\n    return 0;\n  }\n  const position = a.ref.current.compareDocumentPosition(b.ref.current);\n  if (!position) {\n    return 0;\n  }\n  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n    return -1;\n  }\n  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {\n    return 1;\n  }\n  return 0;\n}"], "mappings": "AAAA;AACA,OAAO,SAASA,sBAAsBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC3C,IAAI,CAACD,CAAC,CAACE,GAAG,CAACC,OAAO,IAAI,CAACF,CAAC,CAACC,GAAG,CAACC,OAAO,EAAE;IACpC,OAAO,CAAC;EACV;EACA,MAAMC,QAAQ,GAAGJ,CAAC,CAACE,GAAG,CAACC,OAAO,CAACE,uBAAuB,CAACJ,CAAC,CAACC,GAAG,CAACC,OAAO,CAAC;EACrE,IAAI,CAACC,QAAQ,EAAE;IACb,OAAO,CAAC;EACV;EACA,IAAIA,QAAQ,GAAGE,IAAI,CAACC,2BAA2B,IAAIH,QAAQ,GAAGE,IAAI,CAACE,8BAA8B,EAAE;IACjG,OAAO,CAAC,CAAC;EACX;EACA,IAAIJ,QAAQ,GAAGE,IAAI,CAACG,2BAA2B,IAAIL,QAAQ,GAAGE,IAAI,CAACI,0BAA0B,EAAE;IAC7F,OAAO,CAAC;EACV;EACA,OAAO,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}