{"ast": null, "code": "import { createAnimateManager } from './AnimationManager';\nimport { RequestAnimationFrameTimeoutController } from './timeoutController';\nexport function createDefaultAnimationManager() {\n  return createAnimateManager(new RequestAnimationFrameTimeoutController());\n}", "map": {"version": 3, "names": ["createAnimateManager", "RequestAnimationFrameTimeoutController", "createDefaultAnimationManager"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/recharts/es6/animation/createDefaultAnimationManager.js"], "sourcesContent": ["import { createAnimateManager } from './AnimationManager';\nimport { RequestAnimationFrameTimeoutController } from './timeoutController';\nexport function createDefaultAnimationManager() {\n  return createAnimateManager(new RequestAnimationFrameTimeoutController());\n}"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,oBAAoB;AACzD,SAASC,sCAAsC,QAAQ,qBAAqB;AAC5E,OAAO,SAASC,6BAA6BA,CAAA,EAAG;EAC9C,OAAOF,oBAAoB,CAAC,IAAIC,sCAAsC,CAAC,CAAC,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}