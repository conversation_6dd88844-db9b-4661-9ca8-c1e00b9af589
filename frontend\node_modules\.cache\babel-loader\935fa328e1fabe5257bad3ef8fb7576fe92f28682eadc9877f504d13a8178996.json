{"ast": null, "code": "class TimeoutError extends Error {\n  constructor(message = 'The operation was timed out') {\n    super(message);\n    this.name = 'TimeoutError';\n  }\n}\nexport { TimeoutError };", "map": {"version": 3, "names": ["TimeoutError", "Error", "constructor", "message", "name"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/error/TimeoutError.mjs"], "sourcesContent": ["class TimeoutError extends Error {\n    constructor(message = 'The operation was timed out') {\n        super(message);\n        this.name = 'TimeoutError';\n    }\n}\n\nexport { TimeoutError };\n"], "mappings": "AAAA,MAAMA,YAAY,SAASC,KAAK,CAAC;EAC7BC,WAAWA,CAACC,OAAO,GAAG,6BAA6B,EAAE;IACjD,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,cAAc;EAC9B;AACJ;AAEA,SAASJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}