{"ast": null, "code": "import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/index.js\";\nexport const useGridStatePersistence = apiRef => {\n  const exportState = React.useCallback((params = {}) => {\n    const stateToExport = apiRef.current.unstable_applyPipeProcessors('exportState', {}, params);\n    return stateToExport;\n  }, [apiRef]);\n  const restoreState = React.useCallback(stateToRestore => {\n    const response = apiRef.current.unstable_applyPipeProcessors('restoreState', {\n      callbacks: []\n    }, {\n      stateToRestore\n    });\n    response.callbacks.forEach(callback => {\n      callback();\n    });\n  }, [apiRef]);\n  const statePersistenceApi = {\n    exportState,\n    restoreState\n  };\n  useGridApiMethod(apiRef, statePersistenceApi, 'public');\n};", "map": {"version": 3, "names": ["React", "useGridApiMethod", "useGridStatePersistence", "apiRef", "exportState", "useCallback", "params", "stateToExport", "current", "unstable_applyPipeProcessors", "restoreState", "stateToRestore", "response", "callbacks", "for<PERSON>ach", "callback", "statePersistenceApi"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/statePersistence/useGridStatePersistence.js"], "sourcesContent": ["import * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/index.js\";\nexport const useGridStatePersistence = apiRef => {\n  const exportState = React.useCallback((params = {}) => {\n    const stateToExport = apiRef.current.unstable_applyPipeProcessors('exportState', {}, params);\n    return stateToExport;\n  }, [apiRef]);\n  const restoreState = React.useCallback(stateToRestore => {\n    const response = apiRef.current.unstable_applyPipeProcessors('restoreState', {\n      callbacks: []\n    }, {\n      stateToRestore\n    });\n    response.callbacks.forEach(callback => {\n      callback();\n    });\n  }, [apiRef]);\n  const statePersistenceApi = {\n    exportState,\n    restoreState\n  };\n  useGridApiMethod(apiRef, statePersistenceApi, 'public');\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,OAAO,MAAMC,uBAAuB,GAAGC,MAAM,IAAI;EAC/C,MAAMC,WAAW,GAAGJ,KAAK,CAACK,WAAW,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IACrD,MAAMC,aAAa,GAAGJ,MAAM,CAACK,OAAO,CAACC,4BAA4B,CAAC,aAAa,EAAE,CAAC,CAAC,EAAEH,MAAM,CAAC;IAC5F,OAAOC,aAAa;EACtB,CAAC,EAAE,CAACJ,MAAM,CAAC,CAAC;EACZ,MAAMO,YAAY,GAAGV,KAAK,CAACK,WAAW,CAACM,cAAc,IAAI;IACvD,MAAMC,QAAQ,GAAGT,MAAM,CAACK,OAAO,CAACC,4BAA4B,CAAC,cAAc,EAAE;MAC3EI,SAAS,EAAE;IACb,CAAC,EAAE;MACDF;IACF,CAAC,CAAC;IACFC,QAAQ,CAACC,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI;MACrCA,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EACZ,MAAMa,mBAAmB,GAAG;IAC1BZ,WAAW;IACXM;EACF,CAAC;EACDT,gBAAgB,CAACE,MAAM,EAAEa,mBAAmB,EAAE,QAAQ,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}