{"ast": null, "code": "function keyBy(arr, getKeyFromItem) {\n  const result = {};\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    const key = getKeyFromItem(item);\n    result[key] = item;\n  }\n  return result;\n}\nexport { keyBy };", "map": {"version": 3, "names": ["keyBy", "arr", "getKeyFromItem", "result", "i", "length", "item", "key"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/keyBy.mjs"], "sourcesContent": ["function keyBy(arr, getKeyFromItem) {\n    const result = {};\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        const key = getKeyFromItem(item);\n        result[key] = item;\n    }\n    return result;\n}\n\nexport { keyBy };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,GAAG,EAAEC,cAAc,EAAE;EAChC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGL,GAAG,CAACG,CAAC,CAAC;IACnB,MAAMG,GAAG,GAAGL,cAAc,CAACI,IAAI,CAAC;IAChCH,MAAM,CAACI,GAAG,CAAC,GAAGD,IAAI;EACtB;EACA,OAAOH,MAAM;AACjB;AAEA,SAASH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}