{"ast": null, "code": "function after(n, func) {\n  if (!Number.isInteger(n) || n < 0) {\n    throw new Error(`n must be a non-negative integer.`);\n  }\n  let counter = 0;\n  return (...args) => {\n    if (++counter >= n) {\n      return func(...args);\n    }\n    return undefined;\n  };\n}\nexport { after };", "map": {"version": 3, "names": ["after", "n", "func", "Number", "isInteger", "Error", "counter", "args", "undefined"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/after.mjs"], "sourcesContent": ["function after(n, func) {\n    if (!Number.isInteger(n) || n < 0) {\n        throw new Error(`n must be a non-negative integer.`);\n    }\n    let counter = 0;\n    return (...args) => {\n        if (++counter >= n) {\n            return func(...args);\n        }\n        return undefined;\n    };\n}\n\nexport { after };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,CAAC,EAAEC,IAAI,EAAE;EACpB,IAAI,CAACC,MAAM,CAACC,SAAS,CAACH,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;IAC/B,MAAM,IAAII,KAAK,CAAC,mCAAmC,CAAC;EACxD;EACA,IAAIC,OAAO,GAAG,CAAC;EACf,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChB,IAAI,EAAED,OAAO,IAAIL,CAAC,EAAE;MAChB,OAAOC,IAAI,CAAC,GAAGK,IAAI,CAAC;IACxB;IACA,OAAOC,SAAS;EACpB,CAAC;AACL;AAEA,SAASR,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}