{"ast": null, "code": "function isPrimitive(value) {\n  return value == null || typeof value !== 'object' && typeof value !== 'function';\n}\nexport { isPrimitive };", "map": {"version": 3, "names": ["isPrimitive", "value"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isPrimitive.mjs"], "sourcesContent": ["function isPrimitive(value) {\n    return value == null || (typeof value !== 'object' && typeof value !== 'function');\n}\n\nexport { isPrimitive };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,KAAK,EAAE;EACxB,OAAOA,KAAK,IAAI,IAAI,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAW;AACtF;AAEA,SAASD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}