{"ast": null, "code": "import { warnOnce } from '@mui/x-internals/warning';\nimport { isNumber } from \"../../utils/utils.js\";\nimport { GridSignature } from \"../../constants/signature.js\";\nexport const propValidatorsDataGrid = [props => props.autoPageSize && props.autoHeight && ['MUI X: `<DataGrid autoPageSize={true} autoHeight={true} />` are not valid props.', 'You cannot use both the `autoPageSize` and `autoHeight` props at the same time because `autoHeight` scales the height of the Data Grid according to the `pageSize`.', '', 'Please remove one of these two props.'].join('\\n') || undefined, props => props.paginationMode === 'client' && props.paginationMeta != null && ['MUI X: Usage of the `paginationMeta` prop with client-side pagination (`paginationMode=\"client\"`) has no effect.', '`paginationMeta` is only meant to be used with `paginationMode=\"server\"`.'].join('\\n') || undefined, props => props.signature === GridSignature.DataGrid && props.paginationMode === 'client' && isNumber(props.rowCount) && ['MUI X: Usage of the `rowCount` prop with client side pagination (`paginationMode=\"client\"`) has no effect.', '`rowCount` is only meant to be used with `paginationMode=\"server\"`.'].join('\\n') || undefined, props => props.paginationMode === 'server' && props.rowCount == null && !props.dataSource && [\"MUI X: The `rowCount` prop must be passed using `paginationMode='server'`\", 'For more detail, see http://mui.com/components/data-grid/pagination/#index-based-pagination'].join('\\n') || undefined];\nexport function validateProps(props, validators) {\n  validators.forEach(validator => {\n    const message = validator(props);\n    if (message) {\n      warnOnce(message, 'error');\n    }\n  });\n}", "map": {"version": 3, "names": ["warnOnce", "isNumber", "GridSignature", "propValidatorsDataGrid", "props", "autoPageSize", "autoHeight", "join", "undefined", "paginationMode", "paginationMeta", "signature", "DataGrid", "rowCount", "dataSource", "validateProps", "validators", "for<PERSON>ach", "validator", "message"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/internals/utils/propValidation.js"], "sourcesContent": ["import { warnOnce } from '@mui/x-internals/warning';\nimport { isNumber } from \"../../utils/utils.js\";\nimport { GridSignature } from \"../../constants/signature.js\";\nexport const propValidatorsDataGrid = [props => props.autoPageSize && props.autoHeight && ['MUI X: `<DataGrid autoPageSize={true} autoHeight={true} />` are not valid props.', 'You cannot use both the `autoPageSize` and `autoHeight` props at the same time because `autoHeight` scales the height of the Data Grid according to the `pageSize`.', '', 'Please remove one of these two props.'].join('\\n') || undefined, props => props.paginationMode === 'client' && props.paginationMeta != null && ['MUI X: Usage of the `paginationMeta` prop with client-side pagination (`paginationMode=\"client\"`) has no effect.', '`paginationMeta` is only meant to be used with `paginationMode=\"server\"`.'].join('\\n') || undefined, props => props.signature === GridSignature.DataGrid && props.paginationMode === 'client' && isNumber(props.rowCount) && ['MUI X: Usage of the `rowCount` prop with client side pagination (`paginationMode=\"client\"`) has no effect.', '`rowCount` is only meant to be used with `paginationMode=\"server\"`.'].join('\\n') || undefined, props => props.paginationMode === 'server' && props.rowCount == null && !props.dataSource && [\"MUI X: The `rowCount` prop must be passed using `paginationMode='server'`\", 'For more detail, see http://mui.com/components/data-grid/pagination/#index-based-pagination'].join('\\n') || undefined];\nexport function validateProps(props, validators) {\n  validators.forEach(validator => {\n    const message = validator(props);\n    if (message) {\n      warnOnce(message, 'error');\n    }\n  });\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,OAAO,MAAMC,sBAAsB,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACC,YAAY,IAAID,KAAK,CAACE,UAAU,IAAI,CAAC,kFAAkF,EAAE,qKAAqK,EAAE,EAAE,EAAE,uCAAuC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,IAAIC,SAAS,EAAEJ,KAAK,IAAIA,KAAK,CAACK,cAAc,KAAK,QAAQ,IAAIL,KAAK,CAACM,cAAc,IAAI,IAAI,IAAI,CAAC,kHAAkH,EAAE,2EAA2E,CAAC,CAACH,IAAI,CAAC,IAAI,CAAC,IAAIC,SAAS,EAAEJ,KAAK,IAAIA,KAAK,CAACO,SAAS,KAAKT,aAAa,CAACU,QAAQ,IAAIR,KAAK,CAACK,cAAc,KAAK,QAAQ,IAAIR,QAAQ,CAACG,KAAK,CAACS,QAAQ,CAAC,IAAI,CAAC,4GAA4G,EAAE,qEAAqE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,IAAIC,SAAS,EAAEJ,KAAK,IAAIA,KAAK,CAACK,cAAc,KAAK,QAAQ,IAAIL,KAAK,CAACS,QAAQ,IAAI,IAAI,IAAI,CAACT,KAAK,CAACU,UAAU,IAAI,CAAC,2EAA2E,EAAE,6FAA6F,CAAC,CAACP,IAAI,CAAC,IAAI,CAAC,IAAIC,SAAS,CAAC;AAC9yC,OAAO,SAASO,aAAaA,CAACX,KAAK,EAAEY,UAAU,EAAE;EAC/CA,UAAU,CAACC,OAAO,CAACC,SAAS,IAAI;IAC9B,MAAMC,OAAO,GAAGD,SAAS,CAACd,KAAK,CAAC;IAChC,IAAIe,OAAO,EAAE;MACXnB,QAAQ,CAACmB,OAAO,EAAE,OAAO,CAAC;IAC5B;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}