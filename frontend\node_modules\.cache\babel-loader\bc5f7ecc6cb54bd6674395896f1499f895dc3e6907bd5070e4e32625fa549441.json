{"ast": null, "code": "import { median } from './median.mjs';\nfunction medianBy(items, getValue) {\n  const nums = items.map(x => getValue(x));\n  return median(nums);\n}\nexport { medianBy };", "map": {"version": 3, "names": ["median", "medianBy", "items", "getValue", "nums", "map", "x"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/math/medianBy.mjs"], "sourcesContent": ["import { median } from './median.mjs';\n\nfunction medianBy(items, getValue) {\n    const nums = items.map(x => getValue(x));\n    return median(nums);\n}\n\nexport { medianBy };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,SAASC,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC/B,MAAMC,IAAI,GAAGF,KAAK,CAACG,GAAG,CAACC,CAAC,IAAIH,QAAQ,CAACG,CAAC,CAAC,CAAC;EACxC,OAAON,MAAM,CAACI,IAAI,CAAC;AACvB;AAEA,SAASH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}