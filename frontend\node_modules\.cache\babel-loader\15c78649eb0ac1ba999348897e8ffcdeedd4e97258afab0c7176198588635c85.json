{"ast": null, "code": "function memoize(fn, options = {}) {\n  const {\n    cache = new Map(),\n    getCacheKey\n  } = options;\n  const memoizedFn = function (arg) {\n    const key = getCacheKey ? getCacheKey(arg) : arg;\n    if (cache.has(key)) {\n      return cache.get(key);\n    }\n    const result = fn.call(this, arg);\n    cache.set(key, result);\n    return result;\n  };\n  memoizedFn.cache = cache;\n  return memoizedFn;\n}\nexport { memoize };", "map": {"version": 3, "names": ["memoize", "fn", "options", "cache", "Map", "get<PERSON><PERSON><PERSON><PERSON>", "memoizedFn", "arg", "key", "has", "get", "result", "call", "set"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/memoize.mjs"], "sourcesContent": ["function memoize(fn, options = {}) {\n    const { cache = new Map(), getCacheKey } = options;\n    const memoizedFn = function (arg) {\n        const key = getCacheKey ? getCacheKey(arg) : arg;\n        if (cache.has(key)) {\n            return cache.get(key);\n        }\n        const result = fn.call(this, arg);\n        cache.set(key, result);\n        return result;\n    };\n    memoizedFn.cache = cache;\n    return memoizedFn;\n}\n\nexport { memoize };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,EAAE,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC/B,MAAM;IAAEC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IAAEC;EAAY,CAAC,GAAGH,OAAO;EAClD,MAAMI,UAAU,GAAG,SAAAA,CAAUC,GAAG,EAAE;IAC9B,MAAMC,GAAG,GAAGH,WAAW,GAAGA,WAAW,CAACE,GAAG,CAAC,GAAGA,GAAG;IAChD,IAAIJ,KAAK,CAACM,GAAG,CAACD,GAAG,CAAC,EAAE;MAChB,OAAOL,KAAK,CAACO,GAAG,CAACF,GAAG,CAAC;IACzB;IACA,MAAMG,MAAM,GAAGV,EAAE,CAACW,IAAI,CAAC,IAAI,EAAEL,GAAG,CAAC;IACjCJ,KAAK,CAACU,GAAG,CAACL,GAAG,EAAEG,MAAM,CAAC;IACtB,OAAOA,MAAM;EACjB,CAAC;EACDL,UAAU,CAACH,KAAK,GAAGA,KAAK;EACxB,OAAOG,UAAU;AACrB;AAEA,SAASN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}