{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction identity(x) {\n  return x;\n}\nexports.identity = identity;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "identity", "x"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/identity.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction identity(x) {\n    return x;\n}\n\nexports.identity = identity;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,QAAQA,CAACC,CAAC,EAAE;EACjB,OAAOA,CAAC;AACZ;AAEAL,OAAO,CAACI,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}