{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridClasses, getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['bottomContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nconst Element = styled('div')({\n  position: 'sticky',\n  zIndex: 40,\n  bottom: 'calc(var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize))'\n});\nexport function GridBottomContainer(props) {\n  const classes = useUtilityClasses();\n  return /*#__PURE__*/_jsx(Element, _extends({}, props, {\n    className: clsx(classes.root, gridClasses['container--bottom']),\n    role: \"presentation\"\n  }));\n}", "map": {"version": 3, "names": ["_extends", "React", "clsx", "styled", "composeClasses", "gridClasses", "getDataGridUtilityClass", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "Element", "position", "zIndex", "bottom", "GridBottomContainer", "props", "classes", "className", "role"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/virtualization/GridBottomContainer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport { styled } from '@mui/system';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { gridClasses, getDataGridUtilityClass } from \"../../constants/gridClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['bottomContainer']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, {});\n};\nconst Element = styled('div')({\n  position: 'sticky',\n  zIndex: 40,\n  bottom: 'calc(var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize))'\n});\nexport function GridBottomContainer(props) {\n  const classes = useUtilityClasses();\n  return /*#__PURE__*/_jsx(Element, _extends({}, props, {\n    className: clsx(classes.root, gridClasses['container--bottom']),\n    role: \"presentation\"\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,WAAW,EAAEC,uBAAuB,QAAQ,gCAAgC;AACrF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,iBAAiB;EAC1B,CAAC;EACD,OAAOP,cAAc,CAACM,KAAK,EAAEJ,uBAAuB,EAAE,CAAC,CAAC,CAAC;AAC3D,CAAC;AACD,MAAMM,OAAO,GAAGT,MAAM,CAAC,KAAK,CAAC,CAAC;EAC5BU,QAAQ,EAAE,QAAQ;EAClBC,MAAM,EAAE,EAAE;EACVC,MAAM,EAAE;AACV,CAAC,CAAC;AACF,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,MAAMC,OAAO,GAAGT,iBAAiB,CAAC,CAAC;EACnC,OAAO,aAAaD,IAAI,CAACI,OAAO,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEiB,KAAK,EAAE;IACpDE,SAAS,EAAEjB,IAAI,CAACgB,OAAO,CAACP,IAAI,EAAEN,WAAW,CAAC,mBAAmB,CAAC,CAAC;IAC/De,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}