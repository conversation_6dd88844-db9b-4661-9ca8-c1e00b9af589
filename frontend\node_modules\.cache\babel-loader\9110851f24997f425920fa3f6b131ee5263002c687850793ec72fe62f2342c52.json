{"ast": null, "code": "export class FinalizationRegistryBasedCleanupTracking {\n  registry = (() => new FinalizationRegistry(unsubscribe => {\n    if (typeof unsubscribe === 'function') {\n      unsubscribe();\n    }\n  }))();\n  register(object, unsubscribe, unregisterToken) {\n    this.registry.register(object, unsubscribe, unregisterToken);\n  }\n  unregister(unregisterToken) {\n    this.registry.unregister(unregisterToken);\n  }\n  reset() {}\n}", "map": {"version": 3, "names": ["FinalizationRegistryBasedCleanupTracking", "registry", "FinalizationRegistry", "unsubscribe", "register", "object", "unregisterToken", "unregister", "reset"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/cleanupTracking/FinalizationRegistryBasedCleanupTracking.js"], "sourcesContent": ["export class FinalizationRegistryBasedCleanupTracking {\n  registry = (() => new FinalizationRegistry(unsubscribe => {\n    if (typeof unsubscribe === 'function') {\n      unsubscribe();\n    }\n  }))();\n  register(object, unsubscribe, unregisterToken) {\n    this.registry.register(object, unsubscribe, unregisterToken);\n  }\n  unregister(unregisterToken) {\n    this.registry.unregister(unregisterToken);\n  }\n  reset() {}\n}"], "mappings": "AAAA,OAAO,MAAMA,wCAAwC,CAAC;EACpDC,QAAQ,GAAG,CAAC,MAAM,IAAIC,oBAAoB,CAACC,WAAW,IAAI;IACxD,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;MACrCA,WAAW,CAAC,CAAC;IACf;EACF,CAAC,CAAC,EAAE,CAAC;EACLC,QAAQA,CAACC,MAAM,EAAEF,WAAW,EAAEG,eAAe,EAAE;IAC7C,IAAI,CAACL,QAAQ,CAACG,QAAQ,CAACC,MAAM,EAAEF,WAAW,EAAEG,eAAe,CAAC;EAC9D;EACAC,UAAUA,CAACD,eAAe,EAAE;IAC1B,IAAI,CAACL,QAAQ,CAACM,UAAU,CAACD,eAAe,CAAC;EAC3C;EACAE,KAAKA,CAAA,EAAG,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}