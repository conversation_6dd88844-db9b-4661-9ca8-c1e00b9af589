{"ast": null, "code": "function takeWhile(arr, shouldContinueTaking) {\n  const result = [];\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    if (!shouldContinueTaking(item)) {\n      break;\n    }\n    result.push(item);\n  }\n  return result;\n}\nexport { takeWhile };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "arr", "shouldContinueTaking", "result", "i", "length", "item", "push"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/takeWhile.mjs"], "sourcesContent": ["function takeWhile(arr, shouldContinueTaking) {\n    const result = [];\n    for (let i = 0; i < arr.length; i++) {\n        const item = arr[i];\n        if (!shouldContinueTaking(item)) {\n            break;\n        }\n        result.push(item);\n    }\n    return result;\n}\n\nexport { takeWhile };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,GAAG,EAAEC,oBAAoB,EAAE;EAC1C,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGL,GAAG,CAACG,CAAC,CAAC;IACnB,IAAI,CAACF,oBAAoB,CAACI,IAAI,CAAC,EAAE;MAC7B;IACJ;IACAH,MAAM,CAACI,IAAI,CAACD,IAAI,CAAC;EACrB;EACA,OAAOH,MAAM;AACjB;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}