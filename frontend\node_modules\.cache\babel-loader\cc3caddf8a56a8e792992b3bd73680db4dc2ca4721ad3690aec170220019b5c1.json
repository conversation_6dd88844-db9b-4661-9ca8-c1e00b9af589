{"ast": null, "code": "const deburrMap = new Map(Object.entries({\n  Æ: 'Ae',\n  Ð: 'D',\n  Ø: 'O',\n  Þ: 'Th',\n  ß: 'ss',\n  æ: 'ae',\n  ð: 'd',\n  ø: 'o',\n  þ: 'th',\n  Đ: 'D',\n  đ: 'd',\n  Ħ: 'H',\n  ħ: 'h',\n  ı: 'i',\n  Ĳ: 'IJ',\n  ĳ: 'ij',\n  ĸ: 'k',\n  Ŀ: 'L',\n  ŀ: 'l',\n  Ł: 'L',\n  ł: 'l',\n  ŉ: \"'n\",\n  Ŋ: 'N',\n  ŋ: 'n',\n  Œ: 'Oe',\n  œ: 'oe',\n  Ŧ: 'T',\n  ŧ: 't',\n  ſ: 's'\n}));\nfunction deburr(str) {\n  str = str.normalize('NFD');\n  let result = '';\n  for (let i = 0; i < str.length; i++) {\n    const char = str[i];\n    if (char >= '\\u0300' && char <= '\\u036f' || char >= '\\ufe20' && char <= '\\ufe23') {\n      continue;\n    }\n    result += deburrMap.get(char) ?? char;\n  }\n  return result;\n}\nexport { deburr };", "map": {"version": 3, "names": ["deburrMap", "Map", "Object", "entries", "<PERSON>", "Ð", "Ø", "Þ", "ß", "æ", "ð", "ø", "þ", "Đ", "đ", "Ħ", "ħ", "ı", "Ĳ", "ĳ", "ĸ", "Ŀ", "ŀ", "Ł", "ł", "ŉ", "Ŋ", "ŋ", "Œ", "œ", "Ŧ", "ŧ", "ſ", "deburr", "str", "normalize", "result", "i", "length", "char", "get"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/string/deburr.mjs"], "sourcesContent": ["const deburrMap = new Map(Object.entries({\n    Æ: 'Ae',\n    Ð: 'D',\n    Ø: 'O',\n    Þ: 'Th',\n    ß: 'ss',\n    æ: 'ae',\n    ð: 'd',\n    ø: 'o',\n    þ: 'th',\n    Đ: 'D',\n    đ: 'd',\n    Ħ: 'H',\n    ħ: 'h',\n    ı: 'i',\n    Ĳ: 'IJ',\n    ĳ: 'ij',\n    ĸ: 'k',\n    Ŀ: 'L',\n    ŀ: 'l',\n    Ł: 'L',\n    ł: 'l',\n    ŉ: \"'n\",\n    Ŋ: 'N',\n    ŋ: 'n',\n    Œ: 'Oe',\n    œ: 'oe',\n    Ŧ: 'T',\n    ŧ: 't',\n    ſ: 's',\n}));\nfunction deburr(str) {\n    str = str.normalize('NFD');\n    let result = '';\n    for (let i = 0; i < str.length; i++) {\n        const char = str[i];\n        if ((char >= '\\u0300' && char <= '\\u036f') || (char >= '\\ufe20' && char <= '\\ufe23')) {\n            continue;\n        }\n        result += deburrMap.get(char) ?? char;\n    }\n    return result;\n}\n\nexport { deburr };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACC,OAAO,CAAC;EACrCC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,IAAI;EACPC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE;AACP,CAAC,CAAC,CAAC;AACH,SAASC,MAAMA,CAACC,GAAG,EAAE;EACjBA,GAAG,GAAGA,GAAG,CAACC,SAAS,CAAC,KAAK,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,IAAI,GAAGL,GAAG,CAACG,CAAC,CAAC;IACnB,IAAKE,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,QAAQ,IAAMA,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,QAAS,EAAE;MAClF;IACJ;IACAH,MAAM,IAAIpC,SAAS,CAACwC,GAAG,CAACD,IAAI,CAAC,IAAIA,IAAI;EACzC;EACA,OAAOH,MAAM;AACjB;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}