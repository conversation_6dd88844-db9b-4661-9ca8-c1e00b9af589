{"ast": null, "code": "import { rtlFlipSide } from \"../../utils/rtlFlipSide.js\";\nexport function attachPinnedStyle(style, isRtl, pinnedPosition, pinnedOffset) {\n  const side = rtlFlipSide(pinnedPosition, isRtl);\n  if (!side || pinnedOffset === undefined) {\n    return style;\n  }\n  style[side] = pinnedOffset;\n  return style;\n}", "map": {"version": 3, "names": ["rtlFlipSide", "attachPinnedStyle", "style", "isRtl", "pinnedPosition", "pinnedOffset", "side", "undefined"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/internals/utils/attachPinnedStyle.js"], "sourcesContent": ["import { rtlFlipSide } from \"../../utils/rtlFlipSide.js\";\nexport function attachPinnedStyle(style, isRtl, pinnedPosition, pinnedOffset) {\n  const side = rtlFlipSide(pinnedPosition, isRtl);\n  if (!side || pinnedOffset === undefined) {\n    return style;\n  }\n  style[side] = pinnedOffset;\n  return style;\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,4BAA4B;AACxD,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,cAAc,EAAEC,YAAY,EAAE;EAC5E,MAAMC,IAAI,GAAGN,WAAW,CAACI,cAAc,EAAED,KAAK,CAAC;EAC/C,IAAI,CAACG,IAAI,IAAID,YAAY,KAAKE,SAAS,EAAE;IACvC,OAAOL,KAAK;EACd;EACAA,KAAK,CAACI,IAAI,CAAC,GAAGD,YAAY;EAC1B,OAAOH,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}