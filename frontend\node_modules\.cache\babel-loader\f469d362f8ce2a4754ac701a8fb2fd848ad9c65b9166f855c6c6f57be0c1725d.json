{"ast": null, "code": "import { ary } from './ary.mjs';\nfunction unary(func) {\n  return ary(func, 1);\n}\nexport { unary };", "map": {"version": 3, "names": ["ary", "unary", "func"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/unary.mjs"], "sourcesContent": ["import { ary } from './ary.mjs';\n\nfunction unary(func) {\n    return ary(func, 1);\n}\n\nexport { unary };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,WAAW;AAE/B,SAASC,KAAKA,CAACC,IAAI,EAAE;EACjB,OAAOF,GAAG,CAACE,IAAI,EAAE,CAAC,CAAC;AACvB;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}