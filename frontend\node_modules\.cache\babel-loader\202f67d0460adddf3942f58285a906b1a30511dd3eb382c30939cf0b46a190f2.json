{"ast": null, "code": "import { bisect } from \"d3-array\";\nimport { interpolate as interpolateValue, interpolateNumber, interpolateRound } from \"d3-interpolate\";\nimport constant from \"./constant.js\";\nimport number from \"./number.js\";\nvar unit = [0, 1];\nexport function identity(x) {\n  return x;\n}\nfunction normalize(a, b) {\n  return (b -= a = +a) ? function (x) {\n    return (x - a) / b;\n  } : constant(isNaN(b) ? NaN : 0.5);\n}\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function (x) {\n    return Math.max(a, Math.min(b, x));\n  };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0],\n    d1 = domain[1],\n    r0 = range[0],\n    r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function (x) {\n    return r0(d0(x));\n  };\n}\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n    d = new Array(j),\n    r = new Array(j),\n    i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n  return function (x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\nexport function copy(source, target) {\n  return target.domain(source.domain()).range(source.range()).interpolate(source.interpolate()).clamp(source.clamp()).unknown(source.unknown());\n}\nexport function transformer() {\n  var domain = unit,\n    range = unit,\n    interpolate = interpolateValue,\n    transform,\n    untransform,\n    unknown,\n    clamp = identity,\n    piecewise,\n    output,\n    input;\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n  scale.invert = function (y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n  scale.domain = function (_) {\n    return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();\n  };\n  scale.range = function (_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n  scale.rangeRound = function (_) {\n    return range = Array.from(_), interpolate = interpolateRound, rescale();\n  };\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n  scale.interpolate = function (_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  return function (t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\nexport default function continuous() {\n  return transformer()(identity, identity);\n}", "map": {"version": 3, "names": ["bisect", "interpolate", "interpolateV<PERSON>ue", "interpolateNumber", "interpolateRound", "constant", "number", "unit", "identity", "x", "normalize", "a", "b", "isNaN", "NaN", "clamper", "t", "Math", "max", "min", "bimap", "domain", "range", "d0", "d1", "r0", "r1", "polymap", "j", "length", "d", "Array", "r", "i", "slice", "reverse", "copy", "source", "target", "clamp", "unknown", "transformer", "transform", "untransform", "piecewise", "output", "input", "rescale", "n", "scale", "map", "invert", "y", "_", "arguments", "from", "rangeRound", "u", "continuous"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/d3-scale/src/continuous.js"], "sourcesContent": ["import {bisect} from \"d3-array\";\nimport {interpolate as interpolateValue, interpolateNumber, interpolateRound} from \"d3-interpolate\";\nimport constant from \"./constant.js\";\nimport number from \"./number.js\";\n\nvar unit = [0, 1];\n\nexport function identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : constant(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = bisect(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nexport function copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nexport function transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = interpolateValue,\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), interpolateNumber)))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, number), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = interpolateRound, rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nexport default function continuous() {\n  return transformer()(identity, identity);\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,UAAU;AAC/B,SAAQC,WAAW,IAAIC,gBAAgB,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAO,gBAAgB;AACnG,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAEhC,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAEjB,OAAO,SAASC,QAAQA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC;AACV;AAEA,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvB,OAAO,CAACA,CAAC,IAAKD,CAAC,GAAG,CAACA,CAAE,IACf,UAASF,CAAC,EAAE;IAAE,OAAO,CAACA,CAAC,GAAGE,CAAC,IAAIC,CAAC;EAAE,CAAC,GACnCP,QAAQ,CAACQ,KAAK,CAACD,CAAC,CAAC,GAAGE,GAAG,GAAG,GAAG,CAAC;AACtC;AAEA,SAASC,OAAOA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAII,CAAC;EACL,IAAIL,CAAC,GAAGC,CAAC,EAAEI,CAAC,GAAGL,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAEA,CAAC,GAAGI,CAAC;EAC9B,OAAO,UAASP,CAAC,EAAE;IAAE,OAAOQ,IAAI,CAACC,GAAG,CAACP,CAAC,EAAEM,IAAI,CAACE,GAAG,CAACP,CAAC,EAAEH,CAAC,CAAC,CAAC;EAAE,CAAC;AAC5D;;AAEA;AACA;AACA,SAASW,KAAKA,CAACC,MAAM,EAAEC,KAAK,EAAErB,WAAW,EAAE;EACzC,IAAIsB,EAAE,GAAGF,MAAM,CAAC,CAAC,CAAC;IAAEG,EAAE,GAAGH,MAAM,CAAC,CAAC,CAAC;IAAEI,EAAE,GAAGH,KAAK,CAAC,CAAC,CAAC;IAAEI,EAAE,GAAGJ,KAAK,CAAC,CAAC,CAAC;EAChE,IAAIE,EAAE,GAAGD,EAAE,EAAEA,EAAE,GAAGb,SAAS,CAACc,EAAE,EAAED,EAAE,CAAC,EAAEE,EAAE,GAAGxB,WAAW,CAACyB,EAAE,EAAED,EAAE,CAAC,CAAC,KACzDF,EAAE,GAAGb,SAAS,CAACa,EAAE,EAAEC,EAAE,CAAC,EAAEC,EAAE,GAAGxB,WAAW,CAACwB,EAAE,EAAEC,EAAE,CAAC;EACrD,OAAO,UAASjB,CAAC,EAAE;IAAE,OAAOgB,EAAE,CAACF,EAAE,CAACd,CAAC,CAAC,CAAC;EAAE,CAAC;AAC1C;AAEA,SAASkB,OAAOA,CAACN,MAAM,EAAEC,KAAK,EAAErB,WAAW,EAAE;EAC3C,IAAI2B,CAAC,GAAGX,IAAI,CAACE,GAAG,CAACE,MAAM,CAACQ,MAAM,EAAEP,KAAK,CAACO,MAAM,CAAC,GAAG,CAAC;IAC7CC,CAAC,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC;IAChBI,CAAC,GAAG,IAAID,KAAK,CAACH,CAAC,CAAC;IAChBK,CAAC,GAAG,CAAC,CAAC;;EAEV;EACA,IAAIZ,MAAM,CAACO,CAAC,CAAC,GAAGP,MAAM,CAAC,CAAC,CAAC,EAAE;IACzBA,MAAM,GAAGA,MAAM,CAACa,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACjCb,KAAK,GAAGA,KAAK,CAACY,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACjC;EAEA,OAAO,EAAEF,CAAC,GAAGL,CAAC,EAAE;IACdE,CAAC,CAACG,CAAC,CAAC,GAAGvB,SAAS,CAACW,MAAM,CAACY,CAAC,CAAC,EAAEZ,MAAM,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1CD,CAAC,CAACC,CAAC,CAAC,GAAGhC,WAAW,CAACqB,KAAK,CAACW,CAAC,CAAC,EAAEX,KAAK,CAACW,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C;EAEA,OAAO,UAASxB,CAAC,EAAE;IACjB,IAAIwB,CAAC,GAAGjC,MAAM,CAACqB,MAAM,EAAEZ,CAAC,EAAE,CAAC,EAAEmB,CAAC,CAAC,GAAG,CAAC;IACnC,OAAOI,CAAC,CAACC,CAAC,CAAC,CAACH,CAAC,CAACG,CAAC,CAAC,CAACxB,CAAC,CAAC,CAAC;EACtB,CAAC;AACH;AAEA,OAAO,SAAS2B,IAAIA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACnC,OAAOA,MAAM,CACRjB,MAAM,CAACgB,MAAM,CAAChB,MAAM,CAAC,CAAC,CAAC,CACvBC,KAAK,CAACe,MAAM,CAACf,KAAK,CAAC,CAAC,CAAC,CACrBrB,WAAW,CAACoC,MAAM,CAACpC,WAAW,CAAC,CAAC,CAAC,CACjCsC,KAAK,CAACF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACrBC,OAAO,CAACH,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC;AAChC;AAEA,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC5B,IAAIpB,MAAM,GAAGd,IAAI;IACbe,KAAK,GAAGf,IAAI;IACZN,WAAW,GAAGC,gBAAgB;IAC9BwC,SAAS;IACTC,WAAW;IACXH,OAAO;IACPD,KAAK,GAAG/B,QAAQ;IAChBoC,SAAS;IACTC,MAAM;IACNC,KAAK;EAET,SAASC,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,GAAG/B,IAAI,CAACE,GAAG,CAACE,MAAM,CAACQ,MAAM,EAAEP,KAAK,CAACO,MAAM,CAAC;IAC7C,IAAIU,KAAK,KAAK/B,QAAQ,EAAE+B,KAAK,GAAGxB,OAAO,CAACM,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC2B,CAAC,GAAG,CAAC,CAAC,CAAC;IACjEJ,SAAS,GAAGI,CAAC,GAAG,CAAC,GAAGrB,OAAO,GAAGP,KAAK;IACnCyB,MAAM,GAAGC,KAAK,GAAG,IAAI;IACrB,OAAOG,KAAK;EACd;EAEA,SAASA,KAAKA,CAACxC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAII,KAAK,CAACJ,CAAC,GAAG,CAACA,CAAC,CAAC,GAAG+B,OAAO,GAAG,CAACK,MAAM,KAAKA,MAAM,GAAGD,SAAS,CAACvB,MAAM,CAAC6B,GAAG,CAACR,SAAS,CAAC,EAAEpB,KAAK,EAAErB,WAAW,CAAC,CAAC,EAAEyC,SAAS,CAACH,KAAK,CAAC9B,CAAC,CAAC,CAAC,CAAC;EAChJ;EAEAwC,KAAK,CAACE,MAAM,GAAG,UAASC,CAAC,EAAE;IACzB,OAAOb,KAAK,CAACI,WAAW,CAAC,CAACG,KAAK,KAAKA,KAAK,GAAGF,SAAS,CAACtB,KAAK,EAAED,MAAM,CAAC6B,GAAG,CAACR,SAAS,CAAC,EAAEvC,iBAAiB,CAAC,CAAC,EAAEiD,CAAC,CAAC,CAAC,CAAC;EAC/G,CAAC;EAEDH,KAAK,CAAC5B,MAAM,GAAG,UAASgC,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACzB,MAAM,IAAIR,MAAM,GAAGU,KAAK,CAACwB,IAAI,CAACF,CAAC,EAAE/C,MAAM,CAAC,EAAEyC,OAAO,CAAC,CAAC,IAAI1B,MAAM,CAACa,KAAK,CAAC,CAAC;EACxF,CAAC;EAEDe,KAAK,CAAC3B,KAAK,GAAG,UAAS+B,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACzB,MAAM,IAAIP,KAAK,GAAGS,KAAK,CAACwB,IAAI,CAACF,CAAC,CAAC,EAAEN,OAAO,CAAC,CAAC,IAAIzB,KAAK,CAACY,KAAK,CAAC,CAAC;EAC9E,CAAC;EAEDe,KAAK,CAACO,UAAU,GAAG,UAASH,CAAC,EAAE;IAC7B,OAAO/B,KAAK,GAAGS,KAAK,CAACwB,IAAI,CAACF,CAAC,CAAC,EAAEpD,WAAW,GAAGG,gBAAgB,EAAE2C,OAAO,CAAC,CAAC;EACzE,CAAC;EAEDE,KAAK,CAACV,KAAK,GAAG,UAASc,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACzB,MAAM,IAAIU,KAAK,GAAGc,CAAC,GAAG,IAAI,GAAG7C,QAAQ,EAAEuC,OAAO,CAAC,CAAC,IAAIR,KAAK,KAAK/B,QAAQ;EACzF,CAAC;EAEDyC,KAAK,CAAChD,WAAW,GAAG,UAASoD,CAAC,EAAE;IAC9B,OAAOC,SAAS,CAACzB,MAAM,IAAI5B,WAAW,GAAGoD,CAAC,EAAEN,OAAO,CAAC,CAAC,IAAI9C,WAAW;EACtE,CAAC;EAEDgD,KAAK,CAACT,OAAO,GAAG,UAASa,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACzB,MAAM,IAAIW,OAAO,GAAGa,CAAC,EAAEJ,KAAK,IAAIT,OAAO;EAC1D,CAAC;EAED,OAAO,UAASxB,CAAC,EAAEyC,CAAC,EAAE;IACpBf,SAAS,GAAG1B,CAAC,EAAE2B,WAAW,GAAGc,CAAC;IAC9B,OAAOV,OAAO,CAAC,CAAC;EAClB,CAAC;AACH;AAEA,eAAe,SAASW,UAAUA,CAAA,EAAG;EACnC,OAAOjB,WAAW,CAAC,CAAC,CAACjC,QAAQ,EAAEA,QAAQ,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}