{"ast": null, "code": "import * as React from 'react';\nimport reactMajor from \"../reactMajor/index.js\";\n\n// Compatibility shim that ensures stable props object for forwardRef components\n// Fixes https://github.com/facebook/react/issues/31613\n// We ensure that the ref is always present in the props object (even if that's not the case for older versions of React) to avoid the footgun of spreading props over the ref in the newer versions of React.\n// Footgun: <Component ref={ref} {...props} /> will break past React 19, but the types will now warn us that we should use <Component {...props} ref={ref} /> instead.\nexport const forwardRef = render => {\n  if (reactMajor >= 19) {\n    const Component = props => render(props, props.ref ?? null);\n    Component.displayName = render.displayName ?? render.name;\n    return Component;\n  }\n  return /*#__PURE__*/React.forwardRef(render);\n};\nif (process.env.NODE_ENV !== \"production\") forwardRef.displayName = \"forwardRef\";", "map": {"version": 3, "names": ["React", "reactMajor", "forwardRef", "render", "Component", "props", "ref", "displayName", "name", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/forwardRef/forwardRef.js"], "sourcesContent": ["import * as React from 'react';\nimport reactMajor from \"../reactMajor/index.js\";\n\n// Compatibility shim that ensures stable props object for forwardRef components\n// Fixes https://github.com/facebook/react/issues/31613\n// We ensure that the ref is always present in the props object (even if that's not the case for older versions of React) to avoid the footgun of spreading props over the ref in the newer versions of React.\n// Footgun: <Component ref={ref} {...props} /> will break past React 19, but the types will now warn us that we should use <Component {...props} ref={ref} /> instead.\nexport const forwardRef = render => {\n  if (reactMajor >= 19) {\n    const Component = props => render(props, props.ref ?? null);\n    Component.displayName = render.displayName ?? render.name;\n    return Component;\n  }\n  return /*#__PURE__*/React.forwardRef(render);\n};\nif (process.env.NODE_ENV !== \"production\") forwardRef.displayName = \"forwardRef\";"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,wBAAwB;;AAE/C;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAGC,MAAM,IAAI;EAClC,IAAIF,UAAU,IAAI,EAAE,EAAE;IACpB,MAAMG,SAAS,GAAGC,KAAK,IAAIF,MAAM,CAACE,KAAK,EAAEA,KAAK,CAACC,GAAG,IAAI,IAAI,CAAC;IAC3DF,SAAS,CAACG,WAAW,GAAGJ,MAAM,CAACI,WAAW,IAAIJ,MAAM,CAACK,IAAI;IACzD,OAAOJ,SAAS;EAClB;EACA,OAAO,aAAaJ,KAAK,CAACE,UAAU,CAACC,MAAM,CAAC;AAC9C,CAAC;AACD,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAET,UAAU,CAACK,WAAW,GAAG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}