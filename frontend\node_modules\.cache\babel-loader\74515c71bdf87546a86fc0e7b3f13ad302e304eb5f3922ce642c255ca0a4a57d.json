{"ast": null, "code": "function flow(...funcs) {\n  return function (...args) {\n    let result = funcs.length ? funcs[0].apply(this, args) : args[0];\n    for (let i = 1; i < funcs.length; i++) {\n      result = funcs[i].call(this, result);\n    }\n    return result;\n  };\n}\nexport { flow };", "map": {"version": 3, "names": ["flow", "funcs", "args", "result", "length", "apply", "i", "call"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/flow.mjs"], "sourcesContent": ["function flow(...funcs) {\n    return function (...args) {\n        let result = funcs.length ? funcs[0].apply(this, args) : args[0];\n        for (let i = 1; i < funcs.length; i++) {\n            result = funcs[i].call(this, result);\n        }\n        return result;\n    };\n}\n\nexport { flow };\n"], "mappings": "AAAA,SAASA,IAAIA,CAAC,GAAGC,KAAK,EAAE;EACpB,OAAO,UAAU,GAAGC,IAAI,EAAE;IACtB,IAAIC,MAAM,GAAGF,KAAK,CAACG,MAAM,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC;IAChE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAE;MACnCH,MAAM,GAAGF,KAAK,CAACK,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,EAAEJ,MAAM,CAAC;IACxC;IACA,OAAOA,MAAM;EACjB,CAAC;AACL;AAEA,SAASH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}