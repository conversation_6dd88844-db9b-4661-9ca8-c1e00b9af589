{"ast": null, "code": "function getSymbols(object) {\n  return Object.getOwnPropertySymbols(object).filter(symbol => Object.prototype.propertyIsEnumerable.call(object, symbol));\n}\nexport { getSymbols };", "map": {"version": 3, "names": ["getSymbols", "object", "Object", "getOwnPropertySymbols", "filter", "symbol", "prototype", "propertyIsEnumerable", "call"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/compat/_internal/getSymbols.mjs"], "sourcesContent": ["function getSymbols(object) {\n    return Object.getOwnPropertySymbols(object).filter(symbol => Object.prototype.propertyIsEnumerable.call(object, symbol));\n}\n\nexport { getSymbols };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,MAAM,EAAE;EACxB,OAAOC,MAAM,CAACC,qBAAqB,CAACF,MAAM,CAAC,CAACG,MAAM,CAACC,MAAM,IAAIH,MAAM,CAACI,SAAS,CAACC,oBAAoB,CAACC,IAAI,CAACP,MAAM,EAAEI,MAAM,CAAC,CAAC;AAC5H;AAEA,SAASL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}