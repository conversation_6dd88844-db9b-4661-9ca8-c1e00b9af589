{"ast": null, "code": "import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { GRID_TREE_DATA_GROUPING_FIELD, GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../internals/constants.js\";\nimport { isGroupingColumn } from \"../../../internals/utils/gridRowGroupingUtils.js\";\nimport { gridVisibleColumnDefinitionsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { gridFocusColumnGroupHeaderSelector } from \"../focus/index.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { gridHeaderFilteringEditFieldSelector, gridHeaderFilteringMenuSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { getLeftColumnIndex, getRightColumnIndex, findNonRowSpannedCell } from \"./utils.js\";\nimport { createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridVisibleRowsSelector } from \"../pagination/index.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nconst gridVisibleRowsWithPinnedRowsSelector = createSelectorMemoized(gridVisibleRowsSelector, gridPinnedRowsSelector, (visibleRows, pinnedRows) => {\n  return (pinnedRows.top || []).concat(visibleRows.rows, pinnedRows.bottom || []);\n});\n\n/**\n * @requires useGridSorting (method) - can be after\n * @requires useGridFilter (state) - can be after\n * @requires useGridColumns (state, method) - can be after\n * @requires useGridDimensions (method) - can be after\n * @requires useGridFocus (method) - can be after\n * @requires useGridScroll (method) - can be after\n * @requires useGridColumnSpanning (method) - can be after\n */\nexport const useGridKeyboardNavigation = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridKeyboardNavigation');\n  const isRtl = useRtl();\n  const getCurrentPageRows = React.useCallback(() => {\n    return gridVisibleRowsWithPinnedRowsSelector(apiRef);\n  }, [apiRef]);\n  const headerFilteringEnabled = props.signature !== 'DataGrid' && props.headerFilters;\n\n  /**\n   * @param {number} colIndex Index of the column to focus\n   * @param {GridRowId} rowId index of the row to focus\n   * @param {string} closestColumnToUse Which closest column cell to use when the cell is spanned by `colSpan`.\n   * @param {string} rowSpanScanDirection Which direction to search to find the next cell not hidden by `rowSpan`.\n   * TODO replace with apiRef.current.moveFocusToRelativeCell()\n   */\n  const goToCell = React.useCallback((colIndex, rowId, closestColumnToUse = 'left', rowSpanScanDirection = 'up') => {\n    const visibleSortedRows = gridExpandedSortedRowEntriesSelector(apiRef);\n    const nextCellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, colIndex);\n    if (nextCellColSpanInfo && nextCellColSpanInfo.spannedByColSpan) {\n      if (closestColumnToUse === 'left') {\n        colIndex = nextCellColSpanInfo.leftVisibleCellIndex;\n      } else if (closestColumnToUse === 'right') {\n        colIndex = nextCellColSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    const field = gridVisibleColumnFieldsSelector(apiRef)[colIndex];\n    const nonRowSpannedRowId = findNonRowSpannedCell(apiRef, rowId, colIndex, rowSpanScanDirection);\n    // `scrollToIndexes` requires a rowIndex relative to all visible rows.\n    // Those rows do not include pinned rows, but pinned rows do not need scroll anyway.\n    const rowIndexRelativeToAllRows = visibleSortedRows.findIndex(row => row.id === nonRowSpannedRowId);\n    logger.debug(`Navigating to cell row ${rowIndexRelativeToAllRows}, col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex,\n      rowIndex: rowIndexRelativeToAllRows\n    });\n    apiRef.current.setCellFocus(nonRowSpannedRowId, field);\n  }, [apiRef, logger]);\n  const goToHeader = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef, logger]);\n  const goToHeaderFilter = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header filter col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFilterFocus(field, event);\n  }, [apiRef, logger]);\n  const goToGroupHeader = React.useCallback((colIndex, depth, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const {\n      field\n    } = apiRef.current.getVisibleColumns()[colIndex];\n    apiRef.current.setColumnGroupHeaderFocus(field, depth, event);\n  }, [apiRef, logger]);\n  const getRowIdFromIndex = React.useCallback(rowIndex => {\n    return getCurrentPageRows()[rowIndex]?.id;\n  }, [getCurrentPageRows]);\n  const handleColumnHeaderKeyDown = React.useCallback((params, event) => {\n    const headerTitleNode = event.currentTarget.querySelector(`.${gridClasses.columnHeaderTitleContainerContent}`);\n    const isFromInsideContent = !!headerTitleNode && headerTitleNode.contains(event.target);\n    if (isFromInsideContent && params.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // When focus is on a nested input, keyboard events have no effect to avoid conflicts with native events.\n      // There is one exception for the checkBoxHeader\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = currentPageRows.length > 0 ? 0 : null;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    const columnGroupMaxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (headerFilteringEnabled) {\n            goToHeaderFilter(colIndexBefore, event);\n          } else if (firstRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(firstRowIndexInPage));\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeader(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeader(leftColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (columnGroupMaxDepth > 0) {\n            goToGroupHeader(colIndexBefore, columnGroupMaxDepth - 1, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeader(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeader(lastColIndex, event);\n          break;\n        }\n      case 'Enter':\n        {\n          if (event.ctrlKey || event.metaKey) {\n            apiRef.current.toggleColumnMenu(params.field);\n          }\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, headerFilteringEnabled, goToHeaderFilter, goToCell, getRowIdFromIndex, isRtl, goToHeader, goToGroupHeader]);\n  const handleHeaderFilterKeyDown = React.useCallback((params, event) => {\n    const isEditing = gridHeaderFilteringEditFieldSelector(apiRef) === params.field;\n    const isHeaderMenuOpen = gridHeaderFilteringMenuSelector(apiRef) === params.field;\n    if (isEditing || isHeaderMenuOpen || !isNavigationKey(event.key)) {\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          const rowId = getRowIdFromIndex(firstRowIndexInPage);\n          if (firstRowIndexInPage !== null && rowId != null) {\n            goToCell(colIndexBefore, rowId);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeaderFilter(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeaderFilter(leftColIndex, event);\n          } else {\n            apiRef.current.setColumnHeaderFilterFocus(params.field, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          goToHeader(colIndexBefore, event);\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeaderFilter(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeaderFilter(lastColIndex, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, goToHeaderFilter, isRtl, goToHeader, goToCell, getRowIdFromIndex]);\n  const handleColumnGroupHeaderKeyDown = React.useCallback((params, event) => {\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup === null) {\n      return;\n    }\n    const {\n      field: currentField,\n      depth: currentDepth\n    } = focusedColumnGroup;\n    const {\n      fields,\n      depth,\n      maxDepth\n    } = params;\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const currentColIndex = apiRef.current.getColumnIndex(currentField);\n    const colIndexBefore = currentField ? apiRef.current.getColumnIndex(currentField) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (depth === maxDepth - 1) {\n            goToHeader(currentColIndex, event);\n          } else {\n            goToGroupHeader(currentColIndex, currentDepth + 1, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (depth > 0) {\n            goToGroupHeader(currentColIndex, currentDepth - 1, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const remainingRightColumns = fields.length - fields.indexOf(currentField) - 1;\n          if (currentColIndex + remainingRightColumns + 1 <= lastColIndex) {\n            goToGroupHeader(currentColIndex + remainingRightColumns + 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const remainingLeftColumns = fields.indexOf(currentField);\n          if (currentColIndex - remainingLeftColumns - 1 >= firstColIndex) {\n            goToGroupHeader(currentColIndex - remainingLeftColumns - 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToGroupHeader(firstColIndex, currentDepth, event);\n          break;\n        }\n      case 'End':\n        {\n          goToGroupHeader(lastColIndex, currentDepth, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, goToHeader, goToGroupHeader, goToCell, getRowIdFromIndex]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Ignore portal\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n\n    // Get the most recent params because the cell mode may have changed by another listener\n    const cellParams = apiRef.current.getCellParams(params.id, params.field);\n    if (cellParams.cellMode === GridCellModes.Edit || !isNavigationKey(event.key)) {\n      return;\n    }\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    if (currentPageRows.length === 0) {\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const rowIndexBefore = currentPageRows.findIndex(row => row.id === params.id);\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          // \"Enter\" is only triggered by the row / cell editing feature\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore + 1), isRtl ? 'right' : 'left', 'down');\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (rowIndexBefore > firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore - 1));\n          } else if (headerFilteringEnabled) {\n            goToHeaderFilter(colIndexBefore, event);\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToCell(rightColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'left' : 'right');\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToCell(leftColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'right' : 'left');\n          }\n          break;\n        }\n      case 'Tab':\n        {\n          // \"Tab\" is only triggered by the row / cell editing feature\n          if (event.shiftKey && colIndexBefore > firstColIndex) {\n            goToCell(colIndexBefore - 1, getRowIdFromIndex(rowIndexBefore), 'left');\n          } else if (!event.shiftKey && colIndexBefore < lastColIndex) {\n            goToCell(colIndexBefore + 1, getRowIdFromIndex(rowIndexBefore), 'right');\n          }\n          break;\n        }\n      case ' ':\n        {\n          const field = params.field;\n          if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n            break;\n          }\n          const colDef = params.colDef;\n          if (colDef && (colDef.field === GRID_TREE_DATA_GROUPING_FIELD || isGroupingColumn(colDef.field))) {\n            break;\n          }\n          if (!event.shiftKey && rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageUp':\n        {\n          // Go to the first row before going to header\n          const nextRowIndex = Math.max(rowIndexBefore - viewportPageSize, firstRowIndexInPage);\n          if (nextRowIndex !== rowIndexBefore && nextRowIndex >= firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(nextRowIndex));\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'Home':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(firstColIndex, getRowIdFromIndex(firstRowIndexInPage));\n          } else {\n            goToCell(firstColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      case 'End':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(lastColIndex, getRowIdFromIndex(lastRowIndexInPage));\n          } else {\n            goToCell(lastColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, isRtl, goToCell, getRowIdFromIndex, headerFilteringEnabled, goToHeaderFilter, goToHeader]);\n  const checkIfCanStartEditing = React.useCallback((initialValue, {\n    event\n  }) => {\n    if (event.key === ' ') {\n      // Space scrolls to the last row\n      return false;\n    }\n    return initialValue;\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'canStartEditing', checkIfCanStartEditing);\n  useGridEvent(apiRef, 'columnHeaderKeyDown', handleColumnHeaderKeyDown);\n  useGridEvent(apiRef, 'headerFilterKeyDown', handleHeaderFilterKeyDown);\n  useGridEvent(apiRef, 'columnGroupHeaderKeyDown', handleColumnGroupHeaderKeyDown);\n  useGridEvent(apiRef, 'cellKeyDown', handleCellKeyDown);\n};", "map": {"version": 3, "names": ["React", "useRtl", "GRID_TREE_DATA_GROUPING_FIELD", "GRID_DETAIL_PANEL_TOGGLE_FIELD", "isGroupingColumn", "gridVisibleColumnDefinitionsSelector", "gridVisibleColumnFieldsSelector", "useGridLogger", "useGridEvent", "gridExpandedSortedRowEntriesSelector", "GRID_CHECKBOX_SELECTION_COL_DEF", "gridClasses", "GridCellModes", "isNavigationKey", "gridFocusColumnGroupHeaderSelector", "gridColumnGroupsHeaderMaxDepthSelector", "gridHeaderFilteringEditFieldSelector", "gridHeaderFilteringMenuSelector", "useGridRegisterPipeProcessor", "isEventTargetInPortal", "getLeftColumnIndex", "getRightColumnIndex", "findNonRowSpannedCell", "createSelectorMemoized", "gridVisibleRowsSelector", "gridPinnedRowsSelector", "gridVisibleRowsWithPinnedRowsSelector", "visibleRows", "pinnedRows", "top", "concat", "rows", "bottom", "useGridKeyboardNavigation", "apiRef", "props", "logger", "isRtl", "getCurrentPageRows", "useCallback", "headerFilteringEnabled", "signature", "headerFilters", "goToCell", "colIndex", "rowId", "closestColumnToUse", "rowSpanScanDirection", "visibleSortedRows", "nextCellColSpanInfo", "current", "unstable_getCellColSpanInfo", "spannedByColSpan", "leftVisibleCellIndex", "rightVisibleCellIndex", "field", "nonRowSpannedRowId", "rowIndexRelativeToAllRows", "findIndex", "row", "id", "debug", "scrollToIndexes", "rowIndex", "setCellFocus", "goToHeader", "event", "getVisibleColumns", "setColumnHeaderFocus", "goToHeaderFilter", "setColumnHeaderFilterFocus", "goToGroupHeader", "depth", "setColumnGroupHeaderFocus", "getRowIdFromIndex", "handleColumnHeaderKeyDown", "params", "headerTitleNode", "currentTarget", "querySelector", "columnHeaderTitleContainerContent", "isFromInsideContent", "contains", "target", "currentPageRows", "viewportPageSize", "getViewportPageSize", "colIndexBefore", "getColumnIndex", "firstRowIndexInPage", "length", "lastRowIndexInPage", "firstColIndex", "lastColIndex", "columnGroupMaxDepth", "shouldPreventDefault", "key", "rightColIndex", "currentColIndex", "leftColIndex", "Math", "min", "ctrl<PERSON>ey", "metaKey", "toggleColumnMenu", "preventDefault", "handleHeaderFilterKeyDown", "isEditing", "isHeaderMenuOpen", "handleColumnGroupHeaderKeyDown", "focusedColumnGroup", "current<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "fields", "max<PERSON><PERSON><PERSON>", "remainingRightColumns", "indexOf", "remainingLeftColumns", "handleCellKeyDown", "cellParams", "getCellParams", "cellMode", "Edit", "canUpdateFocus", "unstable_applyPipeProcessors", "cell", "rowIndexBefore", "shift<PERSON>ey", "colDef", "nextRowIndex", "max", "checkIfCanStartEditing", "initialValue"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/keyboardNavigation/useGridKeyboardNavigation.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { GRID_TREE_DATA_GROUPING_FIELD, GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../internals/constants.js\";\nimport { isGroupingColumn } from \"../../../internals/utils/gridRowGroupingUtils.js\";\nimport { gridVisibleColumnDefinitionsSelector, gridVisibleColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { gridFocusColumnGroupHeaderSelector } from \"../focus/index.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nimport { gridHeaderFilteringEditFieldSelector, gridHeaderFilteringMenuSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { getLeftColumnIndex, getRightColumnIndex, findNonRowSpannedCell } from \"./utils.js\";\nimport { createSelectorMemoized } from \"../../../utils/createSelector.js\";\nimport { gridVisibleRowsSelector } from \"../pagination/index.js\";\nimport { gridPinnedRowsSelector } from \"../rows/gridRowsSelector.js\";\nconst gridVisibleRowsWithPinnedRowsSelector = createSelectorMemoized(gridVisibleRowsSelector, gridPinnedRowsSelector, (visibleRows, pinnedRows) => {\n  return (pinnedRows.top || []).concat(visibleRows.rows, pinnedRows.bottom || []);\n});\n\n/**\n * @requires useGridSorting (method) - can be after\n * @requires useGridFilter (state) - can be after\n * @requires useGridColumns (state, method) - can be after\n * @requires useGridDimensions (method) - can be after\n * @requires useGridFocus (method) - can be after\n * @requires useGridScroll (method) - can be after\n * @requires useGridColumnSpanning (method) - can be after\n */\nexport const useGridKeyboardNavigation = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridKeyboardNavigation');\n  const isRtl = useRtl();\n  const getCurrentPageRows = React.useCallback(() => {\n    return gridVisibleRowsWithPinnedRowsSelector(apiRef);\n  }, [apiRef]);\n  const headerFilteringEnabled = props.signature !== 'DataGrid' && props.headerFilters;\n\n  /**\n   * @param {number} colIndex Index of the column to focus\n   * @param {GridRowId} rowId index of the row to focus\n   * @param {string} closestColumnToUse Which closest column cell to use when the cell is spanned by `colSpan`.\n   * @param {string} rowSpanScanDirection Which direction to search to find the next cell not hidden by `rowSpan`.\n   * TODO replace with apiRef.current.moveFocusToRelativeCell()\n   */\n  const goToCell = React.useCallback((colIndex, rowId, closestColumnToUse = 'left', rowSpanScanDirection = 'up') => {\n    const visibleSortedRows = gridExpandedSortedRowEntriesSelector(apiRef);\n    const nextCellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, colIndex);\n    if (nextCellColSpanInfo && nextCellColSpanInfo.spannedByColSpan) {\n      if (closestColumnToUse === 'left') {\n        colIndex = nextCellColSpanInfo.leftVisibleCellIndex;\n      } else if (closestColumnToUse === 'right') {\n        colIndex = nextCellColSpanInfo.rightVisibleCellIndex;\n      }\n    }\n    const field = gridVisibleColumnFieldsSelector(apiRef)[colIndex];\n    const nonRowSpannedRowId = findNonRowSpannedCell(apiRef, rowId, colIndex, rowSpanScanDirection);\n    // `scrollToIndexes` requires a rowIndex relative to all visible rows.\n    // Those rows do not include pinned rows, but pinned rows do not need scroll anyway.\n    const rowIndexRelativeToAllRows = visibleSortedRows.findIndex(row => row.id === nonRowSpannedRowId);\n    logger.debug(`Navigating to cell row ${rowIndexRelativeToAllRows}, col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex,\n      rowIndex: rowIndexRelativeToAllRows\n    });\n    apiRef.current.setCellFocus(nonRowSpannedRowId, field);\n  }, [apiRef, logger]);\n  const goToHeader = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFocus(field, event);\n  }, [apiRef, logger]);\n  const goToHeaderFilter = React.useCallback((colIndex, event) => {\n    logger.debug(`Navigating to header filter col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const field = apiRef.current.getVisibleColumns()[colIndex].field;\n    apiRef.current.setColumnHeaderFilterFocus(field, event);\n  }, [apiRef, logger]);\n  const goToGroupHeader = React.useCallback((colIndex, depth, event) => {\n    logger.debug(`Navigating to header col ${colIndex}`);\n    apiRef.current.scrollToIndexes({\n      colIndex\n    });\n    const {\n      field\n    } = apiRef.current.getVisibleColumns()[colIndex];\n    apiRef.current.setColumnGroupHeaderFocus(field, depth, event);\n  }, [apiRef, logger]);\n  const getRowIdFromIndex = React.useCallback(rowIndex => {\n    return getCurrentPageRows()[rowIndex]?.id;\n  }, [getCurrentPageRows]);\n  const handleColumnHeaderKeyDown = React.useCallback((params, event) => {\n    const headerTitleNode = event.currentTarget.querySelector(`.${gridClasses.columnHeaderTitleContainerContent}`);\n    const isFromInsideContent = !!headerTitleNode && headerTitleNode.contains(event.target);\n    if (isFromInsideContent && params.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // When focus is on a nested input, keyboard events have no effect to avoid conflicts with native events.\n      // There is one exception for the checkBoxHeader\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = currentPageRows.length > 0 ? 0 : null;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    const columnGroupMaxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (headerFilteringEnabled) {\n            goToHeaderFilter(colIndexBefore, event);\n          } else if (firstRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(firstRowIndexInPage));\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeader(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeader(leftColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (columnGroupMaxDepth > 0) {\n            goToGroupHeader(colIndexBefore, columnGroupMaxDepth - 1, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeader(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeader(lastColIndex, event);\n          break;\n        }\n      case 'Enter':\n        {\n          if (event.ctrlKey || event.metaKey) {\n            apiRef.current.toggleColumnMenu(params.field);\n          }\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, headerFilteringEnabled, goToHeaderFilter, goToCell, getRowIdFromIndex, isRtl, goToHeader, goToGroupHeader]);\n  const handleHeaderFilterKeyDown = React.useCallback((params, event) => {\n    const isEditing = gridHeaderFilteringEditFieldSelector(apiRef) === params.field;\n    const isHeaderMenuOpen = gridHeaderFilteringMenuSelector(apiRef) === params.field;\n    if (isEditing || isHeaderMenuOpen || !isNavigationKey(event.key)) {\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          const rowId = getRowIdFromIndex(firstRowIndexInPage);\n          if (firstRowIndexInPage !== null && rowId != null) {\n            goToCell(colIndexBefore, rowId);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToHeaderFilter(rightColIndex, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToHeaderFilter(leftColIndex, event);\n          } else {\n            apiRef.current.setColumnHeaderFilterFocus(params.field, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          goToHeader(colIndexBefore, event);\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToHeaderFilter(firstColIndex, event);\n          break;\n        }\n      case 'End':\n        {\n          goToHeaderFilter(lastColIndex, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, goToHeaderFilter, isRtl, goToHeader, goToCell, getRowIdFromIndex]);\n  const handleColumnGroupHeaderKeyDown = React.useCallback((params, event) => {\n    const focusedColumnGroup = gridFocusColumnGroupHeaderSelector(apiRef);\n    if (focusedColumnGroup === null) {\n      return;\n    }\n    const {\n      field: currentField,\n      depth: currentDepth\n    } = focusedColumnGroup;\n    const {\n      fields,\n      depth,\n      maxDepth\n    } = params;\n    const currentPageRows = getCurrentPageRows();\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const currentColIndex = apiRef.current.getColumnIndex(currentField);\n    const colIndexBefore = currentField ? apiRef.current.getColumnIndex(currentField) : 0;\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          if (depth === maxDepth - 1) {\n            goToHeader(currentColIndex, event);\n          } else {\n            goToGroupHeader(currentColIndex, currentDepth + 1, event);\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (depth > 0) {\n            goToGroupHeader(currentColIndex, currentDepth - 1, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const remainingRightColumns = fields.length - fields.indexOf(currentField) - 1;\n          if (currentColIndex + remainingRightColumns + 1 <= lastColIndex) {\n            goToGroupHeader(currentColIndex + remainingRightColumns + 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const remainingLeftColumns = fields.indexOf(currentField);\n          if (currentColIndex - remainingLeftColumns - 1 >= firstColIndex) {\n            goToGroupHeader(currentColIndex - remainingLeftColumns - 1, currentDepth, event);\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (firstRowIndexInPage !== null && lastRowIndexInPage !== null) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(firstRowIndexInPage + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'Home':\n        {\n          goToGroupHeader(firstColIndex, currentDepth, event);\n          break;\n        }\n      case 'End':\n        {\n          goToGroupHeader(lastColIndex, currentDepth, event);\n          break;\n        }\n      case ' ':\n        {\n          // prevent Space event from scrolling\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, goToHeader, goToGroupHeader, goToCell, getRowIdFromIndex]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Ignore portal\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n\n    // Get the most recent params because the cell mode may have changed by another listener\n    const cellParams = apiRef.current.getCellParams(params.id, params.field);\n    if (cellParams.cellMode === GridCellModes.Edit || !isNavigationKey(event.key)) {\n      return;\n    }\n    const canUpdateFocus = apiRef.current.unstable_applyPipeProcessors('canUpdateFocus', true, {\n      event,\n      cell: cellParams\n    });\n    if (!canUpdateFocus) {\n      return;\n    }\n    const currentPageRows = getCurrentPageRows();\n    if (currentPageRows.length === 0) {\n      return;\n    }\n    const viewportPageSize = apiRef.current.getViewportPageSize();\n    const colIndexBefore = params.field ? apiRef.current.getColumnIndex(params.field) : 0;\n    const rowIndexBefore = currentPageRows.findIndex(row => row.id === params.id);\n    const firstRowIndexInPage = 0;\n    const lastRowIndexInPage = currentPageRows.length - 1;\n    const firstColIndex = 0;\n    const lastColIndex = gridVisibleColumnDefinitionsSelector(apiRef).length - 1;\n    let shouldPreventDefault = true;\n    switch (event.key) {\n      case 'ArrowDown':\n        {\n          // \"Enter\" is only triggered by the row / cell editing feature\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore + 1), isRtl ? 'right' : 'left', 'down');\n          }\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (rowIndexBefore > firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(rowIndexBefore - 1));\n          } else if (headerFilteringEnabled) {\n            goToHeaderFilter(colIndexBefore, event);\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'ArrowRight':\n        {\n          const rightColIndex = getRightColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (rightColIndex !== null) {\n            goToCell(rightColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'left' : 'right');\n          }\n          break;\n        }\n      case 'ArrowLeft':\n        {\n          const leftColIndex = getLeftColumnIndex({\n            currentColIndex: colIndexBefore,\n            firstColIndex,\n            lastColIndex,\n            isRtl\n          });\n          if (leftColIndex !== null) {\n            goToCell(leftColIndex, getRowIdFromIndex(rowIndexBefore), isRtl ? 'right' : 'left');\n          }\n          break;\n        }\n      case 'Tab':\n        {\n          // \"Tab\" is only triggered by the row / cell editing feature\n          if (event.shiftKey && colIndexBefore > firstColIndex) {\n            goToCell(colIndexBefore - 1, getRowIdFromIndex(rowIndexBefore), 'left');\n          } else if (!event.shiftKey && colIndexBefore < lastColIndex) {\n            goToCell(colIndexBefore + 1, getRowIdFromIndex(rowIndexBefore), 'right');\n          }\n          break;\n        }\n      case ' ':\n        {\n          const field = params.field;\n          if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n            break;\n          }\n          const colDef = params.colDef;\n          if (colDef && (colDef.field === GRID_TREE_DATA_GROUPING_FIELD || isGroupingColumn(colDef.field))) {\n            break;\n          }\n          if (!event.shiftKey && rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageDown':\n        {\n          if (rowIndexBefore < lastRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(Math.min(rowIndexBefore + viewportPageSize, lastRowIndexInPage)));\n          }\n          break;\n        }\n      case 'PageUp':\n        {\n          // Go to the first row before going to header\n          const nextRowIndex = Math.max(rowIndexBefore - viewportPageSize, firstRowIndexInPage);\n          if (nextRowIndex !== rowIndexBefore && nextRowIndex >= firstRowIndexInPage) {\n            goToCell(colIndexBefore, getRowIdFromIndex(nextRowIndex));\n          } else {\n            goToHeader(colIndexBefore, event);\n          }\n          break;\n        }\n      case 'Home':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(firstColIndex, getRowIdFromIndex(firstRowIndexInPage));\n          } else {\n            goToCell(firstColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      case 'End':\n        {\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            goToCell(lastColIndex, getRowIdFromIndex(lastRowIndexInPage));\n          } else {\n            goToCell(lastColIndex, getRowIdFromIndex(rowIndexBefore));\n          }\n          break;\n        }\n      default:\n        {\n          shouldPreventDefault = false;\n        }\n    }\n    if (shouldPreventDefault) {\n      event.preventDefault();\n    }\n  }, [apiRef, getCurrentPageRows, isRtl, goToCell, getRowIdFromIndex, headerFilteringEnabled, goToHeaderFilter, goToHeader]);\n  const checkIfCanStartEditing = React.useCallback((initialValue, {\n    event\n  }) => {\n    if (event.key === ' ') {\n      // Space scrolls to the last row\n      return false;\n    }\n    return initialValue;\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'canStartEditing', checkIfCanStartEditing);\n  useGridEvent(apiRef, 'columnHeaderKeyDown', handleColumnHeaderKeyDown);\n  useGridEvent(apiRef, 'headerFilterKeyDown', handleHeaderFilterKeyDown);\n  useGridEvent(apiRef, 'columnGroupHeaderKeyDown', handleColumnGroupHeaderKeyDown);\n  useGridEvent(apiRef, 'cellKeyDown', handleCellKeyDown);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,6BAA6B,EAAEC,8BAA8B,QAAQ,iCAAiC;AAC/G,SAASC,gBAAgB,QAAQ,kDAAkD;AACnF,SAASC,oCAAoC,EAAEC,+BAA+B,QAAQ,mCAAmC;AACzH,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,oCAAoC,QAAQ,iCAAiC;AACtF,SAASC,+BAA+B,QAAQ,gDAAgD;AAChG,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,kCAAkC,QAAQ,mBAAmB;AACtE,SAASC,sCAAsC,QAAQ,+CAA+C;AACtG,SAASC,oCAAoC,EAAEC,+BAA+B,QAAQ,oDAAoD;AAC1I,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,qBAAqB,QAAQ,YAAY;AAC3F,SAASC,sBAAsB,QAAQ,kCAAkC;AACzE,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,MAAMC,qCAAqC,GAAGH,sBAAsB,CAACC,uBAAuB,EAAEC,sBAAsB,EAAE,CAACE,WAAW,EAAEC,UAAU,KAAK;EACjJ,OAAO,CAACA,UAAU,CAACC,GAAG,IAAI,EAAE,EAAEC,MAAM,CAACH,WAAW,CAACI,IAAI,EAAEH,UAAU,CAACI,MAAM,IAAI,EAAE,CAAC;AACjF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC1D,MAAMC,MAAM,GAAG7B,aAAa,CAAC2B,MAAM,EAAE,2BAA2B,CAAC;EACjE,MAAMG,KAAK,GAAGpC,MAAM,CAAC,CAAC;EACtB,MAAMqC,kBAAkB,GAAGtC,KAAK,CAACuC,WAAW,CAAC,MAAM;IACjD,OAAOb,qCAAqC,CAACQ,MAAM,CAAC;EACtD,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,MAAMM,sBAAsB,GAAGL,KAAK,CAACM,SAAS,KAAK,UAAU,IAAIN,KAAK,CAACO,aAAa;;EAEpF;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,QAAQ,GAAG3C,KAAK,CAACuC,WAAW,CAAC,CAACK,QAAQ,EAAEC,KAAK,EAAEC,kBAAkB,GAAG,MAAM,EAAEC,oBAAoB,GAAG,IAAI,KAAK;IAChH,MAAMC,iBAAiB,GAAGvC,oCAAoC,CAACyB,MAAM,CAAC;IACtE,MAAMe,mBAAmB,GAAGf,MAAM,CAACgB,OAAO,CAACC,2BAA2B,CAACN,KAAK,EAAED,QAAQ,CAAC;IACvF,IAAIK,mBAAmB,IAAIA,mBAAmB,CAACG,gBAAgB,EAAE;MAC/D,IAAIN,kBAAkB,KAAK,MAAM,EAAE;QACjCF,QAAQ,GAAGK,mBAAmB,CAACI,oBAAoB;MACrD,CAAC,MAAM,IAAIP,kBAAkB,KAAK,OAAO,EAAE;QACzCF,QAAQ,GAAGK,mBAAmB,CAACK,qBAAqB;MACtD;IACF;IACA,MAAMC,KAAK,GAAGjD,+BAA+B,CAAC4B,MAAM,CAAC,CAACU,QAAQ,CAAC;IAC/D,MAAMY,kBAAkB,GAAGlC,qBAAqB,CAACY,MAAM,EAAEW,KAAK,EAAED,QAAQ,EAAEG,oBAAoB,CAAC;IAC/F;IACA;IACA,MAAMU,yBAAyB,GAAGT,iBAAiB,CAACU,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKJ,kBAAkB,CAAC;IACnGpB,MAAM,CAACyB,KAAK,CAAC,0BAA0BJ,yBAAyB,SAASb,QAAQ,EAAE,CAAC;IACpFV,MAAM,CAACgB,OAAO,CAACY,eAAe,CAAC;MAC7BlB,QAAQ;MACRmB,QAAQ,EAAEN;IACZ,CAAC,CAAC;IACFvB,MAAM,CAACgB,OAAO,CAACc,YAAY,CAACR,kBAAkB,EAAED,KAAK,CAAC;EACxD,CAAC,EAAE,CAACrB,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAM6B,UAAU,GAAGjE,KAAK,CAACuC,WAAW,CAAC,CAACK,QAAQ,EAAEsB,KAAK,KAAK;IACxD9B,MAAM,CAACyB,KAAK,CAAC,4BAA4BjB,QAAQ,EAAE,CAAC;IACpDV,MAAM,CAACgB,OAAO,CAACY,eAAe,CAAC;MAC7BlB;IACF,CAAC,CAAC;IACF,MAAMW,KAAK,GAAGrB,MAAM,CAACgB,OAAO,CAACiB,iBAAiB,CAAC,CAAC,CAACvB,QAAQ,CAAC,CAACW,KAAK;IAChErB,MAAM,CAACgB,OAAO,CAACkB,oBAAoB,CAACb,KAAK,EAAEW,KAAK,CAAC;EACnD,CAAC,EAAE,CAAChC,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMiC,gBAAgB,GAAGrE,KAAK,CAACuC,WAAW,CAAC,CAACK,QAAQ,EAAEsB,KAAK,KAAK;IAC9D9B,MAAM,CAACyB,KAAK,CAAC,mCAAmCjB,QAAQ,EAAE,CAAC;IAC3DV,MAAM,CAACgB,OAAO,CAACY,eAAe,CAAC;MAC7BlB;IACF,CAAC,CAAC;IACF,MAAMW,KAAK,GAAGrB,MAAM,CAACgB,OAAO,CAACiB,iBAAiB,CAAC,CAAC,CAACvB,QAAQ,CAAC,CAACW,KAAK;IAChErB,MAAM,CAACgB,OAAO,CAACoB,0BAA0B,CAACf,KAAK,EAAEW,KAAK,CAAC;EACzD,CAAC,EAAE,CAAChC,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMmC,eAAe,GAAGvE,KAAK,CAACuC,WAAW,CAAC,CAACK,QAAQ,EAAE4B,KAAK,EAAEN,KAAK,KAAK;IACpE9B,MAAM,CAACyB,KAAK,CAAC,4BAA4BjB,QAAQ,EAAE,CAAC;IACpDV,MAAM,CAACgB,OAAO,CAACY,eAAe,CAAC;MAC7BlB;IACF,CAAC,CAAC;IACF,MAAM;MACJW;IACF,CAAC,GAAGrB,MAAM,CAACgB,OAAO,CAACiB,iBAAiB,CAAC,CAAC,CAACvB,QAAQ,CAAC;IAChDV,MAAM,CAACgB,OAAO,CAACuB,yBAAyB,CAAClB,KAAK,EAAEiB,KAAK,EAAEN,KAAK,CAAC;EAC/D,CAAC,EAAE,CAAChC,MAAM,EAAEE,MAAM,CAAC,CAAC;EACpB,MAAMsC,iBAAiB,GAAG1E,KAAK,CAACuC,WAAW,CAACwB,QAAQ,IAAI;IACtD,OAAOzB,kBAAkB,CAAC,CAAC,CAACyB,QAAQ,CAAC,EAAEH,EAAE;EAC3C,CAAC,EAAE,CAACtB,kBAAkB,CAAC,CAAC;EACxB,MAAMqC,yBAAyB,GAAG3E,KAAK,CAACuC,WAAW,CAAC,CAACqC,MAAM,EAAEV,KAAK,KAAK;IACrE,MAAMW,eAAe,GAAGX,KAAK,CAACY,aAAa,CAACC,aAAa,CAAC,IAAIpE,WAAW,CAACqE,iCAAiC,EAAE,CAAC;IAC9G,MAAMC,mBAAmB,GAAG,CAAC,CAACJ,eAAe,IAAIA,eAAe,CAACK,QAAQ,CAAChB,KAAK,CAACiB,MAAM,CAAC;IACvF,IAAIF,mBAAmB,IAAIL,MAAM,CAACrB,KAAK,KAAK7C,+BAA+B,CAAC6C,KAAK,EAAE;MACjF;MACA;MACA;IACF;IACA,MAAM6B,eAAe,GAAG9C,kBAAkB,CAAC,CAAC;IAC5C,MAAM+C,gBAAgB,GAAGnD,MAAM,CAACgB,OAAO,CAACoC,mBAAmB,CAAC,CAAC;IAC7D,MAAMC,cAAc,GAAGX,MAAM,CAACrB,KAAK,GAAGrB,MAAM,CAACgB,OAAO,CAACsC,cAAc,CAACZ,MAAM,CAACrB,KAAK,CAAC,GAAG,CAAC;IACrF,MAAMkC,mBAAmB,GAAGL,eAAe,CAACM,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;IACjE,MAAMC,kBAAkB,GAAGP,eAAe,CAACM,MAAM,GAAG,CAAC;IACrD,MAAME,aAAa,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGxF,oCAAoC,CAAC6B,MAAM,CAAC,CAACwD,MAAM,GAAG,CAAC;IAC5E,MAAMI,mBAAmB,GAAG/E,sCAAsC,CAACmB,MAAM,CAAC;IAC1E,IAAI6D,oBAAoB,GAAG,IAAI;IAC/B,QAAQ7B,KAAK,CAAC8B,GAAG;MACf,KAAK,WAAW;QACd;UACE,IAAIxD,sBAAsB,EAAE;YAC1B6B,gBAAgB,CAACkB,cAAc,EAAErB,KAAK,CAAC;UACzC,CAAC,MAAM,IAAIuB,mBAAmB,KAAK,IAAI,EAAE;YACvC9C,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAACe,mBAAmB,CAAC,CAAC;UAClE;UACA;QACF;MACF,KAAK,YAAY;QACf;UACE,MAAMQ,aAAa,GAAG5E,mBAAmB,CAAC;YACxC6E,eAAe,EAAEX,cAAc;YAC/BK,aAAa;YACbC,YAAY;YACZxD;UACF,CAAC,CAAC;UACF,IAAI4D,aAAa,KAAK,IAAI,EAAE;YAC1BhC,UAAU,CAACgC,aAAa,EAAE/B,KAAK,CAAC;UAClC;UACA;QACF;MACF,KAAK,WAAW;QACd;UACE,MAAMiC,YAAY,GAAG/E,kBAAkB,CAAC;YACtC8E,eAAe,EAAEX,cAAc;YAC/BK,aAAa;YACbC,YAAY;YACZxD;UACF,CAAC,CAAC;UACF,IAAI8D,YAAY,KAAK,IAAI,EAAE;YACzBlC,UAAU,CAACkC,YAAY,EAAEjC,KAAK,CAAC;UACjC;UACA;QACF;MACF,KAAK,SAAS;QACZ;UACE,IAAI4B,mBAAmB,GAAG,CAAC,EAAE;YAC3BvB,eAAe,CAACgB,cAAc,EAAEO,mBAAmB,GAAG,CAAC,EAAE5B,KAAK,CAAC;UACjE;UACA;QACF;MACF,KAAK,UAAU;QACb;UACE,IAAIuB,mBAAmB,KAAK,IAAI,IAAIE,kBAAkB,KAAK,IAAI,EAAE;YAC/DhD,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAAC0B,IAAI,CAACC,GAAG,CAACZ,mBAAmB,GAAGJ,gBAAgB,EAAEM,kBAAkB,CAAC,CAAC,CAAC;UACnH;UACA;QACF;MACF,KAAK,MAAM;QACT;UACE1B,UAAU,CAAC2B,aAAa,EAAE1B,KAAK,CAAC;UAChC;QACF;MACF,KAAK,KAAK;QACR;UACED,UAAU,CAAC4B,YAAY,EAAE3B,KAAK,CAAC;UAC/B;QACF;MACF,KAAK,OAAO;QACV;UACE,IAAIA,KAAK,CAACoC,OAAO,IAAIpC,KAAK,CAACqC,OAAO,EAAE;YAClCrE,MAAM,CAACgB,OAAO,CAACsD,gBAAgB,CAAC5B,MAAM,CAACrB,KAAK,CAAC;UAC/C;UACA;QACF;MACF,KAAK,GAAG;QACN;UACE;UACA;QACF;MACF;QACE;UACEwC,oBAAoB,GAAG,KAAK;QAC9B;IACJ;IACA,IAAIA,oBAAoB,EAAE;MACxB7B,KAAK,CAACuC,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACvE,MAAM,EAAEI,kBAAkB,EAAEE,sBAAsB,EAAE6B,gBAAgB,EAAE1B,QAAQ,EAAE+B,iBAAiB,EAAErC,KAAK,EAAE4B,UAAU,EAAEM,eAAe,CAAC,CAAC;EAC3I,MAAMmC,yBAAyB,GAAG1G,KAAK,CAACuC,WAAW,CAAC,CAACqC,MAAM,EAAEV,KAAK,KAAK;IACrE,MAAMyC,SAAS,GAAG3F,oCAAoC,CAACkB,MAAM,CAAC,KAAK0C,MAAM,CAACrB,KAAK;IAC/E,MAAMqD,gBAAgB,GAAG3F,+BAA+B,CAACiB,MAAM,CAAC,KAAK0C,MAAM,CAACrB,KAAK;IACjF,IAAIoD,SAAS,IAAIC,gBAAgB,IAAI,CAAC/F,eAAe,CAACqD,KAAK,CAAC8B,GAAG,CAAC,EAAE;MAChE;IACF;IACA,MAAMZ,eAAe,GAAG9C,kBAAkB,CAAC,CAAC;IAC5C,MAAM+C,gBAAgB,GAAGnD,MAAM,CAACgB,OAAO,CAACoC,mBAAmB,CAAC,CAAC;IAC7D,MAAMC,cAAc,GAAGX,MAAM,CAACrB,KAAK,GAAGrB,MAAM,CAACgB,OAAO,CAACsC,cAAc,CAACZ,MAAM,CAACrB,KAAK,CAAC,GAAG,CAAC;IACrF,MAAMkC,mBAAmB,GAAG,CAAC;IAC7B,MAAME,kBAAkB,GAAGP,eAAe,CAACM,MAAM,GAAG,CAAC;IACrD,MAAME,aAAa,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGxF,oCAAoC,CAAC6B,MAAM,CAAC,CAACwD,MAAM,GAAG,CAAC;IAC5E,IAAIK,oBAAoB,GAAG,IAAI;IAC/B,QAAQ7B,KAAK,CAAC8B,GAAG;MACf,KAAK,WAAW;QACd;UACE,MAAMnD,KAAK,GAAG6B,iBAAiB,CAACe,mBAAmB,CAAC;UACpD,IAAIA,mBAAmB,KAAK,IAAI,IAAI5C,KAAK,IAAI,IAAI,EAAE;YACjDF,QAAQ,CAAC4C,cAAc,EAAE1C,KAAK,CAAC;UACjC;UACA;QACF;MACF,KAAK,YAAY;QACf;UACE,MAAMoD,aAAa,GAAG5E,mBAAmB,CAAC;YACxC6E,eAAe,EAAEX,cAAc;YAC/BK,aAAa;YACbC,YAAY;YACZxD;UACF,CAAC,CAAC;UACF,IAAI4D,aAAa,KAAK,IAAI,EAAE;YAC1B5B,gBAAgB,CAAC4B,aAAa,EAAE/B,KAAK,CAAC;UACxC;UACA;QACF;MACF,KAAK,WAAW;QACd;UACE,MAAMiC,YAAY,GAAG/E,kBAAkB,CAAC;YACtC8E,eAAe,EAAEX,cAAc;YAC/BK,aAAa;YACbC,YAAY;YACZxD;UACF,CAAC,CAAC;UACF,IAAI8D,YAAY,KAAK,IAAI,EAAE;YACzB9B,gBAAgB,CAAC8B,YAAY,EAAEjC,KAAK,CAAC;UACvC,CAAC,MAAM;YACLhC,MAAM,CAACgB,OAAO,CAACoB,0BAA0B,CAACM,MAAM,CAACrB,KAAK,EAAEW,KAAK,CAAC;UAChE;UACA;QACF;MACF,KAAK,SAAS;QACZ;UACED,UAAU,CAACsB,cAAc,EAAErB,KAAK,CAAC;UACjC;QACF;MACF,KAAK,UAAU;QACb;UACE,IAAIuB,mBAAmB,KAAK,IAAI,IAAIE,kBAAkB,KAAK,IAAI,EAAE;YAC/DhD,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAAC0B,IAAI,CAACC,GAAG,CAACZ,mBAAmB,GAAGJ,gBAAgB,EAAEM,kBAAkB,CAAC,CAAC,CAAC;UACnH;UACA;QACF;MACF,KAAK,MAAM;QACT;UACEtB,gBAAgB,CAACuB,aAAa,EAAE1B,KAAK,CAAC;UACtC;QACF;MACF,KAAK,KAAK;QACR;UACEG,gBAAgB,CAACwB,YAAY,EAAE3B,KAAK,CAAC;UACrC;QACF;MACF,KAAK,GAAG;QACN;UACE;UACA;QACF;MACF;QACE;UACE6B,oBAAoB,GAAG,KAAK;QAC9B;IACJ;IACA,IAAIA,oBAAoB,EAAE;MACxB7B,KAAK,CAACuC,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACvE,MAAM,EAAEI,kBAAkB,EAAE+B,gBAAgB,EAAEhC,KAAK,EAAE4B,UAAU,EAAEtB,QAAQ,EAAE+B,iBAAiB,CAAC,CAAC;EAClG,MAAMmC,8BAA8B,GAAG7G,KAAK,CAACuC,WAAW,CAAC,CAACqC,MAAM,EAAEV,KAAK,KAAK;IAC1E,MAAM4C,kBAAkB,GAAGhG,kCAAkC,CAACoB,MAAM,CAAC;IACrE,IAAI4E,kBAAkB,KAAK,IAAI,EAAE;MAC/B;IACF;IACA,MAAM;MACJvD,KAAK,EAAEwD,YAAY;MACnBvC,KAAK,EAAEwC;IACT,CAAC,GAAGF,kBAAkB;IACtB,MAAM;MACJG,MAAM;MACNzC,KAAK;MACL0C;IACF,CAAC,GAAGtC,MAAM;IACV,MAAMQ,eAAe,GAAG9C,kBAAkB,CAAC,CAAC;IAC5C,MAAM+C,gBAAgB,GAAGnD,MAAM,CAACgB,OAAO,CAACoC,mBAAmB,CAAC,CAAC;IAC7D,MAAMY,eAAe,GAAGhE,MAAM,CAACgB,OAAO,CAACsC,cAAc,CAACuB,YAAY,CAAC;IACnE,MAAMxB,cAAc,GAAGwB,YAAY,GAAG7E,MAAM,CAACgB,OAAO,CAACsC,cAAc,CAACuB,YAAY,CAAC,GAAG,CAAC;IACrF,MAAMtB,mBAAmB,GAAG,CAAC;IAC7B,MAAME,kBAAkB,GAAGP,eAAe,CAACM,MAAM,GAAG,CAAC;IACrD,MAAME,aAAa,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGxF,oCAAoC,CAAC6B,MAAM,CAAC,CAACwD,MAAM,GAAG,CAAC;IAC5E,IAAIK,oBAAoB,GAAG,IAAI;IAC/B,QAAQ7B,KAAK,CAAC8B,GAAG;MACf,KAAK,WAAW;QACd;UACE,IAAIxB,KAAK,KAAK0C,QAAQ,GAAG,CAAC,EAAE;YAC1BjD,UAAU,CAACiC,eAAe,EAAEhC,KAAK,CAAC;UACpC,CAAC,MAAM;YACLK,eAAe,CAAC2B,eAAe,EAAEc,YAAY,GAAG,CAAC,EAAE9C,KAAK,CAAC;UAC3D;UACA;QACF;MACF,KAAK,SAAS;QACZ;UACE,IAAIM,KAAK,GAAG,CAAC,EAAE;YACbD,eAAe,CAAC2B,eAAe,EAAEc,YAAY,GAAG,CAAC,EAAE9C,KAAK,CAAC;UAC3D;UACA;QACF;MACF,KAAK,YAAY;QACf;UACE,MAAMiD,qBAAqB,GAAGF,MAAM,CAACvB,MAAM,GAAGuB,MAAM,CAACG,OAAO,CAACL,YAAY,CAAC,GAAG,CAAC;UAC9E,IAAIb,eAAe,GAAGiB,qBAAqB,GAAG,CAAC,IAAItB,YAAY,EAAE;YAC/DtB,eAAe,CAAC2B,eAAe,GAAGiB,qBAAqB,GAAG,CAAC,EAAEH,YAAY,EAAE9C,KAAK,CAAC;UACnF;UACA;QACF;MACF,KAAK,WAAW;QACd;UACE,MAAMmD,oBAAoB,GAAGJ,MAAM,CAACG,OAAO,CAACL,YAAY,CAAC;UACzD,IAAIb,eAAe,GAAGmB,oBAAoB,GAAG,CAAC,IAAIzB,aAAa,EAAE;YAC/DrB,eAAe,CAAC2B,eAAe,GAAGmB,oBAAoB,GAAG,CAAC,EAAEL,YAAY,EAAE9C,KAAK,CAAC;UAClF;UACA;QACF;MACF,KAAK,UAAU;QACb;UACE,IAAIuB,mBAAmB,KAAK,IAAI,IAAIE,kBAAkB,KAAK,IAAI,EAAE;YAC/DhD,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAAC0B,IAAI,CAACC,GAAG,CAACZ,mBAAmB,GAAGJ,gBAAgB,EAAEM,kBAAkB,CAAC,CAAC,CAAC;UACnH;UACA;QACF;MACF,KAAK,MAAM;QACT;UACEpB,eAAe,CAACqB,aAAa,EAAEoB,YAAY,EAAE9C,KAAK,CAAC;UACnD;QACF;MACF,KAAK,KAAK;QACR;UACEK,eAAe,CAACsB,YAAY,EAAEmB,YAAY,EAAE9C,KAAK,CAAC;UAClD;QACF;MACF,KAAK,GAAG;QACN;UACE;UACA;QACF;MACF;QACE;UACE6B,oBAAoB,GAAG,KAAK;QAC9B;IACJ;IACA,IAAIA,oBAAoB,EAAE;MACxB7B,KAAK,CAACuC,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACvE,MAAM,EAAEI,kBAAkB,EAAE2B,UAAU,EAAEM,eAAe,EAAE5B,QAAQ,EAAE+B,iBAAiB,CAAC,CAAC;EAC1F,MAAM4C,iBAAiB,GAAGtH,KAAK,CAACuC,WAAW,CAAC,CAACqC,MAAM,EAAEV,KAAK,KAAK;IAC7D;IACA,IAAI/C,qBAAqB,CAAC+C,KAAK,CAAC,EAAE;MAChC;IACF;;IAEA;IACA,MAAMqD,UAAU,GAAGrF,MAAM,CAACgB,OAAO,CAACsE,aAAa,CAAC5C,MAAM,CAAChB,EAAE,EAAEgB,MAAM,CAACrB,KAAK,CAAC;IACxE,IAAIgE,UAAU,CAACE,QAAQ,KAAK7G,aAAa,CAAC8G,IAAI,IAAI,CAAC7G,eAAe,CAACqD,KAAK,CAAC8B,GAAG,CAAC,EAAE;MAC7E;IACF;IACA,MAAM2B,cAAc,GAAGzF,MAAM,CAACgB,OAAO,CAAC0E,4BAA4B,CAAC,gBAAgB,EAAE,IAAI,EAAE;MACzF1D,KAAK;MACL2D,IAAI,EAAEN;IACR,CAAC,CAAC;IACF,IAAI,CAACI,cAAc,EAAE;MACnB;IACF;IACA,MAAMvC,eAAe,GAAG9C,kBAAkB,CAAC,CAAC;IAC5C,IAAI8C,eAAe,CAACM,MAAM,KAAK,CAAC,EAAE;MAChC;IACF;IACA,MAAML,gBAAgB,GAAGnD,MAAM,CAACgB,OAAO,CAACoC,mBAAmB,CAAC,CAAC;IAC7D,MAAMC,cAAc,GAAGX,MAAM,CAACrB,KAAK,GAAGrB,MAAM,CAACgB,OAAO,CAACsC,cAAc,CAACZ,MAAM,CAACrB,KAAK,CAAC,GAAG,CAAC;IACrF,MAAMuE,cAAc,GAAG1C,eAAe,CAAC1B,SAAS,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKgB,MAAM,CAAChB,EAAE,CAAC;IAC7E,MAAM6B,mBAAmB,GAAG,CAAC;IAC7B,MAAME,kBAAkB,GAAGP,eAAe,CAACM,MAAM,GAAG,CAAC;IACrD,MAAME,aAAa,GAAG,CAAC;IACvB,MAAMC,YAAY,GAAGxF,oCAAoC,CAAC6B,MAAM,CAAC,CAACwD,MAAM,GAAG,CAAC;IAC5E,IAAIK,oBAAoB,GAAG,IAAI;IAC/B,QAAQ7B,KAAK,CAAC8B,GAAG;MACf,KAAK,WAAW;QACd;UACE;UACA,IAAI8B,cAAc,GAAGnC,kBAAkB,EAAE;YACvChD,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAACoD,cAAc,GAAG,CAAC,CAAC,EAAEzF,KAAK,GAAG,OAAO,GAAG,MAAM,EAAE,MAAM,CAAC;UACnG;UACA;QACF;MACF,KAAK,SAAS;QACZ;UACE,IAAIyF,cAAc,GAAGrC,mBAAmB,EAAE;YACxC9C,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAACoD,cAAc,GAAG,CAAC,CAAC,CAAC;UACjE,CAAC,MAAM,IAAItF,sBAAsB,EAAE;YACjC6B,gBAAgB,CAACkB,cAAc,EAAErB,KAAK,CAAC;UACzC,CAAC,MAAM;YACLD,UAAU,CAACsB,cAAc,EAAErB,KAAK,CAAC;UACnC;UACA;QACF;MACF,KAAK,YAAY;QACf;UACE,MAAM+B,aAAa,GAAG5E,mBAAmB,CAAC;YACxC6E,eAAe,EAAEX,cAAc;YAC/BK,aAAa;YACbC,YAAY;YACZxD;UACF,CAAC,CAAC;UACF,IAAI4D,aAAa,KAAK,IAAI,EAAE;YAC1BtD,QAAQ,CAACsD,aAAa,EAAEvB,iBAAiB,CAACoD,cAAc,CAAC,EAAEzF,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;UACtF;UACA;QACF;MACF,KAAK,WAAW;QACd;UACE,MAAM8D,YAAY,GAAG/E,kBAAkB,CAAC;YACtC8E,eAAe,EAAEX,cAAc;YAC/BK,aAAa;YACbC,YAAY;YACZxD;UACF,CAAC,CAAC;UACF,IAAI8D,YAAY,KAAK,IAAI,EAAE;YACzBxD,QAAQ,CAACwD,YAAY,EAAEzB,iBAAiB,CAACoD,cAAc,CAAC,EAAEzF,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC;UACrF;UACA;QACF;MACF,KAAK,KAAK;QACR;UACE;UACA,IAAI6B,KAAK,CAAC6D,QAAQ,IAAIxC,cAAc,GAAGK,aAAa,EAAE;YACpDjD,QAAQ,CAAC4C,cAAc,GAAG,CAAC,EAAEb,iBAAiB,CAACoD,cAAc,CAAC,EAAE,MAAM,CAAC;UACzE,CAAC,MAAM,IAAI,CAAC5D,KAAK,CAAC6D,QAAQ,IAAIxC,cAAc,GAAGM,YAAY,EAAE;YAC3DlD,QAAQ,CAAC4C,cAAc,GAAG,CAAC,EAAEb,iBAAiB,CAACoD,cAAc,CAAC,EAAE,OAAO,CAAC;UAC1E;UACA;QACF;MACF,KAAK,GAAG;QACN;UACE,MAAMvE,KAAK,GAAGqB,MAAM,CAACrB,KAAK;UAC1B,IAAIA,KAAK,KAAKpD,8BAA8B,EAAE;YAC5C;UACF;UACA,MAAM6H,MAAM,GAAGpD,MAAM,CAACoD,MAAM;UAC5B,IAAIA,MAAM,KAAKA,MAAM,CAACzE,KAAK,KAAKrD,6BAA6B,IAAIE,gBAAgB,CAAC4H,MAAM,CAACzE,KAAK,CAAC,CAAC,EAAE;YAChG;UACF;UACA,IAAI,CAACW,KAAK,CAAC6D,QAAQ,IAAID,cAAc,GAAGnC,kBAAkB,EAAE;YAC1DhD,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAAC0B,IAAI,CAACC,GAAG,CAACyB,cAAc,GAAGzC,gBAAgB,EAAEM,kBAAkB,CAAC,CAAC,CAAC;UAC9G;UACA;QACF;MACF,KAAK,UAAU;QACb;UACE,IAAImC,cAAc,GAAGnC,kBAAkB,EAAE;YACvChD,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAAC0B,IAAI,CAACC,GAAG,CAACyB,cAAc,GAAGzC,gBAAgB,EAAEM,kBAAkB,CAAC,CAAC,CAAC;UAC9G;UACA;QACF;MACF,KAAK,QAAQ;QACX;UACE;UACA,MAAMsC,YAAY,GAAG7B,IAAI,CAAC8B,GAAG,CAACJ,cAAc,GAAGzC,gBAAgB,EAAEI,mBAAmB,CAAC;UACrF,IAAIwC,YAAY,KAAKH,cAAc,IAAIG,YAAY,IAAIxC,mBAAmB,EAAE;YAC1E9C,QAAQ,CAAC4C,cAAc,EAAEb,iBAAiB,CAACuD,YAAY,CAAC,CAAC;UAC3D,CAAC,MAAM;YACLhE,UAAU,CAACsB,cAAc,EAAErB,KAAK,CAAC;UACnC;UACA;QACF;MACF,KAAK,MAAM;QACT;UACE,IAAIA,KAAK,CAACoC,OAAO,IAAIpC,KAAK,CAACqC,OAAO,IAAIrC,KAAK,CAAC6D,QAAQ,EAAE;YACpDpF,QAAQ,CAACiD,aAAa,EAAElB,iBAAiB,CAACe,mBAAmB,CAAC,CAAC;UACjE,CAAC,MAAM;YACL9C,QAAQ,CAACiD,aAAa,EAAElB,iBAAiB,CAACoD,cAAc,CAAC,CAAC;UAC5D;UACA;QACF;MACF,KAAK,KAAK;QACR;UACE,IAAI5D,KAAK,CAACoC,OAAO,IAAIpC,KAAK,CAACqC,OAAO,IAAIrC,KAAK,CAAC6D,QAAQ,EAAE;YACpDpF,QAAQ,CAACkD,YAAY,EAAEnB,iBAAiB,CAACiB,kBAAkB,CAAC,CAAC;UAC/D,CAAC,MAAM;YACLhD,QAAQ,CAACkD,YAAY,EAAEnB,iBAAiB,CAACoD,cAAc,CAAC,CAAC;UAC3D;UACA;QACF;MACF;QACE;UACE/B,oBAAoB,GAAG,KAAK;QAC9B;IACJ;IACA,IAAIA,oBAAoB,EAAE;MACxB7B,KAAK,CAACuC,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACvE,MAAM,EAAEI,kBAAkB,EAAED,KAAK,EAAEM,QAAQ,EAAE+B,iBAAiB,EAAElC,sBAAsB,EAAE6B,gBAAgB,EAAEJ,UAAU,CAAC,CAAC;EAC1H,MAAMkE,sBAAsB,GAAGnI,KAAK,CAACuC,WAAW,CAAC,CAAC6F,YAAY,EAAE;IAC9DlE;EACF,CAAC,KAAK;IACJ,IAAIA,KAAK,CAAC8B,GAAG,KAAK,GAAG,EAAE;MACrB;MACA,OAAO,KAAK;IACd;IACA,OAAOoC,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC;EACNlH,4BAA4B,CAACgB,MAAM,EAAE,iBAAiB,EAAEiG,sBAAsB,CAAC;EAC/E3H,YAAY,CAAC0B,MAAM,EAAE,qBAAqB,EAAEyC,yBAAyB,CAAC;EACtEnE,YAAY,CAAC0B,MAAM,EAAE,qBAAqB,EAAEwE,yBAAyB,CAAC;EACtElG,YAAY,CAAC0B,MAAM,EAAE,0BAA0B,EAAE2E,8BAA8B,CAAC;EAChFrG,YAAY,CAAC0B,MAAM,EAAE,aAAa,EAAEoF,iBAAiB,CAAC;AACxD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}