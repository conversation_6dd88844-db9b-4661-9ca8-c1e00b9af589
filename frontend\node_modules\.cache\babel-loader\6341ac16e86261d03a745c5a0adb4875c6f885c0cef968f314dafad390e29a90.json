{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useTheme } from '@mui/material/styles';\nimport { getThemeProps } from '@mui/system';\nimport { GRID_DEFAULT_LOCALE_TEXT } from \"../constants/index.js\";\nimport { DATA_GRID_DEFAULT_SLOTS_COMPONENTS } from \"../constants/defaultGridSlotsComponents.js\";\nimport { computeSlots } from \"../internals/utils/index.js\";\nimport { DATA_GRID_PROPS_DEFAULT_VALUES } from \"../constants/dataGridPropsDefaultValues.js\";\nconst DATA_GRID_FORCED_PROPS = {\n  disableMultipleColumnsFiltering: true,\n  disableMultipleColumnsSorting: true,\n  throttleRowsMs: undefined,\n  hideFooterRowCount: false,\n  pagination: true,\n  checkboxSelectionVisibleOnly: false,\n  disableColumnReorder: true,\n  keepColumnPositionIfDraggedOutside: false,\n  signature: 'DataGrid',\n  listView: false\n};\nconst getDataGridForcedProps = themedProps => _extends({}, DATA_GRID_FORCED_PROPS, themedProps.dataSource ? {\n  filterMode: 'server',\n  sortingMode: 'server',\n  paginationMode: 'server'\n} : {});\nconst defaultSlots = DATA_GRID_DEFAULT_SLOTS_COMPONENTS;\nexport const useDataGridProps = inProps => {\n  const theme = useTheme();\n  const themedProps = React.useMemo(() => getThemeProps({\n    props: inProps,\n    theme,\n    name: 'MuiDataGrid'\n  }), [theme, inProps]);\n  const localeText = React.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);\n  const slots = React.useMemo(() => computeSlots({\n    defaultSlots,\n    slots: themedProps.slots\n  }), [themedProps.slots]);\n  const injectDefaultProps = React.useMemo(() => {\n    return Object.keys(DATA_GRID_PROPS_DEFAULT_VALUES).reduce((acc, key) => {\n      // @ts-ignore\n      acc[key] = themedProps[key] ?? DATA_GRID_PROPS_DEFAULT_VALUES[key];\n      return acc;\n    }, {});\n  }, [themedProps]);\n  return React.useMemo(() => _extends({}, themedProps, injectDefaultProps, {\n    localeText,\n    slots\n  }, getDataGridForcedProps(themedProps)), [themedProps, localeText, slots, injectDefaultProps]);\n};", "map": {"version": 3, "names": ["_extends", "React", "useTheme", "getThemeProps", "GRID_DEFAULT_LOCALE_TEXT", "DATA_GRID_DEFAULT_SLOTS_COMPONENTS", "computeSlots", "DATA_GRID_PROPS_DEFAULT_VALUES", "DATA_GRID_FORCED_PROPS", "disableMultipleColumnsFiltering", "disableMultipleColumnsSorting", "throttleRowsMs", "undefined", "hideFooterRowCount", "pagination", "checkboxSelectionVisibleOnly", "disableColumnReorder", "keepColumnPositionIfDraggedOutside", "signature", "listView", "getDataGridForcedProps", "themedProps", "dataSource", "filterMode", "sortingMode", "paginationMode", "defaultSlots", "useDataGridProps", "inProps", "theme", "useMemo", "props", "name", "localeText", "slots", "injectDefaultProps", "Object", "keys", "reduce", "acc", "key"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/DataGrid/useDataGridProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useTheme } from '@mui/material/styles';\nimport { getThemeProps } from '@mui/system';\nimport { GRID_DEFAULT_LOCALE_TEXT } from \"../constants/index.js\";\nimport { DATA_GRID_DEFAULT_SLOTS_COMPONENTS } from \"../constants/defaultGridSlotsComponents.js\";\nimport { computeSlots } from \"../internals/utils/index.js\";\nimport { DATA_GRID_PROPS_DEFAULT_VALUES } from \"../constants/dataGridPropsDefaultValues.js\";\nconst DATA_GRID_FORCED_PROPS = {\n  disableMultipleColumnsFiltering: true,\n  disableMultipleColumnsSorting: true,\n  throttleRowsMs: undefined,\n  hideFooterRowCount: false,\n  pagination: true,\n  checkboxSelectionVisibleOnly: false,\n  disableColumnReorder: true,\n  keepColumnPositionIfDraggedOutside: false,\n  signature: 'DataGrid',\n  listView: false\n};\nconst getDataGridForcedProps = themedProps => _extends({}, DATA_GRID_FORCED_PROPS, themedProps.dataSource ? {\n  filterMode: 'server',\n  sortingMode: 'server',\n  paginationMode: 'server'\n} : {});\nconst defaultSlots = DATA_GRID_DEFAULT_SLOTS_COMPONENTS;\nexport const useDataGridProps = inProps => {\n  const theme = useTheme();\n  const themedProps = React.useMemo(() => getThemeProps({\n    props: inProps,\n    theme,\n    name: 'MuiDataGrid'\n  }), [theme, inProps]);\n  const localeText = React.useMemo(() => _extends({}, GRID_DEFAULT_LOCALE_TEXT, themedProps.localeText), [themedProps.localeText]);\n  const slots = React.useMemo(() => computeSlots({\n    defaultSlots,\n    slots: themedProps.slots\n  }), [themedProps.slots]);\n  const injectDefaultProps = React.useMemo(() => {\n    return Object.keys(DATA_GRID_PROPS_DEFAULT_VALUES).reduce((acc, key) => {\n      // @ts-ignore\n      acc[key] = themedProps[key] ?? DATA_GRID_PROPS_DEFAULT_VALUES[key];\n      return acc;\n    }, {});\n  }, [themedProps]);\n  return React.useMemo(() => _extends({}, themedProps, injectDefaultProps, {\n    localeText,\n    slots\n  }, getDataGridForcedProps(themedProps)), [themedProps, localeText, slots, injectDefaultProps]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,kCAAkC,QAAQ,4CAA4C;AAC/F,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,MAAMC,sBAAsB,GAAG;EAC7BC,+BAA+B,EAAE,IAAI;EACrCC,6BAA6B,EAAE,IAAI;EACnCC,cAAc,EAAEC,SAAS;EACzBC,kBAAkB,EAAE,KAAK;EACzBC,UAAU,EAAE,IAAI;EAChBC,4BAA4B,EAAE,KAAK;EACnCC,oBAAoB,EAAE,IAAI;EAC1BC,kCAAkC,EAAE,KAAK;EACzCC,SAAS,EAAE,UAAU;EACrBC,QAAQ,EAAE;AACZ,CAAC;AACD,MAAMC,sBAAsB,GAAGC,WAAW,IAAIrB,QAAQ,CAAC,CAAC,CAAC,EAAEQ,sBAAsB,EAAEa,WAAW,CAACC,UAAU,GAAG;EAC1GC,UAAU,EAAE,QAAQ;EACpBC,WAAW,EAAE,QAAQ;EACrBC,cAAc,EAAE;AAClB,CAAC,GAAG,CAAC,CAAC,CAAC;AACP,MAAMC,YAAY,GAAGrB,kCAAkC;AACvD,OAAO,MAAMsB,gBAAgB,GAAGC,OAAO,IAAI;EACzC,MAAMC,KAAK,GAAG3B,QAAQ,CAAC,CAAC;EACxB,MAAMmB,WAAW,GAAGpB,KAAK,CAAC6B,OAAO,CAAC,MAAM3B,aAAa,CAAC;IACpD4B,KAAK,EAAEH,OAAO;IACdC,KAAK;IACLG,IAAI,EAAE;EACR,CAAC,CAAC,EAAE,CAACH,KAAK,EAAED,OAAO,CAAC,CAAC;EACrB,MAAMK,UAAU,GAAGhC,KAAK,CAAC6B,OAAO,CAAC,MAAM9B,QAAQ,CAAC,CAAC,CAAC,EAAEI,wBAAwB,EAAEiB,WAAW,CAACY,UAAU,CAAC,EAAE,CAACZ,WAAW,CAACY,UAAU,CAAC,CAAC;EAChI,MAAMC,KAAK,GAAGjC,KAAK,CAAC6B,OAAO,CAAC,MAAMxB,YAAY,CAAC;IAC7CoB,YAAY;IACZQ,KAAK,EAAEb,WAAW,CAACa;EACrB,CAAC,CAAC,EAAE,CAACb,WAAW,CAACa,KAAK,CAAC,CAAC;EACxB,MAAMC,kBAAkB,GAAGlC,KAAK,CAAC6B,OAAO,CAAC,MAAM;IAC7C,OAAOM,MAAM,CAACC,IAAI,CAAC9B,8BAA8B,CAAC,CAAC+B,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACtE;MACAD,GAAG,CAACC,GAAG,CAAC,GAAGnB,WAAW,CAACmB,GAAG,CAAC,IAAIjC,8BAA8B,CAACiC,GAAG,CAAC;MAClE,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,EAAE,CAAClB,WAAW,CAAC,CAAC;EACjB,OAAOpB,KAAK,CAAC6B,OAAO,CAAC,MAAM9B,QAAQ,CAAC,CAAC,CAAC,EAAEqB,WAAW,EAAEc,kBAAkB,EAAE;IACvEF,UAAU;IACVC;EACF,CAAC,EAAEd,sBAAsB,CAACC,WAAW,CAAC,CAAC,EAAE,CAACA,WAAW,EAAEY,UAAU,EAAEC,KAAK,EAAEC,kBAAkB,CAAC,CAAC;AAChG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}