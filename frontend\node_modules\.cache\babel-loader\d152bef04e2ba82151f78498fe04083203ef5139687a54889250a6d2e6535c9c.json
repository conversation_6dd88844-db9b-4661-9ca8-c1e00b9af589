{"ast": null, "code": "import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridHeaderFilteringStateSelector = createRootSelector(state => state.headerFiltering);\nexport const gridHeaderFilteringEnabledSelector = createSelector(gridHeaderFilteringStateSelector,\n// No initialization in MIT, so we need to default to false to be used by `getTotalHeaderHeight`\nheaderFilteringState => headerFilteringState?.enabled ?? false);\nexport const gridHeaderFilteringEditFieldSelector = createSelector(gridHeaderFilteringStateSelector, headerFilteringState => headerFilteringState.editing);\nexport const gridHeaderFilteringMenuSelector = createSelector(gridHeaderFilteringStateSelector, headerFilteringState => headerFilteringState.menuOpen);", "map": {"version": 3, "names": ["createSelector", "createRootSelector", "gridHeaderFilteringStateSelector", "state", "headerFiltering", "gridHeaderFilteringEnabledSelector", "headerFilteringState", "enabled", "gridHeaderFilteringEditFieldSelector", "editing", "gridHeaderFilteringMenuSelector", "menuOpen"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/headerFiltering/gridHeaderFilteringSelectors.js"], "sourcesContent": ["import { createSelector, createRootSelector } from \"../../../utils/createSelector.js\";\nexport const gridHeaderFilteringStateSelector = createRootSelector(state => state.headerFiltering);\nexport const gridHeaderFilteringEnabledSelector = createSelector(gridHeaderFilteringStateSelector,\n// No initialization in MIT, so we need to default to false to be used by `getTotalHeaderHeight`\nheaderFilteringState => headerFilteringState?.enabled ?? false);\nexport const gridHeaderFilteringEditFieldSelector = createSelector(gridHeaderFilteringStateSelector, headerFilteringState => headerFilteringState.editing);\nexport const gridHeaderFilteringMenuSelector = createSelector(gridHeaderFilteringStateSelector, headerFilteringState => headerFilteringState.menuOpen);"], "mappings": "AAAA,SAASA,cAAc,EAAEC,kBAAkB,QAAQ,kCAAkC;AACrF,OAAO,MAAMC,gCAAgC,GAAGD,kBAAkB,CAACE,KAAK,IAAIA,KAAK,CAACC,eAAe,CAAC;AAClG,OAAO,MAAMC,kCAAkC,GAAGL,cAAc,CAACE,gCAAgC;AACjG;AACAI,oBAAoB,IAAIA,oBAAoB,EAAEC,OAAO,IAAI,KAAK,CAAC;AAC/D,OAAO,MAAMC,oCAAoC,GAAGR,cAAc,CAACE,gCAAgC,EAAEI,oBAAoB,IAAIA,oBAAoB,CAACG,OAAO,CAAC;AAC1J,OAAO,MAAMC,+BAA+B,GAAGV,cAAc,CAACE,gCAAgC,EAAEI,oBAAoB,IAAIA,oBAAoB,CAACK,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}