{"ast": null, "code": "import { mean } from './mean.mjs';\nfunction meanBy(items, getValue) {\n  const nums = items.map(x => getValue(x));\n  return mean(nums);\n}\nexport { meanBy };", "map": {"version": 3, "names": ["mean", "meanBy", "items", "getValue", "nums", "map", "x"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/math/meanBy.mjs"], "sourcesContent": ["import { mean } from './mean.mjs';\n\nfunction meanBy(items, getValue) {\n    const nums = items.map(x => getValue(x));\n    return mean(nums);\n}\n\nexport { meanBy };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AAEjC,SAASC,MAAMA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC7B,MAAMC,IAAI,GAAGF,KAAK,CAACG,GAAG,CAACC,CAAC,IAAIH,QAAQ,CAACG,CAAC,CAAC,CAAC;EACxC,OAAON,IAAI,CAACI,IAAI,CAAC;AACrB;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}