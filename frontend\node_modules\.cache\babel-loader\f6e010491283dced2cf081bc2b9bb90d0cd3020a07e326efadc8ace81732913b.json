{"ast": null, "code": "function mapValues(object, getNewValue) {\n  const result = {};\n  const keys = Object.keys(object);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const value = object[key];\n    result[key] = getNewValue(value, key, object);\n  }\n  return result;\n}\nexport { mapValues };", "map": {"version": 3, "names": ["mapValues", "object", "getNewValue", "result", "keys", "Object", "i", "length", "key", "value"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/object/mapValues.mjs"], "sourcesContent": ["function mapValues(object, getNewValue) {\n    const result = {};\n    const keys = Object.keys(object);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const value = object[key];\n        result[key] = getNewValue(value, key, object);\n    }\n    return result;\n}\n\nexport { mapValues };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,MAAM,EAAEC,WAAW,EAAE;EACpC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACH,MAAM,CAAC;EAChC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,MAAME,GAAG,GAAGJ,IAAI,CAACE,CAAC,CAAC;IACnB,MAAMG,KAAK,GAAGR,MAAM,CAACO,GAAG,CAAC;IACzBL,MAAM,CAACK,GAAG,CAAC,GAAGN,WAAW,CAACO,KAAK,EAAED,GAAG,EAAEP,MAAM,CAAC;EACjD;EACA,OAAOE,MAAM;AACjB;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}