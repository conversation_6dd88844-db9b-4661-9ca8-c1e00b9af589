{"ast": null, "code": "function difference(firstArr, secondArr) {\n  const secondSet = new Set(secondArr);\n  return firstArr.filter(item => !secondSet.has(item));\n}\nexport { difference };", "map": {"version": 3, "names": ["difference", "firstArr", "secondArr", "secondSet", "Set", "filter", "item", "has"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/difference.mjs"], "sourcesContent": ["function difference(firstArr, secondArr) {\n    const secondSet = new Set(secondArr);\n    return firstArr.filter(item => !secondSet.has(item));\n}\n\nexport { difference };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACrC,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAACF,SAAS,CAAC;EACpC,OAAOD,QAAQ,CAACI,MAAM,CAACC,IAAI,IAAI,CAACH,SAAS,CAACI,GAAG,CAACD,IAAI,CAAC,CAAC;AACxD;AAEA,SAASN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}