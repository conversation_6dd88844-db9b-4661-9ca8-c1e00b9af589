{"ast": null, "code": "import useLazyRef from '@mui/utils/useLazyRef';\nimport { Store } from '@mui/x-internals/store';\nimport { Colspan } from \"./features/colspan.js\";\nimport { Dimensions } from \"./features/dimensions.js\";\nimport { Keyboard } from \"./features/keyboard.js\";\nimport { Rowspan } from \"./features/rowspan.js\";\nimport { Virtualization } from \"./features/virtualization.js\";\n\n/* eslint-disable jsdoc/require-param-type */\n/* eslint-disable jsdoc/require-param-description */\n/* eslint-disable jsdoc/require-returns-type */\n\nconst FEATURES = [Dimensions, Virtualization, Colspan, Rowspan, Keyboard];\nexport const useVirtualizer = params => {\n  const store = useLazyRef(() => {\n    return new Store(FEATURES.map(f => f.initialize(params)).reduce((state, partial) => Object.assign(state, partial), {}));\n  }).current;\n  const api = {};\n  for (const feature of FEATURES) {\n    Object.assign(api, feature.use(store, params, api));\n  }\n  return {\n    store,\n    api\n  };\n};", "map": {"version": 3, "names": ["useLazyRef", "Store", "<PERSON><PERSON>", "Dimensions", "Keyboard", "Rowspan", "Virtualization", "FEATURES", "useVirtualizer", "params", "store", "map", "f", "initialize", "reduce", "state", "partial", "Object", "assign", "current", "api", "feature", "use"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-virtualizer/esm/useVirtualizer.js"], "sourcesContent": ["import useLazyRef from '@mui/utils/useLazyRef';\nimport { Store } from '@mui/x-internals/store';\nimport { Colspan } from \"./features/colspan.js\";\nimport { Dimensions } from \"./features/dimensions.js\";\nimport { Keyboard } from \"./features/keyboard.js\";\nimport { Rowspan } from \"./features/rowspan.js\";\nimport { Virtualization } from \"./features/virtualization.js\";\n\n/* eslint-disable jsdoc/require-param-type */\n/* eslint-disable jsdoc/require-param-description */\n/* eslint-disable jsdoc/require-returns-type */\n\nconst FEATURES = [Dimensions, Virtualization, Colspan, Rowspan, Keyboard];\nexport const useVirtualizer = params => {\n  const store = useLazyRef(() => {\n    return new Store(FEATURES.map(f => f.initialize(params)).reduce((state, partial) => Object.assign(state, partial), {}));\n  }).current;\n  const api = {};\n  for (const feature of FEATURES) {\n    Object.assign(api, feature.use(store, params, api));\n  }\n  return {\n    store,\n    api\n  };\n};"], "mappings": "AAAA,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,cAAc,QAAQ,8BAA8B;;AAE7D;AACA;AACA;;AAEA,MAAMC,QAAQ,GAAG,CAACJ,UAAU,EAAEG,cAAc,EAAEJ,OAAO,EAAEG,OAAO,EAAED,QAAQ,CAAC;AACzE,OAAO,MAAMI,cAAc,GAAGC,MAAM,IAAI;EACtC,MAAMC,KAAK,GAAGV,UAAU,CAAC,MAAM;IAC7B,OAAO,IAAIC,KAAK,CAACM,QAAQ,CAACI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAACJ,MAAM,CAAC,CAAC,CAACK,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAKC,MAAM,CAACC,MAAM,CAACH,KAAK,EAAEC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACzH,CAAC,CAAC,CAACG,OAAO;EACV,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,OAAO,IAAId,QAAQ,EAAE;IAC9BU,MAAM,CAACC,MAAM,CAACE,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACZ,KAAK,EAAED,MAAM,EAAEW,GAAG,CAAC,CAAC;EACrD;EACA,OAAO;IACLV,KAAK;IACLU;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}