{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridPanelWrapper } from \"./GridPanelWrapper.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnsPanel(props) {\n  const rootProps = useGridRootProps();\n  return /*#__PURE__*/_jsx(GridPanelWrapper, _extends({}, props, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.columnsManagement, _extends({}, rootProps.slotProps?.columnsManagement))\n  }));\n}\nexport { GridColumnsPanel };", "map": {"version": 3, "names": ["_extends", "React", "GridPanelWrapper", "useGridRootProps", "jsx", "_jsx", "GridColumnsPanel", "props", "rootProps", "children", "slots", "columnsManagement", "slotProps"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/panel/GridColumnsPanel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridPanelWrapper } from \"./GridPanelWrapper.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnsPanel(props) {\n  const rootProps = useGridRootProps();\n  return /*#__PURE__*/_jsx(GridPanelWrapper, _extends({}, props, {\n    children: /*#__PURE__*/_jsx(rootProps.slots.columnsManagement, _extends({}, rootProps.slotProps?.columnsManagement))\n  }));\n}\nexport { GridColumnsPanel };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAMC,SAAS,GAAGL,gBAAgB,CAAC,CAAC;EACpC,OAAO,aAAaE,IAAI,CAACH,gBAAgB,EAAEF,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;IAC7DE,QAAQ,EAAE,aAAaJ,IAAI,CAACG,SAAS,CAACE,KAAK,CAACC,iBAAiB,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEQ,SAAS,CAACI,SAAS,EAAED,iBAAiB,CAAC;EACrH,CAAC,CAAC,CAAC;AACL;AACA,SAASL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}