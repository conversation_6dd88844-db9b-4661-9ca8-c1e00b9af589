{"ast": null, "code": "export function getKeyDefault(params) {\n  return JSON.stringify([params.filterModel, params.sortModel, params.start, params.end]);\n}\nexport class GridDataSourceCacheDefault {\n  constructor({\n    ttl = 300_000,\n    getKey = getKeyDefault\n  }) {\n    this.cache = {};\n    this.ttl = ttl;\n    this.getKey = getKey;\n  }\n  set(key, value) {\n    const keyString = this.getKey(key);\n    const expiry = Date.now() + this.ttl;\n    this.cache[keyString] = {\n      value,\n      expiry\n    };\n  }\n  get(key) {\n    const keyString = this.getKey(key);\n    const entry = this.cache[keyString];\n    if (!entry) {\n      return undefined;\n    }\n    if (Date.now() > entry.expiry) {\n      delete this.cache[keyString];\n      return undefined;\n    }\n    return entry.value;\n  }\n  clear() {\n    this.cache = {};\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params", "JSON", "stringify", "filterModel", "sortModel", "start", "end", "GridDataSourceCacheDefault", "constructor", "ttl", "<PERSON><PERSON><PERSON>", "cache", "set", "key", "value", "keyString", "expiry", "Date", "now", "get", "entry", "undefined", "clear"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/dataSource/cache.js"], "sourcesContent": ["export function getKeyDefault(params) {\n  return JSON.stringify([params.filterModel, params.sortModel, params.start, params.end]);\n}\nexport class GridDataSourceCacheDefault {\n  constructor({\n    ttl = 300_000,\n    getKey = getKeyDefault\n  }) {\n    this.cache = {};\n    this.ttl = ttl;\n    this.getKey = getKey;\n  }\n  set(key, value) {\n    const keyString = this.getKey(key);\n    const expiry = Date.now() + this.ttl;\n    this.cache[keyString] = {\n      value,\n      expiry\n    };\n  }\n  get(key) {\n    const keyString = this.getKey(key);\n    const entry = this.cache[keyString];\n    if (!entry) {\n      return undefined;\n    }\n    if (Date.now() > entry.expiry) {\n      delete this.cache[keyString];\n      return undefined;\n    }\n    return entry.value;\n  }\n  clear() {\n    this.cache = {};\n  }\n}"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOC,IAAI,CAACC,SAAS,CAAC,CAACF,MAAM,CAACG,WAAW,EAAEH,MAAM,CAACI,SAAS,EAAEJ,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,GAAG,CAAC,CAAC;AACzF;AACA,OAAO,MAAMC,0BAA0B,CAAC;EACtCC,WAAWA,CAAC;IACVC,GAAG,GAAG,OAAO;IACbC,MAAM,GAAGX;EACX,CAAC,EAAE;IACD,IAAI,CAACY,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;EACAE,GAAGA,CAACC,GAAG,EAAEC,KAAK,EAAE;IACd,MAAMC,SAAS,GAAG,IAAI,CAACL,MAAM,CAACG,GAAG,CAAC;IAClC,MAAMG,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACT,GAAG;IACpC,IAAI,CAACE,KAAK,CAACI,SAAS,CAAC,GAAG;MACtBD,KAAK;MACLE;IACF,CAAC;EACH;EACAG,GAAGA,CAACN,GAAG,EAAE;IACP,MAAME,SAAS,GAAG,IAAI,CAACL,MAAM,CAACG,GAAG,CAAC;IAClC,MAAMO,KAAK,GAAG,IAAI,CAACT,KAAK,CAACI,SAAS,CAAC;IACnC,IAAI,CAACK,KAAK,EAAE;MACV,OAAOC,SAAS;IAClB;IACA,IAAIJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGE,KAAK,CAACJ,MAAM,EAAE;MAC7B,OAAO,IAAI,CAACL,KAAK,CAACI,SAAS,CAAC;MAC5B,OAAOM,SAAS;IAClB;IACA,OAAOD,KAAK,CAACN,KAAK;EACpB;EACAQ,KAAKA,CAAA,EAAG;IACN,IAAI,CAACX,KAAK,GAAG,CAAC,CAAC;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}