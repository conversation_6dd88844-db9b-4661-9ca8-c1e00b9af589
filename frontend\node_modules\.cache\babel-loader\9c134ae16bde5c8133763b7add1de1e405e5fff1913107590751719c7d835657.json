{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"className\", \"onClick\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useQuickFilterContext } from \"./QuickFilterContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A button that resets the filter value.\n * It renders the `baseIconButton` slot.\n *\n * Demos:\n *\n * - [Quick Filter](https://mui.com/x/react-data-grid/components/quick-filter/)\n *\n * API:\n *\n * - [QuickFilterClear API](https://mui.com/x/api/data-grid/quick-filter-clear/)\n */\nconst QuickFilterClear = forwardRef(function QuickFilterClear(props, ref) {\n  const {\n      render,\n      className,\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const {\n    state,\n    clearValue\n  } = useQuickFilterContext();\n  const resolvedClassName = typeof className === 'function' ? className(state) : className;\n  const handleClick = event => {\n    clearValue();\n    onClick?.(event);\n  };\n  const element = useComponentRenderer(rootProps.slots.baseIconButton, render, _extends({}, rootProps.slotProps?.baseIconButton, {\n    className: resolvedClassName,\n    tabIndex: -1\n  }, other, {\n    onClick: handleClick,\n    ref\n  }), state);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: element\n  });\n});\nif (process.env.NODE_ENV !== \"production\") QuickFilterClear.displayName = \"QuickFilterClear\";\nprocess.env.NODE_ENV !== \"production\" ? QuickFilterClear.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  className: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  color: PropTypes.oneOf(['default', 'inherit', 'primary']),\n  disabled: PropTypes.bool,\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  id: PropTypes.string,\n  label: PropTypes.string,\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func]),\n  role: PropTypes.string,\n  size: PropTypes.oneOf(['large', 'medium', 'small']),\n  style: PropTypes.object,\n  tabIndex: PropTypes.number,\n  title: PropTypes.string,\n  touchRippleRef: PropTypes.any\n} : void 0;\nexport { QuickFilterClear };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "forwardRef", "useComponentRenderer", "useGridRootProps", "useQuickFilterContext", "jsx", "_jsx", "QuickFilterClear", "props", "ref", "render", "className", "onClick", "other", "rootProps", "state", "clearValue", "resolvedClassName", "handleClick", "event", "element", "slots", "baseIconButton", "slotProps", "tabIndex", "Fragment", "children", "process", "env", "NODE_ENV", "displayName", "propTypes", "oneOfType", "func", "string", "color", "oneOf", "disabled", "bool", "edge", "id", "label", "role", "size", "style", "object", "number", "title", "touchRippleRef", "any"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/quickFilter/QuickFilterClear.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"render\", \"className\", \"onClick\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { forwardRef } from '@mui/x-internals/forwardRef';\nimport { useComponentRenderer } from '@mui/x-internals/useComponentRenderer';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useQuickFilterContext } from \"./QuickFilterContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * A button that resets the filter value.\n * It renders the `baseIconButton` slot.\n *\n * Demos:\n *\n * - [Quick Filter](https://mui.com/x/react-data-grid/components/quick-filter/)\n *\n * API:\n *\n * - [QuickFilterClear API](https://mui.com/x/api/data-grid/quick-filter-clear/)\n */\nconst QuickFilterClear = forwardRef(function QuickFilterClear(props, ref) {\n  const {\n      render,\n      className,\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootProps = useGridRootProps();\n  const {\n    state,\n    clearValue\n  } = useQuickFilterContext();\n  const resolvedClassName = typeof className === 'function' ? className(state) : className;\n  const handleClick = event => {\n    clearValue();\n    onClick?.(event);\n  };\n  const element = useComponentRenderer(rootProps.slots.baseIconButton, render, _extends({}, rootProps.slotProps?.baseIconButton, {\n    className: resolvedClassName,\n    tabIndex: -1\n  }, other, {\n    onClick: handleClick,\n    ref\n  }), state);\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: element\n  });\n});\nif (process.env.NODE_ENV !== \"production\") QuickFilterClear.displayName = \"QuickFilterClear\";\nprocess.env.NODE_ENV !== \"production\" ? QuickFilterClear.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  className: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  color: PropTypes.oneOf(['default', 'inherit', 'primary']),\n  disabled: PropTypes.bool,\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  id: PropTypes.string,\n  label: PropTypes.string,\n  /**\n   * A function to customize rendering of the component.\n   */\n  render: PropTypes.oneOfType([PropTypes.element, PropTypes.func]),\n  role: PropTypes.string,\n  size: PropTypes.oneOf(['large', 'medium', 'small']),\n  style: PropTypes.object,\n  tabIndex: PropTypes.number,\n  title: PropTypes.string,\n  touchRippleRef: PropTypes.any\n} : void 0;\nexport { QuickFilterClear };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGN,UAAU,CAAC,SAASM,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxE,MAAM;MACFC,MAAM;MACNC,SAAS;MACTC;IACF,CAAC,GAAGJ,KAAK;IACTK,KAAK,GAAGhB,6BAA6B,CAACW,KAAK,EAAEV,SAAS,CAAC;EACzD,MAAMgB,SAAS,GAAGX,gBAAgB,CAAC,CAAC;EACpC,MAAM;IACJY,KAAK;IACLC;EACF,CAAC,GAAGZ,qBAAqB,CAAC,CAAC;EAC3B,MAAMa,iBAAiB,GAAG,OAAON,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACI,KAAK,CAAC,GAAGJ,SAAS;EACxF,MAAMO,WAAW,GAAGC,KAAK,IAAI;IAC3BH,UAAU,CAAC,CAAC;IACZJ,OAAO,GAAGO,KAAK,CAAC;EAClB,CAAC;EACD,MAAMC,OAAO,GAAGlB,oBAAoB,CAACY,SAAS,CAACO,KAAK,CAACC,cAAc,EAAEZ,MAAM,EAAEd,QAAQ,CAAC,CAAC,CAAC,EAAEkB,SAAS,CAACS,SAAS,EAAED,cAAc,EAAE;IAC7HX,SAAS,EAAEM,iBAAiB;IAC5BO,QAAQ,EAAE,CAAC;EACb,CAAC,EAAEX,KAAK,EAAE;IACRD,OAAO,EAAEM,WAAW;IACpBT;EACF,CAAC,CAAC,EAAEM,KAAK,CAAC;EACV,OAAO,aAAaT,IAAI,CAACP,KAAK,CAAC0B,QAAQ,EAAE;IACvCC,QAAQ,EAAEN;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEtB,gBAAgB,CAACuB,WAAW,GAAG,kBAAkB;AAC5FH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,gBAAgB,CAACwB,SAAS,GAAG;EACnE;EACA;EACA;EACA;EACA;AACF;AACA;EACEpB,SAAS,EAAEX,SAAS,CAACgC,SAAS,CAAC,CAAChC,SAAS,CAACiC,IAAI,EAAEjC,SAAS,CAACkC,MAAM,CAAC,CAAC;EAClEC,KAAK,EAAEnC,SAAS,CAACoC,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EACzDC,QAAQ,EAAErC,SAAS,CAACsC,IAAI;EACxBC,IAAI,EAAEvC,SAAS,CAACoC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC9CI,EAAE,EAAExC,SAAS,CAACkC,MAAM;EACpBO,KAAK,EAAEzC,SAAS,CAACkC,MAAM;EACvB;AACF;AACA;EACExB,MAAM,EAAEV,SAAS,CAACgC,SAAS,CAAC,CAAChC,SAAS,CAACoB,OAAO,EAAEpB,SAAS,CAACiC,IAAI,CAAC,CAAC;EAChES,IAAI,EAAE1C,SAAS,CAACkC,MAAM;EACtBS,IAAI,EAAE3C,SAAS,CAACoC,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;EACnDQ,KAAK,EAAE5C,SAAS,CAAC6C,MAAM;EACvBrB,QAAQ,EAAExB,SAAS,CAAC8C,MAAM;EAC1BC,KAAK,EAAE/C,SAAS,CAACkC,MAAM;EACvBc,cAAc,EAAEhD,SAAS,CAACiD;AAC5B,CAAC,GAAG,KAAK,CAAC;AACV,SAAS1C,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}