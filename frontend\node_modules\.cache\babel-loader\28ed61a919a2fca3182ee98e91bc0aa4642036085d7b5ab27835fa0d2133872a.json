{"ast": null, "code": "function isDate(value) {\n  return value instanceof Date;\n}\nexport { isDate };", "map": {"version": 3, "names": ["isDate", "value", "Date"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isDate.mjs"], "sourcesContent": ["function isDate(value) {\n    return value instanceof Date;\n}\n\nexport { isDate };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,KAAK,EAAE;EACnB,OAAOA,KAAK,YAAYC,IAAI;AAChC;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}