{"ast": null, "code": "function head(arr) {\n  return arr[0];\n}\nexport { head };", "map": {"version": 3, "names": ["head", "arr"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/head.mjs"], "sourcesContent": ["function head(arr) {\n    return arr[0];\n}\n\nexport { head };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,GAAG,EAAE;EACf,OAAOA,GAAG,CAAC,CAAC,CAAC;AACjB;AAEA,SAASD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}