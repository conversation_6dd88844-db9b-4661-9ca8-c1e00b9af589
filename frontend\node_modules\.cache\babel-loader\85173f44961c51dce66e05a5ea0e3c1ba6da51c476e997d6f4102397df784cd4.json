{"ast": null, "code": "import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nexport function useGridApiMethod(privateApiRef, apiMethods, visibility) {\n  const isFirstRender = React.useRef(true);\n  useEnhancedEffect(() => {\n    isFirstRender.current = false;\n    privateApiRef.current.register(visibility, apiMethods);\n  }, [privateApiRef, visibility, apiMethods]);\n  if (isFirstRender.current) {\n    privateApiRef.current.register(visibility, apiMethods);\n  }\n}", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "useGridApiMethod", "privateApiRef", "apiMethods", "visibility", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useRef", "current", "register"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridApiMethod.js"], "sourcesContent": ["import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nexport function useGridApiMethod(privateApiRef, apiMethods, visibility) {\n  const isFirstRender = React.useRef(true);\n  useEnhancedEffect(() => {\n    isFirstRender.current = false;\n    privateApiRef.current.register(visibility, apiMethods);\n  }, [privateApiRef, visibility, apiMethods]);\n  if (isFirstRender.current) {\n    privateApiRef.current.register(visibility, apiMethods);\n  }\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAO,SAASC,gBAAgBA,CAACC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACtE,MAAMC,aAAa,GAAGN,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC;EACxCN,iBAAiB,CAAC,MAAM;IACtBK,aAAa,CAACE,OAAO,GAAG,KAAK;IAC7BL,aAAa,CAACK,OAAO,CAACC,QAAQ,CAACJ,UAAU,EAAED,UAAU,CAAC;EACxD,CAAC,EAAE,CAACD,aAAa,EAAEE,UAAU,EAAED,UAAU,CAAC,CAAC;EAC3C,IAAIE,aAAa,CAACE,OAAO,EAAE;IACzBL,aAAa,CAACK,OAAO,CAACC,QAAQ,CAACJ,UAAU,EAAED,UAAU,CAAC;EACxD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}