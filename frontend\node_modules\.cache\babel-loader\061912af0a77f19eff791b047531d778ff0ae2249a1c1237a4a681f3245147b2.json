{"ast": null, "code": "export const isJSDOM = typeof window !== 'undefined' && /jsdom|HappyDOM/.test(window.navigator.userAgent);", "map": {"version": 3, "names": ["isJSDOM", "window", "test", "navigator", "userAgent"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/isJSDOM.js"], "sourcesContent": ["export const isJSDOM = typeof window !== 'undefined' && /jsdom|HappyDOM/.test(window.navigator.userAgent);"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,gBAAgB,CAACC,IAAI,CAACD,MAAM,CAACE,SAAS,CAACC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}