{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridColumnDefinitionsSelector } from \"../../hooks/features/columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridPreferencePanelStateSelector } from \"../../hooks/features/preferencesPanel/gridPreferencePanelSelector.js\";\nimport { GridPreferencePanelsValue } from \"../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridPanelContext } from \"./GridPanelContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridPreferencesPanel() {\n  const apiRef = useGridApiContext();\n  const columns = useGridSelector(apiRef, gridColumnDefinitionsSelector);\n  const rootProps = useGridRootProps();\n  const preferencePanelState = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const {\n    columnsPanelTriggerRef,\n    filterPanelTriggerRef,\n    aiAssistantPanelTriggerRef\n  } = useGridPanelContext();\n  const panelContent = apiRef.current.unstable_applyPipeProcessors('preferencePanel', null, preferencePanelState.openedPanelValue ?? GridPreferencePanelsValue.filters);\n  let target = null;\n  switch (preferencePanelState.openedPanelValue) {\n    case GridPreferencePanelsValue.filters:\n      target = filterPanelTriggerRef.current;\n      break;\n    case GridPreferencePanelsValue.columns:\n      target = columnsPanelTriggerRef.current;\n      break;\n    case GridPreferencePanelsValue.aiAssistant:\n      target = aiAssistantPanelTriggerRef.current;\n      break;\n    default:\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.panel, _extends({\n    id: preferencePanelState.panelId,\n    open: columns.length > 0 && preferencePanelState.open,\n    \"aria-labelledby\": preferencePanelState.labelId,\n    target: target,\n    onClose: () => apiRef.current.hidePreferences()\n  }, rootProps.slotProps?.panel, {\n    children: panelContent\n  }));\n}", "map": {"version": 3, "names": ["_extends", "React", "gridColumnDefinitionsSelector", "useGridSelector", "gridPreferencePanelStateSelector", "GridPreferencePanelsValue", "useGridApiContext", "useGridRootProps", "useGridPanelContext", "jsx", "_jsx", "GridPreferencesPanel", "apiRef", "columns", "rootProps", "preferencePanelState", "columnsPanelTriggerRef", "filterPanelTriggerRef", "aiAssistantPanelTriggerRef", "panelContent", "current", "unstable_applyPipeProcessors", "openedPanelValue", "filters", "target", "aiAssistant", "slots", "panel", "id", "panelId", "open", "length", "labelId", "onClose", "hidePreferences", "slotProps", "children"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/panel/GridPreferencesPanel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridColumnDefinitionsSelector } from \"../../hooks/features/columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../hooks/utils/useGridSelector.js\";\nimport { gridPreferencePanelStateSelector } from \"../../hooks/features/preferencesPanel/gridPreferencePanelSelector.js\";\nimport { GridPreferencePanelsValue } from \"../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridPanelContext } from \"./GridPanelContext.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridPreferencesPanel() {\n  const apiRef = useGridApiContext();\n  const columns = useGridSelector(apiRef, gridColumnDefinitionsSelector);\n  const rootProps = useGridRootProps();\n  const preferencePanelState = useGridSelector(apiRef, gridPreferencePanelStateSelector);\n  const {\n    columnsPanelTriggerRef,\n    filterPanelTriggerRef,\n    aiAssistantPanelTriggerRef\n  } = useGridPanelContext();\n  const panelContent = apiRef.current.unstable_applyPipeProcessors('preferencePanel', null, preferencePanelState.openedPanelValue ?? GridPreferencePanelsValue.filters);\n  let target = null;\n  switch (preferencePanelState.openedPanelValue) {\n    case GridPreferencePanelsValue.filters:\n      target = filterPanelTriggerRef.current;\n      break;\n    case GridPreferencePanelsValue.columns:\n      target = columnsPanelTriggerRef.current;\n      break;\n    case GridPreferencePanelsValue.aiAssistant:\n      target = aiAssistantPanelTriggerRef.current;\n      break;\n    default:\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.panel, _extends({\n    id: preferencePanelState.panelId,\n    open: columns.length > 0 && preferencePanelState.open,\n    \"aria-labelledby\": preferencePanelState.labelId,\n    target: target,\n    onClose: () => apiRef.current.hidePreferences()\n  }, rootProps.slotProps?.panel, {\n    children: panelContent\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,6BAA6B,QAAQ,qDAAqD;AACnG,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,gCAAgC,QAAQ,sEAAsE;AACvH,SAASC,yBAAyB,QAAQ,oEAAoE;AAC9G,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,oBAAoBA,CAAA,EAAG;EACrC,MAAMC,MAAM,GAAGN,iBAAiB,CAAC,CAAC;EAClC,MAAMO,OAAO,GAAGV,eAAe,CAACS,MAAM,EAAEV,6BAA6B,CAAC;EACtE,MAAMY,SAAS,GAAGP,gBAAgB,CAAC,CAAC;EACpC,MAAMQ,oBAAoB,GAAGZ,eAAe,CAACS,MAAM,EAAER,gCAAgC,CAAC;EACtF,MAAM;IACJY,sBAAsB;IACtBC,qBAAqB;IACrBC;EACF,CAAC,GAAGV,mBAAmB,CAAC,CAAC;EACzB,MAAMW,YAAY,GAAGP,MAAM,CAACQ,OAAO,CAACC,4BAA4B,CAAC,iBAAiB,EAAE,IAAI,EAAEN,oBAAoB,CAACO,gBAAgB,IAAIjB,yBAAyB,CAACkB,OAAO,CAAC;EACrK,IAAIC,MAAM,GAAG,IAAI;EACjB,QAAQT,oBAAoB,CAACO,gBAAgB;IAC3C,KAAKjB,yBAAyB,CAACkB,OAAO;MACpCC,MAAM,GAAGP,qBAAqB,CAACG,OAAO;MACtC;IACF,KAAKf,yBAAyB,CAACQ,OAAO;MACpCW,MAAM,GAAGR,sBAAsB,CAACI,OAAO;MACvC;IACF,KAAKf,yBAAyB,CAACoB,WAAW;MACxCD,MAAM,GAAGN,0BAA0B,CAACE,OAAO;MAC3C;IACF;EACF;EACA,OAAO,aAAaV,IAAI,CAACI,SAAS,CAACY,KAAK,CAACC,KAAK,EAAE3B,QAAQ,CAAC;IACvD4B,EAAE,EAAEb,oBAAoB,CAACc,OAAO;IAChCC,IAAI,EAAEjB,OAAO,CAACkB,MAAM,GAAG,CAAC,IAAIhB,oBAAoB,CAACe,IAAI;IACrD,iBAAiB,EAAEf,oBAAoB,CAACiB,OAAO;IAC/CR,MAAM,EAAEA,MAAM;IACdS,OAAO,EAAEA,CAAA,KAAMrB,MAAM,CAACQ,OAAO,CAACc,eAAe,CAAC;EAChD,CAAC,EAAEpB,SAAS,CAACqB,SAAS,EAAER,KAAK,EAAE;IAC7BS,QAAQ,EAAEjB;EACZ,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}