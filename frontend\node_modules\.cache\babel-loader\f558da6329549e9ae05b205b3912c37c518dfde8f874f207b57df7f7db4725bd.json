{"ast": null, "code": "import { randomInt } from '../math/randomInt.mjs';\nfunction sampleSize(array, size) {\n  if (size > array.length) {\n    throw new Error('Size must be less than or equal to the length of array.');\n  }\n  const result = new Array(size);\n  const selected = new Set();\n  for (let step = array.length - size, resultIndex = 0; step < array.length; step++, resultIndex++) {\n    let index = randomInt(0, step + 1);\n    if (selected.has(index)) {\n      index = step;\n    }\n    selected.add(index);\n    result[resultIndex] = array[index];\n  }\n  return result;\n}\nexport { sampleSize };", "map": {"version": 3, "names": ["randomInt", "sampleSize", "array", "size", "length", "Error", "result", "Array", "selected", "Set", "step", "resultIndex", "index", "has", "add"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/sampleSize.mjs"], "sourcesContent": ["import { randomInt } from '../math/randomInt.mjs';\n\nfunction sampleSize(array, size) {\n    if (size > array.length) {\n        throw new Error('Size must be less than or equal to the length of array.');\n    }\n    const result = new Array(size);\n    const selected = new Set();\n    for (let step = array.length - size, resultIndex = 0; step < array.length; step++, resultIndex++) {\n        let index = randomInt(0, step + 1);\n        if (selected.has(index)) {\n            index = step;\n        }\n        selected.add(index);\n        result[resultIndex] = array[index];\n    }\n    return result;\n}\n\nexport { sampleSize };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,uBAAuB;AAEjD,SAASC,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC7B,IAAIA,IAAI,GAAGD,KAAK,CAACE,MAAM,EAAE;IACrB,MAAM,IAAIC,KAAK,CAAC,yDAAyD,CAAC;EAC9E;EACA,MAAMC,MAAM,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC;EAC9B,MAAMK,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1B,KAAK,IAAIC,IAAI,GAAGR,KAAK,CAACE,MAAM,GAAGD,IAAI,EAAEQ,WAAW,GAAG,CAAC,EAAED,IAAI,GAAGR,KAAK,CAACE,MAAM,EAAEM,IAAI,EAAE,EAAEC,WAAW,EAAE,EAAE;IAC9F,IAAIC,KAAK,GAAGZ,SAAS,CAAC,CAAC,EAAEU,IAAI,GAAG,CAAC,CAAC;IAClC,IAAIF,QAAQ,CAACK,GAAG,CAACD,KAAK,CAAC,EAAE;MACrBA,KAAK,GAAGF,IAAI;IAChB;IACAF,QAAQ,CAACM,GAAG,CAACF,KAAK,CAAC;IACnBN,MAAM,CAACK,WAAW,CAAC,GAAGT,KAAK,CAACU,KAAK,CAAC;EACtC;EACA,OAAON,MAAM;AACjB;AAEA,SAASL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}