{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GridPreferencePanelsValue } from \"../../../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnMenuManageItem(props) {\n  const {\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const showColumns = React.useCallback(event => {\n    onClick(event); // hide column menu\n    apiRef.current.showPreferences(GridPreferencePanelsValue.columns);\n  }, [apiRef, onClick]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: showColumns,\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuManageColumnsIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('columnMenuManageColumns')\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuManageItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuManageItem };", "map": {"version": 3, "names": ["React", "PropTypes", "GridPreferencePanelsValue", "useGridApiContext", "useGridRootProps", "jsx", "_jsx", "GridColumnMenuManageItem", "props", "onClick", "apiRef", "rootProps", "showColumns", "useCallback", "event", "current", "showPreferences", "columns", "disableColumnSelector", "slots", "baseMenuItem", "iconStart", "columnMenuManageColumnsIcon", "fontSize", "children", "getLocaleText", "process", "env", "NODE_ENV", "propTypes", "colDef", "object", "isRequired", "func"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/menu/columnMenu/menuItems/GridColumnMenuManageItem.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GridPreferencePanelsValue } from \"../../../../hooks/features/preferencesPanel/gridPreferencePanelsValue.js\";\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnMenuManageItem(props) {\n  const {\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const showColumns = React.useCallback(event => {\n    onClick(event); // hide column menu\n    apiRef.current.showPreferences(GridPreferencePanelsValue.columns);\n  }, [apiRef, onClick]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: showColumns,\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuManageColumnsIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('columnMenuManageColumns')\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuManageItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuManageItem };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,yBAAyB,QAAQ,0EAA0E;AACpH,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,wBAAwBA,CAACC,KAAK,EAAE;EACvC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,MAAM,GAAGP,iBAAiB,CAAC,CAAC;EAClC,MAAMQ,SAAS,GAAGP,gBAAgB,CAAC,CAAC;EACpC,MAAMQ,WAAW,GAAGZ,KAAK,CAACa,WAAW,CAACC,KAAK,IAAI;IAC7CL,OAAO,CAACK,KAAK,CAAC,CAAC,CAAC;IAChBJ,MAAM,CAACK,OAAO,CAACC,eAAe,CAACd,yBAAyB,CAACe,OAAO,CAAC;EACnE,CAAC,EAAE,CAACP,MAAM,EAAED,OAAO,CAAC,CAAC;EACrB,IAAIE,SAAS,CAACO,qBAAqB,EAAE;IACnC,OAAO,IAAI;EACb;EACA,OAAO,aAAaZ,IAAI,CAACK,SAAS,CAACQ,KAAK,CAACC,YAAY,EAAE;IACrDX,OAAO,EAAEG,WAAW;IACpBS,SAAS,EAAE,aAAaf,IAAI,CAACK,SAAS,CAACQ,KAAK,CAACG,2BAA2B,EAAE;MACxEC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFC,QAAQ,EAAEd,MAAM,CAACK,OAAO,CAACU,aAAa,CAAC,yBAAyB;EAClE,CAAC,CAAC;AACJ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrB,wBAAwB,CAACsB,SAAS,GAAG;EAC3E;EACA;EACA;EACA;EACAC,MAAM,EAAE7B,SAAS,CAAC8B,MAAM,CAACC,UAAU;EACnCvB,OAAO,EAAER,SAAS,CAACgC,IAAI,CAACD;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASzB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}