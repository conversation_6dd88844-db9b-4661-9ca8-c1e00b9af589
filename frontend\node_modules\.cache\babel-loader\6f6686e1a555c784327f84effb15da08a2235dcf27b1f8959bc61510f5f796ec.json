{"ast": null, "code": "import { PinnedColumnPosition } from \"../internals/constants.js\";\nexport const rtlFlipSide = (position, isRtl) => {\n  if (!position) {\n    return undefined;\n  }\n  if (!isRtl) {\n    if (position === PinnedColumnPosition.LEFT) {\n      return 'left';\n    }\n    if (position === PinnedColumnPosition.RIGHT) {\n      return 'right';\n    }\n  } else {\n    if (position === PinnedColumnPosition.LEFT) {\n      return 'right';\n    }\n    if (position === PinnedColumnPosition.RIGHT) {\n      return 'left';\n    }\n  }\n  return undefined;\n};", "map": {"version": 3, "names": ["PinnedColumnPosition", "rtlFlipSide", "position", "isRtl", "undefined", "LEFT", "RIGHT"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/rtlFlipSide.js"], "sourcesContent": ["import { PinnedColumnPosition } from \"../internals/constants.js\";\nexport const rtlFlipSide = (position, isRtl) => {\n  if (!position) {\n    return undefined;\n  }\n  if (!isRtl) {\n    if (position === PinnedColumnPosition.LEFT) {\n      return 'left';\n    }\n    if (position === PinnedColumnPosition.RIGHT) {\n      return 'right';\n    }\n  } else {\n    if (position === PinnedColumnPosition.LEFT) {\n      return 'right';\n    }\n    if (position === PinnedColumnPosition.RIGHT) {\n      return 'left';\n    }\n  }\n  return undefined;\n};"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,OAAO,MAAMC,WAAW,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;EAC9C,IAAI,CAACD,QAAQ,EAAE;IACb,OAAOE,SAAS;EAClB;EACA,IAAI,CAACD,KAAK,EAAE;IACV,IAAID,QAAQ,KAAKF,oBAAoB,CAACK,IAAI,EAAE;MAC1C,OAAO,MAAM;IACf;IACA,IAAIH,QAAQ,KAAKF,oBAAoB,CAACM,KAAK,EAAE;MAC3C,OAAO,OAAO;IAChB;EACF,CAAC,MAAM;IACL,IAAIJ,QAAQ,KAAKF,oBAAoB,CAACK,IAAI,EAAE;MAC1C,OAAO,OAAO;IAChB;IACA,IAAIH,QAAQ,KAAKF,oBAAoB,CAACM,KAAK,EAAE;MAC3C,OAAO,MAAM;IACf;EACF;EACA,OAAOF,SAAS;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}