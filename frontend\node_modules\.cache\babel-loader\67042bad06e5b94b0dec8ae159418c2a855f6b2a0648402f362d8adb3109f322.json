{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nexport const propsStateInitializer = (state, props) => {\n  return _extends({}, state, {\n    props: {\n      listView: props.listView,\n      getRowId: props.getRowId\n    }\n  });\n};\nexport const useGridProps = (apiRef, props) => {\n  React.useEffect(() => {\n    apiRef.current.setState(state => _extends({}, state, {\n      props: {\n        listView: props.listView,\n        getRowId: props.getRowId\n      }\n    }));\n  }, [apiRef, props.listView, props.getRowId]);\n};", "map": {"version": 3, "names": ["_extends", "React", "propsStateInitializer", "state", "props", "listView", "getRowId", "useGridProps", "apiRef", "useEffect", "current", "setState"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/core/useGridProps.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nexport const propsStateInitializer = (state, props) => {\n  return _extends({}, state, {\n    props: {\n      listView: props.listView,\n      getRowId: props.getRowId\n    }\n  });\n};\nexport const useGridProps = (apiRef, props) => {\n  React.useEffect(() => {\n    apiRef.current.setState(state => _extends({}, state, {\n      props: {\n        listView: props.listView,\n        getRowId: props.getRowId\n      }\n    }));\n  }, [apiRef, props.listView, props.getRowId]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,qBAAqB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EACrD,OAAOJ,QAAQ,CAAC,CAAC,CAAC,EAAEG,KAAK,EAAE;IACzBC,KAAK,EAAE;MACLC,QAAQ,EAAED,KAAK,CAACC,QAAQ;MACxBC,QAAQ,EAAEF,KAAK,CAACE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMC,YAAY,GAAGA,CAACC,MAAM,EAAEJ,KAAK,KAAK;EAC7CH,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpBD,MAAM,CAACE,OAAO,CAACC,QAAQ,CAACR,KAAK,IAAIH,QAAQ,CAAC,CAAC,CAAC,EAAEG,KAAK,EAAE;MACnDC,KAAK,EAAE;QACLC,QAAQ,EAAED,KAAK,CAACC,QAAQ;QACxBC,QAAQ,EAAEF,KAAK,CAACE;MAClB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACE,MAAM,EAAEJ,KAAK,CAACC,QAAQ,EAAED,KAAK,CAACE,QAAQ,CAAC,CAAC;AAC9C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}