{"ast": null, "code": "'use strict';\n\nimport AxiosError from './AxiosError.js';\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n *\n * @returns {object} The response.\n */\nexport default function settle(resolve, reject, response) {\n  const validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(new AxiosError('Request failed with status code ' + response.status, [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4], response.config, response.request, response));\n  }\n}", "map": {"version": 3, "names": ["AxiosError", "settle", "resolve", "reject", "response", "validateStatus", "config", "status", "ERR_BAD_REQUEST", "ERR_BAD_RESPONSE", "Math", "floor", "request"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/axios/lib/core/settle.js"], "sourcesContent": ["'use strict';\n\nimport AxiosError from './AxiosError.js';\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n *\n * @returns {object} The response.\n */\nexport default function settle(resolve, reject, response) {\n  const validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(new AxiosError(\n      'Request failed with status code ' + response.status,\n      [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],\n      response.config,\n      response.request,\n      response\n    ));\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,iBAAiB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,MAAMA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EACxD,MAAMC,cAAc,GAAGD,QAAQ,CAACE,MAAM,CAACD,cAAc;EACrD,IAAI,CAACD,QAAQ,CAACG,MAAM,IAAI,CAACF,cAAc,IAAIA,cAAc,CAACD,QAAQ,CAACG,MAAM,CAAC,EAAE;IAC1EL,OAAO,CAACE,QAAQ,CAAC;EACnB,CAAC,MAAM;IACLD,MAAM,CAAC,IAAIH,UAAU,CACnB,kCAAkC,GAAGI,QAAQ,CAACG,MAAM,EACpD,CAACP,UAAU,CAACQ,eAAe,EAAER,UAAU,CAACS,gBAAgB,CAAC,CAACC,IAAI,CAACC,KAAK,CAACP,QAAQ,CAACG,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAChGH,QAAQ,CAACE,MAAM,EACfF,QAAQ,CAACQ,OAAO,EAChBR,QACF,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}