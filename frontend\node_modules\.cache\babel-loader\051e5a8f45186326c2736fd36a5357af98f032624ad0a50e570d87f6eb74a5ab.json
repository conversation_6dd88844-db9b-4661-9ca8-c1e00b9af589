{"ast": null, "code": "import * as React from 'react';\nexport const useGridInitializeState = (initializer, privateApiRef, props, key) => {\n  const previousKey = React.useRef(key);\n  const isInitialized = React.useRef(false);\n  if (key !== previousKey.current) {\n    isInitialized.current = false;\n    previousKey.current = key;\n  }\n  if (!isInitialized.current) {\n    privateApiRef.current.state = initializer(privateApiRef.current.state, props, privateApiRef);\n    isInitialized.current = true;\n  }\n};", "map": {"version": 3, "names": ["React", "useGridInitializeState", "initializer", "privateApiRef", "props", "key", "previousKey", "useRef", "isInitialized", "current", "state"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridInitializeState.js"], "sourcesContent": ["import * as React from 'react';\nexport const useGridInitializeState = (initializer, privateApiRef, props, key) => {\n  const previousKey = React.useRef(key);\n  const isInitialized = React.useRef(false);\n  if (key !== previousKey.current) {\n    isInitialized.current = false;\n    previousKey.current = key;\n  }\n  if (!isInitialized.current) {\n    privateApiRef.current.state = initializer(privateApiRef.current.state, props, privateApiRef);\n    isInitialized.current = true;\n  }\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,sBAAsB,GAAGA,CAACC,WAAW,EAAEC,aAAa,EAAEC,KAAK,EAAEC,GAAG,KAAK;EAChF,MAAMC,WAAW,GAAGN,KAAK,CAACO,MAAM,CAACF,GAAG,CAAC;EACrC,MAAMG,aAAa,GAAGR,KAAK,CAACO,MAAM,CAAC,KAAK,CAAC;EACzC,IAAIF,GAAG,KAAKC,WAAW,CAACG,OAAO,EAAE;IAC/BD,aAAa,CAACC,OAAO,GAAG,KAAK;IAC7BH,WAAW,CAACG,OAAO,GAAGJ,GAAG;EAC3B;EACA,IAAI,CAACG,aAAa,CAACC,OAAO,EAAE;IAC1BN,aAAa,CAACM,OAAO,CAACC,KAAK,GAAGR,WAAW,CAACC,aAAa,CAACM,OAAO,CAACC,KAAK,EAAEN,KAAK,EAAED,aAAa,CAAC;IAC5FK,aAAa,CAACC,OAAO,GAAG,IAAI;EAC9B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}