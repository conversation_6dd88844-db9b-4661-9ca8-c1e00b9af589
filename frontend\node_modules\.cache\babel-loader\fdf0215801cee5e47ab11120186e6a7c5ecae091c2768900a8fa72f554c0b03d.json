{"ast": null, "code": "export const DEFAULT_GRID_AUTOSIZE_OPTIONS = {\n  includeHeaders: true,\n  includeOutliers: false,\n  outliersFactor: 1.5,\n  expand: false,\n  disableColumnVirtualization: true\n};\n\n/**\n * The Resize API interface that is available in the grid `apiRef`.\n */", "map": {"version": 3, "names": ["DEFAULT_GRID_AUTOSIZE_OPTIONS", "includeHeaders", "includeOutliers", "outliersFactor", "expand", "disableColumnVirtualization"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columnResize/gridColumnResizeApi.js"], "sourcesContent": ["export const DEFAULT_GRID_AUTOSIZE_OPTIONS = {\n  includeHeaders: true,\n  includeOutliers: false,\n  outliersFactor: 1.5,\n  expand: false,\n  disableColumnVirtualization: true\n};\n\n/**\n * The Resize API interface that is available in the grid `apiRef`.\n */"], "mappings": "AAAA,OAAO,MAAMA,6BAA6B,GAAG;EAC3CC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,KAAK;EACtBC,cAAc,EAAE,GAAG;EACnBC,MAAM,EAAE,KAAK;EACbC,2BAA2B,EAAE;AAC/B,CAAC;;AAED;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}