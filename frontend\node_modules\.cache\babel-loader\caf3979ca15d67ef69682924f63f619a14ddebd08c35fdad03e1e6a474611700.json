{"ast": null, "code": "function compareValues(a, b, order) {\n  if (a < b) {\n    return order === 'asc' ? -1 : 1;\n  }\n  if (a > b) {\n    return order === 'asc' ? 1 : -1;\n  }\n  return 0;\n}\nexport { compareValues };", "map": {"version": 3, "names": ["compareValues", "a", "b", "order"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/_internal/compareValues.mjs"], "sourcesContent": ["function compareValues(a, b, order) {\n    if (a < b) {\n        return order === 'asc' ? -1 : 1;\n    }\n    if (a > b) {\n        return order === 'asc' ? 1 : -1;\n    }\n    return 0;\n}\n\nexport { compareValues };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAE;EAChC,IAAIF,CAAC,GAAGC,CAAC,EAAE;IACP,OAAOC,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;EACnC;EACA,IAAIF,CAAC,GAAGC,CAAC,EAAE;IACP,OAAOC,KAAK,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EACnC;EACA,OAAO,CAAC;AACZ;AAEA,SAASH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}