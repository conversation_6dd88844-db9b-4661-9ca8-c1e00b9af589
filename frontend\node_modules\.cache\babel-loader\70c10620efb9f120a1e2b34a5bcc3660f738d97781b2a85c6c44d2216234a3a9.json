{"ast": null, "code": "function isSet(value) {\n  return value instanceof Set;\n}\nexport { isSet };", "map": {"version": 3, "names": ["isSet", "value", "Set"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isSet.mjs"], "sourcesContent": ["function isSet(value) {\n    return value instanceof Set;\n}\n\nexport { isSet };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,KAAK,EAAE;EAClB,OAAOA,KAAK,YAAYC,GAAG;AAC/B;AAEA,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}