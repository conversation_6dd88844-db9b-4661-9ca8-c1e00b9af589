{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nexport const GridPrivateApiContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") GridPrivateApiContext.displayName = \"GridPrivateApiContext\";\nexport function useGridPrivateApiContext() {\n  const privateApiRef = React.useContext(GridPrivateApiContext);\n  if (privateApiRef === undefined) {\n    throw new Error(['MUI X: Could not find the Data Grid private context.', 'It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.', 'This can also happen if you are bundling multiple versions of the Data Grid.'].join('\\n'));\n  }\n  return privateApiRef;\n}", "map": {"version": 3, "names": ["React", "GridPrivateApiContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName", "useGridPrivateApiContext", "privateApiRef", "useContext", "Error", "join"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridPrivateApiContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nexport const GridPrivateApiContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== \"production\") GridPrivateApiContext.displayName = \"GridPrivateApiContext\";\nexport function useGridPrivateApiContext() {\n  const privateApiRef = React.useContext(GridPrivateApiContext);\n  if (privateApiRef === undefined) {\n    throw new Error(['MUI X: Could not find the Data Grid private context.', 'It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.', 'This can also happen if you are bundling multiple versions of the Data Grid.'].join('\\n'));\n  }\n  return privateApiRef;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,qBAAqB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAChF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAEL,qBAAqB,CAACM,WAAW,GAAG,uBAAuB;AACtG,OAAO,SAASC,wBAAwBA,CAAA,EAAG;EACzC,MAAMC,aAAa,GAAGT,KAAK,CAACU,UAAU,CAACT,qBAAqB,CAAC;EAC7D,IAAIQ,aAAa,KAAKN,SAAS,EAAE;IAC/B,MAAM,IAAIQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,mHAAmH,EAAE,8EAA8E,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3R;EACA,OAAOH,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}