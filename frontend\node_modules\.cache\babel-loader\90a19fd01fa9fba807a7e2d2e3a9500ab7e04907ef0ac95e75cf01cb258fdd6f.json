{"ast": null, "code": "import * as React from 'react';\nimport { fastObjectShallowCompare } from \"../fastObjectShallowCompare/index.js\";\nexport function fastMemo(component) {\n  return /*#__PURE__*/React.memo(component, fastObjectShallowCompare);\n}", "map": {"version": 3, "names": ["React", "fastObjectShallowCompare", "fastMemo", "component", "memo"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/fastMemo/fastMemo.js"], "sourcesContent": ["import * as React from 'react';\nimport { fastObjectShallowCompare } from \"../fastObjectShallowCompare/index.js\";\nexport function fastMemo(component) {\n  return /*#__PURE__*/React.memo(component, fastObjectShallowCompare);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,wBAAwB,QAAQ,sCAAsC;AAC/E,OAAO,SAASC,QAAQA,CAACC,SAAS,EAAE;EAClC,OAAO,aAAaH,KAAK,CAACI,IAAI,CAACD,SAAS,EAAEF,wBAAwB,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}