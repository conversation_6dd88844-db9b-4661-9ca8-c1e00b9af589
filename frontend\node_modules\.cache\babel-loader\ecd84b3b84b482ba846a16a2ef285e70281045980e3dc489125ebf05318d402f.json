{"ast": null, "code": "import * as React from 'react';\nimport { GridConfigurationContext } from \"../../components/GridConfigurationContext.js\";\nexport const useGridConfiguration = () => {\n  const configuration = React.useContext(GridConfigurationContext);\n  if (configuration === undefined) {\n    throw new Error(['MUI X: Could not find the Data Grid configuration context.', 'It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.', 'This can also happen if you are bundling multiple versions of the Data Grid.'].join('\\n'));\n  }\n  return configuration;\n};", "map": {"version": 3, "names": ["React", "GridConfigurationContext", "useGridConfiguration", "configuration", "useContext", "undefined", "Error", "join"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridConfiguration.js"], "sourcesContent": ["import * as React from 'react';\nimport { GridConfigurationContext } from \"../../components/GridConfigurationContext.js\";\nexport const useGridConfiguration = () => {\n  const configuration = React.useContext(GridConfigurationContext);\n  if (configuration === undefined) {\n    throw new Error(['MUI X: Could not find the Data Grid configuration context.', 'It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.', 'This can also happen if you are bundling multiple versions of the Data Grid.'].join('\\n'));\n  }\n  return configuration;\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,wBAAwB,QAAQ,8CAA8C;AACvF,OAAO,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACxC,MAAMC,aAAa,GAAGH,KAAK,CAACI,UAAU,CAACH,wBAAwB,CAAC;EAChE,IAAIE,aAAa,KAAKE,SAAS,EAAE;IAC/B,MAAM,IAAIC,KAAK,CAAC,CAAC,4DAA4D,EAAE,mHAAmH,EAAE,8EAA8E,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACjS;EACA,OAAOJ,aAAa;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}