{"ast": null, "code": "function pad(str, length, chars = ' ') {\n  return str.padStart(Math.floor((length - str.length) / 2) + str.length, chars).padEnd(length, chars);\n}\nexport { pad };", "map": {"version": 3, "names": ["pad", "str", "length", "chars", "padStart", "Math", "floor", "padEnd"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/string/pad.mjs"], "sourcesContent": ["function pad(str, length, chars = ' ') {\n    return str.padStart(Math.floor((length - str.length) / 2) + str.length, chars).padEnd(length, chars);\n}\n\nexport { pad };\n"], "mappings": "AAAA,SAASA,GAAGA,CAACC,GAAG,EAAEC,MAAM,EAAEC,KAAK,GAAG,GAAG,EAAE;EACnC,OAAOF,GAAG,CAACG,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,CAACJ,MAAM,GAAGD,GAAG,CAACC,MAAM,IAAI,CAAC,CAAC,GAAGD,GAAG,CAACC,MAAM,EAAEC,KAAK,CAAC,CAACI,MAAM,CAACL,MAAM,EAAEC,KAAK,CAAC;AACxG;AAEA,SAASH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}