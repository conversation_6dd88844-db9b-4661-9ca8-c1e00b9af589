{"ast": null, "code": "function isNil(x) {\n  return x == null;\n}\nexport { isNil };", "map": {"version": 3, "names": ["isNil", "x"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isNil.mjs"], "sourcesContent": ["function isNil(x) {\n    return x == null;\n}\n\nexport { isNil };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,CAAC,EAAE;EACd,OAAOA,CAAC,IAAI,IAAI;AACpB;AAEA,SAASD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}