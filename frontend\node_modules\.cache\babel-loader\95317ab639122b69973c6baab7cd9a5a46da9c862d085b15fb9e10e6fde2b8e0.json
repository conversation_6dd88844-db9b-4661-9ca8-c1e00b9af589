{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { Rowspan } from '@mui/x-virtualizer/features';\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridRenderContextSelector } from \"../virtualization/gridVirtualizationSelectors.js\";\nimport { getUnprocessedRange, isRowContextInitialized, getCellValue } from \"./gridRowSpanningUtils.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { runIf } from \"../../../utils/utils.js\";\nimport { gridPageSizeSelector } from \"../pagination/index.js\";\nimport { gridDataRowIdsSelector } from \"./gridRowsSelector.js\";\nconst EMPTY_CACHES = {\n  spannedCells: {},\n  hiddenCells: {},\n  hiddenCellOriginMap: {}\n};\nconst EMPTY_RANGE = {\n  firstRowIndex: 0,\n  lastRowIndex: 0\n};\nconst EMPTY_STATE = {\n  caches: EMPTY_CACHES,\n  processedRange: EMPTY_RANGE\n};\n\n/**\n * Default number of rows to process during state initialization to avoid flickering.\n * Number `20` is arbitrarily chosen to be large enough to cover most of the cases without\n * compromising performance.\n */\nconst DEFAULT_ROWS_TO_PROCESS = 20;\nconst computeRowSpanningState = (apiRef, colDefs, visibleRows, range, rangeToProcess, resetState) => {\n  const virtualizer = apiRef.current.virtualizer;\n  const previousState = resetState ? EMPTY_STATE : Rowspan.selectors.state(virtualizer.store.state);\n  const spannedCells = _extends({}, previousState.caches.spannedCells);\n  const hiddenCells = _extends({}, previousState.caches.hiddenCells);\n  const hiddenCellOriginMap = _extends({}, previousState.caches.hiddenCellOriginMap);\n  const processedRange = {\n    firstRowIndex: Math.min(previousState.processedRange.firstRowIndex, rangeToProcess.firstRowIndex),\n    lastRowIndex: Math.max(previousState.processedRange.lastRowIndex, rangeToProcess.lastRowIndex)\n  };\n  colDefs.forEach((colDef, columnIndex) => {\n    for (let index = rangeToProcess.firstRowIndex; index < rangeToProcess.lastRowIndex; index += 1) {\n      const row = visibleRows[index];\n      if (hiddenCells[row.id]?.[columnIndex]) {\n        continue;\n      }\n      const cellValue = getCellValue(row.model, colDef, apiRef);\n      if (cellValue == null) {\n        continue;\n      }\n      let spannedRowId = row.id;\n      let spannedRowIndex = index;\n      let rowSpan = 0;\n\n      // For first index, also scan in the previous rows to handle the reset state case e.g by sorting\n      const backwardsHiddenCells = [];\n      if (index === rangeToProcess.firstRowIndex) {\n        let prevIndex = index - 1;\n        let prevRowEntry = visibleRows[prevIndex];\n        while (prevIndex >= range.firstRowIndex && prevRowEntry && getCellValue(prevRowEntry.model, colDef, apiRef) === cellValue) {\n          const currentRow = visibleRows[prevIndex + 1];\n          if (hiddenCells[currentRow.id]) {\n            hiddenCells[currentRow.id][columnIndex] = true;\n          } else {\n            hiddenCells[currentRow.id] = {\n              [columnIndex]: true\n            };\n          }\n          backwardsHiddenCells.push(index);\n          rowSpan += 1;\n          spannedRowId = prevRowEntry.id;\n          spannedRowIndex = prevIndex;\n          prevIndex -= 1;\n          prevRowEntry = visibleRows[prevIndex];\n        }\n      }\n      backwardsHiddenCells.forEach(hiddenCellIndex => {\n        if (hiddenCellOriginMap[hiddenCellIndex]) {\n          hiddenCellOriginMap[hiddenCellIndex][columnIndex] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[hiddenCellIndex] = {\n            [columnIndex]: spannedRowIndex\n          };\n        }\n      });\n\n      // Scan the next rows\n      let relativeIndex = index + 1;\n      while (relativeIndex <= range.lastRowIndex && visibleRows[relativeIndex] && getCellValue(visibleRows[relativeIndex].model, colDef, apiRef) === cellValue) {\n        const currentRow = visibleRows[relativeIndex];\n        if (hiddenCells[currentRow.id]) {\n          hiddenCells[currentRow.id][columnIndex] = true;\n        } else {\n          hiddenCells[currentRow.id] = {\n            [columnIndex]: true\n          };\n        }\n        if (hiddenCellOriginMap[relativeIndex]) {\n          hiddenCellOriginMap[relativeIndex][columnIndex] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[relativeIndex] = {\n            [columnIndex]: spannedRowIndex\n          };\n        }\n        relativeIndex += 1;\n        rowSpan += 1;\n      }\n      if (rowSpan > 0) {\n        if (spannedCells[spannedRowId]) {\n          spannedCells[spannedRowId][columnIndex] = rowSpan + 1;\n        } else {\n          spannedCells[spannedRowId] = {\n            [columnIndex]: rowSpan + 1\n          };\n        }\n      }\n    }\n  });\n  return {\n    caches: {\n      spannedCells,\n      hiddenCells,\n      hiddenCellOriginMap\n    },\n    processedRange\n  };\n};\nconst getInitialRangeToProcess = (props, apiRef) => {\n  const rowCount = gridDataRowIdsSelector(apiRef).length;\n  if (props.pagination) {\n    const pageSize = gridPageSizeSelector(apiRef);\n    let paginationLastRowIndex = DEFAULT_ROWS_TO_PROCESS;\n    if (pageSize > 0) {\n      paginationLastRowIndex = pageSize - 1;\n    }\n    return {\n      firstRowIndex: 0,\n      lastRowIndex: Math.min(paginationLastRowIndex, rowCount)\n    };\n  }\n  return {\n    firstRowIndex: 0,\n    lastRowIndex: Math.min(DEFAULT_ROWS_TO_PROCESS, rowCount)\n  };\n};\n\n/**\n * @requires columnsStateInitializer (method) - should be initialized before\n * @requires rowsStateInitializer (method) - should be initialized before\n * @requires filterStateInitializer (method) - should be initialized before\n */\nexport const rowSpanningStateInitializer = (state, props, apiRef) => {\n  if (!props.rowSpanning) {\n    return _extends({}, state, {\n      rowSpanning: EMPTY_STATE\n    });\n  }\n  const rowIds = state.rows.dataRowIds || [];\n  const orderedFields = state.columns.orderedFields || [];\n  const dataRowIdToModelLookup = state.rows.dataRowIdToModelLookup;\n  const columnsLookup = state.columns.lookup;\n  const isFilteringPending = Boolean(state.filter.filterModel.items.length) || Boolean(state.filter.filterModel.quickFilterValues?.length);\n  if (!rowIds.length || !orderedFields.length || !dataRowIdToModelLookup || !columnsLookup || isFilteringPending) {\n    return _extends({}, state, {\n      rowSpanning: EMPTY_STATE\n    });\n  }\n  const rangeToProcess = getInitialRangeToProcess(props, apiRef);\n  const rows = rowIds.map(id => ({\n    id,\n    model: dataRowIdToModelLookup[id]\n  }));\n  const colDefs = orderedFields.map(field => columnsLookup[field]);\n  const rowSpanning = computeRowSpanningState(apiRef, colDefs, rows, rangeToProcess, rangeToProcess, true);\n  return _extends({}, state, {\n    rowSpanning\n  });\n};\nexport const useGridRowSpanning = (apiRef, props) => {\n  const store = apiRef.current.virtualizer.store;\n  const updateRowSpanningState = React.useCallback((renderContext, resetState = false) => {\n    const {\n      range,\n      rows: visibleRows\n    } = getVisibleRows(apiRef);\n    if (range === null || !isRowContextInitialized(renderContext)) {\n      return;\n    }\n    const previousState = resetState ? EMPTY_STATE : Rowspan.selectors.state(store.state);\n    const rangeToProcess = getUnprocessedRange({\n      firstRowIndex: renderContext.firstRowIndex,\n      lastRowIndex: Math.min(renderContext.lastRowIndex, range.lastRowIndex - range.firstRowIndex + 1)\n    }, previousState.processedRange);\n    if (rangeToProcess === null) {\n      return;\n    }\n    const colDefs = gridVisibleColumnDefinitionsSelector(apiRef);\n    const newState = computeRowSpanningState(apiRef, colDefs, visibleRows, range, rangeToProcess, resetState);\n    const newSpannedCellsCount = Object.keys(newState.caches.spannedCells).length;\n    const newHiddenCellsCount = Object.keys(newState.caches.hiddenCells).length;\n    const previousSpannedCellsCount = Object.keys(previousState.caches.spannedCells).length;\n    const previousHiddenCellsCount = Object.keys(previousState.caches.hiddenCells).length;\n    const shouldUpdateState = resetState || newSpannedCellsCount !== previousSpannedCellsCount || newHiddenCellsCount !== previousHiddenCellsCount;\n    const hasNoSpannedCells = newSpannedCellsCount === 0 && previousSpannedCellsCount === 0;\n    if (!shouldUpdateState || hasNoSpannedCells) {\n      return;\n    }\n    store.set('rowSpanning', newState);\n  }, [apiRef, store]);\n\n  // Reset events trigger a full re-computation of the row spanning state:\n  // - The `unstable_rowSpanning` prop is updated (feature flag)\n  // - The filtering is applied\n  // - The sorting is applied\n  // - The `paginationModel` is updated\n  // - The rows are updated\n  const resetRowSpanningState = React.useCallback(() => {\n    const renderContext = gridRenderContextSelector(apiRef);\n    if (!isRowContextInitialized(renderContext)) {\n      return;\n    }\n    updateRowSpanningState(renderContext, true);\n  }, [apiRef, updateRowSpanningState]);\n  useGridEvent(apiRef, 'renderedRowsIntervalChange', runIf(props.rowSpanning, updateRowSpanningState));\n  useGridEvent(apiRef, 'sortedRowsSet', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'paginationModelChange', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'filteredRowsSet', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'columnsChange', runIf(props.rowSpanning, resetRowSpanningState));\n  React.useEffect(() => {\n    if (!props.rowSpanning) {\n      if (store.state.rowSpanning !== EMPTY_STATE) {\n        store.set('rowSpanning', EMPTY_STATE);\n      }\n    } else if (store.state.rowSpanning.caches === EMPTY_CACHES) {\n      resetRowSpanningState();\n    }\n  }, [apiRef, store, resetRowSpanningState, props.rowSpanning]);\n};", "map": {"version": 3, "names": ["_extends", "React", "Rowspan", "gridVisibleColumnDefinitionsSelector", "getVisibleRows", "gridRenderContextSelector", "getUnprocessedRange", "isRowContextInitialized", "getCellValue", "useGridEvent", "runIf", "gridPageSizeSelector", "gridDataRowIdsSelector", "EMPTY_CACHES", "<PERSON><PERSON><PERSON><PERSON>", "hidden<PERSON>ells", "hiddenCellOriginMap", "EMPTY_RANGE", "firstRowIndex", "lastRowIndex", "EMPTY_STATE", "caches", "processedRange", "DEFAULT_ROWS_TO_PROCESS", "computeRowSpanningState", "apiRef", "colDefs", "visibleRows", "range", "rangeToProcess", "resetState", "virtualizer", "current", "previousState", "selectors", "state", "store", "Math", "min", "max", "for<PERSON>ach", "colDef", "columnIndex", "index", "row", "id", "cellValue", "model", "spannedRowId", "spannedRowIndex", "rowSpan", "backwards<PERSON><PERSON>denCells", "prevIndex", "prevRowEntry", "currentRow", "push", "hiddenCellIndex", "relativeIndex", "getInitialRangeToProcess", "props", "rowCount", "length", "pagination", "pageSize", "paginationLastRowIndex", "rowSpanningStateInitializer", "rowSpanning", "rowIds", "rows", "dataRowIds", "orderedFields", "columns", "dataRowIdToModelLookup", "columnsLookup", "lookup", "isFilteringPending", "Boolean", "filter", "filterModel", "items", "quickFilterV<PERSON>ues", "map", "field", "useGridRowSpanning", "updateRowSpanningState", "useCallback", "renderContext", "newState", "newSpannedCellsCount", "Object", "keys", "newHiddenCellsCount", "previousSpannedCellsCount", "previousHiddenCellsCount", "shouldUpdateState", "hasNoSpannedCells", "set", "resetRowSpanningState", "useEffect"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rows/useGridRowSpanning.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { Rowspan } from '@mui/x-virtualizer/features';\nimport { gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { gridRenderContextSelector } from \"../virtualization/gridVirtualizationSelectors.js\";\nimport { getUnprocessedRange, isRowContextInitialized, getCellValue } from \"./gridRowSpanningUtils.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { runIf } from \"../../../utils/utils.js\";\nimport { gridPageSizeSelector } from \"../pagination/index.js\";\nimport { gridDataRowIdsSelector } from \"./gridRowsSelector.js\";\nconst EMPTY_CACHES = {\n  spannedCells: {},\n  hiddenCells: {},\n  hiddenCellOriginMap: {}\n};\nconst EMPTY_RANGE = {\n  firstRowIndex: 0,\n  lastRowIndex: 0\n};\nconst EMPTY_STATE = {\n  caches: EMPTY_CACHES,\n  processedRange: EMPTY_RANGE\n};\n\n/**\n * Default number of rows to process during state initialization to avoid flickering.\n * Number `20` is arbitrarily chosen to be large enough to cover most of the cases without\n * compromising performance.\n */\nconst DEFAULT_ROWS_TO_PROCESS = 20;\nconst computeRowSpanningState = (apiRef, colDefs, visibleRows, range, rangeToProcess, resetState) => {\n  const virtualizer = apiRef.current.virtualizer;\n  const previousState = resetState ? EMPTY_STATE : Rowspan.selectors.state(virtualizer.store.state);\n  const spannedCells = _extends({}, previousState.caches.spannedCells);\n  const hiddenCells = _extends({}, previousState.caches.hiddenCells);\n  const hiddenCellOriginMap = _extends({}, previousState.caches.hiddenCellOriginMap);\n  const processedRange = {\n    firstRowIndex: Math.min(previousState.processedRange.firstRowIndex, rangeToProcess.firstRowIndex),\n    lastRowIndex: Math.max(previousState.processedRange.lastRowIndex, rangeToProcess.lastRowIndex)\n  };\n  colDefs.forEach((colDef, columnIndex) => {\n    for (let index = rangeToProcess.firstRowIndex; index < rangeToProcess.lastRowIndex; index += 1) {\n      const row = visibleRows[index];\n      if (hiddenCells[row.id]?.[columnIndex]) {\n        continue;\n      }\n      const cellValue = getCellValue(row.model, colDef, apiRef);\n      if (cellValue == null) {\n        continue;\n      }\n      let spannedRowId = row.id;\n      let spannedRowIndex = index;\n      let rowSpan = 0;\n\n      // For first index, also scan in the previous rows to handle the reset state case e.g by sorting\n      const backwardsHiddenCells = [];\n      if (index === rangeToProcess.firstRowIndex) {\n        let prevIndex = index - 1;\n        let prevRowEntry = visibleRows[prevIndex];\n        while (prevIndex >= range.firstRowIndex && prevRowEntry && getCellValue(prevRowEntry.model, colDef, apiRef) === cellValue) {\n          const currentRow = visibleRows[prevIndex + 1];\n          if (hiddenCells[currentRow.id]) {\n            hiddenCells[currentRow.id][columnIndex] = true;\n          } else {\n            hiddenCells[currentRow.id] = {\n              [columnIndex]: true\n            };\n          }\n          backwardsHiddenCells.push(index);\n          rowSpan += 1;\n          spannedRowId = prevRowEntry.id;\n          spannedRowIndex = prevIndex;\n          prevIndex -= 1;\n          prevRowEntry = visibleRows[prevIndex];\n        }\n      }\n      backwardsHiddenCells.forEach(hiddenCellIndex => {\n        if (hiddenCellOriginMap[hiddenCellIndex]) {\n          hiddenCellOriginMap[hiddenCellIndex][columnIndex] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[hiddenCellIndex] = {\n            [columnIndex]: spannedRowIndex\n          };\n        }\n      });\n\n      // Scan the next rows\n      let relativeIndex = index + 1;\n      while (relativeIndex <= range.lastRowIndex && visibleRows[relativeIndex] && getCellValue(visibleRows[relativeIndex].model, colDef, apiRef) === cellValue) {\n        const currentRow = visibleRows[relativeIndex];\n        if (hiddenCells[currentRow.id]) {\n          hiddenCells[currentRow.id][columnIndex] = true;\n        } else {\n          hiddenCells[currentRow.id] = {\n            [columnIndex]: true\n          };\n        }\n        if (hiddenCellOriginMap[relativeIndex]) {\n          hiddenCellOriginMap[relativeIndex][columnIndex] = spannedRowIndex;\n        } else {\n          hiddenCellOriginMap[relativeIndex] = {\n            [columnIndex]: spannedRowIndex\n          };\n        }\n        relativeIndex += 1;\n        rowSpan += 1;\n      }\n      if (rowSpan > 0) {\n        if (spannedCells[spannedRowId]) {\n          spannedCells[spannedRowId][columnIndex] = rowSpan + 1;\n        } else {\n          spannedCells[spannedRowId] = {\n            [columnIndex]: rowSpan + 1\n          };\n        }\n      }\n    }\n  });\n  return {\n    caches: {\n      spannedCells,\n      hiddenCells,\n      hiddenCellOriginMap\n    },\n    processedRange\n  };\n};\nconst getInitialRangeToProcess = (props, apiRef) => {\n  const rowCount = gridDataRowIdsSelector(apiRef).length;\n  if (props.pagination) {\n    const pageSize = gridPageSizeSelector(apiRef);\n    let paginationLastRowIndex = DEFAULT_ROWS_TO_PROCESS;\n    if (pageSize > 0) {\n      paginationLastRowIndex = pageSize - 1;\n    }\n    return {\n      firstRowIndex: 0,\n      lastRowIndex: Math.min(paginationLastRowIndex, rowCount)\n    };\n  }\n  return {\n    firstRowIndex: 0,\n    lastRowIndex: Math.min(DEFAULT_ROWS_TO_PROCESS, rowCount)\n  };\n};\n\n/**\n * @requires columnsStateInitializer (method) - should be initialized before\n * @requires rowsStateInitializer (method) - should be initialized before\n * @requires filterStateInitializer (method) - should be initialized before\n */\nexport const rowSpanningStateInitializer = (state, props, apiRef) => {\n  if (!props.rowSpanning) {\n    return _extends({}, state, {\n      rowSpanning: EMPTY_STATE\n    });\n  }\n  const rowIds = state.rows.dataRowIds || [];\n  const orderedFields = state.columns.orderedFields || [];\n  const dataRowIdToModelLookup = state.rows.dataRowIdToModelLookup;\n  const columnsLookup = state.columns.lookup;\n  const isFilteringPending = Boolean(state.filter.filterModel.items.length) || Boolean(state.filter.filterModel.quickFilterValues?.length);\n  if (!rowIds.length || !orderedFields.length || !dataRowIdToModelLookup || !columnsLookup || isFilteringPending) {\n    return _extends({}, state, {\n      rowSpanning: EMPTY_STATE\n    });\n  }\n  const rangeToProcess = getInitialRangeToProcess(props, apiRef);\n  const rows = rowIds.map(id => ({\n    id,\n    model: dataRowIdToModelLookup[id]\n  }));\n  const colDefs = orderedFields.map(field => columnsLookup[field]);\n  const rowSpanning = computeRowSpanningState(apiRef, colDefs, rows, rangeToProcess, rangeToProcess, true);\n  return _extends({}, state, {\n    rowSpanning\n  });\n};\nexport const useGridRowSpanning = (apiRef, props) => {\n  const store = apiRef.current.virtualizer.store;\n  const updateRowSpanningState = React.useCallback((renderContext, resetState = false) => {\n    const {\n      range,\n      rows: visibleRows\n    } = getVisibleRows(apiRef);\n    if (range === null || !isRowContextInitialized(renderContext)) {\n      return;\n    }\n    const previousState = resetState ? EMPTY_STATE : Rowspan.selectors.state(store.state);\n    const rangeToProcess = getUnprocessedRange({\n      firstRowIndex: renderContext.firstRowIndex,\n      lastRowIndex: Math.min(renderContext.lastRowIndex, range.lastRowIndex - range.firstRowIndex + 1)\n    }, previousState.processedRange);\n    if (rangeToProcess === null) {\n      return;\n    }\n    const colDefs = gridVisibleColumnDefinitionsSelector(apiRef);\n    const newState = computeRowSpanningState(apiRef, colDefs, visibleRows, range, rangeToProcess, resetState);\n    const newSpannedCellsCount = Object.keys(newState.caches.spannedCells).length;\n    const newHiddenCellsCount = Object.keys(newState.caches.hiddenCells).length;\n    const previousSpannedCellsCount = Object.keys(previousState.caches.spannedCells).length;\n    const previousHiddenCellsCount = Object.keys(previousState.caches.hiddenCells).length;\n    const shouldUpdateState = resetState || newSpannedCellsCount !== previousSpannedCellsCount || newHiddenCellsCount !== previousHiddenCellsCount;\n    const hasNoSpannedCells = newSpannedCellsCount === 0 && previousSpannedCellsCount === 0;\n    if (!shouldUpdateState || hasNoSpannedCells) {\n      return;\n    }\n    store.set('rowSpanning', newState);\n  }, [apiRef, store]);\n\n  // Reset events trigger a full re-computation of the row spanning state:\n  // - The `unstable_rowSpanning` prop is updated (feature flag)\n  // - The filtering is applied\n  // - The sorting is applied\n  // - The `paginationModel` is updated\n  // - The rows are updated\n  const resetRowSpanningState = React.useCallback(() => {\n    const renderContext = gridRenderContextSelector(apiRef);\n    if (!isRowContextInitialized(renderContext)) {\n      return;\n    }\n    updateRowSpanningState(renderContext, true);\n  }, [apiRef, updateRowSpanningState]);\n  useGridEvent(apiRef, 'renderedRowsIntervalChange', runIf(props.rowSpanning, updateRowSpanningState));\n  useGridEvent(apiRef, 'sortedRowsSet', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'paginationModelChange', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'filteredRowsSet', runIf(props.rowSpanning, resetRowSpanningState));\n  useGridEvent(apiRef, 'columnsChange', runIf(props.rowSpanning, resetRowSpanningState));\n  React.useEffect(() => {\n    if (!props.rowSpanning) {\n      if (store.state.rowSpanning !== EMPTY_STATE) {\n        store.set('rowSpanning', EMPTY_STATE);\n      }\n    } else if (store.state.rowSpanning.caches === EMPTY_CACHES) {\n      resetRowSpanningState();\n    }\n  }, [apiRef, store, resetRowSpanningState, props.rowSpanning]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,oCAAoC,QAAQ,mCAAmC;AACxF,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,mBAAmB,EAAEC,uBAAuB,EAAEC,YAAY,QAAQ,2BAA2B;AACtG,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,MAAMC,YAAY,GAAG;EACnBC,YAAY,EAAE,CAAC,CAAC;EAChBC,WAAW,EAAE,CAAC,CAAC;EACfC,mBAAmB,EAAE,CAAC;AACxB,CAAC;AACD,MAAMC,WAAW,GAAG;EAClBC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE;AAChB,CAAC;AACD,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAER,YAAY;EACpBS,cAAc,EAAEL;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMM,uBAAuB,GAAG,EAAE;AAClC,MAAMC,uBAAuB,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,UAAU,KAAK;EACnG,MAAMC,WAAW,GAAGN,MAAM,CAACO,OAAO,CAACD,WAAW;EAC9C,MAAME,aAAa,GAAGH,UAAU,GAAGV,WAAW,GAAGlB,OAAO,CAACgC,SAAS,CAACC,KAAK,CAACJ,WAAW,CAACK,KAAK,CAACD,KAAK,CAAC;EACjG,MAAMrB,YAAY,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEiC,aAAa,CAACZ,MAAM,CAACP,YAAY,CAAC;EACpE,MAAMC,WAAW,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEiC,aAAa,CAACZ,MAAM,CAACN,WAAW,CAAC;EAClE,MAAMC,mBAAmB,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEiC,aAAa,CAACZ,MAAM,CAACL,mBAAmB,CAAC;EAClF,MAAMM,cAAc,GAAG;IACrBJ,aAAa,EAAEmB,IAAI,CAACC,GAAG,CAACL,aAAa,CAACX,cAAc,CAACJ,aAAa,EAAEW,cAAc,CAACX,aAAa,CAAC;IACjGC,YAAY,EAAEkB,IAAI,CAACE,GAAG,CAACN,aAAa,CAACX,cAAc,CAACH,YAAY,EAAEU,cAAc,CAACV,YAAY;EAC/F,CAAC;EACDO,OAAO,CAACc,OAAO,CAAC,CAACC,MAAM,EAAEC,WAAW,KAAK;IACvC,KAAK,IAAIC,KAAK,GAAGd,cAAc,CAACX,aAAa,EAAEyB,KAAK,GAAGd,cAAc,CAACV,YAAY,EAAEwB,KAAK,IAAI,CAAC,EAAE;MAC9F,MAAMC,GAAG,GAAGjB,WAAW,CAACgB,KAAK,CAAC;MAC9B,IAAI5B,WAAW,CAAC6B,GAAG,CAACC,EAAE,CAAC,GAAGH,WAAW,CAAC,EAAE;QACtC;MACF;MACA,MAAMI,SAAS,GAAGtC,YAAY,CAACoC,GAAG,CAACG,KAAK,EAAEN,MAAM,EAAEhB,MAAM,CAAC;MACzD,IAAIqB,SAAS,IAAI,IAAI,EAAE;QACrB;MACF;MACA,IAAIE,YAAY,GAAGJ,GAAG,CAACC,EAAE;MACzB,IAAII,eAAe,GAAGN,KAAK;MAC3B,IAAIO,OAAO,GAAG,CAAC;;MAEf;MACA,MAAMC,oBAAoB,GAAG,EAAE;MAC/B,IAAIR,KAAK,KAAKd,cAAc,CAACX,aAAa,EAAE;QAC1C,IAAIkC,SAAS,GAAGT,KAAK,GAAG,CAAC;QACzB,IAAIU,YAAY,GAAG1B,WAAW,CAACyB,SAAS,CAAC;QACzC,OAAOA,SAAS,IAAIxB,KAAK,CAACV,aAAa,IAAImC,YAAY,IAAI7C,YAAY,CAAC6C,YAAY,CAACN,KAAK,EAAEN,MAAM,EAAEhB,MAAM,CAAC,KAAKqB,SAAS,EAAE;UACzH,MAAMQ,UAAU,GAAG3B,WAAW,CAACyB,SAAS,GAAG,CAAC,CAAC;UAC7C,IAAIrC,WAAW,CAACuC,UAAU,CAACT,EAAE,CAAC,EAAE;YAC9B9B,WAAW,CAACuC,UAAU,CAACT,EAAE,CAAC,CAACH,WAAW,CAAC,GAAG,IAAI;UAChD,CAAC,MAAM;YACL3B,WAAW,CAACuC,UAAU,CAACT,EAAE,CAAC,GAAG;cAC3B,CAACH,WAAW,GAAG;YACjB,CAAC;UACH;UACAS,oBAAoB,CAACI,IAAI,CAACZ,KAAK,CAAC;UAChCO,OAAO,IAAI,CAAC;UACZF,YAAY,GAAGK,YAAY,CAACR,EAAE;UAC9BI,eAAe,GAAGG,SAAS;UAC3BA,SAAS,IAAI,CAAC;UACdC,YAAY,GAAG1B,WAAW,CAACyB,SAAS,CAAC;QACvC;MACF;MACAD,oBAAoB,CAACX,OAAO,CAACgB,eAAe,IAAI;QAC9C,IAAIxC,mBAAmB,CAACwC,eAAe,CAAC,EAAE;UACxCxC,mBAAmB,CAACwC,eAAe,CAAC,CAACd,WAAW,CAAC,GAAGO,eAAe;QACrE,CAAC,MAAM;UACLjC,mBAAmB,CAACwC,eAAe,CAAC,GAAG;YACrC,CAACd,WAAW,GAAGO;UACjB,CAAC;QACH;MACF,CAAC,CAAC;;MAEF;MACA,IAAIQ,aAAa,GAAGd,KAAK,GAAG,CAAC;MAC7B,OAAOc,aAAa,IAAI7B,KAAK,CAACT,YAAY,IAAIQ,WAAW,CAAC8B,aAAa,CAAC,IAAIjD,YAAY,CAACmB,WAAW,CAAC8B,aAAa,CAAC,CAACV,KAAK,EAAEN,MAAM,EAAEhB,MAAM,CAAC,KAAKqB,SAAS,EAAE;QACxJ,MAAMQ,UAAU,GAAG3B,WAAW,CAAC8B,aAAa,CAAC;QAC7C,IAAI1C,WAAW,CAACuC,UAAU,CAACT,EAAE,CAAC,EAAE;UAC9B9B,WAAW,CAACuC,UAAU,CAACT,EAAE,CAAC,CAACH,WAAW,CAAC,GAAG,IAAI;QAChD,CAAC,MAAM;UACL3B,WAAW,CAACuC,UAAU,CAACT,EAAE,CAAC,GAAG;YAC3B,CAACH,WAAW,GAAG;UACjB,CAAC;QACH;QACA,IAAI1B,mBAAmB,CAACyC,aAAa,CAAC,EAAE;UACtCzC,mBAAmB,CAACyC,aAAa,CAAC,CAACf,WAAW,CAAC,GAAGO,eAAe;QACnE,CAAC,MAAM;UACLjC,mBAAmB,CAACyC,aAAa,CAAC,GAAG;YACnC,CAACf,WAAW,GAAGO;UACjB,CAAC;QACH;QACAQ,aAAa,IAAI,CAAC;QAClBP,OAAO,IAAI,CAAC;MACd;MACA,IAAIA,OAAO,GAAG,CAAC,EAAE;QACf,IAAIpC,YAAY,CAACkC,YAAY,CAAC,EAAE;UAC9BlC,YAAY,CAACkC,YAAY,CAAC,CAACN,WAAW,CAAC,GAAGQ,OAAO,GAAG,CAAC;QACvD,CAAC,MAAM;UACLpC,YAAY,CAACkC,YAAY,CAAC,GAAG;YAC3B,CAACN,WAAW,GAAGQ,OAAO,GAAG;UAC3B,CAAC;QACH;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACL7B,MAAM,EAAE;MACNP,YAAY;MACZC,WAAW;MACXC;IACF,CAAC;IACDM;EACF,CAAC;AACH,CAAC;AACD,MAAMoC,wBAAwB,GAAGA,CAACC,KAAK,EAAElC,MAAM,KAAK;EAClD,MAAMmC,QAAQ,GAAGhD,sBAAsB,CAACa,MAAM,CAAC,CAACoC,MAAM;EACtD,IAAIF,KAAK,CAACG,UAAU,EAAE;IACpB,MAAMC,QAAQ,GAAGpD,oBAAoB,CAACc,MAAM,CAAC;IAC7C,IAAIuC,sBAAsB,GAAGzC,uBAAuB;IACpD,IAAIwC,QAAQ,GAAG,CAAC,EAAE;MAChBC,sBAAsB,GAAGD,QAAQ,GAAG,CAAC;IACvC;IACA,OAAO;MACL7C,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAEkB,IAAI,CAACC,GAAG,CAAC0B,sBAAsB,EAAEJ,QAAQ;IACzD,CAAC;EACH;EACA,OAAO;IACL1C,aAAa,EAAE,CAAC;IAChBC,YAAY,EAAEkB,IAAI,CAACC,GAAG,CAACf,uBAAuB,EAAEqC,QAAQ;EAC1D,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,2BAA2B,GAAGA,CAAC9B,KAAK,EAAEwB,KAAK,EAAElC,MAAM,KAAK;EACnE,IAAI,CAACkC,KAAK,CAACO,WAAW,EAAE;IACtB,OAAOlE,QAAQ,CAAC,CAAC,CAAC,EAAEmC,KAAK,EAAE;MACzB+B,WAAW,EAAE9C;IACf,CAAC,CAAC;EACJ;EACA,MAAM+C,MAAM,GAAGhC,KAAK,CAACiC,IAAI,CAACC,UAAU,IAAI,EAAE;EAC1C,MAAMC,aAAa,GAAGnC,KAAK,CAACoC,OAAO,CAACD,aAAa,IAAI,EAAE;EACvD,MAAME,sBAAsB,GAAGrC,KAAK,CAACiC,IAAI,CAACI,sBAAsB;EAChE,MAAMC,aAAa,GAAGtC,KAAK,CAACoC,OAAO,CAACG,MAAM;EAC1C,MAAMC,kBAAkB,GAAGC,OAAO,CAACzC,KAAK,CAAC0C,MAAM,CAACC,WAAW,CAACC,KAAK,CAAClB,MAAM,CAAC,IAAIe,OAAO,CAACzC,KAAK,CAAC0C,MAAM,CAACC,WAAW,CAACE,iBAAiB,EAAEnB,MAAM,CAAC;EACxI,IAAI,CAACM,MAAM,CAACN,MAAM,IAAI,CAACS,aAAa,CAACT,MAAM,IAAI,CAACW,sBAAsB,IAAI,CAACC,aAAa,IAAIE,kBAAkB,EAAE;IAC9G,OAAO3E,QAAQ,CAAC,CAAC,CAAC,EAAEmC,KAAK,EAAE;MACzB+B,WAAW,EAAE9C;IACf,CAAC,CAAC;EACJ;EACA,MAAMS,cAAc,GAAG6B,wBAAwB,CAACC,KAAK,EAAElC,MAAM,CAAC;EAC9D,MAAM2C,IAAI,GAAGD,MAAM,CAACc,GAAG,CAACpC,EAAE,KAAK;IAC7BA,EAAE;IACFE,KAAK,EAAEyB,sBAAsB,CAAC3B,EAAE;EAClC,CAAC,CAAC,CAAC;EACH,MAAMnB,OAAO,GAAG4C,aAAa,CAACW,GAAG,CAACC,KAAK,IAAIT,aAAa,CAACS,KAAK,CAAC,CAAC;EAChE,MAAMhB,WAAW,GAAG1C,uBAAuB,CAACC,MAAM,EAAEC,OAAO,EAAE0C,IAAI,EAAEvC,cAAc,EAAEA,cAAc,EAAE,IAAI,CAAC;EACxG,OAAO7B,QAAQ,CAAC,CAAC,CAAC,EAAEmC,KAAK,EAAE;IACzB+B;EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMiB,kBAAkB,GAAGA,CAAC1D,MAAM,EAAEkC,KAAK,KAAK;EACnD,MAAMvB,KAAK,GAAGX,MAAM,CAACO,OAAO,CAACD,WAAW,CAACK,KAAK;EAC9C,MAAMgD,sBAAsB,GAAGnF,KAAK,CAACoF,WAAW,CAAC,CAACC,aAAa,EAAExD,UAAU,GAAG,KAAK,KAAK;IACtF,MAAM;MACJF,KAAK;MACLwC,IAAI,EAAEzC;IACR,CAAC,GAAGvB,cAAc,CAACqB,MAAM,CAAC;IAC1B,IAAIG,KAAK,KAAK,IAAI,IAAI,CAACrB,uBAAuB,CAAC+E,aAAa,CAAC,EAAE;MAC7D;IACF;IACA,MAAMrD,aAAa,GAAGH,UAAU,GAAGV,WAAW,GAAGlB,OAAO,CAACgC,SAAS,CAACC,KAAK,CAACC,KAAK,CAACD,KAAK,CAAC;IACrF,MAAMN,cAAc,GAAGvB,mBAAmB,CAAC;MACzCY,aAAa,EAAEoE,aAAa,CAACpE,aAAa;MAC1CC,YAAY,EAAEkB,IAAI,CAACC,GAAG,CAACgD,aAAa,CAACnE,YAAY,EAAES,KAAK,CAACT,YAAY,GAAGS,KAAK,CAACV,aAAa,GAAG,CAAC;IACjG,CAAC,EAAEe,aAAa,CAACX,cAAc,CAAC;IAChC,IAAIO,cAAc,KAAK,IAAI,EAAE;MAC3B;IACF;IACA,MAAMH,OAAO,GAAGvB,oCAAoC,CAACsB,MAAM,CAAC;IAC5D,MAAM8D,QAAQ,GAAG/D,uBAAuB,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,UAAU,CAAC;IACzG,MAAM0D,oBAAoB,GAAGC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAClE,MAAM,CAACP,YAAY,CAAC,CAAC+C,MAAM;IAC7E,MAAM8B,mBAAmB,GAAGF,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAClE,MAAM,CAACN,WAAW,CAAC,CAAC8C,MAAM;IAC3E,MAAM+B,yBAAyB,GAAGH,MAAM,CAACC,IAAI,CAACzD,aAAa,CAACZ,MAAM,CAACP,YAAY,CAAC,CAAC+C,MAAM;IACvF,MAAMgC,wBAAwB,GAAGJ,MAAM,CAACC,IAAI,CAACzD,aAAa,CAACZ,MAAM,CAACN,WAAW,CAAC,CAAC8C,MAAM;IACrF,MAAMiC,iBAAiB,GAAGhE,UAAU,IAAI0D,oBAAoB,KAAKI,yBAAyB,IAAID,mBAAmB,KAAKE,wBAAwB;IAC9I,MAAME,iBAAiB,GAAGP,oBAAoB,KAAK,CAAC,IAAII,yBAAyB,KAAK,CAAC;IACvF,IAAI,CAACE,iBAAiB,IAAIC,iBAAiB,EAAE;MAC3C;IACF;IACA3D,KAAK,CAAC4D,GAAG,CAAC,aAAa,EAAET,QAAQ,CAAC;EACpC,CAAC,EAAE,CAAC9D,MAAM,EAAEW,KAAK,CAAC,CAAC;;EAEnB;EACA;EACA;EACA;EACA;EACA;EACA,MAAM6D,qBAAqB,GAAGhG,KAAK,CAACoF,WAAW,CAAC,MAAM;IACpD,MAAMC,aAAa,GAAGjF,yBAAyB,CAACoB,MAAM,CAAC;IACvD,IAAI,CAAClB,uBAAuB,CAAC+E,aAAa,CAAC,EAAE;MAC3C;IACF;IACAF,sBAAsB,CAACE,aAAa,EAAE,IAAI,CAAC;EAC7C,CAAC,EAAE,CAAC7D,MAAM,EAAE2D,sBAAsB,CAAC,CAAC;EACpC3E,YAAY,CAACgB,MAAM,EAAE,4BAA4B,EAAEf,KAAK,CAACiD,KAAK,CAACO,WAAW,EAAEkB,sBAAsB,CAAC,CAAC;EACpG3E,YAAY,CAACgB,MAAM,EAAE,eAAe,EAAEf,KAAK,CAACiD,KAAK,CAACO,WAAW,EAAE+B,qBAAqB,CAAC,CAAC;EACtFxF,YAAY,CAACgB,MAAM,EAAE,uBAAuB,EAAEf,KAAK,CAACiD,KAAK,CAACO,WAAW,EAAE+B,qBAAqB,CAAC,CAAC;EAC9FxF,YAAY,CAACgB,MAAM,EAAE,iBAAiB,EAAEf,KAAK,CAACiD,KAAK,CAACO,WAAW,EAAE+B,qBAAqB,CAAC,CAAC;EACxFxF,YAAY,CAACgB,MAAM,EAAE,eAAe,EAAEf,KAAK,CAACiD,KAAK,CAACO,WAAW,EAAE+B,qBAAqB,CAAC,CAAC;EACtFhG,KAAK,CAACiG,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvC,KAAK,CAACO,WAAW,EAAE;MACtB,IAAI9B,KAAK,CAACD,KAAK,CAAC+B,WAAW,KAAK9C,WAAW,EAAE;QAC3CgB,KAAK,CAAC4D,GAAG,CAAC,aAAa,EAAE5E,WAAW,CAAC;MACvC;IACF,CAAC,MAAM,IAAIgB,KAAK,CAACD,KAAK,CAAC+B,WAAW,CAAC7C,MAAM,KAAKR,YAAY,EAAE;MAC1DoF,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACxE,MAAM,EAAEW,KAAK,EAAE6D,qBAAqB,EAAEtC,KAAK,CAACO,WAAW,CAAC,CAAC;AAC/D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}