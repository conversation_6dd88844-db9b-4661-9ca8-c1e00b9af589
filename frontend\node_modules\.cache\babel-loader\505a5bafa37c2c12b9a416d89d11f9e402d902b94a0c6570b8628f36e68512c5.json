{"ast": null, "code": "function differenceBy(firstArr, secondArr, mapper) {\n  const mappedSecondSet = new Set(secondArr.map(item => mapper(item)));\n  return firstArr.filter(item => {\n    return !mappedSecondSet.has(mapper(item));\n  });\n}\nexport { differenceBy };", "map": {"version": 3, "names": ["differenceBy", "firstArr", "secondArr", "mapper", "mappedSecondSet", "Set", "map", "item", "filter", "has"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/differenceBy.mjs"], "sourcesContent": ["function differenceBy(firstArr, secondArr, mapper) {\n    const mappedSecondSet = new Set(secondArr.map(item => mapper(item)));\n    return firstArr.filter(item => {\n        return !mappedSecondSet.has(mapper(item));\n    });\n}\n\nexport { differenceBy };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAE;EAC/C,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAACH,SAAS,CAACI,GAAG,CAACC,IAAI,IAAIJ,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC;EACpE,OAAON,QAAQ,CAACO,MAAM,CAACD,IAAI,IAAI;IAC3B,OAAO,CAACH,eAAe,CAACK,GAAG,CAACN,MAAM,CAACI,IAAI,CAAC,CAAC;EAC7C,CAAC,CAAC;AACN;AAEA,SAASP,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}