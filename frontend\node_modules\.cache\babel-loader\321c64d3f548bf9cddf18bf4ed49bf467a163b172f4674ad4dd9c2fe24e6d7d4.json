{"ast": null, "code": "import { createSelector, createRootSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\n/**\n * @category ColumnGrouping\n * @ignore - do not document.\n */\nexport const gridColumnGroupingSelector = createRootSelector(state => state.columnGrouping);\nexport const gridColumnGroupsUnwrappedModelSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.unwrappedGroupingModel ?? {});\nexport const gridColumnGroupsLookupSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.lookup ?? {});\nexport const gridColumnGroupsHeaderStructureSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.headerStructure ?? []);\nexport const gridColumnGroupsHeaderMaxDepthSelector = createSelector(gridColumnGroupingSelector, columnGrouping => columnGrouping?.maxDepth ?? 0);", "map": {"version": 3, "names": ["createSelector", "createRootSelector", "createSelectorMemoized", "gridColumnGroupingSelector", "state", "columnGrouping", "gridColumnGroupsUnwrappedModelSelector", "unwrappedGroupingModel", "gridColumnGroupsLookupSelector", "lookup", "gridColumnGroupsHeaderStructureSelector", "headerStructure", "gridColumnGroupsHeaderMaxDepthSelector", "max<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columnGrouping/gridColumnGroupsSelector.js"], "sourcesContent": ["import { createSelector, createRootSelector, createSelectorMemoized } from \"../../../utils/createSelector.js\";\n/**\n * @category ColumnGrouping\n * @ignore - do not document.\n */\nexport const gridColumnGroupingSelector = createRootSelector(state => state.columnGrouping);\nexport const gridColumnGroupsUnwrappedModelSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.unwrappedGroupingModel ?? {});\nexport const gridColumnGroupsLookupSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.lookup ?? {});\nexport const gridColumnGroupsHeaderStructureSelector = createSelectorMemoized(gridColumnGroupingSelector, columnGrouping => columnGrouping?.headerStructure ?? []);\nexport const gridColumnGroupsHeaderMaxDepthSelector = createSelector(gridColumnGroupingSelector, columnGrouping => columnGrouping?.maxDepth ?? 0);"], "mappings": "AAAA,SAASA,cAAc,EAAEC,kBAAkB,EAAEC,sBAAsB,QAAQ,kCAAkC;AAC7G;AACA;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,GAAGF,kBAAkB,CAACG,KAAK,IAAIA,KAAK,CAACC,cAAc,CAAC;AAC3F,OAAO,MAAMC,sCAAsC,GAAGJ,sBAAsB,CAACC,0BAA0B,EAAEE,cAAc,IAAIA,cAAc,EAAEE,sBAAsB,IAAI,CAAC,CAAC,CAAC;AACxK,OAAO,MAAMC,8BAA8B,GAAGN,sBAAsB,CAACC,0BAA0B,EAAEE,cAAc,IAAIA,cAAc,EAAEI,MAAM,IAAI,CAAC,CAAC,CAAC;AAChJ,OAAO,MAAMC,uCAAuC,GAAGR,sBAAsB,CAACC,0BAA0B,EAAEE,cAAc,IAAIA,cAAc,EAAEM,eAAe,IAAI,EAAE,CAAC;AAClK,OAAO,MAAMC,sCAAsC,GAAGZ,cAAc,CAACG,0BAA0B,EAAEE,cAAc,IAAIA,cAAc,EAAEQ,QAAQ,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}