{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"open\", \"target\", \"onClose\", \"children\", \"position\", \"className\", \"onExited\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { styled } from '@mui/material/styles';\nimport { isHideMenuKey } from \"../../utils/keyboardUtils.js\";\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { useCSSVariablesClass } from \"../../utils/css/context.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['menu']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridMenuRoot = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'Menu'\n})({\n  zIndex: vars.zIndex.menu,\n  [`& .${gridClasses.menuList}`]: {\n    outline: 0\n  }\n});\nfunction GridMenu(props) {\n  const {\n      open,\n      target,\n      onClose,\n      children,\n      position,\n      className,\n      onExited\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const variablesClass = useCSSVariablesClass();\n  const savedFocusRef = React.useRef(null);\n  useEnhancedEffect(() => {\n    if (open) {\n      savedFocusRef.current = document.activeElement instanceof HTMLElement ? document.activeElement : null;\n    } else {\n      savedFocusRef.current?.focus?.();\n      savedFocusRef.current = null;\n    }\n  }, [open]);\n  React.useEffect(() => {\n    // Emit menuOpen or menuClose events\n    const eventName = open ? 'menuOpen' : 'menuClose';\n    apiRef.current.publishEvent(eventName, {\n      target\n    });\n  }, [apiRef, open, target]);\n  const handleClickAway = event => {\n    if (event.target && (target === event.target || target?.contains(event.target))) {\n      return;\n    }\n    onClose(event);\n  };\n  const handleKeyDown = event => {\n    if (isHideMenuKey(event.key)) {\n      onClose(event);\n    }\n  };\n  return /*#__PURE__*/_jsx(GridMenuRoot, _extends({\n    as: rootProps.slots.basePopper,\n    className: clsx(classes.root, className, variablesClass),\n    ownerState: rootProps,\n    open: open,\n    target: target,\n    transition: true,\n    placement: position,\n    onClickAway: handleClickAway,\n    onExited: onExited,\n    clickAwayMouseEvent: \"onMouseDown\",\n    onKeyDown: handleKeyDown\n  }, other, rootProps.slotProps?.basePopper, {\n    children: children\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  className: PropTypes.string,\n  onClose: PropTypes.func.isRequired,\n  onExited: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  position: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  target: HTMLElementType\n} : void 0;\nexport { GridMenu };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "useEnhancedEffect", "HTMLElementType", "styled", "isHideMenuKey", "vars", "useCSSVariablesClass", "getDataGridUtilityClass", "gridClasses", "useGridRootProps", "useGridApiContext", "NotRendered", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "GridMenuRoot", "name", "slot", "zIndex", "menu", "menuList", "outline", "GridMenu", "props", "open", "target", "onClose", "children", "position", "className", "onExited", "other", "apiRef", "rootProps", "variablesClass", "savedFocusRef", "useRef", "current", "document", "activeElement", "HTMLElement", "focus", "useEffect", "eventName", "publishEvent", "handleClickAway", "event", "contains", "handleKeyDown", "key", "as", "basePopper", "transition", "placement", "onClickAway", "clickAwayMouseEvent", "onKeyDown", "slotProps", "process", "env", "NODE_ENV", "propTypes", "node", "string", "func", "isRequired", "bool", "oneOf"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/menu/GridMenu.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"open\", \"target\", \"onClose\", \"children\", \"position\", \"className\", \"onExited\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { styled } from '@mui/material/styles';\nimport { isHideMenuKey } from \"../../utils/keyboardUtils.js\";\nimport { vars } from \"../../constants/cssVariables.js\";\nimport { useCSSVariablesClass } from \"../../utils/css/context.js\";\nimport { getDataGridUtilityClass, gridClasses } from \"../../constants/gridClasses.js\";\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { useGridApiContext } from \"../../hooks/utils/useGridApiContext.js\";\nimport { NotRendered } from \"../../utils/assert.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['menu']\n  };\n  return composeClasses(slots, getDataGridUtilityClass, classes);\n};\nconst GridMenuRoot = styled(NotRendered, {\n  name: 'MuiDataGrid',\n  slot: 'Menu'\n})({\n  zIndex: vars.zIndex.menu,\n  [`& .${gridClasses.menuList}`]: {\n    outline: 0\n  }\n});\nfunction GridMenu(props) {\n  const {\n      open,\n      target,\n      onClose,\n      children,\n      position,\n      className,\n      onExited\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const classes = useUtilityClasses(rootProps);\n  const variablesClass = useCSSVariablesClass();\n  const savedFocusRef = React.useRef(null);\n  useEnhancedEffect(() => {\n    if (open) {\n      savedFocusRef.current = document.activeElement instanceof HTMLElement ? document.activeElement : null;\n    } else {\n      savedFocusRef.current?.focus?.();\n      savedFocusRef.current = null;\n    }\n  }, [open]);\n  React.useEffect(() => {\n    // Emit menuOpen or menuClose events\n    const eventName = open ? 'menuOpen' : 'menuClose';\n    apiRef.current.publishEvent(eventName, {\n      target\n    });\n  }, [apiRef, open, target]);\n  const handleClickAway = event => {\n    if (event.target && (target === event.target || target?.contains(event.target))) {\n      return;\n    }\n    onClose(event);\n  };\n  const handleKeyDown = event => {\n    if (isHideMenuKey(event.key)) {\n      onClose(event);\n    }\n  };\n  return /*#__PURE__*/_jsx(GridMenuRoot, _extends({\n    as: rootProps.slots.basePopper,\n    className: clsx(classes.root, className, variablesClass),\n    ownerState: rootProps,\n    open: open,\n    target: target,\n    transition: true,\n    placement: position,\n    onClickAway: handleClickAway,\n    onExited: onExited,\n    clickAwayMouseEvent: \"onMouseDown\",\n    onKeyDown: handleKeyDown\n  }, other, rootProps.slotProps?.basePopper, {\n    children: children\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GridMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  children: PropTypes.node,\n  className: PropTypes.string,\n  onClose: PropTypes.func.isRequired,\n  onExited: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  position: PropTypes.oneOf(['bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  target: HTMLElementType\n} : void 0;\nexport { GridMenu };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;AAChG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,eAAe,MAAM,4BAA4B;AACxD,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,IAAI,QAAQ,iCAAiC;AACtD,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,uBAAuB,EAAEC,WAAW,QAAQ,gCAAgC;AACrF,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOlB,cAAc,CAACiB,KAAK,EAAEV,uBAAuB,EAAES,OAAO,CAAC;AAChE,CAAC;AACD,MAAMG,YAAY,GAAGhB,MAAM,CAACQ,WAAW,EAAE;EACvCS,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,MAAM,EAAEjB,IAAI,CAACiB,MAAM,CAACC,IAAI;EACxB,CAAC,MAAMf,WAAW,CAACgB,QAAQ,EAAE,GAAG;IAC9BC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAM;MACFC,IAAI;MACJC,MAAM;MACNC,OAAO;MACPC,QAAQ;MACRC,QAAQ;MACRC,SAAS;MACTC;IACF,CAAC,GAAGP,KAAK;IACTQ,KAAK,GAAGxC,6BAA6B,CAACgC,KAAK,EAAE/B,SAAS,CAAC;EACzD,MAAMwC,MAAM,GAAG1B,iBAAiB,CAAC,CAAC;EAClC,MAAM2B,SAAS,GAAG5B,gBAAgB,CAAC,CAAC;EACpC,MAAMO,OAAO,GAAGF,iBAAiB,CAACuB,SAAS,CAAC;EAC5C,MAAMC,cAAc,GAAGhC,oBAAoB,CAAC,CAAC;EAC7C,MAAMiC,aAAa,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EACxCvC,iBAAiB,CAAC,MAAM;IACtB,IAAI2B,IAAI,EAAE;MACRW,aAAa,CAACE,OAAO,GAAGC,QAAQ,CAACC,aAAa,YAAYC,WAAW,GAAGF,QAAQ,CAACC,aAAa,GAAG,IAAI;IACvG,CAAC,MAAM;MACLJ,aAAa,CAACE,OAAO,EAAEI,KAAK,GAAG,CAAC;MAChCN,aAAa,CAACE,OAAO,GAAG,IAAI;IAC9B;EACF,CAAC,EAAE,CAACb,IAAI,CAAC,CAAC;EACV/B,KAAK,CAACiD,SAAS,CAAC,MAAM;IACpB;IACA,MAAMC,SAAS,GAAGnB,IAAI,GAAG,UAAU,GAAG,WAAW;IACjDQ,MAAM,CAACK,OAAO,CAACO,YAAY,CAACD,SAAS,EAAE;MACrClB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACO,MAAM,EAAER,IAAI,EAAEC,MAAM,CAAC,CAAC;EAC1B,MAAMoB,eAAe,GAAGC,KAAK,IAAI;IAC/B,IAAIA,KAAK,CAACrB,MAAM,KAAKA,MAAM,KAAKqB,KAAK,CAACrB,MAAM,IAAIA,MAAM,EAAEsB,QAAQ,CAACD,KAAK,CAACrB,MAAM,CAAC,CAAC,EAAE;MAC/E;IACF;IACAC,OAAO,CAACoB,KAAK,CAAC;EAChB,CAAC;EACD,MAAME,aAAa,GAAGF,KAAK,IAAI;IAC7B,IAAI9C,aAAa,CAAC8C,KAAK,CAACG,GAAG,CAAC,EAAE;MAC5BvB,OAAO,CAACoB,KAAK,CAAC;IAChB;EACF,CAAC;EACD,OAAO,aAAarC,IAAI,CAACM,YAAY,EAAEzB,QAAQ,CAAC;IAC9C4D,EAAE,EAAEjB,SAAS,CAACpB,KAAK,CAACsC,UAAU;IAC9BtB,SAAS,EAAElC,IAAI,CAACiB,OAAO,CAACE,IAAI,EAAEe,SAAS,EAAEK,cAAc,CAAC;IACxDvB,UAAU,EAAEsB,SAAS;IACrBT,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACd2B,UAAU,EAAE,IAAI;IAChBC,SAAS,EAAEzB,QAAQ;IACnB0B,WAAW,EAAET,eAAe;IAC5Bf,QAAQ,EAAEA,QAAQ;IAClByB,mBAAmB,EAAE,aAAa;IAClCC,SAAS,EAAER;EACb,CAAC,EAAEjB,KAAK,EAAEE,SAAS,CAACwB,SAAS,EAAEN,UAAU,EAAE;IACzCxB,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL;AACA+B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtC,QAAQ,CAACuC,SAAS,GAAG;EAC3D;EACA;EACA;EACA;EACAlC,QAAQ,EAAEjC,SAAS,CAACoE,IAAI;EACxBjC,SAAS,EAAEnC,SAAS,CAACqE,MAAM;EAC3BrC,OAAO,EAAEhC,SAAS,CAACsE,IAAI,CAACC,UAAU;EAClCnC,QAAQ,EAAEpC,SAAS,CAACsE,IAAI;EACxBxC,IAAI,EAAE9B,SAAS,CAACwE,IAAI,CAACD,UAAU;EAC/BrC,QAAQ,EAAElC,SAAS,CAACyE,KAAK,CAAC,CAAC,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;EACzK1C,MAAM,EAAE3B;AACV,CAAC,GAAG,KAAK,CAAC;AACV,SAASwB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}