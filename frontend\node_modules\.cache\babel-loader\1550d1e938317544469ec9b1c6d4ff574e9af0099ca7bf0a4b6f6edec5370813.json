{"ast": null, "code": "import { isPlainObject } from './isPlainObject.mjs';\nfunction isJSONValue(value) {\n  switch (typeof value) {\n    case 'object':\n      {\n        return value === null || isJSONArray(value) || isJSONObject(value);\n      }\n    case 'string':\n    case 'number':\n    case 'boolean':\n      {\n        return true;\n      }\n    default:\n      {\n        return false;\n      }\n  }\n}\nfunction isJSONArray(value) {\n  if (!Array.isArray(value)) {\n    return false;\n  }\n  return value.every(item => isJSONValue(item));\n}\nfunction isJSONObject(obj) {\n  if (!isPlainObject(obj)) {\n    return false;\n  }\n  const keys = Reflect.ownKeys(obj);\n  for (let i = 0; i < keys.length; i++) {\n    const key = keys[i];\n    const value = obj[key];\n    if (typeof key !== 'string') {\n      return false;\n    }\n    if (!isJSONValue(value)) {\n      return false;\n    }\n  }\n  return true;\n}\nexport { isJSONArray, isJSONObject, isJSONValue };", "map": {"version": 3, "names": ["isPlainObject", "isJSONValue", "value", "isJSONArray", "isJSONObject", "Array", "isArray", "every", "item", "obj", "keys", "Reflect", "ownKeys", "i", "length", "key"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isJSONValue.mjs"], "sourcesContent": ["import { isPlainObject } from './isPlainObject.mjs';\n\nfunction isJSONValue(value) {\n    switch (typeof value) {\n        case 'object': {\n            return value === null || isJSONArray(value) || isJSONObject(value);\n        }\n        case 'string':\n        case 'number':\n        case 'boolean': {\n            return true;\n        }\n        default: {\n            return false;\n        }\n    }\n}\nfunction isJSONArray(value) {\n    if (!Array.isArray(value)) {\n        return false;\n    }\n    return value.every(item => isJSONValue(item));\n}\nfunction isJSONObject(obj) {\n    if (!isPlainObject(obj)) {\n        return false;\n    }\n    const keys = Reflect.ownKeys(obj);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const value = obj[key];\n        if (typeof key !== 'string') {\n            return false;\n        }\n        if (!isJSONValue(value)) {\n            return false;\n        }\n    }\n    return true;\n}\n\nexport { isJSONArray, isJSONObject, isJSONValue };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AAEnD,SAASC,WAAWA,CAACC,KAAK,EAAE;EACxB,QAAQ,OAAOA,KAAK;IAChB,KAAK,QAAQ;MAAE;QACX,OAAOA,KAAK,KAAK,IAAI,IAAIC,WAAW,CAACD,KAAK,CAAC,IAAIE,YAAY,CAACF,KAAK,CAAC;MACtE;IACA,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,SAAS;MAAE;QACZ,OAAO,IAAI;MACf;IACA;MAAS;QACL,OAAO,KAAK;MAChB;EACJ;AACJ;AACA,SAASC,WAAWA,CAACD,KAAK,EAAE;EACxB,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;IACvB,OAAO,KAAK;EAChB;EACA,OAAOA,KAAK,CAACK,KAAK,CAACC,IAAI,IAAIP,WAAW,CAACO,IAAI,CAAC,CAAC;AACjD;AACA,SAASJ,YAAYA,CAACK,GAAG,EAAE;EACvB,IAAI,CAACT,aAAa,CAACS,GAAG,CAAC,EAAE;IACrB,OAAO,KAAK;EAChB;EACA,MAAMC,IAAI,GAAGC,OAAO,CAACC,OAAO,CAACH,GAAG,CAAC;EACjC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC,MAAME,GAAG,GAAGL,IAAI,CAACG,CAAC,CAAC;IACnB,MAAMX,KAAK,GAAGO,GAAG,CAACM,GAAG,CAAC;IACtB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,IAAI,CAACd,WAAW,CAACC,KAAK,CAAC,EAAE;MACrB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AAEA,SAASC,WAAW,EAAEC,YAAY,EAAEH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}