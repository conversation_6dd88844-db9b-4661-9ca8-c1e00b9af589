{"ast": null, "code": "export { gridDimensionsSelector, gridColumnsTotalWidthSelector } from \"./gridDimensionsSelectors.js\";", "map": {"version": 3, "names": ["gridDimensionsSelector", "gridColumnsTotalWidthSelector"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/dimensions/index.js"], "sourcesContent": ["export { gridDimensionsSelector, gridColumnsTotalWidthSelector } from \"./gridDimensionsSelectors.js\";"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,6BAA6B,QAAQ,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}