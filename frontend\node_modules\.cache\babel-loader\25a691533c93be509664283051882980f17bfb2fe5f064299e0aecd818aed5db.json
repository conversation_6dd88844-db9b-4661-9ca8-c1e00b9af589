{"ast": null, "code": "import * as React from 'react';\nexport function useRunOncePerLoop(callback, nextFrame = false) {\n  const scheduledRef = React.useRef(false);\n  const schedule = React.useCallback((...args) => {\n    if (scheduledRef.current) {\n      return;\n    }\n    scheduledRef.current = true;\n    const runner = () => {\n      scheduledRef.current = false;\n      callback(...args);\n    };\n    if (nextFrame) {\n      if (typeof requestAnimationFrame === 'function') {\n        requestAnimationFrame(runner);\n      }\n      return;\n    }\n    if (typeof queueMicrotask === 'function') {\n      queueMicrotask(runner);\n    } else {\n      Promise.resolve().then(runner);\n    }\n  }, [callback, nextFrame]);\n  return schedule;\n}", "map": {"version": 3, "names": ["React", "useRunOncePerLoop", "callback", "next<PERSON><PERSON><PERSON>", "scheduledRef", "useRef", "schedule", "useCallback", "args", "current", "runner", "requestAnimationFrame", "queueMicrotask", "Promise", "resolve", "then"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useRunOncePerLoop.js"], "sourcesContent": ["import * as React from 'react';\nexport function useRunOncePerLoop(callback, nextFrame = false) {\n  const scheduledRef = React.useRef(false);\n  const schedule = React.useCallback((...args) => {\n    if (scheduledRef.current) {\n      return;\n    }\n    scheduledRef.current = true;\n    const runner = () => {\n      scheduledRef.current = false;\n      callback(...args);\n    };\n    if (nextFrame) {\n      if (typeof requestAnimationFrame === 'function') {\n        requestAnimationFrame(runner);\n      }\n      return;\n    }\n    if (typeof queueMicrotask === 'function') {\n      queueMicrotask(runner);\n    } else {\n      Promise.resolve().then(runner);\n    }\n  }, [callback, nextFrame]);\n  return schedule;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,SAAS,GAAG,KAAK,EAAE;EAC7D,MAAMC,YAAY,GAAGJ,KAAK,CAACK,MAAM,CAAC,KAAK,CAAC;EACxC,MAAMC,QAAQ,GAAGN,KAAK,CAACO,WAAW,CAAC,CAAC,GAAGC,IAAI,KAAK;IAC9C,IAAIJ,YAAY,CAACK,OAAO,EAAE;MACxB;IACF;IACAL,YAAY,CAACK,OAAO,GAAG,IAAI;IAC3B,MAAMC,MAAM,GAAGA,CAAA,KAAM;MACnBN,YAAY,CAACK,OAAO,GAAG,KAAK;MAC5BP,QAAQ,CAAC,GAAGM,IAAI,CAAC;IACnB,CAAC;IACD,IAAIL,SAAS,EAAE;MACb,IAAI,OAAOQ,qBAAqB,KAAK,UAAU,EAAE;QAC/CA,qBAAqB,CAACD,MAAM,CAAC;MAC/B;MACA;IACF;IACA,IAAI,OAAOE,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAACF,MAAM,CAAC;IACxB,CAAC,MAAM;MACLG,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAACL,MAAM,CAAC;IAChC;EACF,CAAC,EAAE,CAACR,QAAQ,EAAEC,SAAS,CAAC,CAAC;EACzB,OAAOG,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}