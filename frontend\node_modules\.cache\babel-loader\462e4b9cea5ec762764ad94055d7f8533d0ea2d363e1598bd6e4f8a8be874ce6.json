{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridRowsLookupSelector, gridRowMaximumTreeDepthSelector, gridRowNodeSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowSelectionManagerSelector, gridRowSelectionStateSelector, gridRowSelectionCountSelector, gridRowSelectionIdsSelector } from \"./gridRowSelectionSelector.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { gridExpandedSortedRowIdsSelector, gridFilteredRowsLookupSelector, gridFilterModelSelector, gridQuickFilterValuesSelector } from \"../filter/gridFilterSelector.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isKeyboardEvent, isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../internals/constants.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { isMultipleRowSelectionEnabled, findRowsToSelect, findRowsToDeselect } from \"./utils.js\";\nimport { createRowSelectionManager } from \"../../../models/gridRowSelectionManager.js\";\nimport { gridPaginatedVisibleSortedGridRowIdsSelector } from \"../pagination/index.js\";\nconst emptyModel = {\n  type: 'include',\n  ids: new Set()\n};\nexport const rowSelectionStateInitializer = (state, props) => _extends({}, state, {\n  rowSelection: props.rowSelection ? props.rowSelectionModel ?? emptyModel : emptyModel\n});\n\n/**\n * @requires useGridRows (state, method) - can be after\n * @requires useGridParamsApi (method) - can be after\n * @requires useGridFocus (state) - can be after\n * @requires useGridKeyboardNavigation (`cellKeyDown` event must first be consumed by it)\n */\nexport const useGridRowSelection = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSelection');\n  const runIfRowSelectionIsEnabled = React.useCallback(callback => (...args) => {\n    if (props.rowSelection) {\n      callback(...args);\n    }\n  }, [props.rowSelection]);\n  const isNestedData = useGridSelector(apiRef, gridRowMaximumTreeDepthSelector) > 1;\n  const applyAutoSelection = props.signature !== GridSignature.DataGrid && (props.rowSelectionPropagation?.parents || props.rowSelectionPropagation?.descendants) && isNestedData;\n  const propRowSelectionModel = React.useMemo(() => {\n    return props.rowSelectionModel;\n  }, [props.rowSelectionModel]);\n  const lastRowToggled = React.useRef(null);\n  apiRef.current.registerControlState({\n    stateId: 'rowSelection',\n    propModel: propRowSelectionModel,\n    propOnChange: props.onRowSelectionModelChange,\n    stateSelector: gridRowSelectionStateSelector,\n    changeEvent: 'rowSelectionChange'\n  });\n  const {\n    checkboxSelection,\n    disableRowSelectionOnClick,\n    isRowSelectable: propIsRowSelectable\n  } = props;\n  const canHaveMultipleSelection = isMultipleRowSelectionEnabled(props);\n  const tree = useGridSelector(apiRef, gridRowTreeSelector);\n  const expandMouseRowRangeSelection = React.useCallback(id => {\n    let endId = id;\n    const startId = lastRowToggled.current ?? id;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (isSelected) {\n      const visibleRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n      const startIndex = visibleRowIds.findIndex(rowId => rowId === startId);\n      const endIndex = visibleRowIds.findIndex(rowId => rowId === endId);\n      if (startIndex === endIndex) {\n        return;\n      }\n      if (startIndex > endIndex) {\n        endId = visibleRowIds[endIndex + 1];\n      } else {\n        endId = visibleRowIds[endIndex - 1];\n      }\n    }\n    lastRowToggled.current = id;\n    apiRef.current.selectRowRange({\n      startId,\n      endId\n    }, !isSelected);\n  }, [apiRef]);\n  const getRowsToBeSelected = useEventCallback(() => {\n    const rowsToBeSelected = props.pagination && props.checkboxSelectionVisibleOnly && props.paginationMode === 'client' ? gridPaginatedVisibleSortedGridRowIdsSelector(apiRef) : gridExpandedSortedRowIdsSelector(apiRef);\n    return rowsToBeSelected;\n  });\n\n  /*\n   * API METHODS\n   */\n  const setRowSelectionModel = React.useCallback((model, reason) => {\n    if (props.signature === GridSignature.DataGrid && !canHaveMultipleSelection && (model.type !== 'include' || model.ids.size > 1)) {\n      throw new Error(['MUI X: `rowSelectionModel` can only contain 1 item in DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection.'].join('\\n'));\n    }\n    const currentModel = gridRowSelectionStateSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug(`Setting selection model`);\n      apiRef.current.setState(state => _extends({}, state, {\n        rowSelection: props.rowSelection ? model : emptyModel\n      }), reason);\n    }\n  }, [apiRef, logger, props.rowSelection, props.signature, canHaveMultipleSelection]);\n  const isRowSelected = React.useCallback(id => {\n    const selectionManager = gridRowSelectionManagerSelector(apiRef);\n    return selectionManager.has(id);\n  }, [apiRef]);\n  const isRowSelectable = React.useCallback(id => {\n    if (props.rowSelection === false) {\n      return false;\n    }\n    if (propIsRowSelectable && !propIsRowSelectable(apiRef.current.getRowParams(id))) {\n      return false;\n    }\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (rowNode?.type === 'footer' || rowNode?.type === 'pinnedRow') {\n      return false;\n    }\n    return true;\n  }, [apiRef, props.rowSelection, propIsRowSelectable]);\n  const getSelectedRows = React.useCallback(() => gridRowSelectionIdsSelector(apiRef), [apiRef]);\n  const selectRow = React.useCallback((id, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.isRowSelectable(id)) {\n      return;\n    }\n    lastRowToggled.current = id;\n    if (resetSelection) {\n      logger.debug(`Setting selection for row ${id}`);\n      const newSelectionModel = {\n        type: 'include',\n        ids: new Set()\n      };\n      const addRow = rowId => {\n        newSelectionModel.ids.add(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      }\n      apiRef.current.setRowSelectionModel(newSelectionModel, 'singleRowSelection');\n    } else {\n      logger.debug(`Toggling selection for row ${id}`);\n      const selectionModel = gridRowSelectionStateSelector(apiRef);\n      const newSelectionModel = {\n        type: selectionModel.type,\n        ids: new Set(selectionModel.ids)\n      };\n      const selectionManager = createRowSelectionManager(newSelectionModel);\n      selectionManager.unselect(id);\n      const addRow = rowId => {\n        selectionManager.select(rowId);\n      };\n      const removeRow = rowId => {\n        selectionManager.unselect(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      } else if (applyAutoSelection) {\n        findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n      }\n      const isSelectionValid = newSelectionModel.type === 'include' && newSelectionModel.ids.size < 2 || canHaveMultipleSelection;\n      if (isSelectionValid) {\n        apiRef.current.setRowSelectionModel(newSelectionModel, 'singleRowSelection');\n      }\n    }\n  }, [apiRef, logger, applyAutoSelection, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, canHaveMultipleSelection]);\n  const selectRows = React.useCallback((ids, isSelected = true, resetSelection = false) => {\n    logger.debug(`Setting selection for several rows`);\n    if (props.rowSelection === false) {\n      return;\n    }\n    const selectableIds = new Set();\n    for (let i = 0; i < ids.length; i += 1) {\n      const id = ids[i];\n      if (apiRef.current.isRowSelectable(id)) {\n        selectableIds.add(id);\n      }\n    }\n    const currentSelectionModel = gridRowSelectionStateSelector(apiRef);\n    let newSelectionModel;\n    if (resetSelection) {\n      newSelectionModel = {\n        type: 'include',\n        ids: selectableIds\n      };\n      if (isSelected) {\n        const selectionManager = createRowSelectionManager(newSelectionModel);\n        if (applyAutoSelection) {\n          const addRow = rowId => {\n            selectionManager.select(rowId);\n          };\n          for (const id of selectableIds) {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          }\n        }\n      } else {\n        newSelectionModel.ids = new Set();\n      }\n      if (currentSelectionModel.type === newSelectionModel.type && newSelectionModel.ids.size === currentSelectionModel.ids.size && Array.from(newSelectionModel.ids).every(id => currentSelectionModel.ids.has(id))) {\n        return;\n      }\n    } else {\n      newSelectionModel = {\n        type: currentSelectionModel.type,\n        ids: new Set(currentSelectionModel.ids)\n      };\n      const selectionManager = createRowSelectionManager(newSelectionModel);\n      const addRow = rowId => {\n        selectionManager.select(rowId);\n      };\n      const removeRow = rowId => {\n        selectionManager.unselect(rowId);\n      };\n      for (const id of selectableIds) {\n        if (isSelected) {\n          selectionManager.select(id);\n          if (applyAutoSelection) {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          }\n        } else {\n          removeRow(id);\n          if (applyAutoSelection) {\n            findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n          }\n        }\n      }\n    }\n    const isSelectionValid = newSelectionModel.type === 'include' && newSelectionModel.ids.size < 2 || canHaveMultipleSelection;\n    if (isSelectionValid) {\n      apiRef.current.setRowSelectionModel(newSelectionModel, 'multipleRowsSelection');\n    }\n  }, [logger, applyAutoSelection, canHaveMultipleSelection, apiRef, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, props.rowSelection]);\n  const getPropagatedRowSelectionModel = React.useCallback(inputSelectionModel => {\n    if (!isNestedData || !applyAutoSelection || inputSelectionModel.ids.size === 0 && inputSelectionModel.type === 'include') {\n      return inputSelectionModel;\n    }\n    const propagatedSelectionModel = {\n      type: inputSelectionModel.type,\n      ids: new Set(inputSelectionModel.ids)\n    };\n    const selectionManager = createRowSelectionManager(propagatedSelectionModel);\n    const addRow = rowId => {\n      selectionManager.select(rowId);\n    };\n    for (const id of inputSelectionModel.ids) {\n      findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow, selectionManager);\n    }\n    return propagatedSelectionModel;\n  }, [apiRef, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, isNestedData, applyAutoSelection]);\n  const selectRowRange = React.useCallback(({\n    startId,\n    endId\n  }, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.getRow(startId) || !apiRef.current.getRow(endId)) {\n      return;\n    }\n    logger.debug(`Expanding selection from row ${startId} to row ${endId}`);\n\n    // Using rows from all pages allow to select a range across several pages\n    const allPagesRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n    const startIndex = allPagesRowIds.indexOf(startId);\n    const endIndex = allPagesRowIds.indexOf(endId);\n    const [start, end] = startIndex > endIndex ? [endIndex, startIndex] : [startIndex, endIndex];\n    const rowsBetweenStartAndEnd = allPagesRowIds.slice(start, end + 1);\n    apiRef.current.selectRows(rowsBetweenStartAndEnd, isSelected, resetSelection);\n  }, [apiRef, logger]);\n  const selectionPublicApi = {\n    selectRow,\n    setRowSelectionModel,\n    getSelectedRows,\n    isRowSelected,\n    isRowSelectable\n  };\n  const selectionPrivateApi = {\n    selectRows,\n    selectRowRange,\n    getPropagatedRowSelectionModel\n  };\n  useGridApiMethod(apiRef, selectionPublicApi, 'public');\n  useGridApiMethod(apiRef, selectionPrivateApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /*\n   * EVENTS\n   */\n  const isFirstRender = React.useRef(true);\n  const removeOutdatedSelection = React.useCallback((sortModelUpdated = false) => {\n    if (isFirstRender.current) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n    const rowTree = gridRowTreeSelector(apiRef);\n    const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n    const isNonExistent = id => {\n      if (props.filterMode === 'server') {\n        return !rowsLookup[id];\n      }\n      return !rowTree[id] || filteredRowsLookup[id] === false;\n    };\n    const newSelectionModel = {\n      type: currentSelection.type,\n      ids: new Set(currentSelection.ids)\n    };\n    const selectionManager = createRowSelectionManager(newSelectionModel);\n    let hasChanged = false;\n    for (const id of currentSelection.ids) {\n      if (isNonExistent(id)) {\n        if (props.keepNonExistentRowsSelected) {\n          continue;\n        }\n        selectionManager.unselect(id);\n        hasChanged = true;\n        continue;\n      }\n      if (!props.rowSelectionPropagation?.parents) {\n        continue;\n      }\n      const node = tree[id];\n      if (node?.type === 'group') {\n        const isAutoGenerated = node.isAutoGenerated;\n        if (isAutoGenerated) {\n          selectionManager.unselect(id);\n          hasChanged = true;\n          continue;\n        }\n        // Keep previously selected tree data parents selected if all their children are filtered out\n        if (!node.children.every(childId => filteredRowsLookup[childId] === false)) {\n          selectionManager.unselect(id);\n          hasChanged = true;\n        }\n      }\n    }\n\n    // For nested data, on row tree updation (filtering, adding rows, etc.) when the selection is\n    // not empty, we need to re-run scanning of the tree to propagate the selection changes\n    // Example: A parent whose de-selected children are filtered out should now be selected\n    const shouldReapplyPropagation = isNestedData && props.rowSelectionPropagation?.parents && (newSelectionModel.ids.size > 0 ||\n    // In case of exclude selection, newSelectionModel.ids.size === 0 means all rows are selected\n    newSelectionModel.type === 'exclude');\n    if (hasChanged || shouldReapplyPropagation && !sortModelUpdated) {\n      if (shouldReapplyPropagation) {\n        if (newSelectionModel.type === 'exclude') {\n          const unfilteredSelectedRowIds = getRowsToBeSelected();\n          const selectedRowIds = [];\n          for (let i = 0; i < unfilteredSelectedRowIds.length; i += 1) {\n            const rowId = unfilteredSelectedRowIds[i];\n            if ((props.keepNonExistentRowsSelected || !isNonExistent(rowId)) && selectionManager.has(rowId)) {\n              selectedRowIds.push(rowId);\n            }\n          }\n          apiRef.current.selectRows(selectedRowIds, true, true);\n        } else {\n          apiRef.current.selectRows(Array.from(newSelectionModel.ids), true, true);\n        }\n      } else {\n        apiRef.current.setRowSelectionModel(newSelectionModel, 'multipleRowsSelection');\n      }\n    }\n  }, [apiRef, isNestedData, props.rowSelectionPropagation?.parents, props.keepNonExistentRowsSelected, props.filterMode, tree, getRowsToBeSelected]);\n  const handleSingleRowSelection = React.useCallback((id, event) => {\n    const hasCtrlKey = event.metaKey || event.ctrlKey;\n\n    // multiple selection is only allowed if:\n    // - it is a checkboxSelection\n    // - it is a keyboard selection\n    // - Ctrl is pressed\n\n    const isMultipleSelectionDisabled = !checkboxSelection && !hasCtrlKey && !isKeyboardEvent(event);\n    const resetSelection = !canHaveMultipleSelection || isMultipleSelectionDisabled;\n    const isSelected = apiRef.current.isRowSelected(id);\n    const selectedRowsCount = gridRowSelectionCountSelector(apiRef);\n\n    // Clicking on a row should toggle the selection except when a range of rows is already selected and the selection should reset\n    // In that case, we want to keep the current row selected (https://github.com/mui/mui-x/pull/15509#discussion_r1878082687)\n    const shouldStaySelected = selectedRowsCount > 1 && resetSelection;\n    const newSelectionState = shouldStaySelected || !isSelected;\n    apiRef.current.selectRow(id, newSelectionState, resetSelection);\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection]);\n  const handleRowClick = React.useCallback((params, event) => {\n    if (disableRowSelectionOnClick) {\n      return;\n    }\n    const field = event.target.closest(`.${gridClasses.cell}`)?.getAttribute('data-field');\n    if (field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // click on checkbox should not trigger row selection\n      return;\n    }\n    if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n      // click to open the detail panel should not select the row\n      return;\n    }\n    if (field) {\n      const column = apiRef.current.getColumn(field);\n      if (column?.type === GRID_ACTIONS_COLUMN_TYPE) {\n        return;\n      }\n    }\n    const rowNode = gridRowNodeSelector(apiRef, params.id);\n    if (rowNode.type === 'pinnedRow') {\n      return;\n    }\n    if (event.shiftKey && canHaveMultipleSelection) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      handleSingleRowSelection(params.id, event);\n    }\n  }, [disableRowSelectionOnClick, canHaveMultipleSelection, apiRef, expandMouseRowRangeSelection, handleSingleRowSelection]);\n  const preventSelectionOnShift = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.shiftKey) {\n      window.getSelection()?.removeAllRanges();\n    }\n  }, [canHaveMultipleSelection]);\n  const handleRowSelectionCheckboxChange = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.nativeEvent.shiftKey) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      apiRef.current.selectRow(params.id, params.value, !canHaveMultipleSelection);\n    }\n  }, [apiRef, expandMouseRowRangeSelection, canHaveMultipleSelection]);\n  const toggleAllRows = React.useCallback(value => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const quickFilterModel = gridQuickFilterValuesSelector(apiRef);\n    const hasFilters = filterModel.items.length > 0 || quickFilterModel?.some(val => val.length);\n    if (!props.isRowSelectable && !props.checkboxSelectionVisibleOnly && (!isNestedData || props.rowSelectionPropagation?.descendants) && !hasFilters && !props.disableRowSelectionExcludeModel) {\n      apiRef.current.setRowSelectionModel({\n        type: value ? 'exclude' : 'include',\n        ids: new Set()\n      }, 'multipleRowsSelection');\n    } else {\n      apiRef.current.selectRows(getRowsToBeSelected(), value);\n    }\n  }, [apiRef, getRowsToBeSelected, props.checkboxSelectionVisibleOnly, props.isRowSelectable, props.rowSelectionPropagation?.descendants, props.disableRowSelectionExcludeModel, isNestedData]);\n  const handleHeaderSelectionCheckboxChange = React.useCallback(params => {\n    toggleAllRows(params.value);\n  }, [toggleAllRows]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Get the most recent cell mode because it may have been changed by another listener\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.Edit) {\n      return;\n    }\n\n    // Ignore portal\n    // Do not apply shortcuts if the focus is not on the cell root component\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    if (isNavigationKey(event.key) && event.shiftKey) {\n      // The cell that has focus after the keyboard navigation\n      const focusCell = gridFocusCellSelector(apiRef);\n      if (focusCell && focusCell.id !== params.id) {\n        event.preventDefault();\n        const isNextRowSelected = apiRef.current.isRowSelected(focusCell.id);\n        if (!canHaveMultipleSelection) {\n          apiRef.current.selectRow(focusCell.id, !isNextRowSelected, true);\n          return;\n        }\n        const newRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(focusCell.id);\n        const previousRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(params.id);\n        let start;\n        let end;\n        if (newRowIndex > previousRowIndex) {\n          if (isNextRowSelected) {\n            // We are navigating to the bottom of the page and adding selected rows\n            start = previousRowIndex;\n            end = newRowIndex - 1;\n          } else {\n            // We are navigating to the bottom of the page and removing selected rows\n            start = previousRowIndex;\n            end = newRowIndex;\n          }\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (isNextRowSelected) {\n            // We are navigating to the top of the page and removing selected rows\n            start = newRowIndex + 1;\n            end = previousRowIndex;\n          } else {\n            // We are navigating to the top of the page and adding selected rows\n            start = newRowIndex;\n            end = previousRowIndex;\n          }\n        }\n        const visibleRows = getVisibleRows(apiRef);\n        const rowsBetweenStartAndEnd = [];\n        for (let i = start; i <= end; i += 1) {\n          rowsBetweenStartAndEnd.push(visibleRows.rows[i].id);\n        }\n        apiRef.current.selectRows(rowsBetweenStartAndEnd, !isNextRowSelected);\n        return;\n      }\n    }\n    if (event.key === ' ' && event.shiftKey) {\n      event.preventDefault();\n      handleSingleRowSelection(params.id, event);\n      return;\n    }\n    if (String.fromCharCode(event.keyCode) === 'A' && (event.ctrlKey || event.metaKey)) {\n      event.preventDefault();\n      toggleAllRows(true);\n    }\n  }, [apiRef, canHaveMultipleSelection, handleSingleRowSelection, toggleAllRows]);\n  const syncControlledState = useEventCallback(() => {\n    if (!props.rowSelection) {\n      apiRef.current.setRowSelectionModel(emptyModel);\n      return;\n    }\n    if (propRowSelectionModel === undefined) {\n      return;\n    }\n    if (!applyAutoSelection || !isNestedData || propRowSelectionModel.type === 'include' && propRowSelectionModel.ids.size === 0) {\n      apiRef.current.setRowSelectionModel(propRowSelectionModel);\n      return;\n    }\n    const newSelectionModel = apiRef.current.getPropagatedRowSelectionModel(propRowSelectionModel);\n    if (newSelectionModel.type !== propRowSelectionModel.type || newSelectionModel.ids.size !== propRowSelectionModel.ids.size || !Array.from(propRowSelectionModel.ids).every(id => newSelectionModel.ids.has(id))) {\n      apiRef.current.setRowSelectionModel(newSelectionModel);\n      return;\n    }\n    apiRef.current.setRowSelectionModel(propRowSelectionModel);\n  });\n  useGridEvent(apiRef, 'sortedRowsSet', runIfRowSelectionIsEnabled(() => removeOutdatedSelection(true)));\n  useGridEvent(apiRef, 'filteredRowsSet', runIfRowSelectionIsEnabled(() => removeOutdatedSelection()));\n  useGridEvent(apiRef, 'rowClick', runIfRowSelectionIsEnabled(handleRowClick));\n  useGridEvent(apiRef, 'rowSelectionCheckboxChange', runIfRowSelectionIsEnabled(handleRowSelectionCheckboxChange));\n  useGridEvent(apiRef, 'headerSelectionCheckboxChange', handleHeaderSelectionCheckboxChange);\n  useGridEvent(apiRef, 'cellMouseDown', runIfRowSelectionIsEnabled(preventSelectionOnShift));\n  useGridEvent(apiRef, 'cellKeyDown', runIfRowSelectionIsEnabled(handleCellKeyDown));\n\n  /*\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    syncControlledState();\n  }, [apiRef, propRowSelectionModel, props.rowSelection, syncControlledState]);\n  const isStateControlled = propRowSelectionModel != null;\n  React.useEffect(() => {\n    if (isStateControlled || !props.rowSelection || typeof isRowSelectable !== 'function') {\n      return;\n    }\n\n    // props.isRowSelectable changed\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    if (currentSelection.type !== 'include') {\n      return;\n    }\n    const selectableIds = new Set();\n    for (const id of currentSelection.ids) {\n      if (isRowSelectable(id)) {\n        selectableIds.add(id);\n      }\n    }\n    if (selectableIds.size < currentSelection.ids.size) {\n      apiRef.current.setRowSelectionModel({\n        type: currentSelection.type,\n        ids: selectableIds\n      });\n    }\n  }, [apiRef, isRowSelectable, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection || isStateControlled) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    if (!canHaveMultipleSelection && (currentSelection.type === 'include' && currentSelection.ids.size > 1 || currentSelection.type === 'exclude')) {\n      // See https://github.com/mui/mui-x/issues/8455\n      apiRef.current.setRowSelectionModel(emptyModel);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    runIfRowSelectionIsEnabled(removeOutdatedSelection);\n  }, [removeOutdatedSelection, runIfRowSelectionIsEnabled]);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    }\n  }, []);\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "GridSignature", "useGridEvent", "useGridApiMethod", "useGridLogger", "useGridSelector", "gridRowsLookupSelector", "gridRowMaximumTreeDepthSelector", "gridRowNodeSelector", "gridRowTreeSelector", "gridRowSelectionManagerSelector", "gridRowSelectionStateSelector", "gridRowSelectionCountSelector", "gridRowSelectionIdsSelector", "gridFocusCellSelector", "gridExpandedSortedRowIdsSelector", "gridFilteredRowsLookupSelector", "gridFilterModelSelector", "gridQuickFilterValuesSelector", "GRID_CHECKBOX_SELECTION_COL_DEF", "GRID_ACTIONS_COLUMN_TYPE", "GridCellModes", "isKeyboardEvent", "isNavigationKey", "getVisibleRows", "GRID_DETAIL_PANEL_TOGGLE_FIELD", "gridClasses", "isEventTargetInPortal", "isMultipleRowSelectionEnabled", "findRowsToSelect", "findRowsToDeselect", "createRowSelectionManager", "gridPaginatedVisibleSortedGridRowIdsSelector", "emptyModel", "type", "ids", "Set", "rowSelectionStateInitializer", "state", "props", "rowSelection", "rowSelectionModel", "useGridRowSelection", "apiRef", "logger", "runIfRowSelectionIsEnabled", "useCallback", "callback", "args", "isNestedData", "applyAutoSelection", "signature", "DataGrid", "rowSelectionPropagation", "parents", "descendants", "propRowSelectionModel", "useMemo", "lastRowToggled", "useRef", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onRowSelectionModelChange", "stateSelector", "changeEvent", "checkboxSelection", "disableRowSelectionOnClick", "isRowSelectable", "propIsRowSelectable", "canHaveMultipleSelection", "tree", "expandMouseRowRangeSelection", "id", "endId", "startId", "isSelected", "isRowSelected", "visibleRowIds", "startIndex", "findIndex", "rowId", "endIndex", "selectRowRange", "getRowsToBeSelected", "rowsToBeSelected", "pagination", "checkboxSelectionVisibleOnly", "paginationMode", "setRowSelectionModel", "model", "reason", "size", "Error", "join", "currentModel", "debug", "setState", "<PERSON><PERSON><PERSON><PERSON>", "has", "getRowParams", "rowNode", "getSelectedRows", "selectRow", "resetSelection", "newSelectionModel", "addRow", "add", "selectionModel", "unselect", "select", "removeRow", "isSelectionValid", "selectRows", "selectableIds", "i", "length", "currentSelectionModel", "Array", "from", "every", "getPropagatedRowSelectionModel", "inputSelectionModel", "propagatedSelectionModel", "getRow", "allPagesRowIds", "indexOf", "start", "end", "rowsBetweenStartAndEnd", "slice", "selectionPublicApi", "selectionPrivateApi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removeOutdatedSelection", "sortModelUpdated", "currentSelection", "rowsLookup", "rowTree", "filteredRowsLookup", "isNonExistent", "filterMode", "has<PERSON><PERSON>ed", "keepNonExistentRowsSelected", "node", "isAutoGenerated", "children", "childId", "shouldReapplyPropagation", "unfilteredSelectedRowIds", "selectedRowIds", "push", "handleSingleRowSelection", "event", "hasCtrlKey", "metaKey", "ctrl<PERSON>ey", "isMultipleSelectionDisabled", "selectedRowsCount", "shouldStaySelected", "newSelectionState", "handleRowClick", "params", "field", "target", "closest", "cell", "getAttribute", "column", "getColumn", "shift<PERSON>ey", "preventSelectionOnShift", "window", "getSelection", "removeAllRanges", "handleRowSelectionCheckboxChange", "nativeEvent", "value", "toggleAllRows", "filterModel", "quickFilterModel", "hasFilters", "items", "some", "val", "disableRowSelectionExcludeModel", "handleHeaderSelectionCheckboxChange", "handleCellKeyDown", "getCellMode", "Edit", "key", "focusCell", "preventDefault", "isNextRowSelected", "newRowIndex", "getRowIndexRelativeToVisibleRows", "previousRowIndex", "visibleRows", "rows", "String", "fromCharCode", "keyCode", "syncControlledState", "undefined", "useEffect", "isStateControlled"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rowSelection/useGridRowSelection.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { GridSignature } from \"../../../constants/signature.js\";\nimport { useGridEvent } from \"../../utils/useGridEvent.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridRowsLookupSelector, gridRowMaximumTreeDepthSelector, gridRowNodeSelector, gridRowTreeSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowSelectionManagerSelector, gridRowSelectionStateSelector, gridRowSelectionCountSelector, gridRowSelectionIdsSelector } from \"./gridRowSelectionSelector.js\";\nimport { gridFocusCellSelector } from \"../focus/gridFocusStateSelector.js\";\nimport { gridExpandedSortedRowIdsSelector, gridFilteredRowsLookupSelector, gridFilterModelSelector, gridQuickFilterValuesSelector } from \"../filter/gridFilterSelector.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_ACTIONS_COLUMN_TYPE } from \"../../../colDef/index.js\";\nimport { GridCellModes } from \"../../../models/gridEditRowModel.js\";\nimport { isKeyboardEvent, isNavigationKey } from \"../../../utils/keyboardUtils.js\";\nimport { getVisibleRows } from \"../../utils/useGridVisibleRows.js\";\nimport { GRID_DETAIL_PANEL_TOGGLE_FIELD } from \"../../../internals/constants.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { isEventTargetInPortal } from \"../../../utils/domUtils.js\";\nimport { isMultipleRowSelectionEnabled, findRowsToSelect, findRowsToDeselect } from \"./utils.js\";\nimport { createRowSelectionManager } from \"../../../models/gridRowSelectionManager.js\";\nimport { gridPaginatedVisibleSortedGridRowIdsSelector } from \"../pagination/index.js\";\nconst emptyModel = {\n  type: 'include',\n  ids: new Set()\n};\nexport const rowSelectionStateInitializer = (state, props) => _extends({}, state, {\n  rowSelection: props.rowSelection ? props.rowSelectionModel ?? emptyModel : emptyModel\n});\n\n/**\n * @requires useGridRows (state, method) - can be after\n * @requires useGridParamsApi (method) - can be after\n * @requires useGridFocus (state) - can be after\n * @requires useGridKeyboardNavigation (`cellKeyDown` event must first be consumed by it)\n */\nexport const useGridRowSelection = (apiRef, props) => {\n  const logger = useGridLogger(apiRef, 'useGridSelection');\n  const runIfRowSelectionIsEnabled = React.useCallback(callback => (...args) => {\n    if (props.rowSelection) {\n      callback(...args);\n    }\n  }, [props.rowSelection]);\n  const isNestedData = useGridSelector(apiRef, gridRowMaximumTreeDepthSelector) > 1;\n  const applyAutoSelection = props.signature !== GridSignature.DataGrid && (props.rowSelectionPropagation?.parents || props.rowSelectionPropagation?.descendants) && isNestedData;\n  const propRowSelectionModel = React.useMemo(() => {\n    return props.rowSelectionModel;\n  }, [props.rowSelectionModel]);\n  const lastRowToggled = React.useRef(null);\n  apiRef.current.registerControlState({\n    stateId: 'rowSelection',\n    propModel: propRowSelectionModel,\n    propOnChange: props.onRowSelectionModelChange,\n    stateSelector: gridRowSelectionStateSelector,\n    changeEvent: 'rowSelectionChange'\n  });\n  const {\n    checkboxSelection,\n    disableRowSelectionOnClick,\n    isRowSelectable: propIsRowSelectable\n  } = props;\n  const canHaveMultipleSelection = isMultipleRowSelectionEnabled(props);\n  const tree = useGridSelector(apiRef, gridRowTreeSelector);\n  const expandMouseRowRangeSelection = React.useCallback(id => {\n    let endId = id;\n    const startId = lastRowToggled.current ?? id;\n    const isSelected = apiRef.current.isRowSelected(id);\n    if (isSelected) {\n      const visibleRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n      const startIndex = visibleRowIds.findIndex(rowId => rowId === startId);\n      const endIndex = visibleRowIds.findIndex(rowId => rowId === endId);\n      if (startIndex === endIndex) {\n        return;\n      }\n      if (startIndex > endIndex) {\n        endId = visibleRowIds[endIndex + 1];\n      } else {\n        endId = visibleRowIds[endIndex - 1];\n      }\n    }\n    lastRowToggled.current = id;\n    apiRef.current.selectRowRange({\n      startId,\n      endId\n    }, !isSelected);\n  }, [apiRef]);\n  const getRowsToBeSelected = useEventCallback(() => {\n    const rowsToBeSelected = props.pagination && props.checkboxSelectionVisibleOnly && props.paginationMode === 'client' ? gridPaginatedVisibleSortedGridRowIdsSelector(apiRef) : gridExpandedSortedRowIdsSelector(apiRef);\n    return rowsToBeSelected;\n  });\n\n  /*\n   * API METHODS\n   */\n  const setRowSelectionModel = React.useCallback((model, reason) => {\n    if (props.signature === GridSignature.DataGrid && !canHaveMultipleSelection && (model.type !== 'include' || model.ids.size > 1)) {\n      throw new Error(['MUI X: `rowSelectionModel` can only contain 1 item in DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection.'].join('\\n'));\n    }\n    const currentModel = gridRowSelectionStateSelector(apiRef);\n    if (currentModel !== model) {\n      logger.debug(`Setting selection model`);\n      apiRef.current.setState(state => _extends({}, state, {\n        rowSelection: props.rowSelection ? model : emptyModel\n      }), reason);\n    }\n  }, [apiRef, logger, props.rowSelection, props.signature, canHaveMultipleSelection]);\n  const isRowSelected = React.useCallback(id => {\n    const selectionManager = gridRowSelectionManagerSelector(apiRef);\n    return selectionManager.has(id);\n  }, [apiRef]);\n  const isRowSelectable = React.useCallback(id => {\n    if (props.rowSelection === false) {\n      return false;\n    }\n    if (propIsRowSelectable && !propIsRowSelectable(apiRef.current.getRowParams(id))) {\n      return false;\n    }\n    const rowNode = gridRowNodeSelector(apiRef, id);\n    if (rowNode?.type === 'footer' || rowNode?.type === 'pinnedRow') {\n      return false;\n    }\n    return true;\n  }, [apiRef, props.rowSelection, propIsRowSelectable]);\n  const getSelectedRows = React.useCallback(() => gridRowSelectionIdsSelector(apiRef), [apiRef]);\n  const selectRow = React.useCallback((id, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.isRowSelectable(id)) {\n      return;\n    }\n    lastRowToggled.current = id;\n    if (resetSelection) {\n      logger.debug(`Setting selection for row ${id}`);\n      const newSelectionModel = {\n        type: 'include',\n        ids: new Set()\n      };\n      const addRow = rowId => {\n        newSelectionModel.ids.add(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      }\n      apiRef.current.setRowSelectionModel(newSelectionModel, 'singleRowSelection');\n    } else {\n      logger.debug(`Toggling selection for row ${id}`);\n      const selectionModel = gridRowSelectionStateSelector(apiRef);\n      const newSelectionModel = {\n        type: selectionModel.type,\n        ids: new Set(selectionModel.ids)\n      };\n      const selectionManager = createRowSelectionManager(newSelectionModel);\n      selectionManager.unselect(id);\n      const addRow = rowId => {\n        selectionManager.select(rowId);\n      };\n      const removeRow = rowId => {\n        selectionManager.unselect(rowId);\n      };\n      if (isSelected) {\n        addRow(id);\n        if (applyAutoSelection) {\n          findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n        }\n      } else if (applyAutoSelection) {\n        findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n      }\n      const isSelectionValid = newSelectionModel.type === 'include' && newSelectionModel.ids.size < 2 || canHaveMultipleSelection;\n      if (isSelectionValid) {\n        apiRef.current.setRowSelectionModel(newSelectionModel, 'singleRowSelection');\n      }\n    }\n  }, [apiRef, logger, applyAutoSelection, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, canHaveMultipleSelection]);\n  const selectRows = React.useCallback((ids, isSelected = true, resetSelection = false) => {\n    logger.debug(`Setting selection for several rows`);\n    if (props.rowSelection === false) {\n      return;\n    }\n    const selectableIds = new Set();\n    for (let i = 0; i < ids.length; i += 1) {\n      const id = ids[i];\n      if (apiRef.current.isRowSelectable(id)) {\n        selectableIds.add(id);\n      }\n    }\n    const currentSelectionModel = gridRowSelectionStateSelector(apiRef);\n    let newSelectionModel;\n    if (resetSelection) {\n      newSelectionModel = {\n        type: 'include',\n        ids: selectableIds\n      };\n      if (isSelected) {\n        const selectionManager = createRowSelectionManager(newSelectionModel);\n        if (applyAutoSelection) {\n          const addRow = rowId => {\n            selectionManager.select(rowId);\n          };\n          for (const id of selectableIds) {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          }\n        }\n      } else {\n        newSelectionModel.ids = new Set();\n      }\n      if (currentSelectionModel.type === newSelectionModel.type && newSelectionModel.ids.size === currentSelectionModel.ids.size && Array.from(newSelectionModel.ids).every(id => currentSelectionModel.ids.has(id))) {\n        return;\n      }\n    } else {\n      newSelectionModel = {\n        type: currentSelectionModel.type,\n        ids: new Set(currentSelectionModel.ids)\n      };\n      const selectionManager = createRowSelectionManager(newSelectionModel);\n      const addRow = rowId => {\n        selectionManager.select(rowId);\n      };\n      const removeRow = rowId => {\n        selectionManager.unselect(rowId);\n      };\n      for (const id of selectableIds) {\n        if (isSelected) {\n          selectionManager.select(id);\n          if (applyAutoSelection) {\n            findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow);\n          }\n        } else {\n          removeRow(id);\n          if (applyAutoSelection) {\n            findRowsToDeselect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, removeRow);\n          }\n        }\n      }\n    }\n    const isSelectionValid = newSelectionModel.type === 'include' && newSelectionModel.ids.size < 2 || canHaveMultipleSelection;\n    if (isSelectionValid) {\n      apiRef.current.setRowSelectionModel(newSelectionModel, 'multipleRowsSelection');\n    }\n  }, [logger, applyAutoSelection, canHaveMultipleSelection, apiRef, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, props.rowSelection]);\n  const getPropagatedRowSelectionModel = React.useCallback(inputSelectionModel => {\n    if (!isNestedData || !applyAutoSelection || inputSelectionModel.ids.size === 0 && inputSelectionModel.type === 'include') {\n      return inputSelectionModel;\n    }\n    const propagatedSelectionModel = {\n      type: inputSelectionModel.type,\n      ids: new Set(inputSelectionModel.ids)\n    };\n    const selectionManager = createRowSelectionManager(propagatedSelectionModel);\n    const addRow = rowId => {\n      selectionManager.select(rowId);\n    };\n    for (const id of inputSelectionModel.ids) {\n      findRowsToSelect(apiRef, tree, id, props.rowSelectionPropagation?.descendants ?? false, props.rowSelectionPropagation?.parents ?? false, addRow, selectionManager);\n    }\n    return propagatedSelectionModel;\n  }, [apiRef, tree, props.rowSelectionPropagation?.descendants, props.rowSelectionPropagation?.parents, isNestedData, applyAutoSelection]);\n  const selectRowRange = React.useCallback(({\n    startId,\n    endId\n  }, isSelected = true, resetSelection = false) => {\n    if (!apiRef.current.getRow(startId) || !apiRef.current.getRow(endId)) {\n      return;\n    }\n    logger.debug(`Expanding selection from row ${startId} to row ${endId}`);\n\n    // Using rows from all pages allow to select a range across several pages\n    const allPagesRowIds = gridExpandedSortedRowIdsSelector(apiRef);\n    const startIndex = allPagesRowIds.indexOf(startId);\n    const endIndex = allPagesRowIds.indexOf(endId);\n    const [start, end] = startIndex > endIndex ? [endIndex, startIndex] : [startIndex, endIndex];\n    const rowsBetweenStartAndEnd = allPagesRowIds.slice(start, end + 1);\n    apiRef.current.selectRows(rowsBetweenStartAndEnd, isSelected, resetSelection);\n  }, [apiRef, logger]);\n  const selectionPublicApi = {\n    selectRow,\n    setRowSelectionModel,\n    getSelectedRows,\n    isRowSelected,\n    isRowSelectable\n  };\n  const selectionPrivateApi = {\n    selectRows,\n    selectRowRange,\n    getPropagatedRowSelectionModel\n  };\n  useGridApiMethod(apiRef, selectionPublicApi, 'public');\n  useGridApiMethod(apiRef, selectionPrivateApi, props.signature === GridSignature.DataGrid ? 'private' : 'public');\n\n  /*\n   * EVENTS\n   */\n  const isFirstRender = React.useRef(true);\n  const removeOutdatedSelection = React.useCallback((sortModelUpdated = false) => {\n    if (isFirstRender.current) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    const rowsLookup = gridRowsLookupSelector(apiRef);\n    const rowTree = gridRowTreeSelector(apiRef);\n    const filteredRowsLookup = gridFilteredRowsLookupSelector(apiRef);\n    const isNonExistent = id => {\n      if (props.filterMode === 'server') {\n        return !rowsLookup[id];\n      }\n      return !rowTree[id] || filteredRowsLookup[id] === false;\n    };\n    const newSelectionModel = {\n      type: currentSelection.type,\n      ids: new Set(currentSelection.ids)\n    };\n    const selectionManager = createRowSelectionManager(newSelectionModel);\n    let hasChanged = false;\n    for (const id of currentSelection.ids) {\n      if (isNonExistent(id)) {\n        if (props.keepNonExistentRowsSelected) {\n          continue;\n        }\n        selectionManager.unselect(id);\n        hasChanged = true;\n        continue;\n      }\n      if (!props.rowSelectionPropagation?.parents) {\n        continue;\n      }\n      const node = tree[id];\n      if (node?.type === 'group') {\n        const isAutoGenerated = node.isAutoGenerated;\n        if (isAutoGenerated) {\n          selectionManager.unselect(id);\n          hasChanged = true;\n          continue;\n        }\n        // Keep previously selected tree data parents selected if all their children are filtered out\n        if (!node.children.every(childId => filteredRowsLookup[childId] === false)) {\n          selectionManager.unselect(id);\n          hasChanged = true;\n        }\n      }\n    }\n\n    // For nested data, on row tree updation (filtering, adding rows, etc.) when the selection is\n    // not empty, we need to re-run scanning of the tree to propagate the selection changes\n    // Example: A parent whose de-selected children are filtered out should now be selected\n    const shouldReapplyPropagation = isNestedData && props.rowSelectionPropagation?.parents && (newSelectionModel.ids.size > 0 ||\n    // In case of exclude selection, newSelectionModel.ids.size === 0 means all rows are selected\n    newSelectionModel.type === 'exclude');\n    if (hasChanged || shouldReapplyPropagation && !sortModelUpdated) {\n      if (shouldReapplyPropagation) {\n        if (newSelectionModel.type === 'exclude') {\n          const unfilteredSelectedRowIds = getRowsToBeSelected();\n          const selectedRowIds = [];\n          for (let i = 0; i < unfilteredSelectedRowIds.length; i += 1) {\n            const rowId = unfilteredSelectedRowIds[i];\n            if ((props.keepNonExistentRowsSelected || !isNonExistent(rowId)) && selectionManager.has(rowId)) {\n              selectedRowIds.push(rowId);\n            }\n          }\n          apiRef.current.selectRows(selectedRowIds, true, true);\n        } else {\n          apiRef.current.selectRows(Array.from(newSelectionModel.ids), true, true);\n        }\n      } else {\n        apiRef.current.setRowSelectionModel(newSelectionModel, 'multipleRowsSelection');\n      }\n    }\n  }, [apiRef, isNestedData, props.rowSelectionPropagation?.parents, props.keepNonExistentRowsSelected, props.filterMode, tree, getRowsToBeSelected]);\n  const handleSingleRowSelection = React.useCallback((id, event) => {\n    const hasCtrlKey = event.metaKey || event.ctrlKey;\n\n    // multiple selection is only allowed if:\n    // - it is a checkboxSelection\n    // - it is a keyboard selection\n    // - Ctrl is pressed\n\n    const isMultipleSelectionDisabled = !checkboxSelection && !hasCtrlKey && !isKeyboardEvent(event);\n    const resetSelection = !canHaveMultipleSelection || isMultipleSelectionDisabled;\n    const isSelected = apiRef.current.isRowSelected(id);\n    const selectedRowsCount = gridRowSelectionCountSelector(apiRef);\n\n    // Clicking on a row should toggle the selection except when a range of rows is already selected and the selection should reset\n    // In that case, we want to keep the current row selected (https://github.com/mui/mui-x/pull/15509#discussion_r1878082687)\n    const shouldStaySelected = selectedRowsCount > 1 && resetSelection;\n    const newSelectionState = shouldStaySelected || !isSelected;\n    apiRef.current.selectRow(id, newSelectionState, resetSelection);\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection]);\n  const handleRowClick = React.useCallback((params, event) => {\n    if (disableRowSelectionOnClick) {\n      return;\n    }\n    const field = event.target.closest(`.${gridClasses.cell}`)?.getAttribute('data-field');\n    if (field === GRID_CHECKBOX_SELECTION_COL_DEF.field) {\n      // click on checkbox should not trigger row selection\n      return;\n    }\n    if (field === GRID_DETAIL_PANEL_TOGGLE_FIELD) {\n      // click to open the detail panel should not select the row\n      return;\n    }\n    if (field) {\n      const column = apiRef.current.getColumn(field);\n      if (column?.type === GRID_ACTIONS_COLUMN_TYPE) {\n        return;\n      }\n    }\n    const rowNode = gridRowNodeSelector(apiRef, params.id);\n    if (rowNode.type === 'pinnedRow') {\n      return;\n    }\n    if (event.shiftKey && canHaveMultipleSelection) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      handleSingleRowSelection(params.id, event);\n    }\n  }, [disableRowSelectionOnClick, canHaveMultipleSelection, apiRef, expandMouseRowRangeSelection, handleSingleRowSelection]);\n  const preventSelectionOnShift = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.shiftKey) {\n      window.getSelection()?.removeAllRanges();\n    }\n  }, [canHaveMultipleSelection]);\n  const handleRowSelectionCheckboxChange = React.useCallback((params, event) => {\n    if (canHaveMultipleSelection && event.nativeEvent.shiftKey) {\n      expandMouseRowRangeSelection(params.id);\n    } else {\n      apiRef.current.selectRow(params.id, params.value, !canHaveMultipleSelection);\n    }\n  }, [apiRef, expandMouseRowRangeSelection, canHaveMultipleSelection]);\n  const toggleAllRows = React.useCallback(value => {\n    const filterModel = gridFilterModelSelector(apiRef);\n    const quickFilterModel = gridQuickFilterValuesSelector(apiRef);\n    const hasFilters = filterModel.items.length > 0 || quickFilterModel?.some(val => val.length);\n    if (!props.isRowSelectable && !props.checkboxSelectionVisibleOnly && (!isNestedData || props.rowSelectionPropagation?.descendants) && !hasFilters && !props.disableRowSelectionExcludeModel) {\n      apiRef.current.setRowSelectionModel({\n        type: value ? 'exclude' : 'include',\n        ids: new Set()\n      }, 'multipleRowsSelection');\n    } else {\n      apiRef.current.selectRows(getRowsToBeSelected(), value);\n    }\n  }, [apiRef, getRowsToBeSelected, props.checkboxSelectionVisibleOnly, props.isRowSelectable, props.rowSelectionPropagation?.descendants, props.disableRowSelectionExcludeModel, isNestedData]);\n  const handleHeaderSelectionCheckboxChange = React.useCallback(params => {\n    toggleAllRows(params.value);\n  }, [toggleAllRows]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    // Get the most recent cell mode because it may have been changed by another listener\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.Edit) {\n      return;\n    }\n\n    // Ignore portal\n    // Do not apply shortcuts if the focus is not on the cell root component\n    if (isEventTargetInPortal(event)) {\n      return;\n    }\n    if (isNavigationKey(event.key) && event.shiftKey) {\n      // The cell that has focus after the keyboard navigation\n      const focusCell = gridFocusCellSelector(apiRef);\n      if (focusCell && focusCell.id !== params.id) {\n        event.preventDefault();\n        const isNextRowSelected = apiRef.current.isRowSelected(focusCell.id);\n        if (!canHaveMultipleSelection) {\n          apiRef.current.selectRow(focusCell.id, !isNextRowSelected, true);\n          return;\n        }\n        const newRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(focusCell.id);\n        const previousRowIndex = apiRef.current.getRowIndexRelativeToVisibleRows(params.id);\n        let start;\n        let end;\n        if (newRowIndex > previousRowIndex) {\n          if (isNextRowSelected) {\n            // We are navigating to the bottom of the page and adding selected rows\n            start = previousRowIndex;\n            end = newRowIndex - 1;\n          } else {\n            // We are navigating to the bottom of the page and removing selected rows\n            start = previousRowIndex;\n            end = newRowIndex;\n          }\n        } else {\n          // eslint-disable-next-line no-lonely-if\n          if (isNextRowSelected) {\n            // We are navigating to the top of the page and removing selected rows\n            start = newRowIndex + 1;\n            end = previousRowIndex;\n          } else {\n            // We are navigating to the top of the page and adding selected rows\n            start = newRowIndex;\n            end = previousRowIndex;\n          }\n        }\n        const visibleRows = getVisibleRows(apiRef);\n        const rowsBetweenStartAndEnd = [];\n        for (let i = start; i <= end; i += 1) {\n          rowsBetweenStartAndEnd.push(visibleRows.rows[i].id);\n        }\n        apiRef.current.selectRows(rowsBetweenStartAndEnd, !isNextRowSelected);\n        return;\n      }\n    }\n    if (event.key === ' ' && event.shiftKey) {\n      event.preventDefault();\n      handleSingleRowSelection(params.id, event);\n      return;\n    }\n    if (String.fromCharCode(event.keyCode) === 'A' && (event.ctrlKey || event.metaKey)) {\n      event.preventDefault();\n      toggleAllRows(true);\n    }\n  }, [apiRef, canHaveMultipleSelection, handleSingleRowSelection, toggleAllRows]);\n  const syncControlledState = useEventCallback(() => {\n    if (!props.rowSelection) {\n      apiRef.current.setRowSelectionModel(emptyModel);\n      return;\n    }\n    if (propRowSelectionModel === undefined) {\n      return;\n    }\n    if (!applyAutoSelection || !isNestedData || propRowSelectionModel.type === 'include' && propRowSelectionModel.ids.size === 0) {\n      apiRef.current.setRowSelectionModel(propRowSelectionModel);\n      return;\n    }\n    const newSelectionModel = apiRef.current.getPropagatedRowSelectionModel(propRowSelectionModel);\n    if (newSelectionModel.type !== propRowSelectionModel.type || newSelectionModel.ids.size !== propRowSelectionModel.ids.size || !Array.from(propRowSelectionModel.ids).every(id => newSelectionModel.ids.has(id))) {\n      apiRef.current.setRowSelectionModel(newSelectionModel);\n      return;\n    }\n    apiRef.current.setRowSelectionModel(propRowSelectionModel);\n  });\n  useGridEvent(apiRef, 'sortedRowsSet', runIfRowSelectionIsEnabled(() => removeOutdatedSelection(true)));\n  useGridEvent(apiRef, 'filteredRowsSet', runIfRowSelectionIsEnabled(() => removeOutdatedSelection()));\n  useGridEvent(apiRef, 'rowClick', runIfRowSelectionIsEnabled(handleRowClick));\n  useGridEvent(apiRef, 'rowSelectionCheckboxChange', runIfRowSelectionIsEnabled(handleRowSelectionCheckboxChange));\n  useGridEvent(apiRef, 'headerSelectionCheckboxChange', handleHeaderSelectionCheckboxChange);\n  useGridEvent(apiRef, 'cellMouseDown', runIfRowSelectionIsEnabled(preventSelectionOnShift));\n  useGridEvent(apiRef, 'cellKeyDown', runIfRowSelectionIsEnabled(handleCellKeyDown));\n\n  /*\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    syncControlledState();\n  }, [apiRef, propRowSelectionModel, props.rowSelection, syncControlledState]);\n  const isStateControlled = propRowSelectionModel != null;\n  React.useEffect(() => {\n    if (isStateControlled || !props.rowSelection || typeof isRowSelectable !== 'function') {\n      return;\n    }\n\n    // props.isRowSelectable changed\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    if (currentSelection.type !== 'include') {\n      return;\n    }\n    const selectableIds = new Set();\n    for (const id of currentSelection.ids) {\n      if (isRowSelectable(id)) {\n        selectableIds.add(id);\n      }\n    }\n    if (selectableIds.size < currentSelection.ids.size) {\n      apiRef.current.setRowSelectionModel({\n        type: currentSelection.type,\n        ids: selectableIds\n      });\n    }\n  }, [apiRef, isRowSelectable, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    if (!props.rowSelection || isStateControlled) {\n      return;\n    }\n    const currentSelection = gridRowSelectionStateSelector(apiRef);\n    if (!canHaveMultipleSelection && (currentSelection.type === 'include' && currentSelection.ids.size > 1 || currentSelection.type === 'exclude')) {\n      // See https://github.com/mui/mui-x/issues/8455\n      apiRef.current.setRowSelectionModel(emptyModel);\n    }\n  }, [apiRef, canHaveMultipleSelection, checkboxSelection, isStateControlled, props.rowSelection]);\n  React.useEffect(() => {\n    runIfRowSelectionIsEnabled(removeOutdatedSelection);\n  }, [removeOutdatedSelection, runIfRowSelectionIsEnabled]);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    }\n  }, []);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,sBAAsB,EAAEC,+BAA+B,EAAEC,mBAAmB,EAAEC,mBAAmB,QAAQ,6BAA6B;AAC/I,SAASC,+BAA+B,EAAEC,6BAA6B,EAAEC,6BAA6B,EAAEC,2BAA2B,QAAQ,+BAA+B;AAC1K,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,gCAAgC,EAAEC,8BAA8B,EAAEC,uBAAuB,EAAEC,6BAA6B,QAAQ,iCAAiC;AAC1K,SAASC,+BAA+B,EAAEC,wBAAwB,QAAQ,0BAA0B;AACpG,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,eAAe,EAAEC,eAAe,QAAQ,iCAAiC;AAClF,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,8BAA8B,QAAQ,iCAAiC;AAChF,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,6BAA6B,EAAEC,gBAAgB,EAAEC,kBAAkB,QAAQ,YAAY;AAChG,SAASC,yBAAyB,QAAQ,4CAA4C;AACtF,SAASC,4CAA4C,QAAQ,wBAAwB;AACrF,MAAMC,UAAU,GAAG;EACjBC,IAAI,EAAE,SAAS;EACfC,GAAG,EAAE,IAAIC,GAAG,CAAC;AACf,CAAC;AACD,OAAO,MAAMC,4BAA4B,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAKzC,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;EAChFE,YAAY,EAAED,KAAK,CAACC,YAAY,GAAGD,KAAK,CAACE,iBAAiB,IAAIR,UAAU,GAAGA;AAC7E,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,mBAAmB,GAAGA,CAACC,MAAM,EAAEJ,KAAK,KAAK;EACpD,MAAMK,MAAM,GAAGxC,aAAa,CAACuC,MAAM,EAAE,kBAAkB,CAAC;EACxD,MAAME,0BAA0B,GAAG9C,KAAK,CAAC+C,WAAW,CAACC,QAAQ,IAAI,CAAC,GAAGC,IAAI,KAAK;IAC5E,IAAIT,KAAK,CAACC,YAAY,EAAE;MACtBO,QAAQ,CAAC,GAAGC,IAAI,CAAC;IACnB;EACF,CAAC,EAAE,CAACT,KAAK,CAACC,YAAY,CAAC,CAAC;EACxB,MAAMS,YAAY,GAAG5C,eAAe,CAACsC,MAAM,EAAEpC,+BAA+B,CAAC,GAAG,CAAC;EACjF,MAAM2C,kBAAkB,GAAGX,KAAK,CAACY,SAAS,KAAKlD,aAAa,CAACmD,QAAQ,KAAKb,KAAK,CAACc,uBAAuB,EAAEC,OAAO,IAAIf,KAAK,CAACc,uBAAuB,EAAEE,WAAW,CAAC,IAAIN,YAAY;EAC/K,MAAMO,qBAAqB,GAAGzD,KAAK,CAAC0D,OAAO,CAAC,MAAM;IAChD,OAAOlB,KAAK,CAACE,iBAAiB;EAChC,CAAC,EAAE,CAACF,KAAK,CAACE,iBAAiB,CAAC,CAAC;EAC7B,MAAMiB,cAAc,GAAG3D,KAAK,CAAC4D,MAAM,CAAC,IAAI,CAAC;EACzChB,MAAM,CAACiB,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,cAAc;IACvBC,SAAS,EAAEP,qBAAqB;IAChCQ,YAAY,EAAEzB,KAAK,CAAC0B,yBAAyB;IAC7CC,aAAa,EAAEvD,6BAA6B;IAC5CwD,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM;IACJC,iBAAiB;IACjBC,0BAA0B;IAC1BC,eAAe,EAAEC;EACnB,CAAC,GAAGhC,KAAK;EACT,MAAMiC,wBAAwB,GAAG5C,6BAA6B,CAACW,KAAK,CAAC;EACrE,MAAMkC,IAAI,GAAGpE,eAAe,CAACsC,MAAM,EAAElC,mBAAmB,CAAC;EACzD,MAAMiE,4BAA4B,GAAG3E,KAAK,CAAC+C,WAAW,CAAC6B,EAAE,IAAI;IAC3D,IAAIC,KAAK,GAAGD,EAAE;IACd,MAAME,OAAO,GAAGnB,cAAc,CAACE,OAAO,IAAIe,EAAE;IAC5C,MAAMG,UAAU,GAAGnC,MAAM,CAACiB,OAAO,CAACmB,aAAa,CAACJ,EAAE,CAAC;IACnD,IAAIG,UAAU,EAAE;MACd,MAAME,aAAa,GAAGjE,gCAAgC,CAAC4B,MAAM,CAAC;MAC9D,MAAMsC,UAAU,GAAGD,aAAa,CAACE,SAAS,CAACC,KAAK,IAAIA,KAAK,KAAKN,OAAO,CAAC;MACtE,MAAMO,QAAQ,GAAGJ,aAAa,CAACE,SAAS,CAACC,KAAK,IAAIA,KAAK,KAAKP,KAAK,CAAC;MAClE,IAAIK,UAAU,KAAKG,QAAQ,EAAE;QAC3B;MACF;MACA,IAAIH,UAAU,GAAGG,QAAQ,EAAE;QACzBR,KAAK,GAAGI,aAAa,CAACI,QAAQ,GAAG,CAAC,CAAC;MACrC,CAAC,MAAM;QACLR,KAAK,GAAGI,aAAa,CAACI,QAAQ,GAAG,CAAC,CAAC;MACrC;IACF;IACA1B,cAAc,CAACE,OAAO,GAAGe,EAAE;IAC3BhC,MAAM,CAACiB,OAAO,CAACyB,cAAc,CAAC;MAC5BR,OAAO;MACPD;IACF,CAAC,EAAE,CAACE,UAAU,CAAC;EACjB,CAAC,EAAE,CAACnC,MAAM,CAAC,CAAC;EACZ,MAAM2C,mBAAmB,GAAGtF,gBAAgB,CAAC,MAAM;IACjD,MAAMuF,gBAAgB,GAAGhD,KAAK,CAACiD,UAAU,IAAIjD,KAAK,CAACkD,4BAA4B,IAAIlD,KAAK,CAACmD,cAAc,KAAK,QAAQ,GAAG1D,4CAA4C,CAACW,MAAM,CAAC,GAAG5B,gCAAgC,CAAC4B,MAAM,CAAC;IACtN,OAAO4C,gBAAgB;EACzB,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAMI,oBAAoB,GAAG5F,KAAK,CAAC+C,WAAW,CAAC,CAAC8C,KAAK,EAAEC,MAAM,KAAK;IAChE,IAAItD,KAAK,CAACY,SAAS,KAAKlD,aAAa,CAACmD,QAAQ,IAAI,CAACoB,wBAAwB,KAAKoB,KAAK,CAAC1D,IAAI,KAAK,SAAS,IAAI0D,KAAK,CAACzD,GAAG,CAAC2D,IAAI,GAAG,CAAC,CAAC,EAAE;MAC/H,MAAM,IAAIC,KAAK,CAAC,CAAC,iEAAiE,EAAE,+FAA+F,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClM;IACA,MAAMC,YAAY,GAAGtF,6BAA6B,CAACgC,MAAM,CAAC;IAC1D,IAAIsD,YAAY,KAAKL,KAAK,EAAE;MAC1BhD,MAAM,CAACsD,KAAK,CAAC,yBAAyB,CAAC;MACvCvD,MAAM,CAACiB,OAAO,CAACuC,QAAQ,CAAC7D,KAAK,IAAIxC,QAAQ,CAAC,CAAC,CAAC,EAAEwC,KAAK,EAAE;QACnDE,YAAY,EAAED,KAAK,CAACC,YAAY,GAAGoD,KAAK,GAAG3D;MAC7C,CAAC,CAAC,EAAE4D,MAAM,CAAC;IACb;EACF,CAAC,EAAE,CAAClD,MAAM,EAAEC,MAAM,EAAEL,KAAK,CAACC,YAAY,EAAED,KAAK,CAACY,SAAS,EAAEqB,wBAAwB,CAAC,CAAC;EACnF,MAAMO,aAAa,GAAGhF,KAAK,CAAC+C,WAAW,CAAC6B,EAAE,IAAI;IAC5C,MAAMyB,gBAAgB,GAAG1F,+BAA+B,CAACiC,MAAM,CAAC;IAChE,OAAOyD,gBAAgB,CAACC,GAAG,CAAC1B,EAAE,CAAC;EACjC,CAAC,EAAE,CAAChC,MAAM,CAAC,CAAC;EACZ,MAAM2B,eAAe,GAAGvE,KAAK,CAAC+C,WAAW,CAAC6B,EAAE,IAAI;IAC9C,IAAIpC,KAAK,CAACC,YAAY,KAAK,KAAK,EAAE;MAChC,OAAO,KAAK;IACd;IACA,IAAI+B,mBAAmB,IAAI,CAACA,mBAAmB,CAAC5B,MAAM,CAACiB,OAAO,CAAC0C,YAAY,CAAC3B,EAAE,CAAC,CAAC,EAAE;MAChF,OAAO,KAAK;IACd;IACA,MAAM4B,OAAO,GAAG/F,mBAAmB,CAACmC,MAAM,EAAEgC,EAAE,CAAC;IAC/C,IAAI4B,OAAO,EAAErE,IAAI,KAAK,QAAQ,IAAIqE,OAAO,EAAErE,IAAI,KAAK,WAAW,EAAE;MAC/D,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACS,MAAM,EAAEJ,KAAK,CAACC,YAAY,EAAE+B,mBAAmB,CAAC,CAAC;EACrD,MAAMiC,eAAe,GAAGzG,KAAK,CAAC+C,WAAW,CAAC,MAAMjC,2BAA2B,CAAC8B,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC9F,MAAM8D,SAAS,GAAG1G,KAAK,CAAC+C,WAAW,CAAC,CAAC6B,EAAE,EAAEG,UAAU,GAAG,IAAI,EAAE4B,cAAc,GAAG,KAAK,KAAK;IACrF,IAAI,CAAC/D,MAAM,CAACiB,OAAO,CAACU,eAAe,CAACK,EAAE,CAAC,EAAE;MACvC;IACF;IACAjB,cAAc,CAACE,OAAO,GAAGe,EAAE;IAC3B,IAAI+B,cAAc,EAAE;MAClB9D,MAAM,CAACsD,KAAK,CAAC,6BAA6BvB,EAAE,EAAE,CAAC;MAC/C,MAAMgC,iBAAiB,GAAG;QACxBzE,IAAI,EAAE,SAAS;QACfC,GAAG,EAAE,IAAIC,GAAG,CAAC;MACf,CAAC;MACD,MAAMwE,MAAM,GAAGzB,KAAK,IAAI;QACtBwB,iBAAiB,CAACxE,GAAG,CAAC0E,GAAG,CAAC1B,KAAK,CAAC;MAClC,CAAC;MACD,IAAIL,UAAU,EAAE;QACd8B,MAAM,CAACjC,EAAE,CAAC;QACV,IAAIzB,kBAAkB,EAAE;UACtBrB,gBAAgB,CAACc,MAAM,EAAE8B,IAAI,EAAEE,EAAE,EAAEpC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEsD,MAAM,CAAC;QAClJ;MACF;MACAjE,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAACgB,iBAAiB,EAAE,oBAAoB,CAAC;IAC9E,CAAC,MAAM;MACL/D,MAAM,CAACsD,KAAK,CAAC,8BAA8BvB,EAAE,EAAE,CAAC;MAChD,MAAMmC,cAAc,GAAGnG,6BAA6B,CAACgC,MAAM,CAAC;MAC5D,MAAMgE,iBAAiB,GAAG;QACxBzE,IAAI,EAAE4E,cAAc,CAAC5E,IAAI;QACzBC,GAAG,EAAE,IAAIC,GAAG,CAAC0E,cAAc,CAAC3E,GAAG;MACjC,CAAC;MACD,MAAMiE,gBAAgB,GAAGrE,yBAAyB,CAAC4E,iBAAiB,CAAC;MACrEP,gBAAgB,CAACW,QAAQ,CAACpC,EAAE,CAAC;MAC7B,MAAMiC,MAAM,GAAGzB,KAAK,IAAI;QACtBiB,gBAAgB,CAACY,MAAM,CAAC7B,KAAK,CAAC;MAChC,CAAC;MACD,MAAM8B,SAAS,GAAG9B,KAAK,IAAI;QACzBiB,gBAAgB,CAACW,QAAQ,CAAC5B,KAAK,CAAC;MAClC,CAAC;MACD,IAAIL,UAAU,EAAE;QACd8B,MAAM,CAACjC,EAAE,CAAC;QACV,IAAIzB,kBAAkB,EAAE;UACtBrB,gBAAgB,CAACc,MAAM,EAAE8B,IAAI,EAAEE,EAAE,EAAEpC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEsD,MAAM,CAAC;QAClJ;MACF,CAAC,MAAM,IAAI1D,kBAAkB,EAAE;QAC7BpB,kBAAkB,CAACa,MAAM,EAAE8B,IAAI,EAAEE,EAAE,EAAEpC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAE2D,SAAS,CAAC;MACvJ;MACA,MAAMC,gBAAgB,GAAGP,iBAAiB,CAACzE,IAAI,KAAK,SAAS,IAAIyE,iBAAiB,CAACxE,GAAG,CAAC2D,IAAI,GAAG,CAAC,IAAItB,wBAAwB;MAC3H,IAAI0C,gBAAgB,EAAE;QACpBvE,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAACgB,iBAAiB,EAAE,oBAAoB,CAAC;MAC9E;IACF;EACF,CAAC,EAAE,CAAChE,MAAM,EAAEC,MAAM,EAAEM,kBAAkB,EAAEuB,IAAI,EAAElC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,EAAEkB,wBAAwB,CAAC,CAAC;EAC5J,MAAM2C,UAAU,GAAGpH,KAAK,CAAC+C,WAAW,CAAC,CAACX,GAAG,EAAE2C,UAAU,GAAG,IAAI,EAAE4B,cAAc,GAAG,KAAK,KAAK;IACvF9D,MAAM,CAACsD,KAAK,CAAC,oCAAoC,CAAC;IAClD,IAAI3D,KAAK,CAACC,YAAY,KAAK,KAAK,EAAE;MAChC;IACF;IACA,MAAM4E,aAAa,GAAG,IAAIhF,GAAG,CAAC,CAAC;IAC/B,KAAK,IAAIiF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlF,GAAG,CAACmF,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACtC,MAAM1C,EAAE,GAAGxC,GAAG,CAACkF,CAAC,CAAC;MACjB,IAAI1E,MAAM,CAACiB,OAAO,CAACU,eAAe,CAACK,EAAE,CAAC,EAAE;QACtCyC,aAAa,CAACP,GAAG,CAAClC,EAAE,CAAC;MACvB;IACF;IACA,MAAM4C,qBAAqB,GAAG5G,6BAA6B,CAACgC,MAAM,CAAC;IACnE,IAAIgE,iBAAiB;IACrB,IAAID,cAAc,EAAE;MAClBC,iBAAiB,GAAG;QAClBzE,IAAI,EAAE,SAAS;QACfC,GAAG,EAAEiF;MACP,CAAC;MACD,IAAItC,UAAU,EAAE;QACd,MAAMsB,gBAAgB,GAAGrE,yBAAyB,CAAC4E,iBAAiB,CAAC;QACrE,IAAIzD,kBAAkB,EAAE;UACtB,MAAM0D,MAAM,GAAGzB,KAAK,IAAI;YACtBiB,gBAAgB,CAACY,MAAM,CAAC7B,KAAK,CAAC;UAChC,CAAC;UACD,KAAK,MAAMR,EAAE,IAAIyC,aAAa,EAAE;YAC9BvF,gBAAgB,CAACc,MAAM,EAAE8B,IAAI,EAAEE,EAAE,EAAEpC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEsD,MAAM,CAAC;UAClJ;QACF;MACF,CAAC,MAAM;QACLD,iBAAiB,CAACxE,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;MACnC;MACA,IAAImF,qBAAqB,CAACrF,IAAI,KAAKyE,iBAAiB,CAACzE,IAAI,IAAIyE,iBAAiB,CAACxE,GAAG,CAAC2D,IAAI,KAAKyB,qBAAqB,CAACpF,GAAG,CAAC2D,IAAI,IAAI0B,KAAK,CAACC,IAAI,CAACd,iBAAiB,CAACxE,GAAG,CAAC,CAACuF,KAAK,CAAC/C,EAAE,IAAI4C,qBAAqB,CAACpF,GAAG,CAACkE,GAAG,CAAC1B,EAAE,CAAC,CAAC,EAAE;QAC9M;MACF;IACF,CAAC,MAAM;MACLgC,iBAAiB,GAAG;QAClBzE,IAAI,EAAEqF,qBAAqB,CAACrF,IAAI;QAChCC,GAAG,EAAE,IAAIC,GAAG,CAACmF,qBAAqB,CAACpF,GAAG;MACxC,CAAC;MACD,MAAMiE,gBAAgB,GAAGrE,yBAAyB,CAAC4E,iBAAiB,CAAC;MACrE,MAAMC,MAAM,GAAGzB,KAAK,IAAI;QACtBiB,gBAAgB,CAACY,MAAM,CAAC7B,KAAK,CAAC;MAChC,CAAC;MACD,MAAM8B,SAAS,GAAG9B,KAAK,IAAI;QACzBiB,gBAAgB,CAACW,QAAQ,CAAC5B,KAAK,CAAC;MAClC,CAAC;MACD,KAAK,MAAMR,EAAE,IAAIyC,aAAa,EAAE;QAC9B,IAAItC,UAAU,EAAE;UACdsB,gBAAgB,CAACY,MAAM,CAACrC,EAAE,CAAC;UAC3B,IAAIzB,kBAAkB,EAAE;YACtBrB,gBAAgB,CAACc,MAAM,EAAE8B,IAAI,EAAEE,EAAE,EAAEpC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEsD,MAAM,CAAC;UAClJ;QACF,CAAC,MAAM;UACLK,SAAS,CAACtC,EAAE,CAAC;UACb,IAAIzB,kBAAkB,EAAE;YACtBpB,kBAAkB,CAACa,MAAM,EAAE8B,IAAI,EAAEE,EAAE,EAAEpC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAE2D,SAAS,CAAC;UACvJ;QACF;MACF;IACF;IACA,MAAMC,gBAAgB,GAAGP,iBAAiB,CAACzE,IAAI,KAAK,SAAS,IAAIyE,iBAAiB,CAACxE,GAAG,CAAC2D,IAAI,GAAG,CAAC,IAAItB,wBAAwB;IAC3H,IAAI0C,gBAAgB,EAAE;MACpBvE,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAACgB,iBAAiB,EAAE,uBAAuB,CAAC;IACjF;EACF,CAAC,EAAE,CAAC/D,MAAM,EAAEM,kBAAkB,EAAEsB,wBAAwB,EAAE7B,MAAM,EAAE8B,IAAI,EAAElC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,EAAEf,KAAK,CAACC,YAAY,CAAC,CAAC;EAChL,MAAMmF,8BAA8B,GAAG5H,KAAK,CAAC+C,WAAW,CAAC8E,mBAAmB,IAAI;IAC9E,IAAI,CAAC3E,YAAY,IAAI,CAACC,kBAAkB,IAAI0E,mBAAmB,CAACzF,GAAG,CAAC2D,IAAI,KAAK,CAAC,IAAI8B,mBAAmB,CAAC1F,IAAI,KAAK,SAAS,EAAE;MACxH,OAAO0F,mBAAmB;IAC5B;IACA,MAAMC,wBAAwB,GAAG;MAC/B3F,IAAI,EAAE0F,mBAAmB,CAAC1F,IAAI;MAC9BC,GAAG,EAAE,IAAIC,GAAG,CAACwF,mBAAmB,CAACzF,GAAG;IACtC,CAAC;IACD,MAAMiE,gBAAgB,GAAGrE,yBAAyB,CAAC8F,wBAAwB,CAAC;IAC5E,MAAMjB,MAAM,GAAGzB,KAAK,IAAI;MACtBiB,gBAAgB,CAACY,MAAM,CAAC7B,KAAK,CAAC;IAChC,CAAC;IACD,KAAK,MAAMR,EAAE,IAAIiD,mBAAmB,CAACzF,GAAG,EAAE;MACxCN,gBAAgB,CAACc,MAAM,EAAE8B,IAAI,EAAEE,EAAE,EAAEpC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,IAAI,KAAK,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,IAAI,KAAK,EAAEsD,MAAM,EAAER,gBAAgB,CAAC;IACpK;IACA,OAAOyB,wBAAwB;EACjC,CAAC,EAAE,CAAClF,MAAM,EAAE8B,IAAI,EAAElC,KAAK,CAACc,uBAAuB,EAAEE,WAAW,EAAEhB,KAAK,CAACc,uBAAuB,EAAEC,OAAO,EAAEL,YAAY,EAAEC,kBAAkB,CAAC,CAAC;EACxI,MAAMmC,cAAc,GAAGtF,KAAK,CAAC+C,WAAW,CAAC,CAAC;IACxC+B,OAAO;IACPD;EACF,CAAC,EAAEE,UAAU,GAAG,IAAI,EAAE4B,cAAc,GAAG,KAAK,KAAK;IAC/C,IAAI,CAAC/D,MAAM,CAACiB,OAAO,CAACkE,MAAM,CAACjD,OAAO,CAAC,IAAI,CAAClC,MAAM,CAACiB,OAAO,CAACkE,MAAM,CAAClD,KAAK,CAAC,EAAE;MACpE;IACF;IACAhC,MAAM,CAACsD,KAAK,CAAC,gCAAgCrB,OAAO,WAAWD,KAAK,EAAE,CAAC;;IAEvE;IACA,MAAMmD,cAAc,GAAGhH,gCAAgC,CAAC4B,MAAM,CAAC;IAC/D,MAAMsC,UAAU,GAAG8C,cAAc,CAACC,OAAO,CAACnD,OAAO,CAAC;IAClD,MAAMO,QAAQ,GAAG2C,cAAc,CAACC,OAAO,CAACpD,KAAK,CAAC;IAC9C,MAAM,CAACqD,KAAK,EAAEC,GAAG,CAAC,GAAGjD,UAAU,GAAGG,QAAQ,GAAG,CAACA,QAAQ,EAAEH,UAAU,CAAC,GAAG,CAACA,UAAU,EAAEG,QAAQ,CAAC;IAC5F,MAAM+C,sBAAsB,GAAGJ,cAAc,CAACK,KAAK,CAACH,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC;IACnEvF,MAAM,CAACiB,OAAO,CAACuD,UAAU,CAACgB,sBAAsB,EAAErD,UAAU,EAAE4B,cAAc,CAAC;EAC/E,CAAC,EAAE,CAAC/D,MAAM,EAAEC,MAAM,CAAC,CAAC;EACpB,MAAMyF,kBAAkB,GAAG;IACzB5B,SAAS;IACTd,oBAAoB;IACpBa,eAAe;IACfzB,aAAa;IACbT;EACF,CAAC;EACD,MAAMgE,mBAAmB,GAAG;IAC1BnB,UAAU;IACV9B,cAAc;IACdsC;EACF,CAAC;EACDxH,gBAAgB,CAACwC,MAAM,EAAE0F,kBAAkB,EAAE,QAAQ,CAAC;EACtDlI,gBAAgB,CAACwC,MAAM,EAAE2F,mBAAmB,EAAE/F,KAAK,CAACY,SAAS,KAAKlD,aAAa,CAACmD,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;;EAEhH;AACF;AACA;EACE,MAAMmF,aAAa,GAAGxI,KAAK,CAAC4D,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM6E,uBAAuB,GAAGzI,KAAK,CAAC+C,WAAW,CAAC,CAAC2F,gBAAgB,GAAG,KAAK,KAAK;IAC9E,IAAIF,aAAa,CAAC3E,OAAO,EAAE;MACzB;IACF;IACA,MAAM8E,gBAAgB,GAAG/H,6BAA6B,CAACgC,MAAM,CAAC;IAC9D,MAAMgG,UAAU,GAAGrI,sBAAsB,CAACqC,MAAM,CAAC;IACjD,MAAMiG,OAAO,GAAGnI,mBAAmB,CAACkC,MAAM,CAAC;IAC3C,MAAMkG,kBAAkB,GAAG7H,8BAA8B,CAAC2B,MAAM,CAAC;IACjE,MAAMmG,aAAa,GAAGnE,EAAE,IAAI;MAC1B,IAAIpC,KAAK,CAACwG,UAAU,KAAK,QAAQ,EAAE;QACjC,OAAO,CAACJ,UAAU,CAAChE,EAAE,CAAC;MACxB;MACA,OAAO,CAACiE,OAAO,CAACjE,EAAE,CAAC,IAAIkE,kBAAkB,CAAClE,EAAE,CAAC,KAAK,KAAK;IACzD,CAAC;IACD,MAAMgC,iBAAiB,GAAG;MACxBzE,IAAI,EAAEwG,gBAAgB,CAACxG,IAAI;MAC3BC,GAAG,EAAE,IAAIC,GAAG,CAACsG,gBAAgB,CAACvG,GAAG;IACnC,CAAC;IACD,MAAMiE,gBAAgB,GAAGrE,yBAAyB,CAAC4E,iBAAiB,CAAC;IACrE,IAAIqC,UAAU,GAAG,KAAK;IACtB,KAAK,MAAMrE,EAAE,IAAI+D,gBAAgB,CAACvG,GAAG,EAAE;MACrC,IAAI2G,aAAa,CAACnE,EAAE,CAAC,EAAE;QACrB,IAAIpC,KAAK,CAAC0G,2BAA2B,EAAE;UACrC;QACF;QACA7C,gBAAgB,CAACW,QAAQ,CAACpC,EAAE,CAAC;QAC7BqE,UAAU,GAAG,IAAI;QACjB;MACF;MACA,IAAI,CAACzG,KAAK,CAACc,uBAAuB,EAAEC,OAAO,EAAE;QAC3C;MACF;MACA,MAAM4F,IAAI,GAAGzE,IAAI,CAACE,EAAE,CAAC;MACrB,IAAIuE,IAAI,EAAEhH,IAAI,KAAK,OAAO,EAAE;QAC1B,MAAMiH,eAAe,GAAGD,IAAI,CAACC,eAAe;QAC5C,IAAIA,eAAe,EAAE;UACnB/C,gBAAgB,CAACW,QAAQ,CAACpC,EAAE,CAAC;UAC7BqE,UAAU,GAAG,IAAI;UACjB;QACF;QACA;QACA,IAAI,CAACE,IAAI,CAACE,QAAQ,CAAC1B,KAAK,CAAC2B,OAAO,IAAIR,kBAAkB,CAACQ,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;UAC1EjD,gBAAgB,CAACW,QAAQ,CAACpC,EAAE,CAAC;UAC7BqE,UAAU,GAAG,IAAI;QACnB;MACF;IACF;;IAEA;IACA;IACA;IACA,MAAMM,wBAAwB,GAAGrG,YAAY,IAAIV,KAAK,CAACc,uBAAuB,EAAEC,OAAO,KAAKqD,iBAAiB,CAACxE,GAAG,CAAC2D,IAAI,GAAG,CAAC;IAC1H;IACAa,iBAAiB,CAACzE,IAAI,KAAK,SAAS,CAAC;IACrC,IAAI8G,UAAU,IAAIM,wBAAwB,IAAI,CAACb,gBAAgB,EAAE;MAC/D,IAAIa,wBAAwB,EAAE;QAC5B,IAAI3C,iBAAiB,CAACzE,IAAI,KAAK,SAAS,EAAE;UACxC,MAAMqH,wBAAwB,GAAGjE,mBAAmB,CAAC,CAAC;UACtD,MAAMkE,cAAc,GAAG,EAAE;UACzB,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,wBAAwB,CAACjC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;YAC3D,MAAMlC,KAAK,GAAGoE,wBAAwB,CAAClC,CAAC,CAAC;YACzC,IAAI,CAAC9E,KAAK,CAAC0G,2BAA2B,IAAI,CAACH,aAAa,CAAC3D,KAAK,CAAC,KAAKiB,gBAAgB,CAACC,GAAG,CAAClB,KAAK,CAAC,EAAE;cAC/FqE,cAAc,CAACC,IAAI,CAACtE,KAAK,CAAC;YAC5B;UACF;UACAxC,MAAM,CAACiB,OAAO,CAACuD,UAAU,CAACqC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC;QACvD,CAAC,MAAM;UACL7G,MAAM,CAACiB,OAAO,CAACuD,UAAU,CAACK,KAAK,CAACC,IAAI,CAACd,iBAAiB,CAACxE,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;QAC1E;MACF,CAAC,MAAM;QACLQ,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAACgB,iBAAiB,EAAE,uBAAuB,CAAC;MACjF;IACF;EACF,CAAC,EAAE,CAAChE,MAAM,EAAEM,YAAY,EAAEV,KAAK,CAACc,uBAAuB,EAAEC,OAAO,EAAEf,KAAK,CAAC0G,2BAA2B,EAAE1G,KAAK,CAACwG,UAAU,EAAEtE,IAAI,EAAEa,mBAAmB,CAAC,CAAC;EAClJ,MAAMoE,wBAAwB,GAAG3J,KAAK,CAAC+C,WAAW,CAAC,CAAC6B,EAAE,EAAEgF,KAAK,KAAK;IAChE,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO,IAAIF,KAAK,CAACG,OAAO;;IAEjD;IACA;IACA;IACA;;IAEA,MAAMC,2BAA2B,GAAG,CAAC3F,iBAAiB,IAAI,CAACwF,UAAU,IAAI,CAACtI,eAAe,CAACqI,KAAK,CAAC;IAChG,MAAMjD,cAAc,GAAG,CAAClC,wBAAwB,IAAIuF,2BAA2B;IAC/E,MAAMjF,UAAU,GAAGnC,MAAM,CAACiB,OAAO,CAACmB,aAAa,CAACJ,EAAE,CAAC;IACnD,MAAMqF,iBAAiB,GAAGpJ,6BAA6B,CAAC+B,MAAM,CAAC;;IAE/D;IACA;IACA,MAAMsH,kBAAkB,GAAGD,iBAAiB,GAAG,CAAC,IAAItD,cAAc;IAClE,MAAMwD,iBAAiB,GAAGD,kBAAkB,IAAI,CAACnF,UAAU;IAC3DnC,MAAM,CAACiB,OAAO,CAAC6C,SAAS,CAAC9B,EAAE,EAAEuF,iBAAiB,EAAExD,cAAc,CAAC;EACjE,CAAC,EAAE,CAAC/D,MAAM,EAAE6B,wBAAwB,EAAEJ,iBAAiB,CAAC,CAAC;EACzD,MAAM+F,cAAc,GAAGpK,KAAK,CAAC+C,WAAW,CAAC,CAACsH,MAAM,EAAET,KAAK,KAAK;IAC1D,IAAItF,0BAA0B,EAAE;MAC9B;IACF;IACA,MAAMgG,KAAK,GAAGV,KAAK,CAACW,MAAM,CAACC,OAAO,CAAC,IAAI7I,WAAW,CAAC8I,IAAI,EAAE,CAAC,EAAEC,YAAY,CAAC,YAAY,CAAC;IACtF,IAAIJ,KAAK,KAAKlJ,+BAA+B,CAACkJ,KAAK,EAAE;MACnD;MACA;IACF;IACA,IAAIA,KAAK,KAAK5I,8BAA8B,EAAE;MAC5C;MACA;IACF;IACA,IAAI4I,KAAK,EAAE;MACT,MAAMK,MAAM,GAAG/H,MAAM,CAACiB,OAAO,CAAC+G,SAAS,CAACN,KAAK,CAAC;MAC9C,IAAIK,MAAM,EAAExI,IAAI,KAAKd,wBAAwB,EAAE;QAC7C;MACF;IACF;IACA,MAAMmF,OAAO,GAAG/F,mBAAmB,CAACmC,MAAM,EAAEyH,MAAM,CAACzF,EAAE,CAAC;IACtD,IAAI4B,OAAO,CAACrE,IAAI,KAAK,WAAW,EAAE;MAChC;IACF;IACA,IAAIyH,KAAK,CAACiB,QAAQ,IAAIpG,wBAAwB,EAAE;MAC9CE,4BAA4B,CAAC0F,MAAM,CAACzF,EAAE,CAAC;IACzC,CAAC,MAAM;MACL+E,wBAAwB,CAACU,MAAM,CAACzF,EAAE,EAAEgF,KAAK,CAAC;IAC5C;EACF,CAAC,EAAE,CAACtF,0BAA0B,EAAEG,wBAAwB,EAAE7B,MAAM,EAAE+B,4BAA4B,EAAEgF,wBAAwB,CAAC,CAAC;EAC1H,MAAMmB,uBAAuB,GAAG9K,KAAK,CAAC+C,WAAW,CAAC,CAACsH,MAAM,EAAET,KAAK,KAAK;IACnE,IAAInF,wBAAwB,IAAImF,KAAK,CAACiB,QAAQ,EAAE;MAC9CE,MAAM,CAACC,YAAY,CAAC,CAAC,EAAEC,eAAe,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE,CAACxG,wBAAwB,CAAC,CAAC;EAC9B,MAAMyG,gCAAgC,GAAGlL,KAAK,CAAC+C,WAAW,CAAC,CAACsH,MAAM,EAAET,KAAK,KAAK;IAC5E,IAAInF,wBAAwB,IAAImF,KAAK,CAACuB,WAAW,CAACN,QAAQ,EAAE;MAC1DlG,4BAA4B,CAAC0F,MAAM,CAACzF,EAAE,CAAC;IACzC,CAAC,MAAM;MACLhC,MAAM,CAACiB,OAAO,CAAC6C,SAAS,CAAC2D,MAAM,CAACzF,EAAE,EAAEyF,MAAM,CAACe,KAAK,EAAE,CAAC3G,wBAAwB,CAAC;IAC9E;EACF,CAAC,EAAE,CAAC7B,MAAM,EAAE+B,4BAA4B,EAAEF,wBAAwB,CAAC,CAAC;EACpE,MAAM4G,aAAa,GAAGrL,KAAK,CAAC+C,WAAW,CAACqI,KAAK,IAAI;IAC/C,MAAME,WAAW,GAAGpK,uBAAuB,CAAC0B,MAAM,CAAC;IACnD,MAAM2I,gBAAgB,GAAGpK,6BAA6B,CAACyB,MAAM,CAAC;IAC9D,MAAM4I,UAAU,GAAGF,WAAW,CAACG,KAAK,CAAClE,MAAM,GAAG,CAAC,IAAIgE,gBAAgB,EAAEG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACpE,MAAM,CAAC;IAC5F,IAAI,CAAC/E,KAAK,CAAC+B,eAAe,IAAI,CAAC/B,KAAK,CAACkD,4BAA4B,KAAK,CAACxC,YAAY,IAAIV,KAAK,CAACc,uBAAuB,EAAEE,WAAW,CAAC,IAAI,CAACgI,UAAU,IAAI,CAAChJ,KAAK,CAACoJ,+BAA+B,EAAE;MAC3LhJ,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAAC;QAClCzD,IAAI,EAAEiJ,KAAK,GAAG,SAAS,GAAG,SAAS;QACnChJ,GAAG,EAAE,IAAIC,GAAG,CAAC;MACf,CAAC,EAAE,uBAAuB,CAAC;IAC7B,CAAC,MAAM;MACLO,MAAM,CAACiB,OAAO,CAACuD,UAAU,CAAC7B,mBAAmB,CAAC,CAAC,EAAE6F,KAAK,CAAC;IACzD;EACF,CAAC,EAAE,CAACxI,MAAM,EAAE2C,mBAAmB,EAAE/C,KAAK,CAACkD,4BAA4B,EAAElD,KAAK,CAAC+B,eAAe,EAAE/B,KAAK,CAACc,uBAAuB,EAAEE,WAAW,EAAEhB,KAAK,CAACoJ,+BAA+B,EAAE1I,YAAY,CAAC,CAAC;EAC7L,MAAM2I,mCAAmC,GAAG7L,KAAK,CAAC+C,WAAW,CAACsH,MAAM,IAAI;IACtEgB,aAAa,CAAChB,MAAM,CAACe,KAAK,CAAC;EAC7B,CAAC,EAAE,CAACC,aAAa,CAAC,CAAC;EACnB,MAAMS,iBAAiB,GAAG9L,KAAK,CAAC+C,WAAW,CAAC,CAACsH,MAAM,EAAET,KAAK,KAAK;IAC7D;IACA,IAAIhH,MAAM,CAACiB,OAAO,CAACkI,WAAW,CAAC1B,MAAM,CAACzF,EAAE,EAAEyF,MAAM,CAACC,KAAK,CAAC,KAAKhJ,aAAa,CAAC0K,IAAI,EAAE;MAC9E;IACF;;IAEA;IACA;IACA,IAAIpK,qBAAqB,CAACgI,KAAK,CAAC,EAAE;MAChC;IACF;IACA,IAAIpI,eAAe,CAACoI,KAAK,CAACqC,GAAG,CAAC,IAAIrC,KAAK,CAACiB,QAAQ,EAAE;MAChD;MACA,MAAMqB,SAAS,GAAGnL,qBAAqB,CAAC6B,MAAM,CAAC;MAC/C,IAAIsJ,SAAS,IAAIA,SAAS,CAACtH,EAAE,KAAKyF,MAAM,CAACzF,EAAE,EAAE;QAC3CgF,KAAK,CAACuC,cAAc,CAAC,CAAC;QACtB,MAAMC,iBAAiB,GAAGxJ,MAAM,CAACiB,OAAO,CAACmB,aAAa,CAACkH,SAAS,CAACtH,EAAE,CAAC;QACpE,IAAI,CAACH,wBAAwB,EAAE;UAC7B7B,MAAM,CAACiB,OAAO,CAAC6C,SAAS,CAACwF,SAAS,CAACtH,EAAE,EAAE,CAACwH,iBAAiB,EAAE,IAAI,CAAC;UAChE;QACF;QACA,MAAMC,WAAW,GAAGzJ,MAAM,CAACiB,OAAO,CAACyI,gCAAgC,CAACJ,SAAS,CAACtH,EAAE,CAAC;QACjF,MAAM2H,gBAAgB,GAAG3J,MAAM,CAACiB,OAAO,CAACyI,gCAAgC,CAACjC,MAAM,CAACzF,EAAE,CAAC;QACnF,IAAIsD,KAAK;QACT,IAAIC,GAAG;QACP,IAAIkE,WAAW,GAAGE,gBAAgB,EAAE;UAClC,IAAIH,iBAAiB,EAAE;YACrB;YACAlE,KAAK,GAAGqE,gBAAgB;YACxBpE,GAAG,GAAGkE,WAAW,GAAG,CAAC;UACvB,CAAC,MAAM;YACL;YACAnE,KAAK,GAAGqE,gBAAgB;YACxBpE,GAAG,GAAGkE,WAAW;UACnB;QACF,CAAC,MAAM;UACL;UACA,IAAID,iBAAiB,EAAE;YACrB;YACAlE,KAAK,GAAGmE,WAAW,GAAG,CAAC;YACvBlE,GAAG,GAAGoE,gBAAgB;UACxB,CAAC,MAAM;YACL;YACArE,KAAK,GAAGmE,WAAW;YACnBlE,GAAG,GAAGoE,gBAAgB;UACxB;QACF;QACA,MAAMC,WAAW,GAAG/K,cAAc,CAACmB,MAAM,CAAC;QAC1C,MAAMwF,sBAAsB,GAAG,EAAE;QACjC,KAAK,IAAId,CAAC,GAAGY,KAAK,EAAEZ,CAAC,IAAIa,GAAG,EAAEb,CAAC,IAAI,CAAC,EAAE;UACpCc,sBAAsB,CAACsB,IAAI,CAAC8C,WAAW,CAACC,IAAI,CAACnF,CAAC,CAAC,CAAC1C,EAAE,CAAC;QACrD;QACAhC,MAAM,CAACiB,OAAO,CAACuD,UAAU,CAACgB,sBAAsB,EAAE,CAACgE,iBAAiB,CAAC;QACrE;MACF;IACF;IACA,IAAIxC,KAAK,CAACqC,GAAG,KAAK,GAAG,IAAIrC,KAAK,CAACiB,QAAQ,EAAE;MACvCjB,KAAK,CAACuC,cAAc,CAAC,CAAC;MACtBxC,wBAAwB,CAACU,MAAM,CAACzF,EAAE,EAAEgF,KAAK,CAAC;MAC1C;IACF;IACA,IAAI8C,MAAM,CAACC,YAAY,CAAC/C,KAAK,CAACgD,OAAO,CAAC,KAAK,GAAG,KAAKhD,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACE,OAAO,CAAC,EAAE;MAClFF,KAAK,CAACuC,cAAc,CAAC,CAAC;MACtBd,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACzI,MAAM,EAAE6B,wBAAwB,EAAEkF,wBAAwB,EAAE0B,aAAa,CAAC,CAAC;EAC/E,MAAMwB,mBAAmB,GAAG5M,gBAAgB,CAAC,MAAM;IACjD,IAAI,CAACuC,KAAK,CAACC,YAAY,EAAE;MACvBG,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAAC1D,UAAU,CAAC;MAC/C;IACF;IACA,IAAIuB,qBAAqB,KAAKqJ,SAAS,EAAE;MACvC;IACF;IACA,IAAI,CAAC3J,kBAAkB,IAAI,CAACD,YAAY,IAAIO,qBAAqB,CAACtB,IAAI,KAAK,SAAS,IAAIsB,qBAAqB,CAACrB,GAAG,CAAC2D,IAAI,KAAK,CAAC,EAAE;MAC5HnD,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAACnC,qBAAqB,CAAC;MAC1D;IACF;IACA,MAAMmD,iBAAiB,GAAGhE,MAAM,CAACiB,OAAO,CAAC+D,8BAA8B,CAACnE,qBAAqB,CAAC;IAC9F,IAAImD,iBAAiB,CAACzE,IAAI,KAAKsB,qBAAqB,CAACtB,IAAI,IAAIyE,iBAAiB,CAACxE,GAAG,CAAC2D,IAAI,KAAKtC,qBAAqB,CAACrB,GAAG,CAAC2D,IAAI,IAAI,CAAC0B,KAAK,CAACC,IAAI,CAACjE,qBAAqB,CAACrB,GAAG,CAAC,CAACuF,KAAK,CAAC/C,EAAE,IAAIgC,iBAAiB,CAACxE,GAAG,CAACkE,GAAG,CAAC1B,EAAE,CAAC,CAAC,EAAE;MAC/MhC,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAACgB,iBAAiB,CAAC;MACtD;IACF;IACAhE,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAACnC,qBAAqB,CAAC;EAC5D,CAAC,CAAC;EACFtD,YAAY,CAACyC,MAAM,EAAE,eAAe,EAAEE,0BAA0B,CAAC,MAAM2F,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;EACtGtI,YAAY,CAACyC,MAAM,EAAE,iBAAiB,EAAEE,0BAA0B,CAAC,MAAM2F,uBAAuB,CAAC,CAAC,CAAC,CAAC;EACpGtI,YAAY,CAACyC,MAAM,EAAE,UAAU,EAAEE,0BAA0B,CAACsH,cAAc,CAAC,CAAC;EAC5EjK,YAAY,CAACyC,MAAM,EAAE,4BAA4B,EAAEE,0BAA0B,CAACoI,gCAAgC,CAAC,CAAC;EAChH/K,YAAY,CAACyC,MAAM,EAAE,+BAA+B,EAAEiJ,mCAAmC,CAAC;EAC1F1L,YAAY,CAACyC,MAAM,EAAE,eAAe,EAAEE,0BAA0B,CAACgI,uBAAuB,CAAC,CAAC;EAC1F3K,YAAY,CAACyC,MAAM,EAAE,aAAa,EAAEE,0BAA0B,CAACgJ,iBAAiB,CAAC,CAAC;;EAElF;AACF;AACA;EACE9L,KAAK,CAAC+M,SAAS,CAAC,MAAM;IACpBF,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACjK,MAAM,EAAEa,qBAAqB,EAAEjB,KAAK,CAACC,YAAY,EAAEoK,mBAAmB,CAAC,CAAC;EAC5E,MAAMG,iBAAiB,GAAGvJ,qBAAqB,IAAI,IAAI;EACvDzD,KAAK,CAAC+M,SAAS,CAAC,MAAM;IACpB,IAAIC,iBAAiB,IAAI,CAACxK,KAAK,CAACC,YAAY,IAAI,OAAO8B,eAAe,KAAK,UAAU,EAAE;MACrF;IACF;;IAEA;IACA,MAAMoE,gBAAgB,GAAG/H,6BAA6B,CAACgC,MAAM,CAAC;IAC9D,IAAI+F,gBAAgB,CAACxG,IAAI,KAAK,SAAS,EAAE;MACvC;IACF;IACA,MAAMkF,aAAa,GAAG,IAAIhF,GAAG,CAAC,CAAC;IAC/B,KAAK,MAAMuC,EAAE,IAAI+D,gBAAgB,CAACvG,GAAG,EAAE;MACrC,IAAImC,eAAe,CAACK,EAAE,CAAC,EAAE;QACvByC,aAAa,CAACP,GAAG,CAAClC,EAAE,CAAC;MACvB;IACF;IACA,IAAIyC,aAAa,CAACtB,IAAI,GAAG4C,gBAAgB,CAACvG,GAAG,CAAC2D,IAAI,EAAE;MAClDnD,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAAC;QAClCzD,IAAI,EAAEwG,gBAAgB,CAACxG,IAAI;QAC3BC,GAAG,EAAEiF;MACP,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACzE,MAAM,EAAE2B,eAAe,EAAEyI,iBAAiB,EAAExK,KAAK,CAACC,YAAY,CAAC,CAAC;EACpEzC,KAAK,CAAC+M,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvK,KAAK,CAACC,YAAY,IAAIuK,iBAAiB,EAAE;MAC5C;IACF;IACA,MAAMrE,gBAAgB,GAAG/H,6BAA6B,CAACgC,MAAM,CAAC;IAC9D,IAAI,CAAC6B,wBAAwB,KAAKkE,gBAAgB,CAACxG,IAAI,KAAK,SAAS,IAAIwG,gBAAgB,CAACvG,GAAG,CAAC2D,IAAI,GAAG,CAAC,IAAI4C,gBAAgB,CAACxG,IAAI,KAAK,SAAS,CAAC,EAAE;MAC9I;MACAS,MAAM,CAACiB,OAAO,CAAC+B,oBAAoB,CAAC1D,UAAU,CAAC;IACjD;EACF,CAAC,EAAE,CAACU,MAAM,EAAE6B,wBAAwB,EAAEJ,iBAAiB,EAAE2I,iBAAiB,EAAExK,KAAK,CAACC,YAAY,CAAC,CAAC;EAChGzC,KAAK,CAAC+M,SAAS,CAAC,MAAM;IACpBjK,0BAA0B,CAAC2F,uBAAuB,CAAC;EACrD,CAAC,EAAE,CAACA,uBAAuB,EAAE3F,0BAA0B,CAAC,CAAC;EACzD9C,KAAK,CAAC+M,SAAS,CAAC,MAAM;IACpB,IAAIvE,aAAa,CAAC3E,OAAO,EAAE;MACzB2E,aAAa,CAAC3E,OAAO,GAAG,KAAK;IAC/B;EACF,CAAC,EAAE,EAAE,CAAC;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}