{"ast": null, "code": "import * as React from 'react';\nexport function useFirstRender(callback) {\n  const isFirstRender = React.useRef(true);\n  if (isFirstRender.current) {\n    isFirstRender.current = false;\n    callback();\n  }\n}", "map": {"version": 3, "names": ["React", "useFirstRender", "callback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useRef", "current"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/useFirstRender/useFirstRender.js"], "sourcesContent": ["import * as React from 'react';\nexport function useFirstRender(callback) {\n  const isFirstRender = React.useRef(true);\n  if (isFirstRender.current) {\n    isFirstRender.current = false;\n    callback();\n  }\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,cAAcA,CAACC,QAAQ,EAAE;EACvC,MAAMC,aAAa,GAAGH,KAAK,CAACI,MAAM,CAAC,IAAI,CAAC;EACxC,IAAID,aAAa,CAACE,OAAO,EAAE;IACzBF,aAAa,CAACE,OAAO,GAAG,KAAK;IAC7BH,QAAQ,CAAC,CAAC;EACZ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}