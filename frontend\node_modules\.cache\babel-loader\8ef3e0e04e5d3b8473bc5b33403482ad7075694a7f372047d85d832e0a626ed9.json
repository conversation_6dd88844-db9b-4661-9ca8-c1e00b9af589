{"ast": null, "code": "function dropRightWhile(arr, canContinueDropping) {\n  for (let i = arr.length - 1; i >= 0; i--) {\n    if (!canContinueDropping(arr[i], i, arr)) {\n      return arr.slice(0, i + 1);\n    }\n  }\n  return [];\n}\nexport { dropRightWhile };", "map": {"version": 3, "names": ["dropRightWhile", "arr", "canContinueDropping", "i", "length", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/dropRightWhile.mjs"], "sourcesContent": ["function dropRightWhile(arr, canContinueDropping) {\n    for (let i = arr.length - 1; i >= 0; i--) {\n        if (!canContinueDropping(arr[i], i, arr)) {\n            return arr.slice(0, i + 1);\n        }\n    }\n    return [];\n}\n\nexport { dropRightWhile };\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,GAAG,EAAEC,mBAAmB,EAAE;EAC9C,KAAK,IAAIC,CAAC,GAAGF,GAAG,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACtC,IAAI,CAACD,mBAAmB,CAACD,GAAG,CAACE,CAAC,CAAC,EAAEA,CAAC,EAAEF,GAAG,CAAC,EAAE;MACtC,OAAOA,GAAG,CAACI,KAAK,CAAC,CAAC,EAAEF,CAAC,GAAG,CAAC,CAAC;IAC9B;EACJ;EACA,OAAO,EAAE;AACb;AAEA,SAASH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}