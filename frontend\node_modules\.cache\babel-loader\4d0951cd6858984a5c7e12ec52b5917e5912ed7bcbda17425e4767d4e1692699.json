{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport resolveProps from '@mui/utils/resolveProps';\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF, getGridDefaultColumnTypes } from \"../../../colDef/index.js\";\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from \"./gridColumnsSelector.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridDensityFactorSelector } from \"../density/densitySelector.js\";\nimport { gridHeaderFilteringEnabledSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\nconst COLUMN_TYPES = getGridDefaultColumnTypes();\n\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth({\n  initialFreeSpace,\n  totalFlexUnits,\n  flexColumns\n}) {\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(({\n        field\n      }) => {\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, dimensions) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute their minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    let column = rawState.lookup[columnField];\n    let computedWidth = 0;\n    let isFlex = false;\n    if (rawState.columnVisibilityModel[columnField] !== false) {\n      if (column.flex && column.flex > 0) {\n        totalFlexUnits += column.flex;\n        isFlex = true;\n      } else {\n        computedWidth = clamp(column.width || GRID_STRING_COL_DEF.width, column.minWidth || GRID_STRING_COL_DEF.minWidth, column.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n    }\n    if (column.computedWidth !== computedWidth) {\n      column = _extends({}, column, {\n        computedWidth\n      });\n    }\n    if (isFlex) {\n      flexColumns.push(column);\n    }\n    columnsLookup[columnField] = column;\n  });\n  const availableWidth = dimensions === undefined ? 0 : dimensions.viewportOuterSize.width - (dimensions.hasScrollY ? dimensions.scrollbarSize : 0);\n  const initialFreeSpace = Math.max(availableWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && availableWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field] = _extends({}, columnsLookup[field], {\n        computedWidth: computedColumnWidths[field].computedWidth\n      });\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(([key, value]) => {\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nexport function getDefaultColTypeDef(type) {\n  let colDef = COLUMN_TYPES[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && COLUMN_TYPES[type]) {\n    colDef = COLUMN_TYPES[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = ({\n  apiRef,\n  columnsToUpsert,\n  initialState,\n  columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n  keepOnlyColumnsToUpsert = false,\n  updateInitialVisibilityModel = false\n}) => {\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel,\n      initialColumnVisibilityModel: columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel,\n      initialColumnVisibilityModel: updateInitialVisibilityModel ? columnVisibilityModel : currentState.initialColumnVisibilityModel\n    };\n  }\n  const columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    for (const key in columnsState.lookup) {\n      if (Object.prototype.hasOwnProperty.call(columnsState.lookup, key)) {\n        columnsToKeep[key] = false;\n      }\n    }\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = resolveProps(existingState, _extends({}, getDefaultColTypeDef(newColumn.type), newColumn, {\n      hasBeenResized\n    }));\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, apiRef.current.getRootDimensions?.() ?? undefined);\n};\nexport function getFirstNonSpannedColumnToRender({\n  firstColumnToRender,\n  apiRef,\n  firstRowToRender,\n  lastRowToRender,\n  visibleRows\n}) {\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  let foundStableColumn = false;\n\n  // Keep checking columns until we find one that's not spanned in any visible row\n  while (!foundStableColumn && firstNonSpannedColumnToRender >= 0) {\n    foundStableColumn = true;\n    for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n      const row = visibleRows[i];\n      if (row) {\n        const rowId = visibleRows[i].id;\n        const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstNonSpannedColumnToRender);\n        if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan && cellColSpanInfo.leftVisibleCellIndex < firstNonSpannedColumnToRender) {\n          firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n          foundStableColumn = false;\n          break; // Check the new column index against the visible rows, because it might be spanned\n        }\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, props) {\n  if (props.listView) {\n    return 0;\n  }\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  const isHeaderFilteringEnabled = gridHeaderFilteringEnabledSelector(apiRef);\n  const columnHeadersHeight = Math.floor(props.columnHeaderHeight * densityFactor);\n  const columnGroupHeadersHeight = Math.floor((props.columnGroupHeaderHeight ?? props.columnHeaderHeight) * densityFactor);\n  const filterHeadersHeight = isHeaderFilteringEnabled ? Math.floor((props.headerFilterHeight ?? props.columnHeaderHeight) * densityFactor) : 0;\n  return columnHeadersHeight + columnGroupHeadersHeight * maxDepth + filterHeadersHeight;\n}", "map": {"version": 3, "names": ["_extends", "resolveProps", "DEFAULT_GRID_COL_TYPE_KEY", "GRID_STRING_COL_DEF", "getGridDefaultColumnTypes", "gridColumnsStateSelector", "gridColumnVisibilityModelSelector", "clamp", "gridDensityFactorSelector", "gridHeaderFilteringEnabledSelector", "gridColumnGroupsHeaderMaxDepthSelector", "COLUMNS_DIMENSION_PROPERTIES", "COLUMN_TYPES", "computeFlexColumnsWidth", "initialFreeSpace", "totalFlexUnits", "flexColumns", "uniqueFlexColumns", "Set", "map", "col", "field", "flexColumnsLookup", "all", "frozenFields", "freeze", "value", "frozen", "push", "loopOverFlexItems", "length", "size", "violationsLookup", "min", "max", "remainingFreeSpace", "flexUnits", "totalViolation", "for<PERSON>ach", "computedWidth", "flex", "i", "column", "widthPerFlexUnit", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "Object", "keys", "hydrateColumnsWidth", "rawState", "dimensions", "columnsLookup", "widthAllocatedBeforeFlex", "orderedFields", "columnField", "lookup", "isFlex", "columnVisibilityModel", "width", "availableWidth", "undefined", "viewportOuterSize", "hasScrollY", "scrollbarSize", "Math", "computedColumnWidths", "applyInitialState", "columnsState", "initialState", "columnsWithUpdatedDimensions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cleanOrderedFields", "new<PERSON><PERSON><PERSON><PERSON><PERSON>s", "filter", "newColumnLookup", "newColDef", "hasBeenResized", "entries", "key", "Infinity", "newColumnsState", "getDefaultColTypeDef", "type", "colDef", "createColumnsState", "apiRef", "columnsToUpsert", "keepOnlyColumnsToUpsert", "updateInitialVisibilityModel", "isInsideStateInitializer", "current", "state", "columns", "initialColumnVisibilityModel", "currentState", "columnsToKeep", "prototype", "hasOwnProperty", "call", "columnsToUpsertLookup", "newColumn", "existingState", "columnsStateWithPreProcessing", "unstable_applyPipeProcessors", "columnsStateWithPortableColumns", "getRootDimensions", "getFirstNonSpannedColumnToRender", "firstColumnToRender", "firstRowToRender", "lastRowToRender", "visibleRows", "firstNonSpannedColumnToRender", "foundStableColumn", "row", "rowId", "id", "cellColSpanInfo", "unstable_getCellColSpanInfo", "spannedByColSpan", "leftVisibleCellIndex", "getTotalHeaderHeight", "props", "listView", "densityFactor", "max<PERSON><PERSON><PERSON>", "isHeaderFilteringEnabled", "columnHeadersHeight", "floor", "columnHeaderHeight", "columnGroupHeadersHeight", "columnGroupHeaderHeight", "filterHeadersHeight", "headerFilterHeight"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columns/gridColumnsUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport resolveProps from '@mui/utils/resolveProps';\nimport { DEFAULT_GRID_COL_TYPE_KEY, GRID_STRING_COL_DEF, getGridDefaultColumnTypes } from \"../../../colDef/index.js\";\nimport { gridColumnsStateSelector, gridColumnVisibilityModelSelector } from \"./gridColumnsSelector.js\";\nimport { clamp } from \"../../../utils/utils.js\";\nimport { gridDensityFactorSelector } from \"../density/densitySelector.js\";\nimport { gridHeaderFilteringEnabledSelector } from \"../headerFiltering/gridHeaderFilteringSelectors.js\";\nimport { gridColumnGroupsHeaderMaxDepthSelector } from \"../columnGrouping/gridColumnGroupsSelector.js\";\nexport const COLUMNS_DIMENSION_PROPERTIES = ['maxWidth', 'minWidth', 'width', 'flex'];\nconst COLUMN_TYPES = getGridDefaultColumnTypes();\n\n/**\n * Computes width for flex columns.\n * Based on CSS Flexbox specification:\n * https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n */\nexport function computeFlexColumnsWidth({\n  initialFreeSpace,\n  totalFlexUnits,\n  flexColumns\n}) {\n  const uniqueFlexColumns = new Set(flexColumns.map(col => col.field));\n  const flexColumnsLookup = {\n    all: {},\n    frozenFields: [],\n    freeze: field => {\n      const value = flexColumnsLookup.all[field];\n      if (value && value.frozen !== true) {\n        flexColumnsLookup.all[field].frozen = true;\n        flexColumnsLookup.frozenFields.push(field);\n      }\n    }\n  };\n\n  // Step 5 of https://drafts.csswg.org/css-flexbox-1/#resolve-flexible-lengths\n  function loopOverFlexItems() {\n    // 5a: If all the flex items on the line are frozen, free space has been distributed.\n    if (flexColumnsLookup.frozenFields.length === uniqueFlexColumns.size) {\n      return;\n    }\n    const violationsLookup = {\n      min: {},\n      max: {}\n    };\n    let remainingFreeSpace = initialFreeSpace;\n    let flexUnits = totalFlexUnits;\n    let totalViolation = 0;\n\n    // 5b: Calculate the remaining free space\n    flexColumnsLookup.frozenFields.forEach(field => {\n      remainingFreeSpace -= flexColumnsLookup.all[field].computedWidth;\n      flexUnits -= flexColumnsLookup.all[field].flex;\n    });\n    for (let i = 0; i < flexColumns.length; i += 1) {\n      const column = flexColumns[i];\n      if (flexColumnsLookup.all[column.field] && flexColumnsLookup.all[column.field].frozen === true) {\n        continue;\n      }\n\n      // 5c: Distribute remaining free space proportional to the flex factors\n      const widthPerFlexUnit = remainingFreeSpace / flexUnits;\n      let computedWidth = widthPerFlexUnit * column.flex;\n\n      // 5d: Fix min/max violations\n      if (computedWidth < column.minWidth) {\n        totalViolation += column.minWidth - computedWidth;\n        computedWidth = column.minWidth;\n        violationsLookup.min[column.field] = true;\n      } else if (computedWidth > column.maxWidth) {\n        totalViolation += column.maxWidth - computedWidth;\n        computedWidth = column.maxWidth;\n        violationsLookup.max[column.field] = true;\n      }\n      flexColumnsLookup.all[column.field] = {\n        frozen: false,\n        computedWidth,\n        flex: column.flex\n      };\n    }\n\n    // 5e: Freeze over-flexed items\n    if (totalViolation < 0) {\n      // Freeze all the items with max violations\n      Object.keys(violationsLookup.max).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else if (totalViolation > 0) {\n      // Freeze all the items with min violations\n      Object.keys(violationsLookup.min).forEach(field => {\n        flexColumnsLookup.freeze(field);\n      });\n    } else {\n      // Freeze all items\n      flexColumns.forEach(({\n        field\n      }) => {\n        flexColumnsLookup.freeze(field);\n      });\n    }\n\n    // 5f: Return to the start of this loop\n    loopOverFlexItems();\n  }\n  loopOverFlexItems();\n  return flexColumnsLookup.all;\n}\n\n/**\n * Compute the `computedWidth` (ie: the width the column should have during rendering) based on the `width` / `flex` / `minWidth` / `maxWidth` properties of `GridColDef`.\n * The columns already have been merged with there `type` default values for `minWidth`, `maxWidth` and `width`, thus the `!` for those properties below.\n * TODO: Unit test this function in depth and only keep basic cases for the whole grid testing.\n * TODO: Improve the `GridColDef` typing to reflect the fact that `minWidth` / `maxWidth` and `width` can't be null after the merge with the `type` default values.\n */\nexport const hydrateColumnsWidth = (rawState, dimensions) => {\n  const columnsLookup = {};\n  let totalFlexUnits = 0;\n  let widthAllocatedBeforeFlex = 0;\n  const flexColumns = [];\n\n  // For the non-flex columns, compute their width\n  // For the flex columns, compute their minimum width and how much width must be allocated during the flex allocation\n  rawState.orderedFields.forEach(columnField => {\n    let column = rawState.lookup[columnField];\n    let computedWidth = 0;\n    let isFlex = false;\n    if (rawState.columnVisibilityModel[columnField] !== false) {\n      if (column.flex && column.flex > 0) {\n        totalFlexUnits += column.flex;\n        isFlex = true;\n      } else {\n        computedWidth = clamp(column.width || GRID_STRING_COL_DEF.width, column.minWidth || GRID_STRING_COL_DEF.minWidth, column.maxWidth || GRID_STRING_COL_DEF.maxWidth);\n      }\n      widthAllocatedBeforeFlex += computedWidth;\n    }\n    if (column.computedWidth !== computedWidth) {\n      column = _extends({}, column, {\n        computedWidth\n      });\n    }\n    if (isFlex) {\n      flexColumns.push(column);\n    }\n    columnsLookup[columnField] = column;\n  });\n  const availableWidth = dimensions === undefined ? 0 : dimensions.viewportOuterSize.width - (dimensions.hasScrollY ? dimensions.scrollbarSize : 0);\n  const initialFreeSpace = Math.max(availableWidth - widthAllocatedBeforeFlex, 0);\n\n  // Allocate the remaining space to the flex columns\n  if (totalFlexUnits > 0 && availableWidth > 0) {\n    const computedColumnWidths = computeFlexColumnsWidth({\n      initialFreeSpace,\n      totalFlexUnits,\n      flexColumns\n    });\n    Object.keys(computedColumnWidths).forEach(field => {\n      columnsLookup[field] = _extends({}, columnsLookup[field], {\n        computedWidth: computedColumnWidths[field].computedWidth\n      });\n    });\n  }\n  return _extends({}, rawState, {\n    lookup: columnsLookup\n  });\n};\n\n/**\n * Apply the order and the dimensions of the initial state.\n * The columns not registered in `orderedFields` will be placed after the imported columns.\n */\nexport const applyInitialState = (columnsState, initialState) => {\n  if (!initialState) {\n    return columnsState;\n  }\n  const {\n    orderedFields = [],\n    dimensions = {}\n  } = initialState;\n  const columnsWithUpdatedDimensions = Object.keys(dimensions);\n  if (columnsWithUpdatedDimensions.length === 0 && orderedFields.length === 0) {\n    return columnsState;\n  }\n  const orderedFieldsLookup = {};\n  const cleanOrderedFields = [];\n  for (let i = 0; i < orderedFields.length; i += 1) {\n    const field = orderedFields[i];\n\n    // Ignores the fields in the initialState that matches no field on the current column state\n    if (columnsState.lookup[field]) {\n      orderedFieldsLookup[field] = true;\n      cleanOrderedFields.push(field);\n    }\n  }\n  const newOrderedFields = cleanOrderedFields.length === 0 ? columnsState.orderedFields : [...cleanOrderedFields, ...columnsState.orderedFields.filter(field => !orderedFieldsLookup[field])];\n  const newColumnLookup = _extends({}, columnsState.lookup);\n  for (let i = 0; i < columnsWithUpdatedDimensions.length; i += 1) {\n    const field = columnsWithUpdatedDimensions[i];\n    const newColDef = _extends({}, newColumnLookup[field], {\n      hasBeenResized: true\n    });\n    Object.entries(dimensions[field]).forEach(([key, value]) => {\n      newColDef[key] = value === -1 ? Infinity : value;\n    });\n    newColumnLookup[field] = newColDef;\n  }\n  const newColumnsState = _extends({}, columnsState, {\n    orderedFields: newOrderedFields,\n    lookup: newColumnLookup\n  });\n  return newColumnsState;\n};\nexport function getDefaultColTypeDef(type) {\n  let colDef = COLUMN_TYPES[DEFAULT_GRID_COL_TYPE_KEY];\n  if (type && COLUMN_TYPES[type]) {\n    colDef = COLUMN_TYPES[type];\n  }\n  return colDef;\n}\nexport const createColumnsState = ({\n  apiRef,\n  columnsToUpsert,\n  initialState,\n  columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef),\n  keepOnlyColumnsToUpsert = false,\n  updateInitialVisibilityModel = false\n}) => {\n  const isInsideStateInitializer = !apiRef.current.state.columns;\n  let columnsState;\n  if (isInsideStateInitializer) {\n    columnsState = {\n      orderedFields: [],\n      lookup: {},\n      columnVisibilityModel,\n      initialColumnVisibilityModel: columnVisibilityModel\n    };\n  } else {\n    const currentState = gridColumnsStateSelector(apiRef);\n    columnsState = {\n      orderedFields: keepOnlyColumnsToUpsert ? [] : [...currentState.orderedFields],\n      lookup: _extends({}, currentState.lookup),\n      // Will be cleaned later if keepOnlyColumnsToUpsert=true\n      columnVisibilityModel,\n      initialColumnVisibilityModel: updateInitialVisibilityModel ? columnVisibilityModel : currentState.initialColumnVisibilityModel\n    };\n  }\n  const columnsToKeep = {};\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    for (const key in columnsState.lookup) {\n      if (Object.prototype.hasOwnProperty.call(columnsState.lookup, key)) {\n        columnsToKeep[key] = false;\n      }\n    }\n  }\n  const columnsToUpsertLookup = {};\n  columnsToUpsert.forEach(newColumn => {\n    const {\n      field\n    } = newColumn;\n    columnsToUpsertLookup[field] = true;\n    columnsToKeep[field] = true;\n    let existingState = columnsState.lookup[field];\n    if (existingState == null) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field,\n        hasBeenResized: false\n      });\n      columnsState.orderedFields.push(field);\n    } else if (keepOnlyColumnsToUpsert) {\n      columnsState.orderedFields.push(field);\n    }\n\n    // If the column type has changed - merge the existing state with the default column type definition\n    if (existingState && existingState.type !== newColumn.type) {\n      existingState = _extends({}, getDefaultColTypeDef(newColumn.type), {\n        field\n      });\n    }\n    let hasBeenResized = existingState.hasBeenResized;\n    COLUMNS_DIMENSION_PROPERTIES.forEach(key => {\n      if (newColumn[key] !== undefined) {\n        hasBeenResized = true;\n        if (newColumn[key] === -1) {\n          newColumn[key] = Infinity;\n        }\n      }\n    });\n    columnsState.lookup[field] = resolveProps(existingState, _extends({}, getDefaultColTypeDef(newColumn.type), newColumn, {\n      hasBeenResized\n    }));\n  });\n  if (keepOnlyColumnsToUpsert && !isInsideStateInitializer) {\n    Object.keys(columnsState.lookup).forEach(field => {\n      if (!columnsToKeep[field]) {\n        delete columnsState.lookup[field];\n      }\n    });\n  }\n  const columnsStateWithPreProcessing = apiRef.current.unstable_applyPipeProcessors('hydrateColumns', columnsState);\n  const columnsStateWithPortableColumns = applyInitialState(columnsStateWithPreProcessing, initialState);\n  return hydrateColumnsWidth(columnsStateWithPortableColumns, apiRef.current.getRootDimensions?.() ?? undefined);\n};\nexport function getFirstNonSpannedColumnToRender({\n  firstColumnToRender,\n  apiRef,\n  firstRowToRender,\n  lastRowToRender,\n  visibleRows\n}) {\n  let firstNonSpannedColumnToRender = firstColumnToRender;\n  let foundStableColumn = false;\n\n  // Keep checking columns until we find one that's not spanned in any visible row\n  while (!foundStableColumn && firstNonSpannedColumnToRender >= 0) {\n    foundStableColumn = true;\n    for (let i = firstRowToRender; i < lastRowToRender; i += 1) {\n      const row = visibleRows[i];\n      if (row) {\n        const rowId = visibleRows[i].id;\n        const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, firstNonSpannedColumnToRender);\n        if (cellColSpanInfo && cellColSpanInfo.spannedByColSpan && cellColSpanInfo.leftVisibleCellIndex < firstNonSpannedColumnToRender) {\n          firstNonSpannedColumnToRender = cellColSpanInfo.leftVisibleCellIndex;\n          foundStableColumn = false;\n          break; // Check the new column index against the visible rows, because it might be spanned\n        }\n      }\n    }\n  }\n  return firstNonSpannedColumnToRender;\n}\nexport function getTotalHeaderHeight(apiRef, props) {\n  if (props.listView) {\n    return 0;\n  }\n  const densityFactor = gridDensityFactorSelector(apiRef);\n  const maxDepth = gridColumnGroupsHeaderMaxDepthSelector(apiRef);\n  const isHeaderFilteringEnabled = gridHeaderFilteringEnabledSelector(apiRef);\n  const columnHeadersHeight = Math.floor(props.columnHeaderHeight * densityFactor);\n  const columnGroupHeadersHeight = Math.floor((props.columnGroupHeaderHeight ?? props.columnHeaderHeight) * densityFactor);\n  const filterHeadersHeight = isHeaderFilteringEnabled ? Math.floor((props.headerFilterHeight ?? props.columnHeaderHeight) * densityFactor) : 0;\n  return columnHeadersHeight + columnGroupHeadersHeight * maxDepth + filterHeadersHeight;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,yBAAyB,EAAEC,mBAAmB,EAAEC,yBAAyB,QAAQ,0BAA0B;AACpH,SAASC,wBAAwB,EAAEC,iCAAiC,QAAQ,0BAA0B;AACtG,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,yBAAyB,QAAQ,+BAA+B;AACzE,SAASC,kCAAkC,QAAQ,oDAAoD;AACvG,SAASC,sCAAsC,QAAQ,+CAA+C;AACtG,OAAO,MAAMC,4BAA4B,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC;AACrF,MAAMC,YAAY,GAAGR,yBAAyB,CAAC,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASS,uBAAuBA,CAAC;EACtCC,gBAAgB;EAChBC,cAAc;EACdC;AACF,CAAC,EAAE;EACD,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAACF,WAAW,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,CAAC,CAAC;EACpE,MAAMC,iBAAiB,GAAG;IACxBC,GAAG,EAAE,CAAC,CAAC;IACPC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAEJ,KAAK,IAAI;MACf,MAAMK,KAAK,GAAGJ,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC;MAC1C,IAAIK,KAAK,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,EAAE;QAClCL,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACM,MAAM,GAAG,IAAI;QAC1CL,iBAAiB,CAACE,YAAY,CAACI,IAAI,CAACP,KAAK,CAAC;MAC5C;IACF;EACF,CAAC;;EAED;EACA,SAASQ,iBAAiBA,CAAA,EAAG;IAC3B;IACA,IAAIP,iBAAiB,CAACE,YAAY,CAACM,MAAM,KAAKb,iBAAiB,CAACc,IAAI,EAAE;MACpE;IACF;IACA,MAAMC,gBAAgB,GAAG;MACvBC,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC;IACR,CAAC;IACD,IAAIC,kBAAkB,GAAGrB,gBAAgB;IACzC,IAAIsB,SAAS,GAAGrB,cAAc;IAC9B,IAAIsB,cAAc,GAAG,CAAC;;IAEtB;IACAf,iBAAiB,CAACE,YAAY,CAACc,OAAO,CAACjB,KAAK,IAAI;MAC9Cc,kBAAkB,IAAIb,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACkB,aAAa;MAChEH,SAAS,IAAId,iBAAiB,CAACC,GAAG,CAACF,KAAK,CAAC,CAACmB,IAAI;IAChD,CAAC,CAAC;IACF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,WAAW,CAACc,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;MAC9C,MAAMC,MAAM,GAAG1B,WAAW,CAACyB,CAAC,CAAC;MAC7B,IAAInB,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,IAAIC,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,CAACM,MAAM,KAAK,IAAI,EAAE;QAC9F;MACF;;MAEA;MACA,MAAMgB,gBAAgB,GAAGR,kBAAkB,GAAGC,SAAS;MACvD,IAAIG,aAAa,GAAGI,gBAAgB,GAAGD,MAAM,CAACF,IAAI;;MAElD;MACA,IAAID,aAAa,GAAGG,MAAM,CAACE,QAAQ,EAAE;QACnCP,cAAc,IAAIK,MAAM,CAACE,QAAQ,GAAGL,aAAa;QACjDA,aAAa,GAAGG,MAAM,CAACE,QAAQ;QAC/BZ,gBAAgB,CAACC,GAAG,CAACS,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;MAC3C,CAAC,MAAM,IAAIkB,aAAa,GAAGG,MAAM,CAACG,QAAQ,EAAE;QAC1CR,cAAc,IAAIK,MAAM,CAACG,QAAQ,GAAGN,aAAa;QACjDA,aAAa,GAAGG,MAAM,CAACG,QAAQ;QAC/Bb,gBAAgB,CAACE,GAAG,CAACQ,MAAM,CAACrB,KAAK,CAAC,GAAG,IAAI;MAC3C;MACAC,iBAAiB,CAACC,GAAG,CAACmB,MAAM,CAACrB,KAAK,CAAC,GAAG;QACpCM,MAAM,EAAE,KAAK;QACbY,aAAa;QACbC,IAAI,EAAEE,MAAM,CAACF;MACf,CAAC;IACH;;IAEA;IACA,IAAIH,cAAc,GAAG,CAAC,EAAE;MACtB;MACAS,MAAM,CAACC,IAAI,CAACf,gBAAgB,CAACE,GAAG,CAAC,CAACI,OAAO,CAACjB,KAAK,IAAI;QACjDC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIgB,cAAc,GAAG,CAAC,EAAE;MAC7B;MACAS,MAAM,CAACC,IAAI,CAACf,gBAAgB,CAACC,GAAG,CAAC,CAACK,OAAO,CAACjB,KAAK,IAAI;QACjDC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAL,WAAW,CAACsB,OAAO,CAAC,CAAC;QACnBjB;MACF,CAAC,KAAK;QACJC,iBAAiB,CAACG,MAAM,CAACJ,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;;IAEA;IACAQ,iBAAiB,CAAC,CAAC;EACrB;EACAA,iBAAiB,CAAC,CAAC;EACnB,OAAOP,iBAAiB,CAACC,GAAG;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMyB,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,UAAU,KAAK;EAC3D,MAAMC,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIpC,cAAc,GAAG,CAAC;EACtB,IAAIqC,wBAAwB,GAAG,CAAC;EAChC,MAAMpC,WAAW,GAAG,EAAE;;EAEtB;EACA;EACAiC,QAAQ,CAACI,aAAa,CAACf,OAAO,CAACgB,WAAW,IAAI;IAC5C,IAAIZ,MAAM,GAAGO,QAAQ,CAACM,MAAM,CAACD,WAAW,CAAC;IACzC,IAAIf,aAAa,GAAG,CAAC;IACrB,IAAIiB,MAAM,GAAG,KAAK;IAClB,IAAIP,QAAQ,CAACQ,qBAAqB,CAACH,WAAW,CAAC,KAAK,KAAK,EAAE;MACzD,IAAIZ,MAAM,CAACF,IAAI,IAAIE,MAAM,CAACF,IAAI,GAAG,CAAC,EAAE;QAClCzB,cAAc,IAAI2B,MAAM,CAACF,IAAI;QAC7BgB,MAAM,GAAG,IAAI;MACf,CAAC,MAAM;QACLjB,aAAa,GAAGhC,KAAK,CAACmC,MAAM,CAACgB,KAAK,IAAIvD,mBAAmB,CAACuD,KAAK,EAAEhB,MAAM,CAACE,QAAQ,IAAIzC,mBAAmB,CAACyC,QAAQ,EAAEF,MAAM,CAACG,QAAQ,IAAI1C,mBAAmB,CAAC0C,QAAQ,CAAC;MACpK;MACAO,wBAAwB,IAAIb,aAAa;IAC3C;IACA,IAAIG,MAAM,CAACH,aAAa,KAAKA,aAAa,EAAE;MAC1CG,MAAM,GAAG1C,QAAQ,CAAC,CAAC,CAAC,EAAE0C,MAAM,EAAE;QAC5BH;MACF,CAAC,CAAC;IACJ;IACA,IAAIiB,MAAM,EAAE;MACVxC,WAAW,CAACY,IAAI,CAACc,MAAM,CAAC;IAC1B;IACAS,aAAa,CAACG,WAAW,CAAC,GAAGZ,MAAM;EACrC,CAAC,CAAC;EACF,MAAMiB,cAAc,GAAGT,UAAU,KAAKU,SAAS,GAAG,CAAC,GAAGV,UAAU,CAACW,iBAAiB,CAACH,KAAK,IAAIR,UAAU,CAACY,UAAU,GAAGZ,UAAU,CAACa,aAAa,GAAG,CAAC,CAAC;EACjJ,MAAMjD,gBAAgB,GAAGkD,IAAI,CAAC9B,GAAG,CAACyB,cAAc,GAAGP,wBAAwB,EAAE,CAAC,CAAC;;EAE/E;EACA,IAAIrC,cAAc,GAAG,CAAC,IAAI4C,cAAc,GAAG,CAAC,EAAE;IAC5C,MAAMM,oBAAoB,GAAGpD,uBAAuB,CAAC;MACnDC,gBAAgB;MAChBC,cAAc;MACdC;IACF,CAAC,CAAC;IACF8B,MAAM,CAACC,IAAI,CAACkB,oBAAoB,CAAC,CAAC3B,OAAO,CAACjB,KAAK,IAAI;MACjD8B,aAAa,CAAC9B,KAAK,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEmD,aAAa,CAAC9B,KAAK,CAAC,EAAE;QACxDkB,aAAa,EAAE0B,oBAAoB,CAAC5C,KAAK,CAAC,CAACkB;MAC7C,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,OAAOvC,QAAQ,CAAC,CAAC,CAAC,EAAEiD,QAAQ,EAAE;IAC5BM,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMe,iBAAiB,GAAGA,CAACC,YAAY,EAAEC,YAAY,KAAK;EAC/D,IAAI,CAACA,YAAY,EAAE;IACjB,OAAOD,YAAY;EACrB;EACA,MAAM;IACJd,aAAa,GAAG,EAAE;IAClBH,UAAU,GAAG,CAAC;EAChB,CAAC,GAAGkB,YAAY;EAChB,MAAMC,4BAA4B,GAAGvB,MAAM,CAACC,IAAI,CAACG,UAAU,CAAC;EAC5D,IAAImB,4BAA4B,CAACvC,MAAM,KAAK,CAAC,IAAIuB,aAAa,CAACvB,MAAM,KAAK,CAAC,EAAE;IAC3E,OAAOqC,YAAY;EACrB;EACA,MAAMG,mBAAmB,GAAG,CAAC,CAAC;EAC9B,MAAMC,kBAAkB,GAAG,EAAE;EAC7B,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,aAAa,CAACvB,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;IAChD,MAAMpB,KAAK,GAAGgC,aAAa,CAACZ,CAAC,CAAC;;IAE9B;IACA,IAAI0B,YAAY,CAACZ,MAAM,CAAClC,KAAK,CAAC,EAAE;MAC9BiD,mBAAmB,CAACjD,KAAK,CAAC,GAAG,IAAI;MACjCkD,kBAAkB,CAAC3C,IAAI,CAACP,KAAK,CAAC;IAChC;EACF;EACA,MAAMmD,gBAAgB,GAAGD,kBAAkB,CAACzC,MAAM,KAAK,CAAC,GAAGqC,YAAY,CAACd,aAAa,GAAG,CAAC,GAAGkB,kBAAkB,EAAE,GAAGJ,YAAY,CAACd,aAAa,CAACoB,MAAM,CAACpD,KAAK,IAAI,CAACiD,mBAAmB,CAACjD,KAAK,CAAC,CAAC,CAAC;EAC3L,MAAMqD,eAAe,GAAG1E,QAAQ,CAAC,CAAC,CAAC,EAAEmE,YAAY,CAACZ,MAAM,CAAC;EACzD,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,4BAA4B,CAACvC,MAAM,EAAEW,CAAC,IAAI,CAAC,EAAE;IAC/D,MAAMpB,KAAK,GAAGgD,4BAA4B,CAAC5B,CAAC,CAAC;IAC7C,MAAMkC,SAAS,GAAG3E,QAAQ,CAAC,CAAC,CAAC,EAAE0E,eAAe,CAACrD,KAAK,CAAC,EAAE;MACrDuD,cAAc,EAAE;IAClB,CAAC,CAAC;IACF9B,MAAM,CAAC+B,OAAO,CAAC3B,UAAU,CAAC7B,KAAK,CAAC,CAAC,CAACiB,OAAO,CAAC,CAAC,CAACwC,GAAG,EAAEpD,KAAK,CAAC,KAAK;MAC1DiD,SAAS,CAACG,GAAG,CAAC,GAAGpD,KAAK,KAAK,CAAC,CAAC,GAAGqD,QAAQ,GAAGrD,KAAK;IAClD,CAAC,CAAC;IACFgD,eAAe,CAACrD,KAAK,CAAC,GAAGsD,SAAS;EACpC;EACA,MAAMK,eAAe,GAAGhF,QAAQ,CAAC,CAAC,CAAC,EAAEmE,YAAY,EAAE;IACjDd,aAAa,EAAEmB,gBAAgB;IAC/BjB,MAAM,EAAEmB;EACV,CAAC,CAAC;EACF,OAAOM,eAAe;AACxB,CAAC;AACD,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,IAAIC,MAAM,GAAGvE,YAAY,CAACV,yBAAyB,CAAC;EACpD,IAAIgF,IAAI,IAAItE,YAAY,CAACsE,IAAI,CAAC,EAAE;IAC9BC,MAAM,GAAGvE,YAAY,CAACsE,IAAI,CAAC;EAC7B;EACA,OAAOC,MAAM;AACf;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAAC;EACjCC,MAAM;EACNC,eAAe;EACflB,YAAY;EACZX,qBAAqB,GAAGnD,iCAAiC,CAAC+E,MAAM,CAAC;EACjEE,uBAAuB,GAAG,KAAK;EAC/BC,4BAA4B,GAAG;AACjC,CAAC,KAAK;EACJ,MAAMC,wBAAwB,GAAG,CAACJ,MAAM,CAACK,OAAO,CAACC,KAAK,CAACC,OAAO;EAC9D,IAAIzB,YAAY;EAChB,IAAIsB,wBAAwB,EAAE;IAC5BtB,YAAY,GAAG;MACbd,aAAa,EAAE,EAAE;MACjBE,MAAM,EAAE,CAAC,CAAC;MACVE,qBAAqB;MACrBoC,4BAA4B,EAAEpC;IAChC,CAAC;EACH,CAAC,MAAM;IACL,MAAMqC,YAAY,GAAGzF,wBAAwB,CAACgF,MAAM,CAAC;IACrDlB,YAAY,GAAG;MACbd,aAAa,EAAEkC,uBAAuB,GAAG,EAAE,GAAG,CAAC,GAAGO,YAAY,CAACzC,aAAa,CAAC;MAC7EE,MAAM,EAAEvD,QAAQ,CAAC,CAAC,CAAC,EAAE8F,YAAY,CAACvC,MAAM,CAAC;MACzC;MACAE,qBAAqB;MACrBoC,4BAA4B,EAAEL,4BAA4B,GAAG/B,qBAAqB,GAAGqC,YAAY,CAACD;IACpG,CAAC;EACH;EACA,MAAME,aAAa,GAAG,CAAC,CAAC;EACxB,IAAIR,uBAAuB,IAAI,CAACE,wBAAwB,EAAE;IACxD,KAAK,MAAMX,GAAG,IAAIX,YAAY,CAACZ,MAAM,EAAE;MACrC,IAAIT,MAAM,CAACkD,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC/B,YAAY,CAACZ,MAAM,EAAEuB,GAAG,CAAC,EAAE;QAClEiB,aAAa,CAACjB,GAAG,CAAC,GAAG,KAAK;MAC5B;IACF;EACF;EACA,MAAMqB,qBAAqB,GAAG,CAAC,CAAC;EAChCb,eAAe,CAAChD,OAAO,CAAC8D,SAAS,IAAI;IACnC,MAAM;MACJ/E;IACF,CAAC,GAAG+E,SAAS;IACbD,qBAAqB,CAAC9E,KAAK,CAAC,GAAG,IAAI;IACnC0E,aAAa,CAAC1E,KAAK,CAAC,GAAG,IAAI;IAC3B,IAAIgF,aAAa,GAAGlC,YAAY,CAACZ,MAAM,CAAClC,KAAK,CAAC;IAC9C,IAAIgF,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAGrG,QAAQ,CAAC,CAAC,CAAC,EAAEiF,oBAAoB,CAACmB,SAAS,CAAClB,IAAI,CAAC,EAAE;QACjE7D,KAAK;QACLuD,cAAc,EAAE;MAClB,CAAC,CAAC;MACFT,YAAY,CAACd,aAAa,CAACzB,IAAI,CAACP,KAAK,CAAC;IACxC,CAAC,MAAM,IAAIkE,uBAAuB,EAAE;MAClCpB,YAAY,CAACd,aAAa,CAACzB,IAAI,CAACP,KAAK,CAAC;IACxC;;IAEA;IACA,IAAIgF,aAAa,IAAIA,aAAa,CAACnB,IAAI,KAAKkB,SAAS,CAAClB,IAAI,EAAE;MAC1DmB,aAAa,GAAGrG,QAAQ,CAAC,CAAC,CAAC,EAAEiF,oBAAoB,CAACmB,SAAS,CAAClB,IAAI,CAAC,EAAE;QACjE7D;MACF,CAAC,CAAC;IACJ;IACA,IAAIuD,cAAc,GAAGyB,aAAa,CAACzB,cAAc;IACjDjE,4BAA4B,CAAC2B,OAAO,CAACwC,GAAG,IAAI;MAC1C,IAAIsB,SAAS,CAACtB,GAAG,CAAC,KAAKlB,SAAS,EAAE;QAChCgB,cAAc,GAAG,IAAI;QACrB,IAAIwB,SAAS,CAACtB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UACzBsB,SAAS,CAACtB,GAAG,CAAC,GAAGC,QAAQ;QAC3B;MACF;IACF,CAAC,CAAC;IACFZ,YAAY,CAACZ,MAAM,CAAClC,KAAK,CAAC,GAAGpB,YAAY,CAACoG,aAAa,EAAErG,QAAQ,CAAC,CAAC,CAAC,EAAEiF,oBAAoB,CAACmB,SAAS,CAAClB,IAAI,CAAC,EAAEkB,SAAS,EAAE;MACrHxB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,IAAIW,uBAAuB,IAAI,CAACE,wBAAwB,EAAE;IACxD3C,MAAM,CAACC,IAAI,CAACoB,YAAY,CAACZ,MAAM,CAAC,CAACjB,OAAO,CAACjB,KAAK,IAAI;MAChD,IAAI,CAAC0E,aAAa,CAAC1E,KAAK,CAAC,EAAE;QACzB,OAAO8C,YAAY,CAACZ,MAAM,CAAClC,KAAK,CAAC;MACnC;IACF,CAAC,CAAC;EACJ;EACA,MAAMiF,6BAA6B,GAAGjB,MAAM,CAACK,OAAO,CAACa,4BAA4B,CAAC,gBAAgB,EAAEpC,YAAY,CAAC;EACjH,MAAMqC,+BAA+B,GAAGtC,iBAAiB,CAACoC,6BAA6B,EAAElC,YAAY,CAAC;EACtG,OAAOpB,mBAAmB,CAACwD,+BAA+B,EAAEnB,MAAM,CAACK,OAAO,CAACe,iBAAiB,GAAG,CAAC,IAAI7C,SAAS,CAAC;AAChH,CAAC;AACD,OAAO,SAAS8C,gCAAgCA,CAAC;EAC/CC,mBAAmB;EACnBtB,MAAM;EACNuB,gBAAgB;EAChBC,eAAe;EACfC;AACF,CAAC,EAAE;EACD,IAAIC,6BAA6B,GAAGJ,mBAAmB;EACvD,IAAIK,iBAAiB,GAAG,KAAK;;EAE7B;EACA,OAAO,CAACA,iBAAiB,IAAID,6BAA6B,IAAI,CAAC,EAAE;IAC/DC,iBAAiB,GAAG,IAAI;IACxB,KAAK,IAAIvE,CAAC,GAAGmE,gBAAgB,EAAEnE,CAAC,GAAGoE,eAAe,EAAEpE,CAAC,IAAI,CAAC,EAAE;MAC1D,MAAMwE,GAAG,GAAGH,WAAW,CAACrE,CAAC,CAAC;MAC1B,IAAIwE,GAAG,EAAE;QACP,MAAMC,KAAK,GAAGJ,WAAW,CAACrE,CAAC,CAAC,CAAC0E,EAAE;QAC/B,MAAMC,eAAe,GAAG/B,MAAM,CAACK,OAAO,CAAC2B,2BAA2B,CAACH,KAAK,EAAEH,6BAA6B,CAAC;QACxG,IAAIK,eAAe,IAAIA,eAAe,CAACE,gBAAgB,IAAIF,eAAe,CAACG,oBAAoB,GAAGR,6BAA6B,EAAE;UAC/HA,6BAA6B,GAAGK,eAAe,CAACG,oBAAoB;UACpEP,iBAAiB,GAAG,KAAK;UACzB,MAAM,CAAC;QACT;MACF;IACF;EACF;EACA,OAAOD,6BAA6B;AACtC;AACA,OAAO,SAASS,oBAAoBA,CAACnC,MAAM,EAAEoC,KAAK,EAAE;EAClD,IAAIA,KAAK,CAACC,QAAQ,EAAE;IAClB,OAAO,CAAC;EACV;EACA,MAAMC,aAAa,GAAGnH,yBAAyB,CAAC6E,MAAM,CAAC;EACvD,MAAMuC,QAAQ,GAAGlH,sCAAsC,CAAC2E,MAAM,CAAC;EAC/D,MAAMwC,wBAAwB,GAAGpH,kCAAkC,CAAC4E,MAAM,CAAC;EAC3E,MAAMyC,mBAAmB,GAAG9D,IAAI,CAAC+D,KAAK,CAACN,KAAK,CAACO,kBAAkB,GAAGL,aAAa,CAAC;EAChF,MAAMM,wBAAwB,GAAGjE,IAAI,CAAC+D,KAAK,CAAC,CAACN,KAAK,CAACS,uBAAuB,IAAIT,KAAK,CAACO,kBAAkB,IAAIL,aAAa,CAAC;EACxH,MAAMQ,mBAAmB,GAAGN,wBAAwB,GAAG7D,IAAI,CAAC+D,KAAK,CAAC,CAACN,KAAK,CAACW,kBAAkB,IAAIX,KAAK,CAACO,kBAAkB,IAAIL,aAAa,CAAC,GAAG,CAAC;EAC7I,OAAOG,mBAAmB,GAAGG,wBAAwB,GAAGL,QAAQ,GAAGO,mBAAmB;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}