{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../../../../hooks/features/columns/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnMenuHideItem(props) {\n  const {\n    colDef,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n  const columnsWithMenu = visibleColumns.filter(col => col.disableColumnMenu !== true);\n  // do not allow to hide the last column with menu\n  const disabled = columnsWithMenu.length === 1;\n  const toggleColumn = React.useCallback(event => {\n    /**\n     * Disabled `MenuItem` would trigger `click` event\n     * after imperative `.click()` call on HTML element.\n     * Also, click is triggered in testing environment as well.\n     */\n    if (disabled) {\n      return;\n    }\n    apiRef.current.setColumnVisibility(colDef.field, false);\n    onClick(event);\n  }, [apiRef, colDef.field, onClick, disabled]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  if (colDef.hideable === false) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: toggleColumn,\n    disabled: disabled,\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuHideIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('columnMenuHideColumn')\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuHideItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuHideItem };", "map": {"version": 3, "names": ["React", "PropTypes", "useGridApiContext", "useGridRootProps", "gridVisibleColumnDefinitionsSelector", "jsx", "_jsx", "GridColumnMenuHideItem", "props", "colDef", "onClick", "apiRef", "rootProps", "visibleColumns", "columnsWithMenu", "filter", "col", "disableColumnMenu", "disabled", "length", "toggleColumn", "useCallback", "event", "current", "setColumnVisibility", "field", "disableColumnSelector", "hideable", "slots", "baseMenuItem", "iconStart", "columnMenuHideIcon", "fontSize", "children", "getLocaleText", "process", "env", "NODE_ENV", "propTypes", "object", "isRequired", "func"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/menu/columnMenu/menuItems/GridColumnMenuHideItem.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useGridApiContext } from \"../../../../hooks/utils/useGridApiContext.js\";\nimport { useGridRootProps } from \"../../../../hooks/utils/useGridRootProps.js\";\nimport { gridVisibleColumnDefinitionsSelector } from \"../../../../hooks/features/columns/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnMenuHideItem(props) {\n  const {\n    colDef,\n    onClick\n  } = props;\n  const apiRef = useGridApiContext();\n  const rootProps = useGridRootProps();\n  const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n  const columnsWithMenu = visibleColumns.filter(col => col.disableColumnMenu !== true);\n  // do not allow to hide the last column with menu\n  const disabled = columnsWithMenu.length === 1;\n  const toggleColumn = React.useCallback(event => {\n    /**\n     * Disabled `MenuItem` would trigger `click` event\n     * after imperative `.click()` call on HTML element.\n     * Also, click is triggered in testing environment as well.\n     */\n    if (disabled) {\n      return;\n    }\n    apiRef.current.setColumnVisibility(colDef.field, false);\n    onClick(event);\n  }, [apiRef, colDef.field, onClick, disabled]);\n  if (rootProps.disableColumnSelector) {\n    return null;\n  }\n  if (colDef.hideable === false) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.baseMenuItem, {\n    onClick: toggleColumn,\n    disabled: disabled,\n    iconStart: /*#__PURE__*/_jsx(rootProps.slots.columnMenuHideIcon, {\n      fontSize: \"small\"\n    }),\n    children: apiRef.current.getLocaleText('columnMenuHideColumn')\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnMenuHideItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  colDef: PropTypes.object.isRequired,\n  onClick: PropTypes.func.isRequired\n} : void 0;\nexport { GridColumnMenuHideItem };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,gBAAgB,QAAQ,6CAA6C;AAC9E,SAASC,oCAAoC,QAAQ,6CAA6C;AAClG,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,sBAAsBA,CAACC,KAAK,EAAE;EACrC,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,KAAK;EACT,MAAMG,MAAM,GAAGT,iBAAiB,CAAC,CAAC;EAClC,MAAMU,SAAS,GAAGT,gBAAgB,CAAC,CAAC;EACpC,MAAMU,cAAc,GAAGT,oCAAoC,CAACO,MAAM,CAAC;EACnE,MAAMG,eAAe,GAAGD,cAAc,CAACE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,iBAAiB,KAAK,IAAI,CAAC;EACpF;EACA,MAAMC,QAAQ,GAAGJ,eAAe,CAACK,MAAM,KAAK,CAAC;EAC7C,MAAMC,YAAY,GAAGpB,KAAK,CAACqB,WAAW,CAACC,KAAK,IAAI;IAC9C;AACJ;AACA;AACA;AACA;IACI,IAAIJ,QAAQ,EAAE;MACZ;IACF;IACAP,MAAM,CAACY,OAAO,CAACC,mBAAmB,CAACf,MAAM,CAACgB,KAAK,EAAE,KAAK,CAAC;IACvDf,OAAO,CAACY,KAAK,CAAC;EAChB,CAAC,EAAE,CAACX,MAAM,EAAEF,MAAM,CAACgB,KAAK,EAAEf,OAAO,EAAEQ,QAAQ,CAAC,CAAC;EAC7C,IAAIN,SAAS,CAACc,qBAAqB,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAIjB,MAAM,CAACkB,QAAQ,KAAK,KAAK,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,OAAO,aAAarB,IAAI,CAACM,SAAS,CAACgB,KAAK,CAACC,YAAY,EAAE;IACrDnB,OAAO,EAAEU,YAAY;IACrBF,QAAQ,EAAEA,QAAQ;IAClBY,SAAS,EAAE,aAAaxB,IAAI,CAACM,SAAS,CAACgB,KAAK,CAACG,kBAAkB,EAAE;MAC/DC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFC,QAAQ,EAAEtB,MAAM,CAACY,OAAO,CAACW,aAAa,CAAC,sBAAsB;EAC/D,CAAC,CAAC;AACJ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9B,sBAAsB,CAAC+B,SAAS,GAAG;EACzE;EACA;EACA;EACA;EACA7B,MAAM,EAAER,SAAS,CAACsC,MAAM,CAACC,UAAU;EACnC9B,OAAO,EAAET,SAAS,CAACwC,IAAI,CAACD;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASjC,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}