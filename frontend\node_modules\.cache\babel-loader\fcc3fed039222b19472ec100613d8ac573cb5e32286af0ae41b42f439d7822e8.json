{"ast": null, "code": "import { GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD } from \"../constants.js\";\nexport const getRowGroupingCriteriaFromGroupingField = groupingColDefField => {\n  const match = groupingColDefField.match(/^__row_group_by_columns_group_(.*)__$/);\n  if (!match) {\n    return null;\n  }\n  return match[1];\n};\nexport const isGroupingColumn = field => field === GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD || getRowGroupingCriteriaFromGroupingField(field) !== null;", "map": {"version": 3, "names": ["GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD", "getRowGroupingCriteriaFromGroupingField", "groupingColDefField", "match", "isGroupingColumn", "field"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/internals/utils/gridRowGroupingUtils.js"], "sourcesContent": ["import { GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD } from \"../constants.js\";\nexport const getRowGroupingCriteriaFromGroupingField = groupingColDefField => {\n  const match = groupingColDefField.match(/^__row_group_by_columns_group_(.*)__$/);\n  if (!match) {\n    return null;\n  }\n  return match[1];\n};\nexport const isGroupingColumn = field => field === GRID_ROW_GROUPING_SINGLE_GROUPING_FIELD || getRowGroupingCriteriaFromGroupingField(field) !== null;"], "mappings": "AAAA,SAASA,uCAAuC,QAAQ,iBAAiB;AACzE,OAAO,MAAMC,uCAAuC,GAAGC,mBAAmB,IAAI;EAC5E,MAAMC,KAAK,GAAGD,mBAAmB,CAACC,KAAK,CAAC,uCAAuC,CAAC;EAChF,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,OAAOA,KAAK,CAAC,CAAC,CAAC;AACjB,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGC,KAAK,IAAIA,KAAK,KAAKL,uCAAuC,IAAIC,uCAAuC,CAACI,KAAK,CAAC,KAAK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}