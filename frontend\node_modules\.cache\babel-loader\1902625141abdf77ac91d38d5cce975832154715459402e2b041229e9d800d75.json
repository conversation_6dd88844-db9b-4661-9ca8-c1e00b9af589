{"ast": null, "code": "function isRegExp(value) {\n  return value instanceof RegExp;\n}\nexport { isRegExp };", "map": {"version": 3, "names": ["isRegExp", "value", "RegExp"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isRegExp.mjs"], "sourcesContent": ["function isRegExp(value) {\n    return value instanceof RegExp;\n}\n\nexport { isRegExp };\n"], "mappings": "AAAA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,YAAYC,MAAM;AAClC;AAEA,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}