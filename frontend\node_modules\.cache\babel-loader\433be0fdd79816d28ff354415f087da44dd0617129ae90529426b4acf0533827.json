{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridLogger, useGridApiMethod, useGridEvent } from \"../../utils/index.js\";\nimport { gridColumnMenuSelector } from \"./columnMenuSelector.js\";\nimport { gridColumnLookupSelector, gridColumnVisibilityModelSelector, gridColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nexport const columnMenuStateInitializer = state => _extends({}, state, {\n  columnMenu: {\n    open: false\n  }\n});\n\n/**\n * @requires useGridColumnResize (event)\n * @requires useGridInfiniteLoader (event)\n */\nexport const useGridColumnMenu = apiRef => {\n  const logger = useGridLogger(apiRef, 'useGridColumnMenu');\n\n  /**\n   * API METHODS\n   */\n  const showColumnMenu = React.useCallback(field => {\n    const columnMenuState = gridColumnMenuSelector(apiRef);\n    const newState = {\n      open: true,\n      field\n    };\n    const shouldUpdate = newState.open !== columnMenuState.open || newState.field !== columnMenuState.field;\n    if (shouldUpdate) {\n      apiRef.current.setState(state => {\n        if (state.columnMenu.open && state.columnMenu.field === field) {\n          return state;\n        }\n        logger.debug('Opening Column Menu');\n        return _extends({}, state, {\n          columnMenu: {\n            open: true,\n            field\n          }\n        });\n      });\n      apiRef.current.hidePreferences();\n    }\n  }, [apiRef, logger]);\n  const hideColumnMenu = React.useCallback(() => {\n    const columnMenuState = gridColumnMenuSelector(apiRef);\n    if (columnMenuState.field) {\n      const columnLookup = gridColumnLookupSelector(apiRef);\n      const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n      const orderedFields = gridColumnFieldsSelector(apiRef);\n      let fieldToFocus = columnMenuState.field;\n\n      // If the column was removed from the grid, we need to find the closest visible field\n      if (!columnLookup[fieldToFocus]) {\n        fieldToFocus = orderedFields[0];\n      }\n\n      // If the field to focus is hidden, we need to find the closest visible field\n      if (columnVisibilityModel[fieldToFocus] === false) {\n        // contains visible column fields + the field that was just hidden\n        const visibleOrderedFields = orderedFields.filter(field => {\n          if (field === fieldToFocus) {\n            return true;\n          }\n          return columnVisibilityModel[field] !== false;\n        });\n        const fieldIndex = visibleOrderedFields.indexOf(fieldToFocus);\n        fieldToFocus = visibleOrderedFields[fieldIndex + 1] || visibleOrderedFields[fieldIndex - 1];\n      }\n      apiRef.current.setColumnHeaderFocus(fieldToFocus);\n    }\n    const newState = {\n      open: false,\n      field: undefined\n    };\n    const shouldUpdate = newState.open !== columnMenuState.open || newState.field !== columnMenuState.field;\n    if (shouldUpdate) {\n      apiRef.current.setState(state => {\n        logger.debug('Hiding Column Menu');\n        return _extends({}, state, {\n          columnMenu: newState\n        });\n      });\n    }\n  }, [apiRef, logger]);\n  const toggleColumnMenu = React.useCallback(field => {\n    logger.debug('Toggle Column Menu');\n    const columnMenu = gridColumnMenuSelector(apiRef);\n    if (!columnMenu.open || columnMenu.field !== field) {\n      showColumnMenu(field);\n    } else {\n      hideColumnMenu();\n    }\n  }, [apiRef, logger, showColumnMenu, hideColumnMenu]);\n  const columnMenuApi = {\n    showColumnMenu,\n    hideColumnMenu,\n    toggleColumnMenu\n  };\n  useGridApiMethod(apiRef, columnMenuApi, 'public');\n  useGridEvent(apiRef, 'columnResizeStart', hideColumnMenu);\n  useGridEvent(apiRef, 'virtualScrollerWheel', apiRef.current.hideColumnMenu);\n  useGridEvent(apiRef, 'virtualScrollerTouchMove', apiRef.current.hideColumnMenu);\n};", "map": {"version": 3, "names": ["_extends", "React", "useGridLogger", "useGridApiMethod", "useGridEvent", "gridColumnMenuSelector", "gridColumnLookupSelector", "gridColumnVisibilityModelSelector", "gridColumnFieldsSelector", "columnMenuStateInitializer", "state", "columnMenu", "open", "useGridColumnMenu", "apiRef", "logger", "showColumnMenu", "useCallback", "field", "columnMenuState", "newState", "shouldUpdate", "current", "setState", "debug", "hidePreferences", "hideColumnMenu", "columnLookup", "columnVisibilityModel", "orderedFields", "fieldToFocus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "filter", "fieldIndex", "indexOf", "setColumnHeaderFocus", "undefined", "toggleColumnMenu", "columnMenuApi"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columnMenu/useGridColumnMenu.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridLogger, useGridApiMethod, useGridEvent } from \"../../utils/index.js\";\nimport { gridColumnMenuSelector } from \"./columnMenuSelector.js\";\nimport { gridColumnLookupSelector, gridColumnVisibilityModelSelector, gridColumnFieldsSelector } from \"../columns/gridColumnsSelector.js\";\nexport const columnMenuStateInitializer = state => _extends({}, state, {\n  columnMenu: {\n    open: false\n  }\n});\n\n/**\n * @requires useGridColumnResize (event)\n * @requires useGridInfiniteLoader (event)\n */\nexport const useGridColumnMenu = apiRef => {\n  const logger = useGridLogger(apiRef, 'useGridColumnMenu');\n\n  /**\n   * API METHODS\n   */\n  const showColumnMenu = React.useCallback(field => {\n    const columnMenuState = gridColumnMenuSelector(apiRef);\n    const newState = {\n      open: true,\n      field\n    };\n    const shouldUpdate = newState.open !== columnMenuState.open || newState.field !== columnMenuState.field;\n    if (shouldUpdate) {\n      apiRef.current.setState(state => {\n        if (state.columnMenu.open && state.columnMenu.field === field) {\n          return state;\n        }\n        logger.debug('Opening Column Menu');\n        return _extends({}, state, {\n          columnMenu: {\n            open: true,\n            field\n          }\n        });\n      });\n      apiRef.current.hidePreferences();\n    }\n  }, [apiRef, logger]);\n  const hideColumnMenu = React.useCallback(() => {\n    const columnMenuState = gridColumnMenuSelector(apiRef);\n    if (columnMenuState.field) {\n      const columnLookup = gridColumnLookupSelector(apiRef);\n      const columnVisibilityModel = gridColumnVisibilityModelSelector(apiRef);\n      const orderedFields = gridColumnFieldsSelector(apiRef);\n      let fieldToFocus = columnMenuState.field;\n\n      // If the column was removed from the grid, we need to find the closest visible field\n      if (!columnLookup[fieldToFocus]) {\n        fieldToFocus = orderedFields[0];\n      }\n\n      // If the field to focus is hidden, we need to find the closest visible field\n      if (columnVisibilityModel[fieldToFocus] === false) {\n        // contains visible column fields + the field that was just hidden\n        const visibleOrderedFields = orderedFields.filter(field => {\n          if (field === fieldToFocus) {\n            return true;\n          }\n          return columnVisibilityModel[field] !== false;\n        });\n        const fieldIndex = visibleOrderedFields.indexOf(fieldToFocus);\n        fieldToFocus = visibleOrderedFields[fieldIndex + 1] || visibleOrderedFields[fieldIndex - 1];\n      }\n      apiRef.current.setColumnHeaderFocus(fieldToFocus);\n    }\n    const newState = {\n      open: false,\n      field: undefined\n    };\n    const shouldUpdate = newState.open !== columnMenuState.open || newState.field !== columnMenuState.field;\n    if (shouldUpdate) {\n      apiRef.current.setState(state => {\n        logger.debug('Hiding Column Menu');\n        return _extends({}, state, {\n          columnMenu: newState\n        });\n      });\n    }\n  }, [apiRef, logger]);\n  const toggleColumnMenu = React.useCallback(field => {\n    logger.debug('Toggle Column Menu');\n    const columnMenu = gridColumnMenuSelector(apiRef);\n    if (!columnMenu.open || columnMenu.field !== field) {\n      showColumnMenu(field);\n    } else {\n      hideColumnMenu();\n    }\n  }, [apiRef, logger, showColumnMenu, hideColumnMenu]);\n  const columnMenuApi = {\n    showColumnMenu,\n    hideColumnMenu,\n    toggleColumnMenu\n  };\n  useGridApiMethod(apiRef, columnMenuApi, 'public');\n  useGridEvent(apiRef, 'columnResizeStart', hideColumnMenu);\n  useGridEvent(apiRef, 'virtualScrollerWheel', apiRef.current.hideColumnMenu);\n  useGridEvent(apiRef, 'virtualScrollerTouchMove', apiRef.current.hideColumnMenu);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,YAAY,QAAQ,sBAAsB;AACpF,SAASC,sBAAsB,QAAQ,yBAAyB;AAChE,SAASC,wBAAwB,EAAEC,iCAAiC,EAAEC,wBAAwB,QAAQ,mCAAmC;AACzI,OAAO,MAAMC,0BAA0B,GAAGC,KAAK,IAAIV,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAE;EACrEC,UAAU,EAAE;IACVC,IAAI,EAAE;EACR;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGC,MAAM,IAAI;EACzC,MAAMC,MAAM,GAAGb,aAAa,CAACY,MAAM,EAAE,mBAAmB,CAAC;;EAEzD;AACF;AACA;EACE,MAAME,cAAc,GAAGf,KAAK,CAACgB,WAAW,CAACC,KAAK,IAAI;IAChD,MAAMC,eAAe,GAAGd,sBAAsB,CAACS,MAAM,CAAC;IACtD,MAAMM,QAAQ,GAAG;MACfR,IAAI,EAAE,IAAI;MACVM;IACF,CAAC;IACD,MAAMG,YAAY,GAAGD,QAAQ,CAACR,IAAI,KAAKO,eAAe,CAACP,IAAI,IAAIQ,QAAQ,CAACF,KAAK,KAAKC,eAAe,CAACD,KAAK;IACvG,IAAIG,YAAY,EAAE;MAChBP,MAAM,CAACQ,OAAO,CAACC,QAAQ,CAACb,KAAK,IAAI;QAC/B,IAAIA,KAAK,CAACC,UAAU,CAACC,IAAI,IAAIF,KAAK,CAACC,UAAU,CAACO,KAAK,KAAKA,KAAK,EAAE;UAC7D,OAAOR,KAAK;QACd;QACAK,MAAM,CAACS,KAAK,CAAC,qBAAqB,CAAC;QACnC,OAAOxB,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAE;UACzBC,UAAU,EAAE;YACVC,IAAI,EAAE,IAAI;YACVM;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACFJ,MAAM,CAACQ,OAAO,CAACG,eAAe,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,CAACX,MAAM,EAAEC,MAAM,CAAC,CAAC;EACpB,MAAMW,cAAc,GAAGzB,KAAK,CAACgB,WAAW,CAAC,MAAM;IAC7C,MAAME,eAAe,GAAGd,sBAAsB,CAACS,MAAM,CAAC;IACtD,IAAIK,eAAe,CAACD,KAAK,EAAE;MACzB,MAAMS,YAAY,GAAGrB,wBAAwB,CAACQ,MAAM,CAAC;MACrD,MAAMc,qBAAqB,GAAGrB,iCAAiC,CAACO,MAAM,CAAC;MACvE,MAAMe,aAAa,GAAGrB,wBAAwB,CAACM,MAAM,CAAC;MACtD,IAAIgB,YAAY,GAAGX,eAAe,CAACD,KAAK;;MAExC;MACA,IAAI,CAACS,YAAY,CAACG,YAAY,CAAC,EAAE;QAC/BA,YAAY,GAAGD,aAAa,CAAC,CAAC,CAAC;MACjC;;MAEA;MACA,IAAID,qBAAqB,CAACE,YAAY,CAAC,KAAK,KAAK,EAAE;QACjD;QACA,MAAMC,oBAAoB,GAAGF,aAAa,CAACG,MAAM,CAACd,KAAK,IAAI;UACzD,IAAIA,KAAK,KAAKY,YAAY,EAAE;YAC1B,OAAO,IAAI;UACb;UACA,OAAOF,qBAAqB,CAACV,KAAK,CAAC,KAAK,KAAK;QAC/C,CAAC,CAAC;QACF,MAAMe,UAAU,GAAGF,oBAAoB,CAACG,OAAO,CAACJ,YAAY,CAAC;QAC7DA,YAAY,GAAGC,oBAAoB,CAACE,UAAU,GAAG,CAAC,CAAC,IAAIF,oBAAoB,CAACE,UAAU,GAAG,CAAC,CAAC;MAC7F;MACAnB,MAAM,CAACQ,OAAO,CAACa,oBAAoB,CAACL,YAAY,CAAC;IACnD;IACA,MAAMV,QAAQ,GAAG;MACfR,IAAI,EAAE,KAAK;MACXM,KAAK,EAAEkB;IACT,CAAC;IACD,MAAMf,YAAY,GAAGD,QAAQ,CAACR,IAAI,KAAKO,eAAe,CAACP,IAAI,IAAIQ,QAAQ,CAACF,KAAK,KAAKC,eAAe,CAACD,KAAK;IACvG,IAAIG,YAAY,EAAE;MAChBP,MAAM,CAACQ,OAAO,CAACC,QAAQ,CAACb,KAAK,IAAI;QAC/BK,MAAM,CAACS,KAAK,CAAC,oBAAoB,CAAC;QAClC,OAAOxB,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,EAAE;UACzBC,UAAU,EAAES;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACN,MAAM,EAAEC,MAAM,CAAC,CAAC;EACpB,MAAMsB,gBAAgB,GAAGpC,KAAK,CAACgB,WAAW,CAACC,KAAK,IAAI;IAClDH,MAAM,CAACS,KAAK,CAAC,oBAAoB,CAAC;IAClC,MAAMb,UAAU,GAAGN,sBAAsB,CAACS,MAAM,CAAC;IACjD,IAAI,CAACH,UAAU,CAACC,IAAI,IAAID,UAAU,CAACO,KAAK,KAAKA,KAAK,EAAE;MAClDF,cAAc,CAACE,KAAK,CAAC;IACvB,CAAC,MAAM;MACLQ,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACZ,MAAM,EAAEC,MAAM,EAAEC,cAAc,EAAEU,cAAc,CAAC,CAAC;EACpD,MAAMY,aAAa,GAAG;IACpBtB,cAAc;IACdU,cAAc;IACdW;EACF,CAAC;EACDlC,gBAAgB,CAACW,MAAM,EAAEwB,aAAa,EAAE,QAAQ,CAAC;EACjDlC,YAAY,CAACU,MAAM,EAAE,mBAAmB,EAAEY,cAAc,CAAC;EACzDtB,YAAY,CAACU,MAAM,EAAE,sBAAsB,EAAEA,MAAM,CAACQ,OAAO,CAACI,cAAc,CAAC;EAC3EtB,YAAY,CAACU,MAAM,EAAE,0BAA0B,EAAEA,MAAM,CAACQ,OAAO,CAACI,cAAc,CAAC;AACjF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}