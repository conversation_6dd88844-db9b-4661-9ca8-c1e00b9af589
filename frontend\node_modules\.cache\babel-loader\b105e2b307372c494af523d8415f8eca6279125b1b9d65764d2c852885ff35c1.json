{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { useGridApiContext } from \"../../../hooks/utils/useGridApiContext.js\";\nimport { GridMenu } from \"../GridMenu.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnHeaderMenu({\n  columnMenuId,\n  columnMenuButtonId,\n  ContentComponent,\n  contentComponentProps,\n  field,\n  open,\n  target,\n  onExited\n}) {\n  const apiRef = useGridApiContext();\n  const colDef = apiRef.current.getColumn(field);\n  const hideMenu = useEventCallback(event => {\n    if (event) {\n      // Prevent triggering the sorting\n      event.stopPropagation();\n      if (target?.contains(event.target)) {\n        return;\n      }\n    }\n    apiRef.current.hideColumnMenu();\n  });\n  if (!target || !colDef) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridMenu, {\n    position: `bottom-${colDef.align === 'right' ? 'start' : 'end'}`,\n    open: open,\n    target: target,\n    onClose: hideMenu,\n    onExited: onExited,\n    children: /*#__PURE__*/_jsx(ContentComponent, _extends({\n      colDef: colDef,\n      hideMenu: hideMenu,\n      open: open,\n      id: columnMenuId,\n      labelledby: columnMenuButtonId\n    }, contentComponentProps))\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnMenuButtonId: PropTypes.string,\n  columnMenuId: PropTypes.string,\n  ContentComponent: PropTypes.elementType.isRequired,\n  contentComponentProps: PropTypes.any,\n  field: PropTypes.string.isRequired,\n  onExited: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  target: HTMLElementType\n} : void 0;\nexport { GridColumnHeaderMenu };", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "useEventCallback", "HTMLElementType", "useGridApiContext", "GridMenu", "jsx", "_jsx", "GridColumnHeaderMenu", "columnMenuId", "columnMenuButtonId", "ContentComponent", "contentComponentProps", "field", "open", "target", "onExited", "apiRef", "colDef", "current", "getColumn", "hideMenu", "event", "stopPropagation", "contains", "hideColumnMenu", "position", "align", "onClose", "children", "id", "<PERSON>by", "process", "env", "NODE_ENV", "propTypes", "string", "elementType", "isRequired", "any", "func", "bool"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/menu/columnMenu/GridColumnHeaderMenu.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport { useGridApiContext } from \"../../../hooks/utils/useGridApiContext.js\";\nimport { GridMenu } from \"../GridMenu.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GridColumnHeaderMenu({\n  columnMenuId,\n  columnMenuButtonId,\n  ContentComponent,\n  contentComponentProps,\n  field,\n  open,\n  target,\n  onExited\n}) {\n  const apiRef = useGridApiContext();\n  const colDef = apiRef.current.getColumn(field);\n  const hideMenu = useEventCallback(event => {\n    if (event) {\n      // Prevent triggering the sorting\n      event.stopPropagation();\n      if (target?.contains(event.target)) {\n        return;\n      }\n    }\n    apiRef.current.hideColumnMenu();\n  });\n  if (!target || !colDef) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GridMenu, {\n    position: `bottom-${colDef.align === 'right' ? 'start' : 'end'}`,\n    open: open,\n    target: target,\n    onClose: hideMenu,\n    onExited: onExited,\n    children: /*#__PURE__*/_jsx(ContentComponent, _extends({\n      colDef: colDef,\n      hideMenu: hideMenu,\n      open: open,\n      id: columnMenuId,\n      labelledby: columnMenuButtonId\n    }, contentComponentProps))\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridColumnHeaderMenu.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  columnMenuButtonId: PropTypes.string,\n  columnMenuId: PropTypes.string,\n  ContentComponent: PropTypes.elementType.isRequired,\n  contentComponentProps: PropTypes.any,\n  field: PropTypes.string.isRequired,\n  onExited: PropTypes.func,\n  open: PropTypes.bool.isRequired,\n  target: HTMLElementType\n} : void 0;\nexport { GridColumnHeaderMenu };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,eAAe,MAAM,4BAA4B;AACxD,SAASC,iBAAiB,QAAQ,2CAA2C;AAC7E,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,oBAAoBA,CAAC;EAC5BC,YAAY;EACZC,kBAAkB;EAClBC,gBAAgB;EAChBC,qBAAqB;EACrBC,KAAK;EACLC,IAAI;EACJC,MAAM;EACNC;AACF,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGb,iBAAiB,CAAC,CAAC;EAClC,MAAMc,MAAM,GAAGD,MAAM,CAACE,OAAO,CAACC,SAAS,CAACP,KAAK,CAAC;EAC9C,MAAMQ,QAAQ,GAAGnB,gBAAgB,CAACoB,KAAK,IAAI;IACzC,IAAIA,KAAK,EAAE;MACT;MACAA,KAAK,CAACC,eAAe,CAAC,CAAC;MACvB,IAAIR,MAAM,EAAES,QAAQ,CAACF,KAAK,CAACP,MAAM,CAAC,EAAE;QAClC;MACF;IACF;IACAE,MAAM,CAACE,OAAO,CAACM,cAAc,CAAC,CAAC;EACjC,CAAC,CAAC;EACF,IAAI,CAACV,MAAM,IAAI,CAACG,MAAM,EAAE;IACtB,OAAO,IAAI;EACb;EACA,OAAO,aAAaX,IAAI,CAACF,QAAQ,EAAE;IACjCqB,QAAQ,EAAE,UAAUR,MAAM,CAACS,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG,KAAK,EAAE;IAChEb,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACda,OAAO,EAAEP,QAAQ;IACjBL,QAAQ,EAAEA,QAAQ;IAClBa,QAAQ,EAAE,aAAatB,IAAI,CAACI,gBAAgB,EAAEZ,QAAQ,CAAC;MACrDmB,MAAM,EAAEA,MAAM;MACdG,QAAQ,EAAEA,QAAQ;MAClBP,IAAI,EAAEA,IAAI;MACVgB,EAAE,EAAErB,YAAY;MAChBsB,UAAU,EAAErB;IACd,CAAC,EAAEE,qBAAqB,CAAC;EAC3B,CAAC,CAAC;AACJ;AACAoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,oBAAoB,CAAC2B,SAAS,GAAG;EACvE;EACA;EACA;EACA;EACAzB,kBAAkB,EAAET,SAAS,CAACmC,MAAM;EACpC3B,YAAY,EAAER,SAAS,CAACmC,MAAM;EAC9BzB,gBAAgB,EAAEV,SAAS,CAACoC,WAAW,CAACC,UAAU;EAClD1B,qBAAqB,EAAEX,SAAS,CAACsC,GAAG;EACpC1B,KAAK,EAAEZ,SAAS,CAACmC,MAAM,CAACE,UAAU;EAClCtB,QAAQ,EAAEf,SAAS,CAACuC,IAAI;EACxB1B,IAAI,EAAEb,SAAS,CAACwC,IAAI,CAACH,UAAU;EAC/BvB,MAAM,EAAEZ;AACV,CAAC,GAAG,KAAK,CAAC;AACV,SAASK,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}