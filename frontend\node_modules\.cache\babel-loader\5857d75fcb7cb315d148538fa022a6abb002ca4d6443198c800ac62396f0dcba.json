{"ast": null, "code": "export { isObjectEmpty } from \"./isObjectEmpty.js\";", "map": {"version": 3, "names": ["isObjectEmpty"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/isObjectEmpty/index.js"], "sourcesContent": ["export { isObjectEmpty } from \"./isObjectEmpty.js\";"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}