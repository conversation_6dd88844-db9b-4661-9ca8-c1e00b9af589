{"ast": null, "code": "function isFunction(value) {\n  return typeof value === 'function';\n}\nexport { isFunction };", "map": {"version": 3, "names": ["isFunction", "value"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isFunction.mjs"], "sourcesContent": ["function isFunction(value) {\n    return typeof value === 'function';\n}\n\nexport { isFunction };\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AAEA,SAASD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}