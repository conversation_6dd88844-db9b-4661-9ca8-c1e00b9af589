{"ast": null, "code": "function dropWhile(arr, canContinueDropping) {\n  const dropEndIndex = arr.findIndex((item, index, arr) => !canContinueDropping(item, index, arr));\n  if (dropEndIndex === -1) {\n    return [];\n  }\n  return arr.slice(dropEndIndex);\n}\nexport { dropWhile };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "arr", "canContinueDropping", "dropEndIndex", "findIndex", "item", "index", "slice"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/dropWhile.mjs"], "sourcesContent": ["function dropWhile(arr, canContinueDropping) {\n    const dropEndIndex = arr.findIndex((item, index, arr) => !canContinueDropping(item, index, arr));\n    if (dropEndIndex === -1) {\n        return [];\n    }\n    return arr.slice(dropEndIndex);\n}\n\nexport { dropWhile };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,GAAG,EAAEC,mBAAmB,EAAE;EACzC,MAAMC,YAAY,GAAGF,GAAG,CAACG,SAAS,CAAC,CAACC,IAAI,EAAEC,KAAK,EAAEL,GAAG,KAAK,CAACC,mBAAmB,CAACG,IAAI,EAAEC,KAAK,EAAEL,GAAG,CAAC,CAAC;EAChG,IAAIE,YAAY,KAAK,CAAC,CAAC,EAAE;IACrB,OAAO,EAAE;EACb;EACA,OAAOF,GAAG,CAACM,KAAK,CAACJ,YAAY,CAAC;AAClC;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}