{"ast": null, "code": "function spread(func) {\n  return function (argsArr) {\n    return func.apply(this, argsArr);\n  };\n}\nexport { spread };", "map": {"version": 3, "names": ["spread", "func", "argsArr", "apply"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/spread.mjs"], "sourcesContent": ["function spread(func) {\n    return function (argsArr) {\n        return func.apply(this, argsArr);\n    };\n}\n\nexport { spread };\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,IAAI,EAAE;EAClB,OAAO,UAAUC,OAAO,EAAE;IACtB,OAAOD,IAAI,CAACE,KAAK,CAAC,IAAI,EAAED,OAAO,CAAC;EACpC,CAAC;AACL;AAEA,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}