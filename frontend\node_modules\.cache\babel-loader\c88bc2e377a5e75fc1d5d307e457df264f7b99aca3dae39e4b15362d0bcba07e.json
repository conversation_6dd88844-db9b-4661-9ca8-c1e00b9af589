{"ast": null, "code": "import { AbortError } from '../error/AbortError.mjs';\nfunction delay(ms, {\n  signal\n} = {}) {\n  return new Promise((resolve, reject) => {\n    const abortError = () => {\n      reject(new AbortError());\n    };\n    const abortHandler = () => {\n      clearTimeout(timeoutId);\n      abortError();\n    };\n    if (signal?.aborted) {\n      return abortError();\n    }\n    const timeoutId = setTimeout(() => {\n      signal?.removeEventListener('abort', abortHandler);\n      resolve();\n    }, ms);\n    signal?.addEventListener('abort', abortHandler, {\n      once: true\n    });\n  });\n}\nexport { delay };", "map": {"version": 3, "names": ["AbortError", "delay", "ms", "signal", "Promise", "resolve", "reject", "abortError", "abor<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "timeoutId", "aborted", "setTimeout", "removeEventListener", "addEventListener", "once"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/promise/delay.mjs"], "sourcesContent": ["import { AbortError } from '../error/AbortError.mjs';\n\nfunction delay(ms, { signal } = {}) {\n    return new Promise((resolve, reject) => {\n        const abortError = () => {\n            reject(new AbortError());\n        };\n        const abortHandler = () => {\n            clearTimeout(timeoutId);\n            abortError();\n        };\n        if (signal?.aborted) {\n            return abortError();\n        }\n        const timeoutId = setTimeout(() => {\n            signal?.removeEventListener('abort', abortHandler);\n            resolve();\n        }, ms);\n        signal?.addEventListener('abort', abortHandler, { once: true });\n    });\n}\n\nexport { delay };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,yBAAyB;AAEpD,SAASC,KAAKA,CAACC,EAAE,EAAE;EAAEC;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EAChC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACrBD,MAAM,CAAC,IAAIN,UAAU,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,MAAMQ,YAAY,GAAGA,CAAA,KAAM;MACvBC,YAAY,CAACC,SAAS,CAAC;MACvBH,UAAU,CAAC,CAAC;IAChB,CAAC;IACD,IAAIJ,MAAM,EAAEQ,OAAO,EAAE;MACjB,OAAOJ,UAAU,CAAC,CAAC;IACvB;IACA,MAAMG,SAAS,GAAGE,UAAU,CAAC,MAAM;MAC/BT,MAAM,EAAEU,mBAAmB,CAAC,OAAO,EAAEL,YAAY,CAAC;MAClDH,OAAO,CAAC,CAAC;IACb,CAAC,EAAEH,EAAE,CAAC;IACNC,MAAM,EAAEW,gBAAgB,CAAC,OAAO,EAAEN,YAAY,EAAE;MAAEO,IAAI,EAAE;IAAK,CAAC,CAAC;EACnE,CAAC,CAAC;AACN;AAEA,SAASd,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}