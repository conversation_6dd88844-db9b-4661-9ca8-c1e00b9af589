{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { loadStyleSheets } from '@mui/x-internals/export';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridExpandedRowCountSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { GRID_ID_AUTOGENERATED } from \"../rows/gridRowsUtils.js\";\nimport { defaultGetRowsToExport, getColumnsToExport } from \"./utils.js\";\nimport { getDerivedPaginationModel } from \"../pagination/useGridPaginationModel.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GridPrintExportMenuItem } from \"../../../components/toolbar/index.js\";\nimport { getTotalHeaderHeight } from \"../columns/gridColumnsUtils.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction raf() {\n  return new Promise(resolve => {\n    requestAnimationFrame(() => {\n      resolve();\n    });\n  });\n}\nfunction buildPrintWindow(title) {\n  const iframeEl = document.createElement('iframe');\n  iframeEl.style.position = 'absolute';\n  iframeEl.style.width = '0px';\n  iframeEl.style.height = '0px';\n  iframeEl.title = title || document.title;\n  return iframeEl;\n}\n\n/**\n * @requires useGridColumns (state)\n * @requires useGridFilter (state)\n * @requires useGridSorting (state)\n * @requires useGridParamsApi (method)\n */\nexport const useGridPrintExport = (apiRef, props) => {\n  const hasRootReference = apiRef.current.rootElementRef.current !== null;\n  const logger = useGridLogger(apiRef, 'useGridPrintExport');\n  const doc = React.useRef(null);\n  const previousGridState = React.useRef(null);\n  const previousColumnVisibility = React.useRef({});\n  const previousRows = React.useRef([]);\n  const previousVirtualizationState = React.useRef(null);\n  React.useEffect(() => {\n    doc.current = ownerDocument(apiRef.current.rootElementRef.current);\n  }, [apiRef, hasRootReference]);\n\n  // Returns a promise because updateColumns triggers state update and\n  // the new state needs to be in place before the grid can be sized correctly\n  const updateGridColumnsForPrint = React.useCallback((fields, allColumns, includeCheckboxes) => new Promise(resolve => {\n    const exportedColumnFields = getColumnsToExport({\n      apiRef,\n      options: {\n        fields,\n        allColumns\n      }\n    }).map(column => column.field);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const newColumnVisibilityModel = {};\n    columns.forEach(column => {\n      newColumnVisibilityModel[column.field] = exportedColumnFields.includes(column.field);\n    });\n    if (includeCheckboxes) {\n      newColumnVisibilityModel[GRID_CHECKBOX_SELECTION_COL_DEF.field] = true;\n    }\n    apiRef.current.setColumnVisibilityModel(newColumnVisibilityModel);\n    resolve();\n  }), [apiRef]);\n  const updateGridRowsForPrint = React.useCallback(getRowsToExport => {\n    const rowsToExportIds = getRowsToExport({\n      apiRef\n    });\n    const newRows = rowsToExportIds.reduce((acc, id) => {\n      const row = apiRef.current.getRow(id);\n      if (!row[GRID_ID_AUTOGENERATED]) {\n        acc.push(row);\n      }\n      return acc;\n    }, []);\n    apiRef.current.setRows(newRows);\n  }, [apiRef]);\n  const handlePrintWindowLoad = React.useCallback((printWindow, options) => {\n    const normalizeOptions = _extends({\n      copyStyles: true,\n      hideToolbar: false,\n      hideFooter: false,\n      includeCheckboxes: false\n    }, options);\n    const printDoc = printWindow.contentDocument;\n    if (!printDoc) {\n      return;\n    }\n    const rowsMeta = gridRowsMetaSelector(apiRef);\n    const gridRootElement = apiRef.current.rootElementRef.current;\n    const gridClone = gridRootElement.cloneNode(true);\n\n    // Allow to overflow to not hide the border of the last row\n    const gridMain = gridClone.querySelector(`.${gridClasses.main}`);\n    gridMain.style.overflow = 'visible';\n\n    // See https://support.google.com/chrome/thread/191619088?hl=en&msgid=193009642\n    gridClone.style.contain = 'size';\n    let gridToolbarElementHeight = gridRootElement.querySelector(`.${gridClasses.toolbar}`)?.offsetHeight || 0;\n    let gridFooterElementHeight = gridRootElement.querySelector(`.${gridClasses.footerContainer}`)?.offsetHeight || 0;\n    const gridFooterElement = gridClone.querySelector(`.${gridClasses.footerContainer}`);\n    if (normalizeOptions.hideToolbar) {\n      gridClone.querySelector(`.${gridClasses.toolbar}`)?.remove();\n      gridToolbarElementHeight = 0;\n    }\n    if (normalizeOptions.hideFooter && gridFooterElement) {\n      gridFooterElement.remove();\n      gridFooterElementHeight = 0;\n    }\n\n    // Expand container height to accommodate all rows\n    const computedTotalHeight = rowsMeta.currentPageTotalHeight + getTotalHeaderHeight(apiRef, props) + gridToolbarElementHeight + gridFooterElementHeight;\n    gridClone.style.height = `${computedTotalHeight}px`;\n    // The height above does not include grid border width, so we need to exclude it\n    gridClone.style.boxSizing = 'content-box';\n    if (!normalizeOptions.hideFooter && gridFooterElement) {\n      // the footer is always being placed at the bottom of the page as if all rows are exported\n      // so if getRowsToExport is being used to only export a subset of rows then we need to\n      // adjust the footer position to be correctly placed at the bottom of the grid\n      gridFooterElement.style.position = 'absolute';\n      gridFooterElement.style.width = '100%';\n      gridFooterElement.style.top = `${computedTotalHeight - gridFooterElementHeight}px`;\n    }\n\n    // printDoc.body.appendChild(gridClone); should be enough but a clone isolation bug in Safari\n    // prevents us to do it\n    const container = document.createElement('div');\n    container.appendChild(gridClone);\n    // To avoid an empty page in start on Chromium based browsers\n    printDoc.body.style.marginTop = '0px';\n    printDoc.body.innerHTML = container.innerHTML;\n    const defaultPageStyle = typeof normalizeOptions.pageStyle === 'function' ? normalizeOptions.pageStyle() : normalizeOptions.pageStyle;\n    if (typeof defaultPageStyle === 'string') {\n      // TODO custom styles should always win\n      const styleElement = printDoc.createElement('style');\n      styleElement.appendChild(printDoc.createTextNode(defaultPageStyle));\n      printDoc.head.appendChild(styleElement);\n    }\n    if (normalizeOptions.bodyClassName) {\n      printDoc.body.classList.add(...normalizeOptions.bodyClassName.split(' '));\n    }\n    let stylesheetLoadPromises = [];\n    if (normalizeOptions.copyStyles) {\n      const rootCandidate = gridRootElement.getRootNode();\n      const root = rootCandidate.constructor.name === 'ShadowRoot' ? rootCandidate : doc.current;\n      stylesheetLoadPromises = loadStyleSheets(printDoc, root);\n    }\n\n    // Trigger print\n    if (process.env.NODE_ENV !== 'test') {\n      // wait for remote stylesheets to load\n      Promise.all(stylesheetLoadPromises).then(() => {\n        printWindow.contentWindow.print();\n      });\n    }\n  }, [apiRef, doc, props]);\n  const handlePrintWindowAfterPrint = React.useCallback(printWindow => {\n    // Remove the print iframe\n    doc.current.body.removeChild(printWindow);\n\n    // Revert grid to previous state\n    apiRef.current.restoreState(previousGridState.current || {});\n    if (!previousGridState.current?.columns?.columnVisibilityModel) {\n      // if the apiRef.current.exportState(); did not exported the column visibility, we update it\n      apiRef.current.setColumnVisibilityModel(previousColumnVisibility.current);\n    }\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: previousVirtualizationState.current\n    }));\n    apiRef.current.setRows(previousRows.current);\n\n    // Clear local state\n    previousGridState.current = null;\n    previousColumnVisibility.current = {};\n    previousRows.current = [];\n  }, [apiRef]);\n  const exportDataAsPrint = React.useCallback(async options => {\n    logger.debug(`Export data as Print`);\n    if (!apiRef.current.rootElementRef.current) {\n      throw new Error('MUI X: No grid root element available.');\n    }\n    previousGridState.current = apiRef.current.exportState();\n    // It appends that the visibility model is not exported, especially if columnVisibility is not controlled\n    previousColumnVisibility.current = gridColumnVisibilityModelSelector(apiRef);\n    previousRows.current = apiRef.current.getSortedRows().filter(row => !row[GRID_ID_AUTOGENERATED]);\n    if (props.pagination) {\n      const visibleRowCount = gridExpandedRowCountSelector(apiRef);\n      const paginationModel = {\n        page: 0,\n        pageSize: visibleRowCount\n      };\n      apiRef.current.setState(state => _extends({}, state, {\n        pagination: _extends({}, state.pagination, {\n          paginationModel: getDerivedPaginationModel(state.pagination,\n          // Using signature `DataGridPro` to allow more than 100 rows in the print export\n          'DataGridPro', paginationModel)\n        })\n      }));\n    }\n    previousVirtualizationState.current = apiRef.current.state.virtualization;\n    apiRef.current.unstable_setVirtualization(false);\n    await updateGridColumnsForPrint(options?.fields, options?.allColumns, options?.includeCheckboxes);\n    updateGridRowsForPrint(options?.getRowsToExport ?? defaultGetRowsToExport);\n    await raf(); // wait for the state changes to take action\n    const printWindow = buildPrintWindow(options?.fileName);\n    if (process.env.NODE_ENV === 'test') {\n      doc.current.body.appendChild(printWindow);\n      // In test env, run the all pipeline without waiting for loading\n      handlePrintWindowLoad(printWindow, options);\n      handlePrintWindowAfterPrint(printWindow);\n    } else {\n      printWindow.onload = () => {\n        handlePrintWindowLoad(printWindow, options);\n        const mediaQueryList = printWindow.contentWindow.matchMedia('print');\n        mediaQueryList.addEventListener('change', mql => {\n          const isAfterPrint = mql.matches === false;\n          if (isAfterPrint) {\n            handlePrintWindowAfterPrint(printWindow);\n          }\n        });\n      };\n      doc.current.body.appendChild(printWindow);\n    }\n  }, [props, logger, apiRef, handlePrintWindowLoad, handlePrintWindowAfterPrint, updateGridColumnsForPrint, updateGridRowsForPrint]);\n  const printExportApi = {\n    exportDataAsPrint\n  };\n  useGridApiMethod(apiRef, printExportApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const addExportMenuButtons = React.useCallback((initialValue, options) => {\n    if (options.printOptions?.disableToolbarButton) {\n      return initialValue;\n    }\n    return [...initialValue, {\n      component: /*#__PURE__*/_jsx(GridPrintExportMenuItem, {\n        options: options.printOptions\n      }),\n      componentName: 'printExport'\n    }];\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'exportMenu', addExportMenuButtons);\n};", "map": {"version": 3, "names": ["_extends", "React", "ownerDocument", "loadStyleSheets", "useGridLogger", "gridExpandedRowCountSelector", "gridColumnDefinitionsSelector", "gridColumnVisibilityModelSelector", "gridClasses", "useGridApiMethod", "gridRowsMetaSelector", "GRID_ID_AUTOGENERATED", "defaultGetRowsToExport", "getColumnsToExport", "getDerivedPaginationModel", "useGridRegisterPipeProcessor", "GridPrintExportMenuItem", "getTotalHeaderHeight", "GRID_CHECKBOX_SELECTION_COL_DEF", "jsx", "_jsx", "raf", "Promise", "resolve", "requestAnimationFrame", "buildPrintWindow", "title", "iframeEl", "document", "createElement", "style", "position", "width", "height", "useGridPrintExport", "apiRef", "props", "hasRootReference", "current", "rootElementRef", "logger", "doc", "useRef", "previousGridState", "previousColumnVisibility", "previousRows", "previousVirtualizationState", "useEffect", "updateGridColumnsForPrint", "useCallback", "fields", "allColumns", "includeCheckboxes", "exportedColumnFields", "options", "map", "column", "field", "columns", "newColumnVisibilityModel", "for<PERSON>ach", "includes", "setColumnVisibilityModel", "updateGridRowsForPrint", "getRowsToExport", "rowsToExportIds", "newRows", "reduce", "acc", "id", "row", "getRow", "push", "setRows", "handlePrintWindowLoad", "printWindow", "normalizeOptions", "copyStyles", "hideToolbar", "hideFooter", "printDoc", "contentDocument", "rowsMeta", "gridRootElement", "gridClone", "cloneNode", "gridMain", "querySelector", "main", "overflow", "contain", "gridToolbarElementHeight", "toolbar", "offsetHeight", "gridFooterElementHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gridFooterElement", "remove", "computedTotalHeight", "currentPageTotalHeight", "boxSizing", "top", "container", "append<PERSON><PERSON><PERSON>", "body", "marginTop", "innerHTML", "defaultPageStyle", "pageStyle", "styleElement", "createTextNode", "head", "bodyClassName", "classList", "add", "split", "stylesheetLoadPromises", "rootCandidate", "getRootNode", "root", "constructor", "name", "process", "env", "NODE_ENV", "all", "then", "contentWindow", "print", "handlePrintWindowAfterPrint", "<PERSON><PERSON><PERSON><PERSON>", "restoreState", "columnVisibilityModel", "setState", "state", "virtualization", "exportDataAsPrint", "debug", "Error", "exportState", "getSortedRows", "filter", "pagination", "visibleRowCount", "paginationModel", "page", "pageSize", "unstable_setVirtualization", "fileName", "onload", "mediaQueryList", "matchMedia", "addEventListener", "mql", "isAfterPrint", "matches", "printExportApi", "addExportMenuButtons", "initialValue", "printOptions", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "component", "componentName"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/export/useGridPrintExport.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport ownerDocument from '@mui/utils/ownerDocument';\nimport { loadStyleSheets } from '@mui/x-internals/export';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridExpandedRowCountSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector } from \"../columns/gridColumnsSelector.js\";\nimport { gridClasses } from \"../../../constants/gridClasses.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { GRID_ID_AUTOGENERATED } from \"../rows/gridRowsUtils.js\";\nimport { defaultGetRowsToExport, getColumnsToExport } from \"./utils.js\";\nimport { getDerivedPaginationModel } from \"../pagination/useGridPaginationModel.js\";\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { GridPrintExportMenuItem } from \"../../../components/toolbar/index.js\";\nimport { getTotalHeaderHeight } from \"../columns/gridColumnsUtils.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF } from \"../../../colDef/gridCheckboxSelectionColDef.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction raf() {\n  return new Promise(resolve => {\n    requestAnimationFrame(() => {\n      resolve();\n    });\n  });\n}\nfunction buildPrintWindow(title) {\n  const iframeEl = document.createElement('iframe');\n  iframeEl.style.position = 'absolute';\n  iframeEl.style.width = '0px';\n  iframeEl.style.height = '0px';\n  iframeEl.title = title || document.title;\n  return iframeEl;\n}\n\n/**\n * @requires useGridColumns (state)\n * @requires useGridFilter (state)\n * @requires useGridSorting (state)\n * @requires useGridParamsApi (method)\n */\nexport const useGridPrintExport = (apiRef, props) => {\n  const hasRootReference = apiRef.current.rootElementRef.current !== null;\n  const logger = useGridLogger(apiRef, 'useGridPrintExport');\n  const doc = React.useRef(null);\n  const previousGridState = React.useRef(null);\n  const previousColumnVisibility = React.useRef({});\n  const previousRows = React.useRef([]);\n  const previousVirtualizationState = React.useRef(null);\n  React.useEffect(() => {\n    doc.current = ownerDocument(apiRef.current.rootElementRef.current);\n  }, [apiRef, hasRootReference]);\n\n  // Returns a promise because updateColumns triggers state update and\n  // the new state needs to be in place before the grid can be sized correctly\n  const updateGridColumnsForPrint = React.useCallback((fields, allColumns, includeCheckboxes) => new Promise(resolve => {\n    const exportedColumnFields = getColumnsToExport({\n      apiRef,\n      options: {\n        fields,\n        allColumns\n      }\n    }).map(column => column.field);\n    const columns = gridColumnDefinitionsSelector(apiRef);\n    const newColumnVisibilityModel = {};\n    columns.forEach(column => {\n      newColumnVisibilityModel[column.field] = exportedColumnFields.includes(column.field);\n    });\n    if (includeCheckboxes) {\n      newColumnVisibilityModel[GRID_CHECKBOX_SELECTION_COL_DEF.field] = true;\n    }\n    apiRef.current.setColumnVisibilityModel(newColumnVisibilityModel);\n    resolve();\n  }), [apiRef]);\n  const updateGridRowsForPrint = React.useCallback(getRowsToExport => {\n    const rowsToExportIds = getRowsToExport({\n      apiRef\n    });\n    const newRows = rowsToExportIds.reduce((acc, id) => {\n      const row = apiRef.current.getRow(id);\n      if (!row[GRID_ID_AUTOGENERATED]) {\n        acc.push(row);\n      }\n      return acc;\n    }, []);\n    apiRef.current.setRows(newRows);\n  }, [apiRef]);\n  const handlePrintWindowLoad = React.useCallback((printWindow, options) => {\n    const normalizeOptions = _extends({\n      copyStyles: true,\n      hideToolbar: false,\n      hideFooter: false,\n      includeCheckboxes: false\n    }, options);\n    const printDoc = printWindow.contentDocument;\n    if (!printDoc) {\n      return;\n    }\n    const rowsMeta = gridRowsMetaSelector(apiRef);\n    const gridRootElement = apiRef.current.rootElementRef.current;\n    const gridClone = gridRootElement.cloneNode(true);\n\n    // Allow to overflow to not hide the border of the last row\n    const gridMain = gridClone.querySelector(`.${gridClasses.main}`);\n    gridMain.style.overflow = 'visible';\n\n    // See https://support.google.com/chrome/thread/191619088?hl=en&msgid=193009642\n    gridClone.style.contain = 'size';\n    let gridToolbarElementHeight = gridRootElement.querySelector(`.${gridClasses.toolbar}`)?.offsetHeight || 0;\n    let gridFooterElementHeight = gridRootElement.querySelector(`.${gridClasses.footerContainer}`)?.offsetHeight || 0;\n    const gridFooterElement = gridClone.querySelector(`.${gridClasses.footerContainer}`);\n    if (normalizeOptions.hideToolbar) {\n      gridClone.querySelector(`.${gridClasses.toolbar}`)?.remove();\n      gridToolbarElementHeight = 0;\n    }\n    if (normalizeOptions.hideFooter && gridFooterElement) {\n      gridFooterElement.remove();\n      gridFooterElementHeight = 0;\n    }\n\n    // Expand container height to accommodate all rows\n    const computedTotalHeight = rowsMeta.currentPageTotalHeight + getTotalHeaderHeight(apiRef, props) + gridToolbarElementHeight + gridFooterElementHeight;\n    gridClone.style.height = `${computedTotalHeight}px`;\n    // The height above does not include grid border width, so we need to exclude it\n    gridClone.style.boxSizing = 'content-box';\n    if (!normalizeOptions.hideFooter && gridFooterElement) {\n      // the footer is always being placed at the bottom of the page as if all rows are exported\n      // so if getRowsToExport is being used to only export a subset of rows then we need to\n      // adjust the footer position to be correctly placed at the bottom of the grid\n      gridFooterElement.style.position = 'absolute';\n      gridFooterElement.style.width = '100%';\n      gridFooterElement.style.top = `${computedTotalHeight - gridFooterElementHeight}px`;\n    }\n\n    // printDoc.body.appendChild(gridClone); should be enough but a clone isolation bug in Safari\n    // prevents us to do it\n    const container = document.createElement('div');\n    container.appendChild(gridClone);\n    // To avoid an empty page in start on Chromium based browsers\n    printDoc.body.style.marginTop = '0px';\n    printDoc.body.innerHTML = container.innerHTML;\n    const defaultPageStyle = typeof normalizeOptions.pageStyle === 'function' ? normalizeOptions.pageStyle() : normalizeOptions.pageStyle;\n    if (typeof defaultPageStyle === 'string') {\n      // TODO custom styles should always win\n      const styleElement = printDoc.createElement('style');\n      styleElement.appendChild(printDoc.createTextNode(defaultPageStyle));\n      printDoc.head.appendChild(styleElement);\n    }\n    if (normalizeOptions.bodyClassName) {\n      printDoc.body.classList.add(...normalizeOptions.bodyClassName.split(' '));\n    }\n    let stylesheetLoadPromises = [];\n    if (normalizeOptions.copyStyles) {\n      const rootCandidate = gridRootElement.getRootNode();\n      const root = rootCandidate.constructor.name === 'ShadowRoot' ? rootCandidate : doc.current;\n      stylesheetLoadPromises = loadStyleSheets(printDoc, root);\n    }\n\n    // Trigger print\n    if (process.env.NODE_ENV !== 'test') {\n      // wait for remote stylesheets to load\n      Promise.all(stylesheetLoadPromises).then(() => {\n        printWindow.contentWindow.print();\n      });\n    }\n  }, [apiRef, doc, props]);\n  const handlePrintWindowAfterPrint = React.useCallback(printWindow => {\n    // Remove the print iframe\n    doc.current.body.removeChild(printWindow);\n\n    // Revert grid to previous state\n    apiRef.current.restoreState(previousGridState.current || {});\n    if (!previousGridState.current?.columns?.columnVisibilityModel) {\n      // if the apiRef.current.exportState(); did not exported the column visibility, we update it\n      apiRef.current.setColumnVisibilityModel(previousColumnVisibility.current);\n    }\n    apiRef.current.setState(state => _extends({}, state, {\n      virtualization: previousVirtualizationState.current\n    }));\n    apiRef.current.setRows(previousRows.current);\n\n    // Clear local state\n    previousGridState.current = null;\n    previousColumnVisibility.current = {};\n    previousRows.current = [];\n  }, [apiRef]);\n  const exportDataAsPrint = React.useCallback(async options => {\n    logger.debug(`Export data as Print`);\n    if (!apiRef.current.rootElementRef.current) {\n      throw new Error('MUI X: No grid root element available.');\n    }\n    previousGridState.current = apiRef.current.exportState();\n    // It appends that the visibility model is not exported, especially if columnVisibility is not controlled\n    previousColumnVisibility.current = gridColumnVisibilityModelSelector(apiRef);\n    previousRows.current = apiRef.current.getSortedRows().filter(row => !row[GRID_ID_AUTOGENERATED]);\n    if (props.pagination) {\n      const visibleRowCount = gridExpandedRowCountSelector(apiRef);\n      const paginationModel = {\n        page: 0,\n        pageSize: visibleRowCount\n      };\n      apiRef.current.setState(state => _extends({}, state, {\n        pagination: _extends({}, state.pagination, {\n          paginationModel: getDerivedPaginationModel(state.pagination,\n          // Using signature `DataGridPro` to allow more than 100 rows in the print export\n          'DataGridPro', paginationModel)\n        })\n      }));\n    }\n    previousVirtualizationState.current = apiRef.current.state.virtualization;\n    apiRef.current.unstable_setVirtualization(false);\n    await updateGridColumnsForPrint(options?.fields, options?.allColumns, options?.includeCheckboxes);\n    updateGridRowsForPrint(options?.getRowsToExport ?? defaultGetRowsToExport);\n    await raf(); // wait for the state changes to take action\n    const printWindow = buildPrintWindow(options?.fileName);\n    if (process.env.NODE_ENV === 'test') {\n      doc.current.body.appendChild(printWindow);\n      // In test env, run the all pipeline without waiting for loading\n      handlePrintWindowLoad(printWindow, options);\n      handlePrintWindowAfterPrint(printWindow);\n    } else {\n      printWindow.onload = () => {\n        handlePrintWindowLoad(printWindow, options);\n        const mediaQueryList = printWindow.contentWindow.matchMedia('print');\n        mediaQueryList.addEventListener('change', mql => {\n          const isAfterPrint = mql.matches === false;\n          if (isAfterPrint) {\n            handlePrintWindowAfterPrint(printWindow);\n          }\n        });\n      };\n      doc.current.body.appendChild(printWindow);\n    }\n  }, [props, logger, apiRef, handlePrintWindowLoad, handlePrintWindowAfterPrint, updateGridColumnsForPrint, updateGridRowsForPrint]);\n  const printExportApi = {\n    exportDataAsPrint\n  };\n  useGridApiMethod(apiRef, printExportApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const addExportMenuButtons = React.useCallback((initialValue, options) => {\n    if (options.printOptions?.disableToolbarButton) {\n      return initialValue;\n    }\n    return [...initialValue, {\n      component: /*#__PURE__*/_jsx(GridPrintExportMenuItem, {\n        options: options.printOptions\n      }),\n      componentName: 'printExport'\n    }];\n  }, []);\n  useGridRegisterPipeProcessor(apiRef, 'exportMenu', addExportMenuButtons);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,4BAA4B,QAAQ,iCAAiC;AAC9E,SAASC,6BAA6B,EAAEC,iCAAiC,QAAQ,mCAAmC;AACpH,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,qBAAqB,QAAQ,0BAA0B;AAChE,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,YAAY;AACvE,SAASC,yBAAyB,QAAQ,yCAAyC;AACnF,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,+BAA+B,QAAQ,gDAAgD;AAChG,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,GAAGA,CAAA,EAAG;EACb,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC5BC,qBAAqB,CAAC,MAAM;MAC1BD,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASE,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EACjDF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,UAAU;EACpCJ,QAAQ,CAACG,KAAK,CAACE,KAAK,GAAG,KAAK;EAC5BL,QAAQ,CAACG,KAAK,CAACG,MAAM,GAAG,KAAK;EAC7BN,QAAQ,CAACD,KAAK,GAAGA,KAAK,IAAIE,QAAQ,CAACF,KAAK;EACxC,OAAOC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACnD,MAAMC,gBAAgB,GAAGF,MAAM,CAACG,OAAO,CAACC,cAAc,CAACD,OAAO,KAAK,IAAI;EACvE,MAAME,MAAM,GAAGpC,aAAa,CAAC+B,MAAM,EAAE,oBAAoB,CAAC;EAC1D,MAAMM,GAAG,GAAGxC,KAAK,CAACyC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,iBAAiB,GAAG1C,KAAK,CAACyC,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAME,wBAAwB,GAAG3C,KAAK,CAACyC,MAAM,CAAC,CAAC,CAAC,CAAC;EACjD,MAAMG,YAAY,GAAG5C,KAAK,CAACyC,MAAM,CAAC,EAAE,CAAC;EACrC,MAAMI,2BAA2B,GAAG7C,KAAK,CAACyC,MAAM,CAAC,IAAI,CAAC;EACtDzC,KAAK,CAAC8C,SAAS,CAAC,MAAM;IACpBN,GAAG,CAACH,OAAO,GAAGpC,aAAa,CAACiC,MAAM,CAACG,OAAO,CAACC,cAAc,CAACD,OAAO,CAAC;EACpE,CAAC,EAAE,CAACH,MAAM,EAAEE,gBAAgB,CAAC,CAAC;;EAE9B;EACA;EACA,MAAMW,yBAAyB,GAAG/C,KAAK,CAACgD,WAAW,CAAC,CAACC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,KAAK,IAAI9B,OAAO,CAACC,OAAO,IAAI;IACpH,MAAM8B,oBAAoB,GAAGxC,kBAAkB,CAAC;MAC9CsB,MAAM;MACNmB,OAAO,EAAE;QACPJ,MAAM;QACNC;MACF;IACF,CAAC,CAAC,CAACI,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;IAC9B,MAAMC,OAAO,GAAGpD,6BAA6B,CAAC6B,MAAM,CAAC;IACrD,MAAMwB,wBAAwB,GAAG,CAAC,CAAC;IACnCD,OAAO,CAACE,OAAO,CAACJ,MAAM,IAAI;MACxBG,wBAAwB,CAACH,MAAM,CAACC,KAAK,CAAC,GAAGJ,oBAAoB,CAACQ,QAAQ,CAACL,MAAM,CAACC,KAAK,CAAC;IACtF,CAAC,CAAC;IACF,IAAIL,iBAAiB,EAAE;MACrBO,wBAAwB,CAACzC,+BAA+B,CAACuC,KAAK,CAAC,GAAG,IAAI;IACxE;IACAtB,MAAM,CAACG,OAAO,CAACwB,wBAAwB,CAACH,wBAAwB,CAAC;IACjEpC,OAAO,CAAC,CAAC;EACX,CAAC,CAAC,EAAE,CAACY,MAAM,CAAC,CAAC;EACb,MAAM4B,sBAAsB,GAAG9D,KAAK,CAACgD,WAAW,CAACe,eAAe,IAAI;IAClE,MAAMC,eAAe,GAAGD,eAAe,CAAC;MACtC7B;IACF,CAAC,CAAC;IACF,MAAM+B,OAAO,GAAGD,eAAe,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,EAAE,KAAK;MAClD,MAAMC,GAAG,GAAGnC,MAAM,CAACG,OAAO,CAACiC,MAAM,CAACF,EAAE,CAAC;MACrC,IAAI,CAACC,GAAG,CAAC3D,qBAAqB,CAAC,EAAE;QAC/ByD,GAAG,CAACI,IAAI,CAACF,GAAG,CAAC;MACf;MACA,OAAOF,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IACNjC,MAAM,CAACG,OAAO,CAACmC,OAAO,CAACP,OAAO,CAAC;EACjC,CAAC,EAAE,CAAC/B,MAAM,CAAC,CAAC;EACZ,MAAMuC,qBAAqB,GAAGzE,KAAK,CAACgD,WAAW,CAAC,CAAC0B,WAAW,EAAErB,OAAO,KAAK;IACxE,MAAMsB,gBAAgB,GAAG5E,QAAQ,CAAC;MAChC6E,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjB3B,iBAAiB,EAAE;IACrB,CAAC,EAAEE,OAAO,CAAC;IACX,MAAM0B,QAAQ,GAAGL,WAAW,CAACM,eAAe;IAC5C,IAAI,CAACD,QAAQ,EAAE;MACb;IACF;IACA,MAAME,QAAQ,GAAGxE,oBAAoB,CAACyB,MAAM,CAAC;IAC7C,MAAMgD,eAAe,GAAGhD,MAAM,CAACG,OAAO,CAACC,cAAc,CAACD,OAAO;IAC7D,MAAM8C,SAAS,GAAGD,eAAe,CAACE,SAAS,CAAC,IAAI,CAAC;;IAEjD;IACA,MAAMC,QAAQ,GAAGF,SAAS,CAACG,aAAa,CAAC,IAAI/E,WAAW,CAACgF,IAAI,EAAE,CAAC;IAChEF,QAAQ,CAACxD,KAAK,CAAC2D,QAAQ,GAAG,SAAS;;IAEnC;IACAL,SAAS,CAACtD,KAAK,CAAC4D,OAAO,GAAG,MAAM;IAChC,IAAIC,wBAAwB,GAAGR,eAAe,CAACI,aAAa,CAAC,IAAI/E,WAAW,CAACoF,OAAO,EAAE,CAAC,EAAEC,YAAY,IAAI,CAAC;IAC1G,IAAIC,uBAAuB,GAAGX,eAAe,CAACI,aAAa,CAAC,IAAI/E,WAAW,CAACuF,eAAe,EAAE,CAAC,EAAEF,YAAY,IAAI,CAAC;IACjH,MAAMG,iBAAiB,GAAGZ,SAAS,CAACG,aAAa,CAAC,IAAI/E,WAAW,CAACuF,eAAe,EAAE,CAAC;IACpF,IAAInB,gBAAgB,CAACE,WAAW,EAAE;MAChCM,SAAS,CAACG,aAAa,CAAC,IAAI/E,WAAW,CAACoF,OAAO,EAAE,CAAC,EAAEK,MAAM,CAAC,CAAC;MAC5DN,wBAAwB,GAAG,CAAC;IAC9B;IACA,IAAIf,gBAAgB,CAACG,UAAU,IAAIiB,iBAAiB,EAAE;MACpDA,iBAAiB,CAACC,MAAM,CAAC,CAAC;MAC1BH,uBAAuB,GAAG,CAAC;IAC7B;;IAEA;IACA,MAAMI,mBAAmB,GAAGhB,QAAQ,CAACiB,sBAAsB,GAAGlF,oBAAoB,CAACkB,MAAM,EAAEC,KAAK,CAAC,GAAGuD,wBAAwB,GAAGG,uBAAuB;IACtJV,SAAS,CAACtD,KAAK,CAACG,MAAM,GAAG,GAAGiE,mBAAmB,IAAI;IACnD;IACAd,SAAS,CAACtD,KAAK,CAACsE,SAAS,GAAG,aAAa;IACzC,IAAI,CAACxB,gBAAgB,CAACG,UAAU,IAAIiB,iBAAiB,EAAE;MACrD;MACA;MACA;MACAA,iBAAiB,CAAClE,KAAK,CAACC,QAAQ,GAAG,UAAU;MAC7CiE,iBAAiB,CAAClE,KAAK,CAACE,KAAK,GAAG,MAAM;MACtCgE,iBAAiB,CAAClE,KAAK,CAACuE,GAAG,GAAG,GAAGH,mBAAmB,GAAGJ,uBAAuB,IAAI;IACpF;;IAEA;IACA;IACA,MAAMQ,SAAS,GAAG1E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC/CyE,SAAS,CAACC,WAAW,CAACnB,SAAS,CAAC;IAChC;IACAJ,QAAQ,CAACwB,IAAI,CAAC1E,KAAK,CAAC2E,SAAS,GAAG,KAAK;IACrCzB,QAAQ,CAACwB,IAAI,CAACE,SAAS,GAAGJ,SAAS,CAACI,SAAS;IAC7C,MAAMC,gBAAgB,GAAG,OAAO/B,gBAAgB,CAACgC,SAAS,KAAK,UAAU,GAAGhC,gBAAgB,CAACgC,SAAS,CAAC,CAAC,GAAGhC,gBAAgB,CAACgC,SAAS;IACrI,IAAI,OAAOD,gBAAgB,KAAK,QAAQ,EAAE;MACxC;MACA,MAAME,YAAY,GAAG7B,QAAQ,CAACnD,aAAa,CAAC,OAAO,CAAC;MACpDgF,YAAY,CAACN,WAAW,CAACvB,QAAQ,CAAC8B,cAAc,CAACH,gBAAgB,CAAC,CAAC;MACnE3B,QAAQ,CAAC+B,IAAI,CAACR,WAAW,CAACM,YAAY,CAAC;IACzC;IACA,IAAIjC,gBAAgB,CAACoC,aAAa,EAAE;MAClChC,QAAQ,CAACwB,IAAI,CAACS,SAAS,CAACC,GAAG,CAAC,GAAGtC,gBAAgB,CAACoC,aAAa,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3E;IACA,IAAIC,sBAAsB,GAAG,EAAE;IAC/B,IAAIxC,gBAAgB,CAACC,UAAU,EAAE;MAC/B,MAAMwC,aAAa,GAAGlC,eAAe,CAACmC,WAAW,CAAC,CAAC;MACnD,MAAMC,IAAI,GAAGF,aAAa,CAACG,WAAW,CAACC,IAAI,KAAK,YAAY,GAAGJ,aAAa,GAAG5E,GAAG,CAACH,OAAO;MAC1F8E,sBAAsB,GAAGjH,eAAe,CAAC6E,QAAQ,EAAEuC,IAAI,CAAC;IAC1D;;IAEA;IACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnC;MACAtG,OAAO,CAACuG,GAAG,CAACT,sBAAsB,CAAC,CAACU,IAAI,CAAC,MAAM;QAC7CnD,WAAW,CAACoD,aAAa,CAACC,KAAK,CAAC,CAAC;MACnC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC7F,MAAM,EAAEM,GAAG,EAAEL,KAAK,CAAC,CAAC;EACxB,MAAM6F,2BAA2B,GAAGhI,KAAK,CAACgD,WAAW,CAAC0B,WAAW,IAAI;IACnE;IACAlC,GAAG,CAACH,OAAO,CAACkE,IAAI,CAAC0B,WAAW,CAACvD,WAAW,CAAC;;IAEzC;IACAxC,MAAM,CAACG,OAAO,CAAC6F,YAAY,CAACxF,iBAAiB,CAACL,OAAO,IAAI,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACK,iBAAiB,CAACL,OAAO,EAAEoB,OAAO,EAAE0E,qBAAqB,EAAE;MAC9D;MACAjG,MAAM,CAACG,OAAO,CAACwB,wBAAwB,CAAClB,wBAAwB,CAACN,OAAO,CAAC;IAC3E;IACAH,MAAM,CAACG,OAAO,CAAC+F,QAAQ,CAACC,KAAK,IAAItI,QAAQ,CAAC,CAAC,CAAC,EAAEsI,KAAK,EAAE;MACnDC,cAAc,EAAEzF,2BAA2B,CAACR;IAC9C,CAAC,CAAC,CAAC;IACHH,MAAM,CAACG,OAAO,CAACmC,OAAO,CAAC5B,YAAY,CAACP,OAAO,CAAC;;IAE5C;IACAK,iBAAiB,CAACL,OAAO,GAAG,IAAI;IAChCM,wBAAwB,CAACN,OAAO,GAAG,CAAC,CAAC;IACrCO,YAAY,CAACP,OAAO,GAAG,EAAE;EAC3B,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;EACZ,MAAMqG,iBAAiB,GAAGvI,KAAK,CAACgD,WAAW,CAAC,MAAMK,OAAO,IAAI;IAC3Dd,MAAM,CAACiG,KAAK,CAAC,sBAAsB,CAAC;IACpC,IAAI,CAACtG,MAAM,CAACG,OAAO,CAACC,cAAc,CAACD,OAAO,EAAE;MAC1C,MAAM,IAAIoG,KAAK,CAAC,wCAAwC,CAAC;IAC3D;IACA/F,iBAAiB,CAACL,OAAO,GAAGH,MAAM,CAACG,OAAO,CAACqG,WAAW,CAAC,CAAC;IACxD;IACA/F,wBAAwB,CAACN,OAAO,GAAG/B,iCAAiC,CAAC4B,MAAM,CAAC;IAC5EU,YAAY,CAACP,OAAO,GAAGH,MAAM,CAACG,OAAO,CAACsG,aAAa,CAAC,CAAC,CAACC,MAAM,CAACvE,GAAG,IAAI,CAACA,GAAG,CAAC3D,qBAAqB,CAAC,CAAC;IAChG,IAAIyB,KAAK,CAAC0G,UAAU,EAAE;MACpB,MAAMC,eAAe,GAAG1I,4BAA4B,CAAC8B,MAAM,CAAC;MAC5D,MAAM6G,eAAe,GAAG;QACtBC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAEH;MACZ,CAAC;MACD5G,MAAM,CAACG,OAAO,CAAC+F,QAAQ,CAACC,KAAK,IAAItI,QAAQ,CAAC,CAAC,CAAC,EAAEsI,KAAK,EAAE;QACnDQ,UAAU,EAAE9I,QAAQ,CAAC,CAAC,CAAC,EAAEsI,KAAK,CAACQ,UAAU,EAAE;UACzCE,eAAe,EAAElI,yBAAyB,CAACwH,KAAK,CAACQ,UAAU;UAC3D;UACA,aAAa,EAAEE,eAAe;QAChC,CAAC;MACH,CAAC,CAAC,CAAC;IACL;IACAlG,2BAA2B,CAACR,OAAO,GAAGH,MAAM,CAACG,OAAO,CAACgG,KAAK,CAACC,cAAc;IACzEpG,MAAM,CAACG,OAAO,CAAC6G,0BAA0B,CAAC,KAAK,CAAC;IAChD,MAAMnG,yBAAyB,CAACM,OAAO,EAAEJ,MAAM,EAAEI,OAAO,EAAEH,UAAU,EAAEG,OAAO,EAAEF,iBAAiB,CAAC;IACjGW,sBAAsB,CAACT,OAAO,EAAEU,eAAe,IAAIpD,sBAAsB,CAAC;IAC1E,MAAMS,GAAG,CAAC,CAAC,CAAC,CAAC;IACb,MAAMsD,WAAW,GAAGlD,gBAAgB,CAAC6B,OAAO,EAAE8F,QAAQ,CAAC;IACvD,IAAI1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnCnF,GAAG,CAACH,OAAO,CAACkE,IAAI,CAACD,WAAW,CAAC5B,WAAW,CAAC;MACzC;MACAD,qBAAqB,CAACC,WAAW,EAAErB,OAAO,CAAC;MAC3C2E,2BAA2B,CAACtD,WAAW,CAAC;IAC1C,CAAC,MAAM;MACLA,WAAW,CAAC0E,MAAM,GAAG,MAAM;QACzB3E,qBAAqB,CAACC,WAAW,EAAErB,OAAO,CAAC;QAC3C,MAAMgG,cAAc,GAAG3E,WAAW,CAACoD,aAAa,CAACwB,UAAU,CAAC,OAAO,CAAC;QACpED,cAAc,CAACE,gBAAgB,CAAC,QAAQ,EAAEC,GAAG,IAAI;UAC/C,MAAMC,YAAY,GAAGD,GAAG,CAACE,OAAO,KAAK,KAAK;UAC1C,IAAID,YAAY,EAAE;YAChBzB,2BAA2B,CAACtD,WAAW,CAAC;UAC1C;QACF,CAAC,CAAC;MACJ,CAAC;MACDlC,GAAG,CAACH,OAAO,CAACkE,IAAI,CAACD,WAAW,CAAC5B,WAAW,CAAC;IAC3C;EACF,CAAC,EAAE,CAACvC,KAAK,EAAEI,MAAM,EAAEL,MAAM,EAAEuC,qBAAqB,EAAEuD,2BAA2B,EAAEjF,yBAAyB,EAAEe,sBAAsB,CAAC,CAAC;EAClI,MAAM6F,cAAc,GAAG;IACrBpB;EACF,CAAC;EACD/H,gBAAgB,CAAC0B,MAAM,EAAEyH,cAAc,EAAE,QAAQ,CAAC;;EAElD;AACF;AACA;EACE,MAAMC,oBAAoB,GAAG5J,KAAK,CAACgD,WAAW,CAAC,CAAC6G,YAAY,EAAExG,OAAO,KAAK;IACxE,IAAIA,OAAO,CAACyG,YAAY,EAAEC,oBAAoB,EAAE;MAC9C,OAAOF,YAAY;IACrB;IACA,OAAO,CAAC,GAAGA,YAAY,EAAE;MACvBG,SAAS,EAAE,aAAa7I,IAAI,CAACJ,uBAAuB,EAAE;QACpDsC,OAAO,EAAEA,OAAO,CAACyG;MACnB,CAAC,CAAC;MACFG,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACNnJ,4BAA4B,CAACoB,MAAM,EAAE,YAAY,EAAE0H,oBAAoB,CAAC;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}