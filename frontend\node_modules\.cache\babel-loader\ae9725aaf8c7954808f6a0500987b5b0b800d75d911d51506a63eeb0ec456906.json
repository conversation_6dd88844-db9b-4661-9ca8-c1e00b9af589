{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../utils/index.js\";\nimport { isFunction } from \"../../utils/utils.js\";\nexport const useGridStateInitialization = apiRef => {\n  const controlStateMapRef = React.useRef({});\n  const registerControlState = React.useCallback(controlStateItem => {\n    controlStateMapRef.current[controlStateItem.stateId] = controlStateItem;\n  }, []);\n  const setState = React.useCallback((state, reason) => {\n    let newState;\n    if (isFunction(state)) {\n      newState = state(apiRef.current.state);\n    } else {\n      newState = state;\n    }\n    if (apiRef.current.state === newState) {\n      return false;\n    }\n    const apiRefWithNewState = {\n      current: {\n        state: newState\n      }\n    };\n    let ignoreSetState = false;\n\n    // Apply the control state constraints\n    const updatedControlStateIds = [];\n    Object.keys(controlStateMapRef.current).forEach(stateId => {\n      const controlState = controlStateMapRef.current[stateId];\n      const oldSubState = controlState.stateSelector(apiRef);\n      const newSubState = controlState.stateSelector(apiRefWithNewState);\n      if (newSubState === oldSubState) {\n        return;\n      }\n      updatedControlStateIds.push({\n        stateId: controlState.stateId,\n        hasPropChanged: newSubState !== controlState.propModel\n      });\n\n      // The state is controlled, the prop should always win\n      if (controlState.propModel !== undefined && newSubState !== controlState.propModel) {\n        ignoreSetState = true;\n      }\n    });\n    if (updatedControlStateIds.length > 1) {\n      // Each hook modify its own state, and it should not leak\n      // Events are here to forward to other hooks and apply changes.\n      // You are trying to update several states in a no isolated way.\n      throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${updatedControlStateIds[0].stateId}, therefore, you're not allowed to update ${updatedControlStateIds.map(el => el.stateId).join(', ')} in the same transaction.`);\n    }\n    if (!ignoreSetState) {\n      // We always assign it as we mutate rows for perf reason.\n      apiRef.current.state = newState;\n      apiRef.current.publishEvent('stateChange', newState);\n      apiRef.current.store.update(newState);\n    }\n    if (updatedControlStateIds.length === 1) {\n      const {\n        stateId,\n        hasPropChanged\n      } = updatedControlStateIds[0];\n      const controlState = controlStateMapRef.current[stateId];\n      const model = controlState.stateSelector(apiRefWithNewState);\n      if (controlState.propOnChange && hasPropChanged) {\n        controlState.propOnChange(model, {\n          reason,\n          api: apiRef.current\n        });\n      }\n      if (!ignoreSetState) {\n        apiRef.current.publishEvent(controlState.changeEvent, model, {\n          reason\n        });\n      }\n    }\n    return !ignoreSetState;\n  }, [apiRef]);\n  const updateControlState = React.useCallback((key, state, reason) => {\n    return apiRef.current.setState(previousState => {\n      return _extends({}, previousState, {\n        [key]: state(previousState[key])\n      });\n    }, reason);\n  }, [apiRef]);\n  const publicStateApi = {\n    setState\n  };\n  const privateStateApi = {\n    updateControlState,\n    registerControlState\n  };\n  useGridApiMethod(apiRef, publicStateApi, 'public');\n  useGridApiMethod(apiRef, privateStateApi, 'private');\n};", "map": {"version": 3, "names": ["_extends", "React", "useGridApiMethod", "isFunction", "useGridStateInitialization", "apiRef", "controlStateMapRef", "useRef", "registerControlState", "useCallback", "controlStateItem", "current", "stateId", "setState", "state", "reason", "newState", "apiRefWithNewState", "ignoreSetState", "updatedControlStateIds", "Object", "keys", "for<PERSON>ach", "controlState", "oldSubState", "stateSelector", "newSubState", "push", "hasPropChanged", "propModel", "undefined", "length", "Error", "map", "el", "join", "publishEvent", "store", "update", "model", "propOnChange", "api", "changeEvent", "updateControlState", "key", "previousState", "publicStateApi", "privateStateApi"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/core/useGridStateInitialization.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../utils/index.js\";\nimport { isFunction } from \"../../utils/utils.js\";\nexport const useGridStateInitialization = apiRef => {\n  const controlStateMapRef = React.useRef({});\n  const registerControlState = React.useCallback(controlStateItem => {\n    controlStateMapRef.current[controlStateItem.stateId] = controlStateItem;\n  }, []);\n  const setState = React.useCallback((state, reason) => {\n    let newState;\n    if (isFunction(state)) {\n      newState = state(apiRef.current.state);\n    } else {\n      newState = state;\n    }\n    if (apiRef.current.state === newState) {\n      return false;\n    }\n    const apiRefWithNewState = {\n      current: {\n        state: newState\n      }\n    };\n    let ignoreSetState = false;\n\n    // Apply the control state constraints\n    const updatedControlStateIds = [];\n    Object.keys(controlStateMapRef.current).forEach(stateId => {\n      const controlState = controlStateMapRef.current[stateId];\n      const oldSubState = controlState.stateSelector(apiRef);\n      const newSubState = controlState.stateSelector(apiRefWithNewState);\n      if (newSubState === oldSubState) {\n        return;\n      }\n      updatedControlStateIds.push({\n        stateId: controlState.stateId,\n        hasPropChanged: newSubState !== controlState.propModel\n      });\n\n      // The state is controlled, the prop should always win\n      if (controlState.propModel !== undefined && newSubState !== controlState.propModel) {\n        ignoreSetState = true;\n      }\n    });\n    if (updatedControlStateIds.length > 1) {\n      // Each hook modify its own state, and it should not leak\n      // Events are here to forward to other hooks and apply changes.\n      // You are trying to update several states in a no isolated way.\n      throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${updatedControlStateIds[0].stateId}, therefore, you're not allowed to update ${updatedControlStateIds.map(el => el.stateId).join(', ')} in the same transaction.`);\n    }\n    if (!ignoreSetState) {\n      // We always assign it as we mutate rows for perf reason.\n      apiRef.current.state = newState;\n      apiRef.current.publishEvent('stateChange', newState);\n      apiRef.current.store.update(newState);\n    }\n    if (updatedControlStateIds.length === 1) {\n      const {\n        stateId,\n        hasPropChanged\n      } = updatedControlStateIds[0];\n      const controlState = controlStateMapRef.current[stateId];\n      const model = controlState.stateSelector(apiRefWithNewState);\n      if (controlState.propOnChange && hasPropChanged) {\n        controlState.propOnChange(model, {\n          reason,\n          api: apiRef.current\n        });\n      }\n      if (!ignoreSetState) {\n        apiRef.current.publishEvent(controlState.changeEvent, model, {\n          reason\n        });\n      }\n    }\n    return !ignoreSetState;\n  }, [apiRef]);\n  const updateControlState = React.useCallback((key, state, reason) => {\n    return apiRef.current.setState(previousState => {\n      return _extends({}, previousState, {\n        [key]: state(previousState[key])\n      });\n    }, reason);\n  }, [apiRef]);\n  const publicStateApi = {\n    setState\n  };\n  const privateStateApi = {\n    updateControlState,\n    registerControlState\n  };\n  useGridApiMethod(apiRef, publicStateApi, 'public');\n  useGridApiMethod(apiRef, privateStateApi, 'private');\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAO,MAAMC,0BAA0B,GAAGC,MAAM,IAAI;EAClD,MAAMC,kBAAkB,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C,MAAMC,oBAAoB,GAAGP,KAAK,CAACQ,WAAW,CAACC,gBAAgB,IAAI;IACjEJ,kBAAkB,CAACK,OAAO,CAACD,gBAAgB,CAACE,OAAO,CAAC,GAAGF,gBAAgB;EACzE,CAAC,EAAE,EAAE,CAAC;EACN,MAAMG,QAAQ,GAAGZ,KAAK,CAACQ,WAAW,CAAC,CAACK,KAAK,EAAEC,MAAM,KAAK;IACpD,IAAIC,QAAQ;IACZ,IAAIb,UAAU,CAACW,KAAK,CAAC,EAAE;MACrBE,QAAQ,GAAGF,KAAK,CAACT,MAAM,CAACM,OAAO,CAACG,KAAK,CAAC;IACxC,CAAC,MAAM;MACLE,QAAQ,GAAGF,KAAK;IAClB;IACA,IAAIT,MAAM,CAACM,OAAO,CAACG,KAAK,KAAKE,QAAQ,EAAE;MACrC,OAAO,KAAK;IACd;IACA,MAAMC,kBAAkB,GAAG;MACzBN,OAAO,EAAE;QACPG,KAAK,EAAEE;MACT;IACF,CAAC;IACD,IAAIE,cAAc,GAAG,KAAK;;IAE1B;IACA,MAAMC,sBAAsB,GAAG,EAAE;IACjCC,MAAM,CAACC,IAAI,CAACf,kBAAkB,CAACK,OAAO,CAAC,CAACW,OAAO,CAACV,OAAO,IAAI;MACzD,MAAMW,YAAY,GAAGjB,kBAAkB,CAACK,OAAO,CAACC,OAAO,CAAC;MACxD,MAAMY,WAAW,GAAGD,YAAY,CAACE,aAAa,CAACpB,MAAM,CAAC;MACtD,MAAMqB,WAAW,GAAGH,YAAY,CAACE,aAAa,CAACR,kBAAkB,CAAC;MAClE,IAAIS,WAAW,KAAKF,WAAW,EAAE;QAC/B;MACF;MACAL,sBAAsB,CAACQ,IAAI,CAAC;QAC1Bf,OAAO,EAAEW,YAAY,CAACX,OAAO;QAC7BgB,cAAc,EAAEF,WAAW,KAAKH,YAAY,CAACM;MAC/C,CAAC,CAAC;;MAEF;MACA,IAAIN,YAAY,CAACM,SAAS,KAAKC,SAAS,IAAIJ,WAAW,KAAKH,YAAY,CAACM,SAAS,EAAE;QAClFX,cAAc,GAAG,IAAI;MACvB;IACF,CAAC,CAAC;IACF,IAAIC,sBAAsB,CAACY,MAAM,GAAG,CAAC,EAAE;MACrC;MACA;MACA;MACA,MAAM,IAAIC,KAAK,CAAC,0FAA0Fb,sBAAsB,CAAC,CAAC,CAAC,CAACP,OAAO,6CAA6CO,sBAAsB,CAACc,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACtB,OAAO,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;IAC7Q;IACA,IAAI,CAACjB,cAAc,EAAE;MACnB;MACAb,MAAM,CAACM,OAAO,CAACG,KAAK,GAAGE,QAAQ;MAC/BX,MAAM,CAACM,OAAO,CAACyB,YAAY,CAAC,aAAa,EAAEpB,QAAQ,CAAC;MACpDX,MAAM,CAACM,OAAO,CAAC0B,KAAK,CAACC,MAAM,CAACtB,QAAQ,CAAC;IACvC;IACA,IAAIG,sBAAsB,CAACY,MAAM,KAAK,CAAC,EAAE;MACvC,MAAM;QACJnB,OAAO;QACPgB;MACF,CAAC,GAAGT,sBAAsB,CAAC,CAAC,CAAC;MAC7B,MAAMI,YAAY,GAAGjB,kBAAkB,CAACK,OAAO,CAACC,OAAO,CAAC;MACxD,MAAM2B,KAAK,GAAGhB,YAAY,CAACE,aAAa,CAACR,kBAAkB,CAAC;MAC5D,IAAIM,YAAY,CAACiB,YAAY,IAAIZ,cAAc,EAAE;QAC/CL,YAAY,CAACiB,YAAY,CAACD,KAAK,EAAE;UAC/BxB,MAAM;UACN0B,GAAG,EAAEpC,MAAM,CAACM;QACd,CAAC,CAAC;MACJ;MACA,IAAI,CAACO,cAAc,EAAE;QACnBb,MAAM,CAACM,OAAO,CAACyB,YAAY,CAACb,YAAY,CAACmB,WAAW,EAAEH,KAAK,EAAE;UAC3DxB;QACF,CAAC,CAAC;MACJ;IACF;IACA,OAAO,CAACG,cAAc;EACxB,CAAC,EAAE,CAACb,MAAM,CAAC,CAAC;EACZ,MAAMsC,kBAAkB,GAAG1C,KAAK,CAACQ,WAAW,CAAC,CAACmC,GAAG,EAAE9B,KAAK,EAAEC,MAAM,KAAK;IACnE,OAAOV,MAAM,CAACM,OAAO,CAACE,QAAQ,CAACgC,aAAa,IAAI;MAC9C,OAAO7C,QAAQ,CAAC,CAAC,CAAC,EAAE6C,aAAa,EAAE;QACjC,CAACD,GAAG,GAAG9B,KAAK,CAAC+B,aAAa,CAACD,GAAG,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,EAAE7B,MAAM,CAAC;EACZ,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,MAAMyC,cAAc,GAAG;IACrBjC;EACF,CAAC;EACD,MAAMkC,eAAe,GAAG;IACtBJ,kBAAkB;IAClBnC;EACF,CAAC;EACDN,gBAAgB,CAACG,MAAM,EAAEyC,cAAc,EAAE,QAAQ,CAAC;EAClD5C,gBAAgB,CAACG,MAAM,EAAE0C,eAAe,EAAE,SAAS,CAAC;AACtD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}