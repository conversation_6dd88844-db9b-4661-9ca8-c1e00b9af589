{"ast": null, "code": "function intersectionWith(firstArr, secondArr, areItemsEqual) {\n  return firstArr.filter(firstItem => {\n    return secondArr.some(secondItem => {\n      return areItemsEqual(firstItem, secondItem);\n    });\n  });\n}\nexport { intersectionWith };", "map": {"version": 3, "names": ["intersectionWith", "firstArr", "secondArr", "areItemsEqual", "filter", "firstItem", "some", "secondItem"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/intersectionWith.mjs"], "sourcesContent": ["function intersectionWith(firstArr, secondArr, areItemsEqual) {\n    return firstArr.filter(firstItem => {\n        return secondArr.some(secondItem => {\n            return areItemsEqual(firstItem, secondItem);\n        });\n    });\n}\n\nexport { intersectionWith };\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAC1D,OAAOF,QAAQ,CAACG,MAAM,CAACC,SAAS,IAAI;IAChC,OAAOH,SAAS,CAACI,IAAI,CAACC,UAAU,IAAI;MAChC,OAAOJ,aAAa,CAACE,SAAS,EAAEE,UAAU,CAAC;IAC/C,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AAEA,SAASP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}