{"ast": null, "code": "export function roundToDecimalPlaces(value, decimals) {\n  return Math.round(value * 10 ** decimals) / 10 ** decimals;\n}", "map": {"version": 3, "names": ["roundToDecimalPlaces", "value", "decimals", "Math", "round"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/math/index.js"], "sourcesContent": ["export function roundToDecimalPlaces(value, decimals) {\n  return Math.round(value * 10 ** decimals) / 10 ** decimals;\n}"], "mappings": "AAAA,OAAO,SAASA,oBAAoBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACpD,OAAOC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,EAAE,IAAIC,QAAQ,CAAC,GAAG,EAAE,IAAIA,QAAQ;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}