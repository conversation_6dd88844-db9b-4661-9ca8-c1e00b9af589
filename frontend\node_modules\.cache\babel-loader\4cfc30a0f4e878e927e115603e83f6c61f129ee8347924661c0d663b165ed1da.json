{"ast": null, "code": "import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnPositionsSelector, gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridPageSelector, gridPageSizeSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridRowCountSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridDimensionsSelector } from \"../dimensions/index.js\";\n\n// Logic copied from https://www.w3.org/TR/wai-aria-practices/examples/listbox/js/listbox.js\n// Similar to https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView\nfunction scrollIntoView(dimensions) {\n  const {\n    containerSize,\n    scrollPosition,\n    elementSize,\n    elementOffset\n  } = dimensions;\n  const elementEnd = elementOffset + elementSize;\n  // Always scroll to top when cell is higher than viewport to avoid scroll jump\n  // See https://github.com/mui/mui-x/issues/4513 and https://github.com/mui/mui-x/issues/4514\n  if (elementSize > containerSize) {\n    return elementOffset;\n  }\n  if (elementEnd - containerSize > scrollPosition) {\n    return elementEnd - containerSize;\n  }\n  if (elementOffset < scrollPosition) {\n    return elementOffset;\n  }\n  return undefined;\n}\n\n/**\n * @requires useGridPagination (state) - can be after, async only\n * @requires useGridColumns (state) - can be after, async only\n * @requires useGridRows (state) - can be after, async only\n * @requires useGridRowsMeta (state) - can be after, async only\n * @requires useGridFilter (state)\n * @requires useGridColumnSpanning (method)\n */\nexport const useGridScroll = (apiRef, props) => {\n  const isRtl = useRtl();\n  const logger = useGridLogger(apiRef, 'useGridScroll');\n  const colRef = apiRef.current.columnHeadersContainerRef;\n  const virtualScrollerRef = apiRef.current.virtualScrollerRef;\n  const visibleSortedRows = useGridSelector(apiRef, gridExpandedSortedRowEntriesSelector);\n  const scrollToIndexes = React.useCallback(params => {\n    const dimensions = gridDimensionsSelector(apiRef);\n    const totalRowCount = gridRowCountSelector(apiRef);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const scrollToHeader = params.rowIndex == null;\n    if (!scrollToHeader && totalRowCount === 0 || visibleColumns.length === 0) {\n      return false;\n    }\n    logger.debug(`Scrolling to cell at row ${params.rowIndex}, col: ${params.colIndex} `);\n    let scrollCoordinates = {};\n    if (params.colIndex !== undefined) {\n      const columnPositions = gridColumnPositionsSelector(apiRef);\n      let cellWidth;\n      if (typeof params.rowIndex !== 'undefined') {\n        const rowId = visibleSortedRows[params.rowIndex]?.id;\n        const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, params.colIndex);\n        if (cellColSpanInfo && !cellColSpanInfo.spannedByColSpan) {\n          cellWidth = cellColSpanInfo.cellProps.width;\n        }\n      }\n      if (typeof cellWidth === 'undefined') {\n        cellWidth = visibleColumns[params.colIndex].computedWidth;\n      }\n      // When using RTL, `scrollLeft` becomes negative, so we must ensure that we only compare values.\n      scrollCoordinates.left = scrollIntoView({\n        containerSize: dimensions.viewportOuterSize.width,\n        scrollPosition: Math.abs(virtualScrollerRef.current.scrollLeft),\n        elementSize: cellWidth,\n        elementOffset: columnPositions[params.colIndex]\n      });\n    }\n    if (params.rowIndex !== undefined) {\n      const rowsMeta = gridRowsMetaSelector(apiRef);\n      const page = gridPageSelector(apiRef);\n      const pageSize = gridPageSizeSelector(apiRef);\n      const elementIndex = !props.pagination ? params.rowIndex : params.rowIndex - page * pageSize;\n      const targetOffsetHeight = rowsMeta.positions[elementIndex + 1] ? rowsMeta.positions[elementIndex + 1] - rowsMeta.positions[elementIndex] : rowsMeta.currentPageTotalHeight - rowsMeta.positions[elementIndex];\n      scrollCoordinates.top = scrollIntoView({\n        containerSize: dimensions.viewportInnerSize.height,\n        scrollPosition: virtualScrollerRef.current.scrollTop,\n        elementSize: targetOffsetHeight,\n        elementOffset: rowsMeta.positions[elementIndex]\n      });\n    }\n    scrollCoordinates = apiRef.current.unstable_applyPipeProcessors('scrollToIndexes', scrollCoordinates, params);\n    if (typeof scrollCoordinates.left !== 'undefined' || typeof scrollCoordinates.top !== 'undefined') {\n      apiRef.current.scroll(scrollCoordinates);\n      return true;\n    }\n    return false;\n  }, [logger, apiRef, virtualScrollerRef, props.pagination, visibleSortedRows]);\n  const scroll = React.useCallback(params => {\n    if (virtualScrollerRef.current && params.left !== undefined && colRef.current) {\n      const direction = isRtl ? -1 : 1;\n      colRef.current.scrollLeft = params.left;\n      virtualScrollerRef.current.scrollLeft = direction * params.left;\n      logger.debug(`Scrolling left: ${params.left}`);\n    }\n    if (virtualScrollerRef.current && params.top !== undefined) {\n      virtualScrollerRef.current.scrollTop = params.top;\n      logger.debug(`Scrolling top: ${params.top}`);\n    }\n    logger.debug(`Scrolling, updating container, and viewport`);\n  }, [virtualScrollerRef, isRtl, colRef, logger]);\n  const getScrollPosition = React.useCallback(() => {\n    if (!virtualScrollerRef?.current) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    return {\n      top: virtualScrollerRef.current.scrollTop,\n      left: virtualScrollerRef.current.scrollLeft\n    };\n  }, [virtualScrollerRef]);\n  const scrollApi = {\n    scroll,\n    scrollToIndexes,\n    getScrollPosition\n  };\n  useGridApiMethod(apiRef, scrollApi, 'public');\n};", "map": {"version": 3, "names": ["React", "useRtl", "useGridLogger", "gridColumnPositionsSelector", "gridVisibleColumnDefinitionsSelector", "useGridSelector", "gridPageSelector", "gridPageSizeSelector", "gridRowCountSelector", "gridRowsMetaSelector", "useGridApiMethod", "gridExpandedSortedRowEntriesSelector", "gridDimensionsSelector", "scrollIntoView", "dimensions", "containerSize", "scrollPosition", "elementSize", "elementOffset", "elementEnd", "undefined", "useGridScroll", "apiRef", "props", "isRtl", "logger", "colRef", "current", "columnHeadersContainerRef", "virtualScrollerRef", "visibleSortedRows", "scrollToIndexes", "useCallback", "params", "totalRowCount", "visibleColumns", "scrollToHeader", "rowIndex", "length", "debug", "colIndex", "scrollCoordinates", "columnPositions", "cellWidth", "rowId", "id", "cellColSpanInfo", "unstable_getCellColSpanInfo", "spannedByColSpan", "cellProps", "width", "computedWidth", "left", "viewportOuterSize", "Math", "abs", "scrollLeft", "rowsMeta", "page", "pageSize", "elementIndex", "pagination", "targetOffsetHeight", "positions", "currentPageTotalHeight", "top", "viewportInnerSize", "height", "scrollTop", "unstable_applyPipeProcessors", "scroll", "direction", "getScrollPosition", "scrollApi"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/scroll/useGridScroll.js"], "sourcesContent": ["import * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useGridLogger } from \"../../utils/useGridLogger.js\";\nimport { gridColumnPositionsSelector, gridVisibleColumnDefinitionsSelector } from \"../columns/gridColumnsSelector.js\";\nimport { useGridSelector } from \"../../utils/useGridSelector.js\";\nimport { gridPageSelector, gridPageSizeSelector } from \"../pagination/gridPaginationSelector.js\";\nimport { gridRowCountSelector } from \"../rows/gridRowsSelector.js\";\nimport { gridRowsMetaSelector } from \"../rows/gridRowsMetaSelector.js\";\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { gridExpandedSortedRowEntriesSelector } from \"../filter/gridFilterSelector.js\";\nimport { gridDimensionsSelector } from \"../dimensions/index.js\";\n\n// Logic copied from https://www.w3.org/TR/wai-aria-practices/examples/listbox/js/listbox.js\n// Similar to https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView\nfunction scrollIntoView(dimensions) {\n  const {\n    containerSize,\n    scrollPosition,\n    elementSize,\n    elementOffset\n  } = dimensions;\n  const elementEnd = elementOffset + elementSize;\n  // Always scroll to top when cell is higher than viewport to avoid scroll jump\n  // See https://github.com/mui/mui-x/issues/4513 and https://github.com/mui/mui-x/issues/4514\n  if (elementSize > containerSize) {\n    return elementOffset;\n  }\n  if (elementEnd - containerSize > scrollPosition) {\n    return elementEnd - containerSize;\n  }\n  if (elementOffset < scrollPosition) {\n    return elementOffset;\n  }\n  return undefined;\n}\n\n/**\n * @requires useGridPagination (state) - can be after, async only\n * @requires useGridColumns (state) - can be after, async only\n * @requires useGridRows (state) - can be after, async only\n * @requires useGridRowsMeta (state) - can be after, async only\n * @requires useGridFilter (state)\n * @requires useGridColumnSpanning (method)\n */\nexport const useGridScroll = (apiRef, props) => {\n  const isRtl = useRtl();\n  const logger = useGridLogger(apiRef, 'useGridScroll');\n  const colRef = apiRef.current.columnHeadersContainerRef;\n  const virtualScrollerRef = apiRef.current.virtualScrollerRef;\n  const visibleSortedRows = useGridSelector(apiRef, gridExpandedSortedRowEntriesSelector);\n  const scrollToIndexes = React.useCallback(params => {\n    const dimensions = gridDimensionsSelector(apiRef);\n    const totalRowCount = gridRowCountSelector(apiRef);\n    const visibleColumns = gridVisibleColumnDefinitionsSelector(apiRef);\n    const scrollToHeader = params.rowIndex == null;\n    if (!scrollToHeader && totalRowCount === 0 || visibleColumns.length === 0) {\n      return false;\n    }\n    logger.debug(`Scrolling to cell at row ${params.rowIndex}, col: ${params.colIndex} `);\n    let scrollCoordinates = {};\n    if (params.colIndex !== undefined) {\n      const columnPositions = gridColumnPositionsSelector(apiRef);\n      let cellWidth;\n      if (typeof params.rowIndex !== 'undefined') {\n        const rowId = visibleSortedRows[params.rowIndex]?.id;\n        const cellColSpanInfo = apiRef.current.unstable_getCellColSpanInfo(rowId, params.colIndex);\n        if (cellColSpanInfo && !cellColSpanInfo.spannedByColSpan) {\n          cellWidth = cellColSpanInfo.cellProps.width;\n        }\n      }\n      if (typeof cellWidth === 'undefined') {\n        cellWidth = visibleColumns[params.colIndex].computedWidth;\n      }\n      // When using RTL, `scrollLeft` becomes negative, so we must ensure that we only compare values.\n      scrollCoordinates.left = scrollIntoView({\n        containerSize: dimensions.viewportOuterSize.width,\n        scrollPosition: Math.abs(virtualScrollerRef.current.scrollLeft),\n        elementSize: cellWidth,\n        elementOffset: columnPositions[params.colIndex]\n      });\n    }\n    if (params.rowIndex !== undefined) {\n      const rowsMeta = gridRowsMetaSelector(apiRef);\n      const page = gridPageSelector(apiRef);\n      const pageSize = gridPageSizeSelector(apiRef);\n      const elementIndex = !props.pagination ? params.rowIndex : params.rowIndex - page * pageSize;\n      const targetOffsetHeight = rowsMeta.positions[elementIndex + 1] ? rowsMeta.positions[elementIndex + 1] - rowsMeta.positions[elementIndex] : rowsMeta.currentPageTotalHeight - rowsMeta.positions[elementIndex];\n      scrollCoordinates.top = scrollIntoView({\n        containerSize: dimensions.viewportInnerSize.height,\n        scrollPosition: virtualScrollerRef.current.scrollTop,\n        elementSize: targetOffsetHeight,\n        elementOffset: rowsMeta.positions[elementIndex]\n      });\n    }\n    scrollCoordinates = apiRef.current.unstable_applyPipeProcessors('scrollToIndexes', scrollCoordinates, params);\n    if (typeof scrollCoordinates.left !== 'undefined' || typeof scrollCoordinates.top !== 'undefined') {\n      apiRef.current.scroll(scrollCoordinates);\n      return true;\n    }\n    return false;\n  }, [logger, apiRef, virtualScrollerRef, props.pagination, visibleSortedRows]);\n  const scroll = React.useCallback(params => {\n    if (virtualScrollerRef.current && params.left !== undefined && colRef.current) {\n      const direction = isRtl ? -1 : 1;\n      colRef.current.scrollLeft = params.left;\n      virtualScrollerRef.current.scrollLeft = direction * params.left;\n      logger.debug(`Scrolling left: ${params.left}`);\n    }\n    if (virtualScrollerRef.current && params.top !== undefined) {\n      virtualScrollerRef.current.scrollTop = params.top;\n      logger.debug(`Scrolling top: ${params.top}`);\n    }\n    logger.debug(`Scrolling, updating container, and viewport`);\n  }, [virtualScrollerRef, isRtl, colRef, logger]);\n  const getScrollPosition = React.useCallback(() => {\n    if (!virtualScrollerRef?.current) {\n      return {\n        top: 0,\n        left: 0\n      };\n    }\n    return {\n      top: virtualScrollerRef.current.scrollTop,\n      left: virtualScrollerRef.current.scrollLeft\n    };\n  }, [virtualScrollerRef]);\n  const scrollApi = {\n    scroll,\n    scrollToIndexes,\n    getScrollPosition\n  };\n  useGridApiMethod(apiRef, scrollApi, 'public');\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,2BAA2B,EAAEC,oCAAoC,QAAQ,mCAAmC;AACrH,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,gBAAgB,EAAEC,oBAAoB,QAAQ,yCAAyC;AAChG,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,oCAAoC,QAAQ,iCAAiC;AACtF,SAASC,sBAAsB,QAAQ,wBAAwB;;AAE/D;AACA;AACA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,MAAM;IACJC,aAAa;IACbC,cAAc;IACdC,WAAW;IACXC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,UAAU,GAAGD,aAAa,GAAGD,WAAW;EAC9C;EACA;EACA,IAAIA,WAAW,GAAGF,aAAa,EAAE;IAC/B,OAAOG,aAAa;EACtB;EACA,IAAIC,UAAU,GAAGJ,aAAa,GAAGC,cAAc,EAAE;IAC/C,OAAOG,UAAU,GAAGJ,aAAa;EACnC;EACA,IAAIG,aAAa,GAAGF,cAAc,EAAE;IAClC,OAAOE,aAAa;EACtB;EACA,OAAOE,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC9C,MAAMC,KAAK,GAAGvB,MAAM,CAAC,CAAC;EACtB,MAAMwB,MAAM,GAAGvB,aAAa,CAACoB,MAAM,EAAE,eAAe,CAAC;EACrD,MAAMI,MAAM,GAAGJ,MAAM,CAACK,OAAO,CAACC,yBAAyB;EACvD,MAAMC,kBAAkB,GAAGP,MAAM,CAACK,OAAO,CAACE,kBAAkB;EAC5D,MAAMC,iBAAiB,GAAGzB,eAAe,CAACiB,MAAM,EAAEX,oCAAoC,CAAC;EACvF,MAAMoB,eAAe,GAAG/B,KAAK,CAACgC,WAAW,CAACC,MAAM,IAAI;IAClD,MAAMnB,UAAU,GAAGF,sBAAsB,CAACU,MAAM,CAAC;IACjD,MAAMY,aAAa,GAAG1B,oBAAoB,CAACc,MAAM,CAAC;IAClD,MAAMa,cAAc,GAAG/B,oCAAoC,CAACkB,MAAM,CAAC;IACnE,MAAMc,cAAc,GAAGH,MAAM,CAACI,QAAQ,IAAI,IAAI;IAC9C,IAAI,CAACD,cAAc,IAAIF,aAAa,KAAK,CAAC,IAAIC,cAAc,CAACG,MAAM,KAAK,CAAC,EAAE;MACzE,OAAO,KAAK;IACd;IACAb,MAAM,CAACc,KAAK,CAAC,4BAA4BN,MAAM,CAACI,QAAQ,UAAUJ,MAAM,CAACO,QAAQ,GAAG,CAAC;IACrF,IAAIC,iBAAiB,GAAG,CAAC,CAAC;IAC1B,IAAIR,MAAM,CAACO,QAAQ,KAAKpB,SAAS,EAAE;MACjC,MAAMsB,eAAe,GAAGvC,2BAA2B,CAACmB,MAAM,CAAC;MAC3D,IAAIqB,SAAS;MACb,IAAI,OAAOV,MAAM,CAACI,QAAQ,KAAK,WAAW,EAAE;QAC1C,MAAMO,KAAK,GAAGd,iBAAiB,CAACG,MAAM,CAACI,QAAQ,CAAC,EAAEQ,EAAE;QACpD,MAAMC,eAAe,GAAGxB,MAAM,CAACK,OAAO,CAACoB,2BAA2B,CAACH,KAAK,EAAEX,MAAM,CAACO,QAAQ,CAAC;QAC1F,IAAIM,eAAe,IAAI,CAACA,eAAe,CAACE,gBAAgB,EAAE;UACxDL,SAAS,GAAGG,eAAe,CAACG,SAAS,CAACC,KAAK;QAC7C;MACF;MACA,IAAI,OAAOP,SAAS,KAAK,WAAW,EAAE;QACpCA,SAAS,GAAGR,cAAc,CAACF,MAAM,CAACO,QAAQ,CAAC,CAACW,aAAa;MAC3D;MACA;MACAV,iBAAiB,CAACW,IAAI,GAAGvC,cAAc,CAAC;QACtCE,aAAa,EAAED,UAAU,CAACuC,iBAAiB,CAACH,KAAK;QACjDlC,cAAc,EAAEsC,IAAI,CAACC,GAAG,CAAC1B,kBAAkB,CAACF,OAAO,CAAC6B,UAAU,CAAC;QAC/DvC,WAAW,EAAE0B,SAAS;QACtBzB,aAAa,EAAEwB,eAAe,CAACT,MAAM,CAACO,QAAQ;MAChD,CAAC,CAAC;IACJ;IACA,IAAIP,MAAM,CAACI,QAAQ,KAAKjB,SAAS,EAAE;MACjC,MAAMqC,QAAQ,GAAGhD,oBAAoB,CAACa,MAAM,CAAC;MAC7C,MAAMoC,IAAI,GAAGpD,gBAAgB,CAACgB,MAAM,CAAC;MACrC,MAAMqC,QAAQ,GAAGpD,oBAAoB,CAACe,MAAM,CAAC;MAC7C,MAAMsC,YAAY,GAAG,CAACrC,KAAK,CAACsC,UAAU,GAAG5B,MAAM,CAACI,QAAQ,GAAGJ,MAAM,CAACI,QAAQ,GAAGqB,IAAI,GAAGC,QAAQ;MAC5F,MAAMG,kBAAkB,GAAGL,QAAQ,CAACM,SAAS,CAACH,YAAY,GAAG,CAAC,CAAC,GAAGH,QAAQ,CAACM,SAAS,CAACH,YAAY,GAAG,CAAC,CAAC,GAAGH,QAAQ,CAACM,SAAS,CAACH,YAAY,CAAC,GAAGH,QAAQ,CAACO,sBAAsB,GAAGP,QAAQ,CAACM,SAAS,CAACH,YAAY,CAAC;MAC9MnB,iBAAiB,CAACwB,GAAG,GAAGpD,cAAc,CAAC;QACrCE,aAAa,EAAED,UAAU,CAACoD,iBAAiB,CAACC,MAAM;QAClDnD,cAAc,EAAEa,kBAAkB,CAACF,OAAO,CAACyC,SAAS;QACpDnD,WAAW,EAAE6C,kBAAkB;QAC/B5C,aAAa,EAAEuC,QAAQ,CAACM,SAAS,CAACH,YAAY;MAChD,CAAC,CAAC;IACJ;IACAnB,iBAAiB,GAAGnB,MAAM,CAACK,OAAO,CAAC0C,4BAA4B,CAAC,iBAAiB,EAAE5B,iBAAiB,EAAER,MAAM,CAAC;IAC7G,IAAI,OAAOQ,iBAAiB,CAACW,IAAI,KAAK,WAAW,IAAI,OAAOX,iBAAiB,CAACwB,GAAG,KAAK,WAAW,EAAE;MACjG3C,MAAM,CAACK,OAAO,CAAC2C,MAAM,CAAC7B,iBAAiB,CAAC;MACxC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAAChB,MAAM,EAAEH,MAAM,EAAEO,kBAAkB,EAAEN,KAAK,CAACsC,UAAU,EAAE/B,iBAAiB,CAAC,CAAC;EAC7E,MAAMwC,MAAM,GAAGtE,KAAK,CAACgC,WAAW,CAACC,MAAM,IAAI;IACzC,IAAIJ,kBAAkB,CAACF,OAAO,IAAIM,MAAM,CAACmB,IAAI,KAAKhC,SAAS,IAAIM,MAAM,CAACC,OAAO,EAAE;MAC7E,MAAM4C,SAAS,GAAG/C,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAChCE,MAAM,CAACC,OAAO,CAAC6B,UAAU,GAAGvB,MAAM,CAACmB,IAAI;MACvCvB,kBAAkB,CAACF,OAAO,CAAC6B,UAAU,GAAGe,SAAS,GAAGtC,MAAM,CAACmB,IAAI;MAC/D3B,MAAM,CAACc,KAAK,CAAC,mBAAmBN,MAAM,CAACmB,IAAI,EAAE,CAAC;IAChD;IACA,IAAIvB,kBAAkB,CAACF,OAAO,IAAIM,MAAM,CAACgC,GAAG,KAAK7C,SAAS,EAAE;MAC1DS,kBAAkB,CAACF,OAAO,CAACyC,SAAS,GAAGnC,MAAM,CAACgC,GAAG;MACjDxC,MAAM,CAACc,KAAK,CAAC,kBAAkBN,MAAM,CAACgC,GAAG,EAAE,CAAC;IAC9C;IACAxC,MAAM,CAACc,KAAK,CAAC,6CAA6C,CAAC;EAC7D,CAAC,EAAE,CAACV,kBAAkB,EAAEL,KAAK,EAAEE,MAAM,EAAED,MAAM,CAAC,CAAC;EAC/C,MAAM+C,iBAAiB,GAAGxE,KAAK,CAACgC,WAAW,CAAC,MAAM;IAChD,IAAI,CAACH,kBAAkB,EAAEF,OAAO,EAAE;MAChC,OAAO;QACLsC,GAAG,EAAE,CAAC;QACNb,IAAI,EAAE;MACR,CAAC;IACH;IACA,OAAO;MACLa,GAAG,EAAEpC,kBAAkB,CAACF,OAAO,CAACyC,SAAS;MACzChB,IAAI,EAAEvB,kBAAkB,CAACF,OAAO,CAAC6B;IACnC,CAAC;EACH,CAAC,EAAE,CAAC3B,kBAAkB,CAAC,CAAC;EACxB,MAAM4C,SAAS,GAAG;IAChBH,MAAM;IACNvC,eAAe;IACfyC;EACF,CAAC;EACD9D,gBAAgB,CAACY,MAAM,EAAEmD,SAAS,EAAE,QAAQ,CAAC;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}