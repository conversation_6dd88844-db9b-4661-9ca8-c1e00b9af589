{"ast": null, "code": "export { default as useLazyRef } from '@mui/utils/useLazyRef';", "map": {"version": 3, "names": ["default", "useLazyRef"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useLazyRef.js"], "sourcesContent": ["export { default as useLazyRef } from '@mui/utils/useLazyRef';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,UAAU,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}