{"ast": null, "code": "import { sqrt } from \"../math.js\";\nconst sqrt3 = sqrt(3);\nexport default {\n  draw(context, size) {\n    const y = -sqrt(size / (sqrt3 * 3));\n    context.moveTo(0, y * 2);\n    context.lineTo(-sqrt3 * y, -y);\n    context.lineTo(sqrt3 * y, -y);\n    context.closePath();\n  }\n};", "map": {"version": 3, "names": ["sqrt", "sqrt3", "draw", "context", "size", "y", "moveTo", "lineTo", "closePath"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/d3-shape/src/symbol/triangle.js"], "sourcesContent": ["import {sqrt} from \"../math.js\";\n\nconst sqrt3 = sqrt(3);\n\nexport default {\n  draw(context, size) {\n    const y = -sqrt(size / (sqrt3 * 3));\n    context.moveTo(0, y * 2);\n    context.lineTo(-sqrt3 * y, -y);\n    context.lineTo(sqrt3 * y, -y);\n    context.closePath();\n  }\n};\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,YAAY;AAE/B,MAAMC,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC;AAErB,eAAe;EACbE,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAG,CAACL,IAAI,CAACI,IAAI,IAAIH,KAAK,GAAG,CAAC,CAAC,CAAC;IACnCE,OAAO,CAACG,MAAM,CAAC,CAAC,EAAED,CAAC,GAAG,CAAC,CAAC;IACxBF,OAAO,CAACI,MAAM,CAAC,CAACN,KAAK,GAAGI,CAAC,EAAE,CAACA,CAAC,CAAC;IAC9BF,OAAO,CAACI,MAAM,CAACN,KAAK,GAAGI,CAAC,EAAE,CAACA,CAAC,CAAC;IAC7BF,OAAO,CAACK,SAAS,CAAC,CAAC;EACrB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}