{"ast": null, "code": "import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nconst noop = () => {};\n\n/**\n * Runs an effect once, when `condition` is true.\n */\nexport const useRunOnce = (condition, effect) => {\n  const didRun = React.useRef(false);\n  useEnhancedEffect(() => {\n    if (didRun.current || !condition) {\n      return noop;\n    }\n    didRun.current = true;\n    return effect();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [didRun.current || condition]);\n};", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "noop", "useRunOnce", "condition", "effect", "did<PERSON>un", "useRef", "current"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/useRunOnce/useRunOnce.js"], "sourcesContent": ["import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nconst noop = () => {};\n\n/**\n * Runs an effect once, when `condition` is true.\n */\nexport const useRunOnce = (condition, effect) => {\n  const didRun = React.useRef(false);\n  useEnhancedEffect(() => {\n    if (didRun.current || !condition) {\n      return noop;\n    }\n    didRun.current = true;\n    return effect();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [didRun.current || condition]);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;;AAErB;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAGA,CAACC,SAAS,EAAEC,MAAM,KAAK;EAC/C,MAAMC,MAAM,GAAGN,KAAK,CAACO,MAAM,CAAC,KAAK,CAAC;EAClCN,iBAAiB,CAAC,MAAM;IACtB,IAAIK,MAAM,CAACE,OAAO,IAAI,CAACJ,SAAS,EAAE;MAChC,OAAOF,IAAI;IACb;IACAI,MAAM,CAACE,OAAO,GAAG,IAAI;IACrB,OAAOH,MAAM,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACC,MAAM,CAACE,OAAO,IAAIJ,SAAS,CAAC,CAAC;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}