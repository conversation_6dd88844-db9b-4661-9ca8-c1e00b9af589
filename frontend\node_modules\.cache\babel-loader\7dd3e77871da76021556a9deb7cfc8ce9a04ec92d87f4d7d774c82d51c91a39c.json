{"ast": null, "code": "export { warnOnce, clearWarningsCache } from \"./warning.js\";", "map": {"version": 3, "names": ["warnOnce", "clearWarningsCache"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-internals/esm/warning/index.js"], "sourcesContent": ["export { warnOnce, clearWarningsCache } from \"./warning.js\";"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,kBAAkB,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}