{"ast": null, "code": "function range(start, end, step = 1) {\n  if (end == null) {\n    end = start;\n    start = 0;\n  }\n  if (!Number.isInteger(step) || step === 0) {\n    throw new Error(`The step value must be a non-zero integer.`);\n  }\n  const length = Math.max(Math.ceil((end - start) / step), 0);\n  const result = new Array(length);\n  for (let i = 0; i < length; i++) {\n    result[i] = start + i * step;\n  }\n  return result;\n}\nexport { range };", "map": {"version": 3, "names": ["range", "start", "end", "step", "Number", "isInteger", "Error", "length", "Math", "max", "ceil", "result", "Array", "i"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/math/range.mjs"], "sourcesContent": ["function range(start, end, step = 1) {\n    if (end == null) {\n        end = start;\n        start = 0;\n    }\n    if (!Number.isInteger(step) || step === 0) {\n        throw new Error(`The step value must be a non-zero integer.`);\n    }\n    const length = Math.max(Math.ceil((end - start) / step), 0);\n    const result = new Array(length);\n    for (let i = 0; i < length; i++) {\n        result[i] = start + i * step;\n    }\n    return result;\n}\n\nexport { range };\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,GAAG,CAAC,EAAE;EACjC,IAAID,GAAG,IAAI,IAAI,EAAE;IACbA,GAAG,GAAGD,KAAK;IACXA,KAAK,GAAG,CAAC;EACb;EACA,IAAI,CAACG,MAAM,CAACC,SAAS,CAACF,IAAI,CAAC,IAAIA,IAAI,KAAK,CAAC,EAAE;IACvC,MAAM,IAAIG,KAAK,CAAC,4CAA4C,CAAC;EACjE;EACA,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,IAAI,CAAC,CAACR,GAAG,GAAGD,KAAK,IAAIE,IAAI,CAAC,EAAE,CAAC,CAAC;EAC3D,MAAMQ,MAAM,GAAG,IAAIC,KAAK,CAACL,MAAM,CAAC;EAChC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAEM,CAAC,EAAE,EAAE;IAC7BF,MAAM,CAACE,CAAC,CAAC,GAAGZ,KAAK,GAAGY,CAAC,GAAGV,IAAI;EAChC;EACA,OAAOQ,MAAM;AACjB;AAEA,SAASX,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}