{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { fastObjectShallowCompare } from '@mui/x-internals/fastObjectShallowCompare';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim';\nimport { useLazyRef } from \"./useLazyRef.js\";\nconst defaultCompare = Object.is;\nexport const objectShallowCompare = fastObjectShallowCompare;\nconst arrayShallowCompare = (a, b) => {\n  if (a === b) {\n    return true;\n  }\n  return a.length === b.length && a.every((v, i) => v === b[i]);\n};\nexport const argsEqual = (prev, curr) => {\n  let fn = Object.is;\n  if (curr instanceof Array) {\n    fn = arrayShallowCompare;\n  } else if (curr instanceof Object) {\n    fn = objectShallowCompare;\n  }\n  return fn(prev, curr);\n};\nconst createRefs = () => ({\n  state: null,\n  equals: null,\n  selector: null,\n  args: undefined\n});\nconst EMPTY = [];\nconst emptyGetSnapshot = () => null;\nexport function useGridSelector(apiRef, selector, args = undefined, equals = defaultCompare) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!apiRef.current.state) {\n      warnOnce(['MUI X: `useGridSelector` has been called before the initialization of the state.', 'This hook can only be used inside the context of the grid.']);\n    }\n  }\n  const refs = useLazyRef(createRefs);\n  const didInit = refs.current.selector !== null;\n  const [state, setState] = React.useState(\n  // We don't use an initialization function to avoid allocations\n  didInit ? null : selector(apiRef, args));\n  refs.current.state = state;\n  refs.current.equals = equals;\n  refs.current.selector = selector;\n  const prevArgs = refs.current.args;\n  refs.current.args = args;\n  if (didInit && !argsEqual(prevArgs, args)) {\n    const newState = refs.current.selector(apiRef, refs.current.args);\n    if (!refs.current.equals(refs.current.state, newState)) {\n      refs.current.state = newState;\n      setState(newState);\n    }\n  }\n  const subscribe = React.useCallback(() => {\n    if (refs.current.subscription) {\n      return null;\n    }\n    refs.current.subscription = apiRef.current.store.subscribe(() => {\n      const newState = refs.current.selector(apiRef, refs.current.args);\n      if (!refs.current.equals(refs.current.state, newState)) {\n        refs.current.state = newState;\n        setState(newState);\n      }\n    });\n    return null;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  EMPTY);\n  const unsubscribe = React.useCallback(() => {\n    // Fixes issue in React Strict Mode, where getSnapshot is not called\n    if (!refs.current.subscription) {\n      subscribe();\n    }\n    return () => {\n      if (refs.current.subscription) {\n        refs.current.subscription();\n        refs.current.subscription = undefined;\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, EMPTY);\n  useSyncExternalStore(unsubscribe, subscribe, emptyGetSnapshot);\n  return state;\n}", "map": {"version": 3, "names": ["React", "fastObjectShallowCompare", "warnOnce", "useSyncExternalStore", "useLazyRef", "defaultCompare", "Object", "is", "objectShallowCompare", "arrayShallowCompare", "a", "b", "length", "every", "v", "i", "argsEqual", "prev", "curr", "fn", "Array", "createRefs", "state", "equals", "selector", "args", "undefined", "EMPTY", "emptyGetSnapshot", "useGridSelector", "apiRef", "process", "env", "NODE_ENV", "current", "refs", "didInit", "setState", "useState", "prevArgs", "newState", "subscribe", "useCallback", "subscription", "store", "unsubscribe"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridSelector.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { fastObjectShallowCompare } from '@mui/x-internals/fastObjectShallowCompare';\nimport { warnOnce } from '@mui/x-internals/warning';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim';\nimport { useLazyRef } from \"./useLazyRef.js\";\nconst defaultCompare = Object.is;\nexport const objectShallowCompare = fastObjectShallowCompare;\nconst arrayShallowCompare = (a, b) => {\n  if (a === b) {\n    return true;\n  }\n  return a.length === b.length && a.every((v, i) => v === b[i]);\n};\nexport const argsEqual = (prev, curr) => {\n  let fn = Object.is;\n  if (curr instanceof Array) {\n    fn = arrayShallowCompare;\n  } else if (curr instanceof Object) {\n    fn = objectShallowCompare;\n  }\n  return fn(prev, curr);\n};\nconst createRefs = () => ({\n  state: null,\n  equals: null,\n  selector: null,\n  args: undefined\n});\nconst EMPTY = [];\nconst emptyGetSnapshot = () => null;\nexport function useGridSelector(apiRef, selector, args = undefined, equals = defaultCompare) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!apiRef.current.state) {\n      warnOnce(['MUI X: `useGridSelector` has been called before the initialization of the state.', 'This hook can only be used inside the context of the grid.']);\n    }\n  }\n  const refs = useLazyRef(createRefs);\n  const didInit = refs.current.selector !== null;\n  const [state, setState] = React.useState(\n  // We don't use an initialization function to avoid allocations\n  didInit ? null : selector(apiRef, args));\n  refs.current.state = state;\n  refs.current.equals = equals;\n  refs.current.selector = selector;\n  const prevArgs = refs.current.args;\n  refs.current.args = args;\n  if (didInit && !argsEqual(prevArgs, args)) {\n    const newState = refs.current.selector(apiRef, refs.current.args);\n    if (!refs.current.equals(refs.current.state, newState)) {\n      refs.current.state = newState;\n      setState(newState);\n    }\n  }\n  const subscribe = React.useCallback(() => {\n    if (refs.current.subscription) {\n      return null;\n    }\n    refs.current.subscription = apiRef.current.store.subscribe(() => {\n      const newState = refs.current.selector(apiRef, refs.current.args);\n      if (!refs.current.equals(refs.current.state, newState)) {\n        refs.current.state = newState;\n        setState(newState);\n      }\n    });\n    return null;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  EMPTY);\n  const unsubscribe = React.useCallback(() => {\n    // Fixes issue in React Strict Mode, where getSnapshot is not called\n    if (!refs.current.subscription) {\n      subscribe();\n    }\n    return () => {\n      if (refs.current.subscription) {\n        refs.current.subscription();\n        refs.current.subscription = undefined;\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, EMPTY);\n  useSyncExternalStore(unsubscribe, subscribe, emptyGetSnapshot);\n  return state;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,wBAAwB,QAAQ,2CAA2C;AACpF,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,MAAMC,cAAc,GAAGC,MAAM,CAACC,EAAE;AAChC,OAAO,MAAMC,oBAAoB,GAAGP,wBAAwB;AAC5D,MAAMQ,mBAAmB,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EACpC,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAO,IAAI;EACb;EACA,OAAOD,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,IAAIF,CAAC,CAACG,KAAK,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKH,CAAC,CAACI,CAAC,CAAC,CAAC;AAC/D,CAAC;AACD,OAAO,MAAMC,SAAS,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EACvC,IAAIC,EAAE,GAAGb,MAAM,CAACC,EAAE;EAClB,IAAIW,IAAI,YAAYE,KAAK,EAAE;IACzBD,EAAE,GAAGV,mBAAmB;EAC1B,CAAC,MAAM,IAAIS,IAAI,YAAYZ,MAAM,EAAE;IACjCa,EAAE,GAAGX,oBAAoB;EAC3B;EACA,OAAOW,EAAE,CAACF,IAAI,EAAEC,IAAI,CAAC;AACvB,CAAC;AACD,MAAMG,UAAU,GAAGA,CAAA,MAAO;EACxBC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAEC;AACR,CAAC,CAAC;AACF,MAAMC,KAAK,GAAG,EAAE;AAChB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM,IAAI;AACnC,OAAO,SAASC,eAAeA,CAACC,MAAM,EAAEN,QAAQ,EAAEC,IAAI,GAAGC,SAAS,EAAEH,MAAM,GAAGlB,cAAc,EAAE;EAC3F,IAAI0B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACH,MAAM,CAACI,OAAO,CAACZ,KAAK,EAAE;MACzBpB,QAAQ,CAAC,CAAC,kFAAkF,EAAE,4DAA4D,CAAC,CAAC;IAC9J;EACF;EACA,MAAMiC,IAAI,GAAG/B,UAAU,CAACiB,UAAU,CAAC;EACnC,MAAMe,OAAO,GAAGD,IAAI,CAACD,OAAO,CAACV,QAAQ,KAAK,IAAI;EAC9C,MAAM,CAACF,KAAK,EAAEe,QAAQ,CAAC,GAAGrC,KAAK,CAACsC,QAAQ;EACxC;EACAF,OAAO,GAAG,IAAI,GAAGZ,QAAQ,CAACM,MAAM,EAAEL,IAAI,CAAC,CAAC;EACxCU,IAAI,CAACD,OAAO,CAACZ,KAAK,GAAGA,KAAK;EAC1Ba,IAAI,CAACD,OAAO,CAACX,MAAM,GAAGA,MAAM;EAC5BY,IAAI,CAACD,OAAO,CAACV,QAAQ,GAAGA,QAAQ;EAChC,MAAMe,QAAQ,GAAGJ,IAAI,CAACD,OAAO,CAACT,IAAI;EAClCU,IAAI,CAACD,OAAO,CAACT,IAAI,GAAGA,IAAI;EACxB,IAAIW,OAAO,IAAI,CAACpB,SAAS,CAACuB,QAAQ,EAAEd,IAAI,CAAC,EAAE;IACzC,MAAMe,QAAQ,GAAGL,IAAI,CAACD,OAAO,CAACV,QAAQ,CAACM,MAAM,EAAEK,IAAI,CAACD,OAAO,CAACT,IAAI,CAAC;IACjE,IAAI,CAACU,IAAI,CAACD,OAAO,CAACX,MAAM,CAACY,IAAI,CAACD,OAAO,CAACZ,KAAK,EAAEkB,QAAQ,CAAC,EAAE;MACtDL,IAAI,CAACD,OAAO,CAACZ,KAAK,GAAGkB,QAAQ;MAC7BH,QAAQ,CAACG,QAAQ,CAAC;IACpB;EACF;EACA,MAAMC,SAAS,GAAGzC,KAAK,CAAC0C,WAAW,CAAC,MAAM;IACxC,IAAIP,IAAI,CAACD,OAAO,CAACS,YAAY,EAAE;MAC7B,OAAO,IAAI;IACb;IACAR,IAAI,CAACD,OAAO,CAACS,YAAY,GAAGb,MAAM,CAACI,OAAO,CAACU,KAAK,CAACH,SAAS,CAAC,MAAM;MAC/D,MAAMD,QAAQ,GAAGL,IAAI,CAACD,OAAO,CAACV,QAAQ,CAACM,MAAM,EAAEK,IAAI,CAACD,OAAO,CAACT,IAAI,CAAC;MACjE,IAAI,CAACU,IAAI,CAACD,OAAO,CAACX,MAAM,CAACY,IAAI,CAACD,OAAO,CAACZ,KAAK,EAAEkB,QAAQ,CAAC,EAAE;QACtDL,IAAI,CAACD,OAAO,CAACZ,KAAK,GAAGkB,QAAQ;QAC7BH,QAAQ,CAACG,QAAQ,CAAC;MACpB;IACF,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EACD;EACAb,KAAK,CAAC;EACN,MAAMkB,WAAW,GAAG7C,KAAK,CAAC0C,WAAW,CAAC,MAAM;IAC1C;IACA,IAAI,CAACP,IAAI,CAACD,OAAO,CAACS,YAAY,EAAE;MAC9BF,SAAS,CAAC,CAAC;IACb;IACA,OAAO,MAAM;MACX,IAAIN,IAAI,CAACD,OAAO,CAACS,YAAY,EAAE;QAC7BR,IAAI,CAACD,OAAO,CAACS,YAAY,CAAC,CAAC;QAC3BR,IAAI,CAACD,OAAO,CAACS,YAAY,GAAGjB,SAAS;MACvC;IACF,CAAC;IACD;EACF,CAAC,EAAEC,KAAK,CAAC;EACTxB,oBAAoB,CAAC0C,WAAW,EAAEJ,SAAS,EAAEb,gBAAgB,CAAC;EAC9D,OAAON,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}