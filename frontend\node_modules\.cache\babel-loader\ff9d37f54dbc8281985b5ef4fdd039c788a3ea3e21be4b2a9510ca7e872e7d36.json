{"ast": null, "code": "import { getKeyDefault } from \"../../hooks/features/dataSource/cache.js\";\nexport class TestCache {\n  constructor() {\n    this.cache = new Map();\n  }\n  set(key, value) {\n    this.cache.set(getKeyDefault(key), value);\n  }\n  get(key) {\n    return this.cache.get(getKeyDefault(key));\n  }\n  size() {\n    return this.cache.size;\n  }\n  clear() {\n    this.cache.clear();\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TestCache", "constructor", "cache", "Map", "set", "key", "value", "get", "size", "clear"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/internals/utils/cache.js"], "sourcesContent": ["import { getKeyDefault } from \"../../hooks/features/dataSource/cache.js\";\nexport class TestCache {\n  constructor() {\n    this.cache = new Map();\n  }\n  set(key, value) {\n    this.cache.set(getKeyDefault(key), value);\n  }\n  get(key) {\n    return this.cache.get(getKeyDefault(key));\n  }\n  size() {\n    return this.cache.size;\n  }\n  clear() {\n    this.cache.clear();\n  }\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,0CAA0C;AACxE,OAAO,MAAMC,SAAS,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxB;EACAC,GAAGA,CAACC,GAAG,EAAEC,KAAK,EAAE;IACd,IAAI,CAACJ,KAAK,CAACE,GAAG,CAACL,aAAa,CAACM,GAAG,CAAC,EAAEC,KAAK,CAAC;EAC3C;EACAC,GAAGA,CAACF,GAAG,EAAE;IACP,OAAO,IAAI,CAACH,KAAK,CAACK,GAAG,CAACR,aAAa,CAACM,GAAG,CAAC,CAAC;EAC3C;EACAG,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAACN,KAAK,CAACM,IAAI;EACxB;EACAC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACP,KAAK,CAACO,KAAK,CAAC,CAAC;EACpB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}