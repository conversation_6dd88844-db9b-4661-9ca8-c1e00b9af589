{"ast": null, "code": "import { trimEnd } from './trimEnd.mjs';\nimport { trimStart } from './trimStart.mjs';\nfunction trim(str, chars) {\n  if (chars === undefined) {\n    return str.trim();\n  }\n  return trimStart(trimEnd(str, chars), chars);\n}\nexport { trim };", "map": {"version": 3, "names": ["trimEnd", "trimStart", "trim", "str", "chars", "undefined"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/string/trim.mjs"], "sourcesContent": ["import { trimEnd } from './trimEnd.mjs';\nimport { trimStart } from './trimStart.mjs';\n\nfunction trim(str, chars) {\n    if (chars === undefined) {\n        return str.trim();\n    }\n    return trimStart(trimEnd(str, chars), chars);\n}\n\nexport { trim };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASC,SAAS,QAAQ,iBAAiB;AAE3C,SAASC,IAAIA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACtB,IAAIA,KAAK,KAAKC,SAAS,EAAE;IACrB,OAAOF,GAAG,CAACD,IAAI,CAAC,CAAC;EACrB;EACA,OAAOD,SAAS,CAACD,OAAO,CAACG,GAAG,EAAEC,KAAK,CAAC,EAAEA,KAAK,CAAC;AAChD;AAEA,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}