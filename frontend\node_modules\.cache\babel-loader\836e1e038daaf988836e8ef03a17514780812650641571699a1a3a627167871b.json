{"ast": null, "code": "import { isEqualWith } from './isEqualWith.mjs';\nimport { noop } from '../function/noop.mjs';\nfunction isEqual(a, b) {\n  return isEqualWith(a, b, noop);\n}\nexport { isEqual };", "map": {"version": 3, "names": ["isEqualWith", "noop", "isEqual", "a", "b"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/predicate/isEqual.mjs"], "sourcesContent": ["import { isEqualWith } from './isEqualWith.mjs';\nimport { noop } from '../function/noop.mjs';\n\nfunction isEqual(a, b) {\n    return isEqualWith(a, b, noop);\n}\n\nexport { isEqual };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,QAAQ,sBAAsB;AAE3C,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACnB,OAAOJ,WAAW,CAACG,CAAC,EAAEC,CAAC,EAAEH,IAAI,CAAC;AAClC;AAEA,SAASC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}