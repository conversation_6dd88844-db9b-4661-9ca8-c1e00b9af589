{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/useGridRegisterStrategyProcessor.js\";\nimport { useGridEvent as addEventHandler } from \"../../utils/useGridEvent.js\";\nimport { useGridDataSourceBase } from \"./useGridDataSourceBase.js\";\n/**\n * Community version of the data source hook. Contains implementation of the `useGridDataSourceBase` hook.\n */\nexport const useGridDataSource = (apiRef, props) => {\n  const {\n    api,\n    strategyProcessor,\n    events,\n    setStrategyAvailability\n  } = useGridDataSourceBase(apiRef, props);\n  useGridApiMethod(apiRef, api.public, 'public');\n  useGridRegisterStrategyProcessor(apiRef, strategyProcessor.strategyName, strategyProcessor.group, strategyProcessor.processor);\n  Object.entries(events).forEach(([event, handler]) => {\n    addEventHandler(apiRef, event, handler);\n  });\n  React.useEffect(() => {\n    setStrategyAvailability();\n  }, [setStrategyAvailability]);\n};", "map": {"version": 3, "names": ["React", "useGridApiMethod", "useGridRegisterStrategyProcessor", "useGridEvent", "addEventHandler", "useGridDataSourceBase", "useGridDataSource", "apiRef", "props", "api", "strategyProcessor", "events", "setStrategyAvailability", "public", "strategyName", "group", "processor", "Object", "entries", "for<PERSON>ach", "event", "handler", "useEffect"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/dataSource/useGridDataSource.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useGridApiMethod } from \"../../utils/useGridApiMethod.js\";\nimport { useGridRegisterStrategyProcessor } from \"../../core/strategyProcessing/useGridRegisterStrategyProcessor.js\";\nimport { useGridEvent as addEventHandler } from \"../../utils/useGridEvent.js\";\nimport { useGridDataSourceBase } from \"./useGridDataSourceBase.js\";\n/**\n * Community version of the data source hook. Contains implementation of the `useGridDataSourceBase` hook.\n */\nexport const useGridDataSource = (apiRef, props) => {\n  const {\n    api,\n    strategyProcessor,\n    events,\n    setStrategyAvailability\n  } = useGridDataSourceBase(apiRef, props);\n  useGridApiMethod(apiRef, api.public, 'public');\n  useGridRegisterStrategyProcessor(apiRef, strategyProcessor.strategyName, strategyProcessor.group, strategyProcessor.processor);\n  Object.entries(events).forEach(([event, handler]) => {\n    addEventHandler(apiRef, event, handler);\n  });\n  React.useEffect(() => {\n    setStrategyAvailability();\n  }, [setStrategyAvailability]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,gCAAgC,QAAQ,mEAAmE;AACpH,SAASC,YAAY,IAAIC,eAAe,QAAQ,6BAA6B;AAC7E,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAClD,MAAM;IACJC,GAAG;IACHC,iBAAiB;IACjBC,MAAM;IACNC;EACF,CAAC,GAAGP,qBAAqB,CAACE,MAAM,EAAEC,KAAK,CAAC;EACxCP,gBAAgB,CAACM,MAAM,EAAEE,GAAG,CAACI,MAAM,EAAE,QAAQ,CAAC;EAC9CX,gCAAgC,CAACK,MAAM,EAAEG,iBAAiB,CAACI,YAAY,EAAEJ,iBAAiB,CAACK,KAAK,EAAEL,iBAAiB,CAACM,SAAS,CAAC;EAC9HC,MAAM,CAACC,OAAO,CAACP,MAAM,CAAC,CAACQ,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,OAAO,CAAC,KAAK;IACnDjB,eAAe,CAACG,MAAM,EAAEa,KAAK,EAAEC,OAAO,CAAC;EACzC,CAAC,CAAC;EACFrB,KAAK,CAACsB,SAAS,CAAC,MAAM;IACpBV,uBAAuB,CAAC,CAAC;EAC3B,CAAC,EAAE,CAACA,uBAAuB,CAAC,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}