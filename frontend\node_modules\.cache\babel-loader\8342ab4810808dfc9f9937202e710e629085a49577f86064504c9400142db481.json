{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { getDataGridUtilityClass } from \"../../../constants/index.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_CHECKBOX_SELECTION_FIELD } from \"../../../colDef/index.js\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  return React.useMemo(() => {\n    const slots = {\n      cellCheckbox: ['cellCheckbox'],\n      columnHeaderCheckbox: ['columnHeaderCheckbox']\n    };\n    return composeClasses(slots, getDataGridUtilityClass, classes);\n  }, [classes]);\n};\nexport const useGridRowSelectionPreProcessors = (apiRef, props) => {\n  const ownerState = {\n    classes: props.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const updateSelectionColumn = React.useCallback(columnsState => {\n    const selectionColumn = _extends({}, GRID_CHECKBOX_SELECTION_COL_DEF, {\n      cellClassName: classes.cellCheckbox,\n      headerClassName: classes.columnHeaderCheckbox,\n      headerName: apiRef.current.getLocaleText('checkboxSelectionHeaderName')\n    });\n    const shouldHaveSelectionColumn = props.checkboxSelection;\n    const hasSelectionColumn = columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] != null;\n    if (shouldHaveSelectionColumn && !hasSelectionColumn) {\n      columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] = selectionColumn;\n      columnsState.orderedFields = [GRID_CHECKBOX_SELECTION_FIELD, ...columnsState.orderedFields];\n    } else if (!shouldHaveSelectionColumn && hasSelectionColumn) {\n      delete columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD];\n      columnsState.orderedFields = columnsState.orderedFields.filter(field => field !== GRID_CHECKBOX_SELECTION_FIELD);\n    } else if (shouldHaveSelectionColumn && hasSelectionColumn) {\n      columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] = _extends({}, selectionColumn, columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD]);\n      // If the column is not in the columns array (not a custom selection column), move it to the beginning of the column order\n      if (!props.columns.some(col => col.field === GRID_CHECKBOX_SELECTION_FIELD)) {\n        columnsState.orderedFields = [GRID_CHECKBOX_SELECTION_FIELD, ...columnsState.orderedFields.filter(field => field !== GRID_CHECKBOX_SELECTION_FIELD)];\n      }\n    }\n    return columnsState;\n  }, [apiRef, classes, props.columns, props.checkboxSelection]);\n  useGridRegisterPipeProcessor(apiRef, 'hydrateColumns', updateSelectionColumn);\n};", "map": {"version": 3, "names": ["_extends", "React", "composeClasses", "useGridRegisterPipeProcessor", "getDataGridUtilityClass", "GRID_CHECKBOX_SELECTION_COL_DEF", "GRID_CHECKBOX_SELECTION_FIELD", "useUtilityClasses", "ownerState", "classes", "useMemo", "slots", "cellCheckbox", "columnHeaderCheckbox", "useGridRowSelectionPreProcessors", "apiRef", "props", "updateSelectionColumn", "useCallback", "columnsState", "selectionColumn", "cellClassName", "headerClassName", "headerName", "current", "getLocaleText", "shouldHaveSelectionColumn", "checkboxSelection", "hasSelectionColumn", "lookup", "orderedFields", "filter", "field", "columns", "some", "col"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/rowSelection/useGridRowSelectionPreProcessors.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useGridRegisterPipeProcessor } from \"../../core/pipeProcessing/index.js\";\nimport { getDataGridUtilityClass } from \"../../../constants/index.js\";\nimport { GRID_CHECKBOX_SELECTION_COL_DEF, GRID_CHECKBOX_SELECTION_FIELD } from \"../../../colDef/index.js\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  return React.useMemo(() => {\n    const slots = {\n      cellCheckbox: ['cellCheckbox'],\n      columnHeaderCheckbox: ['columnHeaderCheckbox']\n    };\n    return composeClasses(slots, getDataGridUtilityClass, classes);\n  }, [classes]);\n};\nexport const useGridRowSelectionPreProcessors = (apiRef, props) => {\n  const ownerState = {\n    classes: props.classes\n  };\n  const classes = useUtilityClasses(ownerState);\n  const updateSelectionColumn = React.useCallback(columnsState => {\n    const selectionColumn = _extends({}, GRID_CHECKBOX_SELECTION_COL_DEF, {\n      cellClassName: classes.cellCheckbox,\n      headerClassName: classes.columnHeaderCheckbox,\n      headerName: apiRef.current.getLocaleText('checkboxSelectionHeaderName')\n    });\n    const shouldHaveSelectionColumn = props.checkboxSelection;\n    const hasSelectionColumn = columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] != null;\n    if (shouldHaveSelectionColumn && !hasSelectionColumn) {\n      columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] = selectionColumn;\n      columnsState.orderedFields = [GRID_CHECKBOX_SELECTION_FIELD, ...columnsState.orderedFields];\n    } else if (!shouldHaveSelectionColumn && hasSelectionColumn) {\n      delete columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD];\n      columnsState.orderedFields = columnsState.orderedFields.filter(field => field !== GRID_CHECKBOX_SELECTION_FIELD);\n    } else if (shouldHaveSelectionColumn && hasSelectionColumn) {\n      columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD] = _extends({}, selectionColumn, columnsState.lookup[GRID_CHECKBOX_SELECTION_FIELD]);\n      // If the column is not in the columns array (not a custom selection column), move it to the beginning of the column order\n      if (!props.columns.some(col => col.field === GRID_CHECKBOX_SELECTION_FIELD)) {\n        columnsState.orderedFields = [GRID_CHECKBOX_SELECTION_FIELD, ...columnsState.orderedFields.filter(field => field !== GRID_CHECKBOX_SELECTION_FIELD)];\n      }\n    }\n    return columnsState;\n  }, [apiRef, classes, props.columns, props.checkboxSelection]);\n  useGridRegisterPipeProcessor(apiRef, 'hydrateColumns', updateSelectionColumn);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,4BAA4B,QAAQ,oCAAoC;AACjF,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,+BAA+B,EAAEC,6BAA6B,QAAQ,0BAA0B;AACzG,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,OAAOP,KAAK,CAACS,OAAO,CAAC,MAAM;IACzB,MAAMC,KAAK,GAAG;MACZC,YAAY,EAAE,CAAC,cAAc,CAAC;MAC9BC,oBAAoB,EAAE,CAAC,sBAAsB;IAC/C,CAAC;IACD,OAAOX,cAAc,CAACS,KAAK,EAAEP,uBAAuB,EAAEK,OAAO,CAAC;EAChE,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;AACf,CAAC;AACD,OAAO,MAAMK,gCAAgC,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACjE,MAAMR,UAAU,GAAG;IACjBC,OAAO,EAAEO,KAAK,CAACP;EACjB,CAAC;EACD,MAAMA,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMS,qBAAqB,GAAGhB,KAAK,CAACiB,WAAW,CAACC,YAAY,IAAI;IAC9D,MAAMC,eAAe,GAAGpB,QAAQ,CAAC,CAAC,CAAC,EAAEK,+BAA+B,EAAE;MACpEgB,aAAa,EAAEZ,OAAO,CAACG,YAAY;MACnCU,eAAe,EAAEb,OAAO,CAACI,oBAAoB;MAC7CU,UAAU,EAAER,MAAM,CAACS,OAAO,CAACC,aAAa,CAAC,6BAA6B;IACxE,CAAC,CAAC;IACF,MAAMC,yBAAyB,GAAGV,KAAK,CAACW,iBAAiB;IACzD,MAAMC,kBAAkB,GAAGT,YAAY,CAACU,MAAM,CAACvB,6BAA6B,CAAC,IAAI,IAAI;IACrF,IAAIoB,yBAAyB,IAAI,CAACE,kBAAkB,EAAE;MACpDT,YAAY,CAACU,MAAM,CAACvB,6BAA6B,CAAC,GAAGc,eAAe;MACpED,YAAY,CAACW,aAAa,GAAG,CAACxB,6BAA6B,EAAE,GAAGa,YAAY,CAACW,aAAa,CAAC;IAC7F,CAAC,MAAM,IAAI,CAACJ,yBAAyB,IAAIE,kBAAkB,EAAE;MAC3D,OAAOT,YAAY,CAACU,MAAM,CAACvB,6BAA6B,CAAC;MACzDa,YAAY,CAACW,aAAa,GAAGX,YAAY,CAACW,aAAa,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK1B,6BAA6B,CAAC;IAClH,CAAC,MAAM,IAAIoB,yBAAyB,IAAIE,kBAAkB,EAAE;MAC1DT,YAAY,CAACU,MAAM,CAACvB,6BAA6B,CAAC,GAAGN,QAAQ,CAAC,CAAC,CAAC,EAAEoB,eAAe,EAAED,YAAY,CAACU,MAAM,CAACvB,6BAA6B,CAAC,CAAC;MACtI;MACA,IAAI,CAACU,KAAK,CAACiB,OAAO,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,KAAK,KAAK1B,6BAA6B,CAAC,EAAE;QAC3Ea,YAAY,CAACW,aAAa,GAAG,CAACxB,6BAA6B,EAAE,GAAGa,YAAY,CAACW,aAAa,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK1B,6BAA6B,CAAC,CAAC;MACtJ;IACF;IACA,OAAOa,YAAY;EACrB,CAAC,EAAE,CAACJ,MAAM,EAAEN,OAAO,EAAEO,KAAK,CAACiB,OAAO,EAAEjB,KAAK,CAACW,iBAAiB,CAAC,CAAC;EAC7DxB,4BAA4B,CAACY,MAAM,EAAE,gBAAgB,EAAEE,qBAAqB,CAAC;AAC/E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}