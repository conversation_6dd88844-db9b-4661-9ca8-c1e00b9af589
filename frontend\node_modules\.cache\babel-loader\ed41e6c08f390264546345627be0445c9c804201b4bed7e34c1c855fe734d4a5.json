{"ast": null, "code": "export function isNumber(value) {\n  return typeof value === 'number' && !Number.isNaN(value);\n}\nexport function isFunction(value) {\n  return typeof value === 'function';\n}\nexport function isObject(value) {\n  return typeof value === 'object' && value !== null;\n}\nexport function localStorageAvailable() {\n  try {\n    // Incognito mode might reject access to the localStorage for security reasons.\n    // window isn't defined on Node.js\n    // https://stackoverflow.com/questions/16427636/check-if-localstorage-is-available\n    const key = '__some_random_key_you_are_not_going_to_use__';\n    window.localStorage.setItem(key, key);\n    window.localStorage.removeItem(key);\n    return true;\n  } catch (err) {\n    return false;\n  }\n}\nexport function escapeRegExp(value) {\n  return value.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n}\n\n/**\n * Follows the CSS specification behavior for min and max\n * If min > max, then the min have priority\n */\nexport const clamp = (value, min, max) => Math.max(min, Math.min(max, value));\n\n/**\n * Create an array containing the range [from, to[\n */\nexport function range(from, to) {\n  return Array.from({\n    length: to - from\n  }).map((_, i) => from + i);\n}\n\n// Pseudo random number. See https://stackoverflow.com/a/47593316\nfunction mulberry32(a) {\n  return () => {\n    /* eslint-disable */\n    let t = a += 0x6d2b79f5;\n    t = Math.imul(t ^ t >>> 15, t | 1);\n    t ^= t + Math.imul(t ^ t >>> 7, t | 61);\n    return ((t ^ t >>> 14) >>> 0) / 4294967296;\n    /* eslint-enable */\n  };\n}\n\n/**\n * Create a random number generator from a seed. The seed\n * ensures that the random number generator produces the\n * same sequence of 'random' numbers on every render. It\n * returns a function that generates a random number between\n * a specified min and max.\n */\nexport function createRandomNumberGenerator(seed) {\n  const random = mulberry32(seed);\n  return (min, max) => min + (max - min) * random();\n}\nexport function deepClone(obj) {\n  if (typeof structuredClone === 'function') {\n    return structuredClone(obj);\n  }\n  return JSON.parse(JSON.stringify(obj));\n}\n\n/**\n * Mark a value as used so eslint doesn't complain. Use this instead\n * of a `eslint-disable-next-line react-hooks/exhaustive-deps` because\n * that hint disables checks on all values instead of just one.\n */\nexport function eslintUseValue(_) {}\nexport const runIf = (condition, fn) => params => {\n  if (condition) {\n    fn(params);\n  }\n};", "map": {"version": 3, "names": ["isNumber", "value", "Number", "isNaN", "isFunction", "isObject", "localStorageAvailable", "key", "window", "localStorage", "setItem", "removeItem", "err", "escapeRegExp", "replace", "clamp", "min", "max", "Math", "range", "from", "to", "Array", "length", "map", "_", "i", "mulberry32", "a", "t", "imul", "createRandomNumberGenerator", "seed", "random", "deepClone", "obj", "structuredClone", "JSON", "parse", "stringify", "eslintUseValue", "runIf", "condition", "fn", "params"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/utils.js"], "sourcesContent": ["export function isNumber(value) {\n  return typeof value === 'number' && !Number.isNaN(value);\n}\nexport function isFunction(value) {\n  return typeof value === 'function';\n}\nexport function isObject(value) {\n  return typeof value === 'object' && value !== null;\n}\nexport function localStorageAvailable() {\n  try {\n    // Incognito mode might reject access to the localStorage for security reasons.\n    // window isn't defined on Node.js\n    // https://stackoverflow.com/questions/16427636/check-if-localstorage-is-available\n    const key = '__some_random_key_you_are_not_going_to_use__';\n    window.localStorage.setItem(key, key);\n    window.localStorage.removeItem(key);\n    return true;\n  } catch (err) {\n    return false;\n  }\n}\nexport function escapeRegExp(value) {\n  return value.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n}\n\n/**\n * Follows the CSS specification behavior for min and max\n * If min > max, then the min have priority\n */\nexport const clamp = (value, min, max) => Math.max(min, Math.min(max, value));\n\n/**\n * Create an array containing the range [from, to[\n */\nexport function range(from, to) {\n  return Array.from({\n    length: to - from\n  }).map((_, i) => from + i);\n}\n\n// Pseudo random number. See https://stackoverflow.com/a/47593316\nfunction mulberry32(a) {\n  return () => {\n    /* eslint-disable */\n    let t = a += 0x6d2b79f5;\n    t = Math.imul(t ^ t >>> 15, t | 1);\n    t ^= t + Math.imul(t ^ t >>> 7, t | 61);\n    return ((t ^ t >>> 14) >>> 0) / 4294967296;\n    /* eslint-enable */\n  };\n}\n\n/**\n * Create a random number generator from a seed. The seed\n * ensures that the random number generator produces the\n * same sequence of 'random' numbers on every render. It\n * returns a function that generates a random number between\n * a specified min and max.\n */\nexport function createRandomNumberGenerator(seed) {\n  const random = mulberry32(seed);\n  return (min, max) => min + (max - min) * random();\n}\nexport function deepClone(obj) {\n  if (typeof structuredClone === 'function') {\n    return structuredClone(obj);\n  }\n  return JSON.parse(JSON.stringify(obj));\n}\n\n/**\n * Mark a value as used so eslint doesn't complain. Use this instead\n * of a `eslint-disable-next-line react-hooks/exhaustive-deps` because\n * that hint disables checks on all values instead of just one.\n */\nexport function eslintUseValue(_) {}\nexport const runIf = (condition, fn) => params => {\n  if (condition) {\n    fn(params);\n  }\n};"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1D;AACA,OAAO,SAASG,UAAUA,CAACH,KAAK,EAAE;EAChC,OAAO,OAAOA,KAAK,KAAK,UAAU;AACpC;AACA,OAAO,SAASI,QAAQA,CAACJ,KAAK,EAAE;EAC9B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI;AACpD;AACA,OAAO,SAASK,qBAAqBA,CAAA,EAAG;EACtC,IAAI;IACF;IACA;IACA;IACA,MAAMC,GAAG,GAAG,8CAA8C;IAC1DC,MAAM,CAACC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAEA,GAAG,CAAC;IACrCC,MAAM,CAACC,YAAY,CAACE,UAAU,CAACJ,GAAG,CAAC;IACnC,OAAO,IAAI;EACb,CAAC,CAAC,OAAOK,GAAG,EAAE;IACZ,OAAO,KAAK;EACd;AACF;AACA,OAAO,SAASC,YAAYA,CAACZ,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACa,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,KAAK,GAAGA,CAACd,KAAK,EAAEe,GAAG,EAAEC,GAAG,KAAKC,IAAI,CAACD,GAAG,CAACD,GAAG,EAAEE,IAAI,CAACF,GAAG,CAACC,GAAG,EAAEhB,KAAK,CAAC,CAAC;;AAE7E;AACA;AACA;AACA,OAAO,SAASkB,KAAKA,CAACC,IAAI,EAAEC,EAAE,EAAE;EAC9B,OAAOC,KAAK,CAACF,IAAI,CAAC;IAChBG,MAAM,EAAEF,EAAE,GAAGD;EACf,CAAC,CAAC,CAACI,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKN,IAAI,GAAGM,CAAC,CAAC;AAC5B;;AAEA;AACA,SAASC,UAAUA,CAACC,CAAC,EAAE;EACrB,OAAO,MAAM;IACX;IACA,IAAIC,CAAC,GAAGD,CAAC,IAAI,UAAU;IACvBC,CAAC,GAAGX,IAAI,CAACY,IAAI,CAACD,CAAC,GAAGA,CAAC,KAAK,EAAE,EAAEA,CAAC,GAAG,CAAC,CAAC;IAClCA,CAAC,IAAIA,CAAC,GAAGX,IAAI,CAACY,IAAI,CAACD,CAAC,GAAGA,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC;IACvC,OAAO,CAAC,CAACA,CAAC,GAAGA,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,UAAU;IAC1C;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,2BAA2BA,CAACC,IAAI,EAAE;EAChD,MAAMC,MAAM,GAAGN,UAAU,CAACK,IAAI,CAAC;EAC/B,OAAO,CAAChB,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAG,CAACC,GAAG,GAAGD,GAAG,IAAIiB,MAAM,CAAC,CAAC;AACnD;AACA,OAAO,SAASC,SAASA,CAACC,GAAG,EAAE;EAC7B,IAAI,OAAOC,eAAe,KAAK,UAAU,EAAE;IACzC,OAAOA,eAAe,CAACD,GAAG,CAAC;EAC7B;EACA,OAAOE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,GAAG,CAAC,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,cAAcA,CAACf,CAAC,EAAE,CAAC;AACnC,OAAO,MAAMgB,KAAK,GAAGA,CAACC,SAAS,EAAEC,EAAE,KAAKC,MAAM,IAAI;EAChD,IAAIF,SAAS,EAAE;IACbC,EAAE,CAACC,MAAM,CAAC;EACZ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}