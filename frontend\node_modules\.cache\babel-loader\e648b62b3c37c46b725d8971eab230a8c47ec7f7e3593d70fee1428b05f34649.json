{"ast": null, "code": "function findKey(obj, predicate) {\n  const keys = Object.keys(obj);\n  return keys.find(key => predicate(obj[key], key, obj));\n}\nexport { findKey };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "obj", "predicate", "keys", "Object", "find", "key"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/object/findKey.mjs"], "sourcesContent": ["function findKey(obj, predicate) {\n    const keys = Object.keys(obj);\n    return keys.find(key => predicate(obj[key], key, obj));\n}\n\nexport { findKey };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,GAAG,EAAEC,SAAS,EAAE;EAC7B,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,GAAG,CAAC;EAC7B,OAAOE,IAAI,CAACE,IAAI,CAACC,GAAG,IAAIJ,SAAS,CAACD,GAAG,CAACK,GAAG,CAAC,EAAEA,GAAG,EAAEL,GAAG,CAAC,CAAC;AAC1D;AAEA,SAASD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}