{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridFooterPlaceholder() {\n  const rootProps = useGridRootProps();\n  if (rootProps.hideFooter) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.footer, _extends({}, rootProps.slotProps?.footer /* FIXME: typing error */));\n}", "map": {"version": 3, "names": ["_extends", "React", "useGridRootProps", "jsx", "_jsx", "GridFooterPlaceholder", "rootProps", "hideFooter", "slots", "footer", "slotProps"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/base/GridFooterPlaceholder.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useGridRootProps } from \"../../hooks/utils/useGridRootProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function GridFooterPlaceholder() {\n  const rootProps = useGridRootProps();\n  if (rootProps.hideFooter) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(rootProps.slots.footer, _extends({}, rootProps.slotProps?.footer /* FIXME: typing error */));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,qBAAqBA,CAAA,EAAG;EACtC,MAAMC,SAAS,GAAGJ,gBAAgB,CAAC,CAAC;EACpC,IAAII,SAAS,CAACC,UAAU,EAAE;IACxB,OAAO,IAAI;EACb;EACA,OAAO,aAAaH,IAAI,CAACE,SAAS,CAACE,KAAK,CAACC,MAAM,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEM,SAAS,CAACI,SAAS,EAAED,MAAM,CAAC,yBAAyB,CAAC,CAAC;AACvH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}