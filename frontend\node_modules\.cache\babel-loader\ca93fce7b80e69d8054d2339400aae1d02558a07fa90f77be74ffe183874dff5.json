{"ast": null, "code": "import * as React from 'react';\nexport function useGridLogger(privateApiRef, name) {\n  const logger = React.useRef(null);\n  if (logger.current) {\n    return logger.current;\n  }\n  const newLogger = privateApiRef.current.getLogger(name);\n  logger.current = newLogger;\n  return newLogger;\n}", "map": {"version": 3, "names": ["React", "useGridLogger", "privateApiRef", "name", "logger", "useRef", "current", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/utils/useGridLogger.js"], "sourcesContent": ["import * as React from 'react';\nexport function useGridLogger(privateApiRef, name) {\n  const logger = React.useRef(null);\n  if (logger.current) {\n    return logger.current;\n  }\n  const newLogger = privateApiRef.current.getLogger(name);\n  logger.current = newLogger;\n  return newLogger;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,aAAaA,CAACC,aAAa,EAAEC,IAAI,EAAE;EACjD,MAAMC,MAAM,GAAGJ,KAAK,CAACK,MAAM,CAAC,IAAI,CAAC;EACjC,IAAID,MAAM,CAACE,OAAO,EAAE;IAClB,OAAOF,MAAM,CAACE,OAAO;EACvB;EACA,MAAMC,SAAS,GAAGL,aAAa,CAACI,OAAO,CAACE,SAAS,CAACL,IAAI,CAAC;EACvDC,MAAM,CAACE,OAAO,GAAGC,SAAS;EAC1B,OAAOA,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}