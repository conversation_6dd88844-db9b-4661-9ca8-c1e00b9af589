{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"item\", \"applyValue\", \"type\", \"apiRef\", \"focusElementRef\", \"tabIndex\", \"isFilterActive\", \"clearButton\", \"headerFilterMenu\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { getValueFromValueOptions, getValueOptions, isSingleSelectColDef } from \"./filterPanelUtils.js\";\nimport { createElement as _createElement } from \"react\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst renderSingleSelectOptions = ({\n  column,\n  OptionComponent,\n  getOptionLabel,\n  getOptionValue,\n  isSelectNative,\n  baseSelectOptionProps\n}) => {\n  const iterableColumnValues = ['', ...(getValueOptions(column) || [])];\n  return iterableColumnValues.map(option => {\n    const value = getOptionValue(option);\n    let label = getOptionLabel(option);\n    if (label === '') {\n      label = ' '; // To force the height of the empty option\n    }\n    return /*#__PURE__*/_createElement(OptionComponent, _extends({}, baseSelectOptionProps, {\n      native: isSelectNative,\n      key: value,\n      value: value\n    }), label);\n  });\n};\nfunction GridFilterInputSingleSelect(props) {\n  const {\n      item,\n      applyValue,\n      type,\n      apiRef,\n      focusElementRef,\n      tabIndex,\n      clearButton,\n      headerFilterMenu,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const filterValue = item.value ?? '';\n  const id = useId();\n  const labelId = useId();\n  const rootProps = useGridRootProps();\n  const isSelectNative = rootProps.slotProps?.baseSelect?.native ?? false;\n  const resolvedColumn = apiRef.current.getColumn(item.field);\n  const getOptionValue = resolvedColumn.getOptionValue;\n  const getOptionLabel = resolvedColumn.getOptionLabel;\n  const currentValueOptions = React.useMemo(() => {\n    return getValueOptions(resolvedColumn);\n  }, [resolvedColumn]);\n  const onFilterChange = React.useCallback(event => {\n    let value = event.target.value;\n\n    // NativeSelect casts the value to a string.\n    value = getValueFromValueOptions(value, currentValueOptions, getOptionValue);\n    applyValue(_extends({}, item, {\n      value\n    }));\n  }, [currentValueOptions, getOptionValue, applyValue, item]);\n  if (!resolvedColumn || !isSingleSelectColDef(resolvedColumn)) {\n    return null;\n  }\n  const label = slotProps?.root.label ?? apiRef.current.getLocaleText('filterPanelInputLabel');\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(rootProps.slots.baseSelect, _extends({\n      fullWidth: true,\n      id: id,\n      label: label,\n      labelId: labelId,\n      value: filterValue,\n      onChange: onFilterChange,\n      slotProps: {\n        htmlInput: _extends({\n          tabIndex,\n          ref: focusElementRef,\n          type: type || 'text',\n          placeholder: slotProps?.root.placeholder ?? apiRef.current.getLocaleText('filterPanelInputPlaceholder')\n        }, slotProps?.root.slotProps?.htmlInput)\n      },\n      native: isSelectNative\n    }, rootProps.slotProps?.baseSelect, other, slotProps?.root, {\n      children: renderSingleSelectOptions({\n        column: resolvedColumn,\n        OptionComponent: rootProps.slots.baseSelectOption,\n        getOptionLabel,\n        getOptionValue,\n        isSelectNative,\n        baseSelectOptionProps: rootProps.slotProps?.baseSelectOption\n      })\n    })), headerFilterMenu, clearButton]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridFilterInputSingleSelect.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object.isRequired\n  }).isRequired,\n  applyValue: PropTypes.func.isRequired,\n  className: PropTypes.string,\n  clearButton: PropTypes.node,\n  disabled: PropTypes.bool,\n  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),\n  headerFilterMenu: PropTypes.node,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: (props, propName) => {\n      if (props[propName] == null) {\n        return null;\n      }\n      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n        return new Error(`Expected prop '${propName}' to be of type Element`);\n      }\n      return null;\n    }\n  })]),\n  /**\n   * It is `true` if the filter either has a value or an operator with no value\n   * required is selected (for example `isEmpty`)\n   */\n  isFilterActive: PropTypes.bool,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  onBlur: PropTypes.func,\n  onFocus: PropTypes.func,\n  slotProps: PropTypes.object,\n  tabIndex: PropTypes.number,\n  type: PropTypes.oneOf(['singleSelect'])\n} : void 0;\nexport { GridFilterInputSingleSelect };", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "useId", "useGridRootProps", "getValueFromValueOptions", "getValueOptions", "isSingleSelectColDef", "createElement", "_createElement", "jsx", "_jsx", "jsxs", "_jsxs", "renderSingleSelectOptions", "column", "OptionComponent", "getOptionLabel", "getOptionValue", "isSelectNative", "baseSelectOptionProps", "iterableColumnValues", "map", "option", "value", "label", "native", "key", "GridFilterInputSingleSelect", "props", "item", "applyValue", "type", "apiRef", "focusElementRef", "tabIndex", "clearButton", "headerFilterMenu", "slotProps", "other", "filterValue", "id", "labelId", "rootProps", "baseSelect", "resolvedColumn", "current", "getColumn", "field", "currentValueOptions", "useMemo", "onFilterChange", "useCallback", "event", "target", "root", "getLocaleText", "Fragment", "children", "slots", "fullWidth", "onChange", "htmlInput", "ref", "placeholder", "baseSelectOption", "process", "env", "NODE_ENV", "propTypes", "shape", "object", "isRequired", "func", "className", "string", "node", "disabled", "bool", "oneOfType", "inputRef", "propName", "nodeType", "Error", "isFilterActive", "number", "operator", "any", "onBlur", "onFocus", "oneOf"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/panel/filterPanel/GridFilterInputSingleSelect.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"item\", \"applyValue\", \"type\", \"apiRef\", \"focusElementRef\", \"tabIndex\", \"isFilterActive\", \"clearButton\", \"headerFilterMenu\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useId from '@mui/utils/useId';\nimport { useGridRootProps } from \"../../../hooks/utils/useGridRootProps.js\";\nimport { getValueFromValueOptions, getValueOptions, isSingleSelectColDef } from \"./filterPanelUtils.js\";\nimport { createElement as _createElement } from \"react\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst renderSingleSelectOptions = ({\n  column,\n  OptionComponent,\n  getOptionLabel,\n  getOptionValue,\n  isSelectNative,\n  baseSelectOptionProps\n}) => {\n  const iterableColumnValues = ['', ...(getValueOptions(column) || [])];\n  return iterableColumnValues.map(option => {\n    const value = getOptionValue(option);\n    let label = getOptionLabel(option);\n    if (label === '') {\n      label = ' '; // To force the height of the empty option\n    }\n    return /*#__PURE__*/_createElement(OptionComponent, _extends({}, baseSelectOptionProps, {\n      native: isSelectNative,\n      key: value,\n      value: value\n    }), label);\n  });\n};\nfunction GridFilterInputSingleSelect(props) {\n  const {\n      item,\n      applyValue,\n      type,\n      apiRef,\n      focusElementRef,\n      tabIndex,\n      clearButton,\n      headerFilterMenu,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const filterValue = item.value ?? '';\n  const id = useId();\n  const labelId = useId();\n  const rootProps = useGridRootProps();\n  const isSelectNative = rootProps.slotProps?.baseSelect?.native ?? false;\n  const resolvedColumn = apiRef.current.getColumn(item.field);\n  const getOptionValue = resolvedColumn.getOptionValue;\n  const getOptionLabel = resolvedColumn.getOptionLabel;\n  const currentValueOptions = React.useMemo(() => {\n    return getValueOptions(resolvedColumn);\n  }, [resolvedColumn]);\n  const onFilterChange = React.useCallback(event => {\n    let value = event.target.value;\n\n    // NativeSelect casts the value to a string.\n    value = getValueFromValueOptions(value, currentValueOptions, getOptionValue);\n    applyValue(_extends({}, item, {\n      value\n    }));\n  }, [currentValueOptions, getOptionValue, applyValue, item]);\n  if (!resolvedColumn || !isSingleSelectColDef(resolvedColumn)) {\n    return null;\n  }\n  const label = slotProps?.root.label ?? apiRef.current.getLocaleText('filterPanelInputLabel');\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(rootProps.slots.baseSelect, _extends({\n      fullWidth: true,\n      id: id,\n      label: label,\n      labelId: labelId,\n      value: filterValue,\n      onChange: onFilterChange,\n      slotProps: {\n        htmlInput: _extends({\n          tabIndex,\n          ref: focusElementRef,\n          type: type || 'text',\n          placeholder: slotProps?.root.placeholder ?? apiRef.current.getLocaleText('filterPanelInputPlaceholder')\n        }, slotProps?.root.slotProps?.htmlInput)\n      },\n      native: isSelectNative\n    }, rootProps.slotProps?.baseSelect, other, slotProps?.root, {\n      children: renderSingleSelectOptions({\n        column: resolvedColumn,\n        OptionComponent: rootProps.slots.baseSelectOption,\n        getOptionLabel,\n        getOptionValue,\n        isSelectNative,\n        baseSelectOptionProps: rootProps.slotProps?.baseSelectOption\n      })\n    })), headerFilterMenu, clearButton]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GridFilterInputSingleSelect.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object.isRequired\n  }).isRequired,\n  applyValue: PropTypes.func.isRequired,\n  className: PropTypes.string,\n  clearButton: PropTypes.node,\n  disabled: PropTypes.bool,\n  focusElementRef: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.func, PropTypes.object]),\n  headerFilterMenu: PropTypes.node,\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: (props, propName) => {\n      if (props[propName] == null) {\n        return null;\n      }\n      if (typeof props[propName] !== 'object' || props[propName].nodeType !== 1) {\n        return new Error(`Expected prop '${propName}' to be of type Element`);\n      }\n      return null;\n    }\n  })]),\n  /**\n   * It is `true` if the filter either has a value or an operator with no value\n   * required is selected (for example `isEmpty`)\n   */\n  isFilterActive: PropTypes.bool,\n  item: PropTypes.shape({\n    field: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    operator: PropTypes.string.isRequired,\n    value: PropTypes.any\n  }).isRequired,\n  onBlur: PropTypes.func,\n  onFocus: PropTypes.func,\n  slotProps: PropTypes.object,\n  tabIndex: PropTypes.number,\n  type: PropTypes.oneOf(['singleSelect'])\n} : void 0;\nexport { GridFilterInputSingleSelect };"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,kBAAkB,EAAE,WAAW,CAAC;AAC3J,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,wBAAwB,EAAEC,eAAe,EAAEC,oBAAoB,QAAQ,uBAAuB;AACvG,SAASC,aAAa,IAAIC,cAAc,QAAQ,OAAO;AACvD,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,yBAAyB,GAAGA,CAAC;EACjCC,MAAM;EACNC,eAAe;EACfC,cAAc;EACdC,cAAc;EACdC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,MAAMC,oBAAoB,GAAG,CAAC,EAAE,EAAE,IAAIf,eAAe,CAACS,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;EACrE,OAAOM,oBAAoB,CAACC,GAAG,CAACC,MAAM,IAAI;IACxC,MAAMC,KAAK,GAAGN,cAAc,CAACK,MAAM,CAAC;IACpC,IAAIE,KAAK,GAAGR,cAAc,CAACM,MAAM,CAAC;IAClC,IAAIE,KAAK,KAAK,EAAE,EAAE;MAChBA,KAAK,GAAG,GAAG,CAAC,CAAC;IACf;IACA,OAAO,aAAahB,cAAc,CAACO,eAAe,EAAEjB,QAAQ,CAAC,CAAC,CAAC,EAAEqB,qBAAqB,EAAE;MACtFM,MAAM,EAAEP,cAAc;MACtBQ,GAAG,EAAEH,KAAK;MACVA,KAAK,EAAEA;IACT,CAAC,CAAC,EAAEC,KAAK,CAAC;EACZ,CAAC,CAAC;AACJ,CAAC;AACD,SAASG,2BAA2BA,CAACC,KAAK,EAAE;EAC1C,MAAM;MACFC,IAAI;MACJC,UAAU;MACVC,IAAI;MACJC,MAAM;MACNC,eAAe;MACfC,QAAQ;MACRC,WAAW;MACXC,gBAAgB;MAChBC;IACF,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAGzC,6BAA6B,CAAC+B,KAAK,EAAE7B,SAAS,CAAC;EACzD,MAAMwC,WAAW,GAAGV,IAAI,CAACN,KAAK,IAAI,EAAE;EACpC,MAAMiB,EAAE,GAAGtC,KAAK,CAAC,CAAC;EAClB,MAAMuC,OAAO,GAAGvC,KAAK,CAAC,CAAC;EACvB,MAAMwC,SAAS,GAAGvC,gBAAgB,CAAC,CAAC;EACpC,MAAMe,cAAc,GAAGwB,SAAS,CAACL,SAAS,EAAEM,UAAU,EAAElB,MAAM,IAAI,KAAK;EACvE,MAAMmB,cAAc,GAAGZ,MAAM,CAACa,OAAO,CAACC,SAAS,CAACjB,IAAI,CAACkB,KAAK,CAAC;EAC3D,MAAM9B,cAAc,GAAG2B,cAAc,CAAC3B,cAAc;EACpD,MAAMD,cAAc,GAAG4B,cAAc,CAAC5B,cAAc;EACpD,MAAMgC,mBAAmB,GAAGhD,KAAK,CAACiD,OAAO,CAAC,MAAM;IAC9C,OAAO5C,eAAe,CAACuC,cAAc,CAAC;EACxC,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACpB,MAAMM,cAAc,GAAGlD,KAAK,CAACmD,WAAW,CAACC,KAAK,IAAI;IAChD,IAAI7B,KAAK,GAAG6B,KAAK,CAACC,MAAM,CAAC9B,KAAK;;IAE9B;IACAA,KAAK,GAAGnB,wBAAwB,CAACmB,KAAK,EAAEyB,mBAAmB,EAAE/B,cAAc,CAAC;IAC5Ea,UAAU,CAAChC,QAAQ,CAAC,CAAC,CAAC,EAAE+B,IAAI,EAAE;MAC5BN;IACF,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACyB,mBAAmB,EAAE/B,cAAc,EAAEa,UAAU,EAAED,IAAI,CAAC,CAAC;EAC3D,IAAI,CAACe,cAAc,IAAI,CAACtC,oBAAoB,CAACsC,cAAc,CAAC,EAAE;IAC5D,OAAO,IAAI;EACb;EACA,MAAMpB,KAAK,GAAGa,SAAS,EAAEiB,IAAI,CAAC9B,KAAK,IAAIQ,MAAM,CAACa,OAAO,CAACU,aAAa,CAAC,uBAAuB,CAAC;EAC5F,OAAO,aAAa3C,KAAK,CAACZ,KAAK,CAACwD,QAAQ,EAAE;IACxCC,QAAQ,EAAE,CAAC,aAAa/C,IAAI,CAACgC,SAAS,CAACgB,KAAK,CAACf,UAAU,EAAE7C,QAAQ,CAAC;MAChE6D,SAAS,EAAE,IAAI;MACfnB,EAAE,EAAEA,EAAE;MACNhB,KAAK,EAAEA,KAAK;MACZiB,OAAO,EAAEA,OAAO;MAChBlB,KAAK,EAAEgB,WAAW;MAClBqB,QAAQ,EAAEV,cAAc;MACxBb,SAAS,EAAE;QACTwB,SAAS,EAAE/D,QAAQ,CAAC;UAClBoC,QAAQ;UACR4B,GAAG,EAAE7B,eAAe;UACpBF,IAAI,EAAEA,IAAI,IAAI,MAAM;UACpBgC,WAAW,EAAE1B,SAAS,EAAEiB,IAAI,CAACS,WAAW,IAAI/B,MAAM,CAACa,OAAO,CAACU,aAAa,CAAC,6BAA6B;QACxG,CAAC,EAAElB,SAAS,EAAEiB,IAAI,CAACjB,SAAS,EAAEwB,SAAS;MACzC,CAAC;MACDpC,MAAM,EAAEP;IACV,CAAC,EAAEwB,SAAS,CAACL,SAAS,EAAEM,UAAU,EAAEL,KAAK,EAAED,SAAS,EAAEiB,IAAI,EAAE;MAC1DG,QAAQ,EAAE5C,yBAAyB,CAAC;QAClCC,MAAM,EAAE8B,cAAc;QACtB7B,eAAe,EAAE2B,SAAS,CAACgB,KAAK,CAACM,gBAAgB;QACjDhD,cAAc;QACdC,cAAc;QACdC,cAAc;QACdC,qBAAqB,EAAEuB,SAAS,CAACL,SAAS,EAAE2B;MAC9C,CAAC;IACH,CAAC,CAAC,CAAC,EAAE5B,gBAAgB,EAAED,WAAW;EACpC,CAAC,CAAC;AACJ;AACA8B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,2BAA2B,CAACyC,SAAS,GAAG;EAC9E;EACA;EACA;EACA;EACApC,MAAM,EAAE/B,SAAS,CAACoE,KAAK,CAAC;IACtBxB,OAAO,EAAE5C,SAAS,CAACqE,MAAM,CAACC;EAC5B,CAAC,CAAC,CAACA,UAAU;EACbzC,UAAU,EAAE7B,SAAS,CAACuE,IAAI,CAACD,UAAU;EACrCE,SAAS,EAAExE,SAAS,CAACyE,MAAM;EAC3BvC,WAAW,EAAElC,SAAS,CAAC0E,IAAI;EAC3BC,QAAQ,EAAE3E,SAAS,CAAC4E,IAAI;EACxB5C,eAAe,EAAEhC,SAAS,CAAC,sCAAsC6E,SAAS,CAAC,CAAC7E,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACqE,MAAM,CAAC,CAAC;EAC9GlC,gBAAgB,EAAEnC,SAAS,CAAC0E,IAAI;EAChCI,QAAQ,EAAE9E,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACoE,KAAK,CAAC;IAC7DxB,OAAO,EAAEA,CAACjB,KAAK,EAAEoD,QAAQ,KAAK;MAC5B,IAAIpD,KAAK,CAACoD,QAAQ,CAAC,IAAI,IAAI,EAAE;QAC3B,OAAO,IAAI;MACb;MACA,IAAI,OAAOpD,KAAK,CAACoD,QAAQ,CAAC,KAAK,QAAQ,IAAIpD,KAAK,CAACoD,QAAQ,CAAC,CAACC,QAAQ,KAAK,CAAC,EAAE;QACzE,OAAO,IAAIC,KAAK,CAAC,kBAAkBF,QAAQ,yBAAyB,CAAC;MACvE;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;EACEG,cAAc,EAAElF,SAAS,CAAC4E,IAAI;EAC9BhD,IAAI,EAAE5B,SAAS,CAACoE,KAAK,CAAC;IACpBtB,KAAK,EAAE9C,SAAS,CAACyE,MAAM,CAACH,UAAU;IAClC/B,EAAE,EAAEvC,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAACmF,MAAM,EAAEnF,SAAS,CAACyE,MAAM,CAAC,CAAC;IAC7DW,QAAQ,EAAEpF,SAAS,CAACyE,MAAM,CAACH,UAAU;IACrChD,KAAK,EAAEtB,SAAS,CAACqF;EACnB,CAAC,CAAC,CAACf,UAAU;EACbgB,MAAM,EAAEtF,SAAS,CAACuE,IAAI;EACtBgB,OAAO,EAAEvF,SAAS,CAACuE,IAAI;EACvBnC,SAAS,EAAEpC,SAAS,CAACqE,MAAM;EAC3BpC,QAAQ,EAAEjC,SAAS,CAACmF,MAAM;EAC1BrD,IAAI,EAAE9B,SAAS,CAACwF,KAAK,CAAC,CAAC,cAAc,CAAC;AACxC,CAAC,GAAG,KAAK,CAAC;AACV,SAAS9D,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}