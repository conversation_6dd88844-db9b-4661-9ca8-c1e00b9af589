{"ast": null, "code": "function pull(arr, valuesToRemove) {\n  const valuesSet = new Set(valuesToRemove);\n  let resultIndex = 0;\n  for (let i = 0; i < arr.length; i++) {\n    if (valuesSet.has(arr[i])) {\n      continue;\n    }\n    if (!Object.hasOwn(arr, i)) {\n      delete arr[resultIndex++];\n      continue;\n    }\n    arr[resultIndex++] = arr[i];\n  }\n  arr.length = resultIndex;\n  return arr;\n}\nexport { pull };", "map": {"version": 3, "names": ["pull", "arr", "valuesToRemove", "valuesSet", "Set", "resultIndex", "i", "length", "has", "Object", "hasOwn"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/array/pull.mjs"], "sourcesContent": ["function pull(arr, valuesToRemove) {\n    const valuesSet = new Set(valuesToRemove);\n    let resultIndex = 0;\n    for (let i = 0; i < arr.length; i++) {\n        if (valuesSet.has(arr[i])) {\n            continue;\n        }\n        if (!Object.hasOwn(arr, i)) {\n            delete arr[resultIndex++];\n            continue;\n        }\n        arr[resultIndex++] = arr[i];\n    }\n    arr.length = resultIndex;\n    return arr;\n}\n\nexport { pull };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,GAAG,EAAEC,cAAc,EAAE;EAC/B,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAACF,cAAc,CAAC;EACzC,IAAIG,WAAW,GAAG,CAAC;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,IAAIH,SAAS,CAACK,GAAG,CAACP,GAAG,CAACK,CAAC,CAAC,CAAC,EAAE;MACvB;IACJ;IACA,IAAI,CAACG,MAAM,CAACC,MAAM,CAACT,GAAG,EAAEK,CAAC,CAAC,EAAE;MACxB,OAAOL,GAAG,CAACI,WAAW,EAAE,CAAC;MACzB;IACJ;IACAJ,GAAG,CAACI,WAAW,EAAE,CAAC,GAAGJ,GAAG,CAACK,CAAC,CAAC;EAC/B;EACAL,GAAG,CAACM,MAAM,GAAGF,WAAW;EACxB,OAAOJ,GAAG;AACd;AAEA,SAASD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}