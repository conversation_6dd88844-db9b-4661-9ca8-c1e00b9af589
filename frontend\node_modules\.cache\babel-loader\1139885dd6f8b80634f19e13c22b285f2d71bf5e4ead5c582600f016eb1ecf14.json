{"ast": null, "code": "import { PinnedColumnPosition } from \"../constants.js\";\nexport const getPinnedCellOffset = (pinnedPosition, computedWidth, columnIndex, columnPositions, columnsTotalWidth, scrollbarWidth) => {\n  let pinnedOffset;\n  switch (pinnedPosition) {\n    case PinnedColumnPosition.LEFT:\n      pinnedOffset = columnPositions[columnIndex];\n      break;\n    case PinnedColumnPosition.RIGHT:\n      pinnedOffset = columnsTotalWidth - columnPositions[columnIndex] - computedWidth + scrollbarWidth;\n      break;\n    default:\n      pinnedOffset = undefined;\n      break;\n  }\n\n  // XXX: fix this properly\n  if (Number.isNaN(pinnedOffset)) {\n    pinnedOffset = undefined;\n  }\n  return pinnedOffset;\n};", "map": {"version": 3, "names": ["PinnedColumnPosition", "getPinnedCellOffset", "pinnedPosition", "computedWidth", "columnIndex", "columnPositions", "columnsTotalWidth", "scrollbarWidth", "pinnedOffset", "LEFT", "RIGHT", "undefined", "Number", "isNaN"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/internals/utils/getPinnedCellOffset.js"], "sourcesContent": ["import { PinnedColumnPosition } from \"../constants.js\";\nexport const getPinnedCellOffset = (pinnedPosition, computedWidth, columnIndex, columnPositions, columnsTotalWidth, scrollbarWidth) => {\n  let pinnedOffset;\n  switch (pinnedPosition) {\n    case PinnedColumnPosition.LEFT:\n      pinnedOffset = columnPositions[columnIndex];\n      break;\n    case PinnedColumnPosition.RIGHT:\n      pinnedOffset = columnsTotalWidth - columnPositions[columnIndex] - computedWidth + scrollbarWidth;\n      break;\n    default:\n      pinnedOffset = undefined;\n      break;\n  }\n\n  // XXX: fix this properly\n  if (Number.isNaN(pinnedOffset)) {\n    pinnedOffset = undefined;\n  }\n  return pinnedOffset;\n};"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,iBAAiB;AACtD,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,cAAc,EAAEC,aAAa,EAAEC,WAAW,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,cAAc,KAAK;EACrI,IAAIC,YAAY;EAChB,QAAQN,cAAc;IACpB,KAAKF,oBAAoB,CAACS,IAAI;MAC5BD,YAAY,GAAGH,eAAe,CAACD,WAAW,CAAC;MAC3C;IACF,KAAKJ,oBAAoB,CAACU,KAAK;MAC7BF,YAAY,GAAGF,iBAAiB,GAAGD,eAAe,CAACD,WAAW,CAAC,GAAGD,aAAa,GAAGI,cAAc;MAChG;IACF;MACEC,YAAY,GAAGG,SAAS;MACxB;EACJ;;EAEA;EACA,IAAIC,MAAM,CAACC,KAAK,CAACL,YAAY,CAAC,EAAE;IAC9BA,YAAY,GAAGG,SAAS;EAC1B;EACA,OAAOH,YAAY;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}