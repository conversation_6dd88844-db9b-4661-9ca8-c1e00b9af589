{"ast": null, "code": "import { createSelector, createSelectorMemoized, createRootSelector } from \"../../../utils/createSelector.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"./gridColumnsInterfaces.js\";\nimport { gridIsRtlSelector } from \"../../core/gridCoreSelector.js\";\nimport { gridListColumnSelector, gridListViewSelector } from \"../listView/index.js\";\n\n/**\n * Get the columns state\n * @category Columns\n */\nexport const gridColumnsStateSelector = createRootSelector(state => state.columns);\n\n/**\n * Get an array of column fields in the order rendered on screen.\n * @category Columns\n */\nexport const gridColumnFieldsSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.orderedFields);\n\n/**\n * Get the columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridColumnLookupSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.lookup);\n\n/**\n * Get an array of column definitions in the order rendered on screen..\n * @category Columns\n */\nexport const gridColumnDefinitionsSelector = createSelectorMemoized(gridColumnFieldsSelector, gridColumnLookupSelector, (allFields, lookup) => allFields.map(field => lookup[field]));\n\n/**\n * Get the column visibility model, containing the visibility status of each column.\n * If a column is not registered in the model, it is visible.\n * @category Visible Columns\n */\nexport const gridColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.columnVisibilityModel);\n\n/**\n * Get the \"initial\" column visibility model, containing the visibility status of each column.\n * It is updated when the `columns` prop is updated or when `updateColumns` API method is called.\n * If a column is not registered in the model, it is visible.\n * @category Visible Columns\n */\nexport const gridInitialColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.initialColumnVisibilityModel);\n\n/**\n * Get the visible columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Visible Columns\n */\nexport const gridVisibleColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector, gridListViewSelector, gridListColumnSelector, (columns, columnVisibilityModel, listView, listColumn) => listView && listColumn ? [listColumn] : columns.filter(column => columnVisibilityModel[column.field] !== false));\n\n/**\n * Get the field of each visible column.\n * @category Visible Columns\n */\nexport const gridVisibleColumnFieldsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => visibleColumns.map(column => column.field));\n\n/**\n * Get the visible pinned columns model.\n * @category Visible Columns\n */\nexport const gridPinnedColumnsSelector = createRootSelector(state => state.pinnedColumns);\n\n/**\n * Get all existing pinned columns. Place the columns on the side that depends on the rtl state.\n * @category Pinned Columns\n * @ignore - Do not document\n */\nexport const gridExistingPinnedColumnSelector = createSelectorMemoized(gridPinnedColumnsSelector, gridColumnFieldsSelector, gridIsRtlSelector, (model, orderedFields, isRtl) => filterMissingColumns(model, orderedFields, isRtl));\n\n/**\n * Get the visible pinned columns.\n * @category Visible Columns\n */\nexport const gridVisiblePinnedColumnDefinitionsSelector = createSelectorMemoized(gridColumnsStateSelector, gridPinnedColumnsSelector, gridVisibleColumnFieldsSelector, gridIsRtlSelector, gridListViewSelector, (columnsState, model, visibleColumnFields, isRtl, listView) => {\n  if (listView) {\n    return EMPTY_PINNED_COLUMN_FIELDS;\n  }\n  const visiblePinnedFields = filterMissingColumns(model, visibleColumnFields, isRtl);\n  const visiblePinnedColumns = {\n    left: visiblePinnedFields.left.map(field => columnsState.lookup[field]),\n    right: visiblePinnedFields.right.map(field => columnsState.lookup[field])\n  };\n  return visiblePinnedColumns;\n});\nfunction filterMissingColumns(pinnedColumns, columns, invert) {\n  if (!Array.isArray(pinnedColumns.left) && !Array.isArray(pinnedColumns.right)) {\n    return EMPTY_PINNED_COLUMN_FIELDS;\n  }\n  if (pinnedColumns.left?.length === 0 && pinnedColumns.right?.length === 0) {\n    return EMPTY_PINNED_COLUMN_FIELDS;\n  }\n  const filter = (newPinnedColumns, remainingColumns) => {\n    if (!Array.isArray(newPinnedColumns)) {\n      return [];\n    }\n    return newPinnedColumns.filter(field => remainingColumns.includes(field));\n  };\n  const leftPinnedColumns = filter(pinnedColumns.left, columns);\n  const columnsWithoutLeftPinnedColumns = columns.filter(\n  // Filter out from the remaining columns those columns already pinned to the left\n  field => !leftPinnedColumns.includes(field));\n  const rightPinnedColumns = filter(pinnedColumns.right, columnsWithoutLeftPinnedColumns);\n  if (invert) {\n    return {\n      left: rightPinnedColumns,\n      right: leftPinnedColumns\n    };\n  }\n  return {\n    left: leftPinnedColumns,\n    right: rightPinnedColumns\n  };\n}\n\n/**\n * Get the left position in pixel of each visible columns relative to the left of the first column.\n * @category Visible Columns\n */\nexport const gridColumnPositionsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => {\n  const positions = [];\n  let currentPosition = 0;\n  for (let i = 0; i < visibleColumns.length; i += 1) {\n    positions.push(currentPosition);\n    currentPosition += visibleColumns[i].computedWidth;\n  }\n  return positions;\n});\n\n/**\n * Get the filterable columns as an array.\n * @category Columns\n */\nexport const gridFilterableColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.filter(col => col.filterable));\n\n/**\n * Get the filterable columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridFilterableColumnLookupSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.reduce((acc, col) => {\n  if (col.filterable) {\n    acc[col.field] = col;\n  }\n  return acc;\n}, {}));\n\n/**\n * Checks if some column has a colSpan field.\n * @category Columns\n * @ignore - Do not document\n */\nexport const gridHasColSpanSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.some(column => column.colSpan !== undefined));", "map": {"version": 3, "names": ["createSelector", "createSelectorMemoized", "createRootSelector", "EMPTY_PINNED_COLUMN_FIELDS", "gridIsRtlSelector", "gridListColumnSelector", "gridListViewSelector", "gridColumnsStateSelector", "state", "columns", "gridColumnFieldsSelector", "columnsState", "orderedFields", "gridColumnLookupSelector", "lookup", "gridColumnDefinitionsSelector", "allFields", "map", "field", "gridColumnVisibilityModelSelector", "columnVisibilityModel", "gridInitialColumnVisibilityModelSelector", "initialColumnVisibilityModel", "gridVisibleColumnDefinitionsSelector", "listView", "listColumn", "filter", "column", "gridVisibleColumnFieldsSelector", "visibleColumns", "gridPinnedColumnsSelector", "pinnedColumns", "gridExistingPinnedColumnSelector", "model", "isRtl", "filterMissingColumns", "gridVisiblePinnedColumnDefinitionsSelector", "visibleColumnFields", "<PERSON><PERSON><PERSON><PERSON><PERSON>ields", "visiblePinnedColumns", "left", "right", "invert", "Array", "isArray", "length", "newPinnedColumns", "remainingColumns", "includes", "leftPinnedColumns", "columnsWithoutLeftPinnedColumns", "rightPinnedColumns", "gridColumnPositionsSelector", "positions", "currentPosition", "i", "push", "computedWidth", "gridFilterableColumnDefinitionsSelector", "col", "filterable", "gridFilterableColumnLookupSelector", "reduce", "acc", "gridHasColSpanSelector", "some", "colSpan", "undefined"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/columns/gridColumnsSelector.js"], "sourcesContent": ["import { createSelector, createSelectorMemoized, createRootSelector } from \"../../../utils/createSelector.js\";\nimport { EMPTY_PINNED_COLUMN_FIELDS } from \"./gridColumnsInterfaces.js\";\nimport { gridIsRtlSelector } from \"../../core/gridCoreSelector.js\";\nimport { gridListColumnSelector, gridListViewSelector } from \"../listView/index.js\";\n\n/**\n * Get the columns state\n * @category Columns\n */\nexport const gridColumnsStateSelector = createRootSelector(state => state.columns);\n\n/**\n * Get an array of column fields in the order rendered on screen.\n * @category Columns\n */\nexport const gridColumnFieldsSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.orderedFields);\n\n/**\n * Get the columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridColumnLookupSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.lookup);\n\n/**\n * Get an array of column definitions in the order rendered on screen..\n * @category Columns\n */\nexport const gridColumnDefinitionsSelector = createSelectorMemoized(gridColumnFieldsSelector, gridColumnLookupSelector, (allFields, lookup) => allFields.map(field => lookup[field]));\n\n/**\n * Get the column visibility model, containing the visibility status of each column.\n * If a column is not registered in the model, it is visible.\n * @category Visible Columns\n */\nexport const gridColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.columnVisibilityModel);\n\n/**\n * Get the \"initial\" column visibility model, containing the visibility status of each column.\n * It is updated when the `columns` prop is updated or when `updateColumns` API method is called.\n * If a column is not registered in the model, it is visible.\n * @category Visible Columns\n */\nexport const gridInitialColumnVisibilityModelSelector = createSelector(gridColumnsStateSelector, columnsState => columnsState.initialColumnVisibilityModel);\n\n/**\n * Get the visible columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Visible Columns\n */\nexport const gridVisibleColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, gridColumnVisibilityModelSelector, gridListViewSelector, gridListColumnSelector, (columns, columnVisibilityModel, listView, listColumn) => listView && listColumn ? [listColumn] : columns.filter(column => columnVisibilityModel[column.field] !== false));\n\n/**\n * Get the field of each visible column.\n * @category Visible Columns\n */\nexport const gridVisibleColumnFieldsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => visibleColumns.map(column => column.field));\n\n/**\n * Get the visible pinned columns model.\n * @category Visible Columns\n */\nexport const gridPinnedColumnsSelector = createRootSelector(state => state.pinnedColumns);\n\n/**\n * Get all existing pinned columns. Place the columns on the side that depends on the rtl state.\n * @category Pinned Columns\n * @ignore - Do not document\n */\nexport const gridExistingPinnedColumnSelector = createSelectorMemoized(gridPinnedColumnsSelector, gridColumnFieldsSelector, gridIsRtlSelector, (model, orderedFields, isRtl) => filterMissingColumns(model, orderedFields, isRtl));\n\n/**\n * Get the visible pinned columns.\n * @category Visible Columns\n */\nexport const gridVisiblePinnedColumnDefinitionsSelector = createSelectorMemoized(gridColumnsStateSelector, gridPinnedColumnsSelector, gridVisibleColumnFieldsSelector, gridIsRtlSelector, gridListViewSelector, (columnsState, model, visibleColumnFields, isRtl, listView) => {\n  if (listView) {\n    return EMPTY_PINNED_COLUMN_FIELDS;\n  }\n  const visiblePinnedFields = filterMissingColumns(model, visibleColumnFields, isRtl);\n  const visiblePinnedColumns = {\n    left: visiblePinnedFields.left.map(field => columnsState.lookup[field]),\n    right: visiblePinnedFields.right.map(field => columnsState.lookup[field])\n  };\n  return visiblePinnedColumns;\n});\nfunction filterMissingColumns(pinnedColumns, columns, invert) {\n  if (!Array.isArray(pinnedColumns.left) && !Array.isArray(pinnedColumns.right)) {\n    return EMPTY_PINNED_COLUMN_FIELDS;\n  }\n  if (pinnedColumns.left?.length === 0 && pinnedColumns.right?.length === 0) {\n    return EMPTY_PINNED_COLUMN_FIELDS;\n  }\n  const filter = (newPinnedColumns, remainingColumns) => {\n    if (!Array.isArray(newPinnedColumns)) {\n      return [];\n    }\n    return newPinnedColumns.filter(field => remainingColumns.includes(field));\n  };\n  const leftPinnedColumns = filter(pinnedColumns.left, columns);\n  const columnsWithoutLeftPinnedColumns = columns.filter(\n  // Filter out from the remaining columns those columns already pinned to the left\n  field => !leftPinnedColumns.includes(field));\n  const rightPinnedColumns = filter(pinnedColumns.right, columnsWithoutLeftPinnedColumns);\n  if (invert) {\n    return {\n      left: rightPinnedColumns,\n      right: leftPinnedColumns\n    };\n  }\n  return {\n    left: leftPinnedColumns,\n    right: rightPinnedColumns\n  };\n}\n\n/**\n * Get the left position in pixel of each visible columns relative to the left of the first column.\n * @category Visible Columns\n */\nexport const gridColumnPositionsSelector = createSelectorMemoized(gridVisibleColumnDefinitionsSelector, visibleColumns => {\n  const positions = [];\n  let currentPosition = 0;\n  for (let i = 0; i < visibleColumns.length; i += 1) {\n    positions.push(currentPosition);\n    currentPosition += visibleColumns[i].computedWidth;\n  }\n  return positions;\n});\n\n/**\n * Get the filterable columns as an array.\n * @category Columns\n */\nexport const gridFilterableColumnDefinitionsSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.filter(col => col.filterable));\n\n/**\n * Get the filterable columns as a lookup (an object containing the field for keys and the definition for values).\n * @category Columns\n */\nexport const gridFilterableColumnLookupSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.reduce((acc, col) => {\n  if (col.filterable) {\n    acc[col.field] = col;\n  }\n  return acc;\n}, {}));\n\n/**\n * Checks if some column has a colSpan field.\n * @category Columns\n * @ignore - Do not document\n */\nexport const gridHasColSpanSelector = createSelectorMemoized(gridColumnDefinitionsSelector, columns => columns.some(column => column.colSpan !== undefined));"], "mappings": "AAAA,SAASA,cAAc,EAAEC,sBAAsB,EAAEC,kBAAkB,QAAQ,kCAAkC;AAC7G,SAASC,0BAA0B,QAAQ,4BAA4B;AACvE,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,sBAAsB,EAAEC,oBAAoB,QAAQ,sBAAsB;;AAEnF;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGL,kBAAkB,CAACM,KAAK,IAAIA,KAAK,CAACC,OAAO,CAAC;;AAElF;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGV,cAAc,CAACO,wBAAwB,EAAEI,YAAY,IAAIA,YAAY,CAACC,aAAa,CAAC;;AAE5H;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGb,cAAc,CAACO,wBAAwB,EAAEI,YAAY,IAAIA,YAAY,CAACG,MAAM,CAAC;;AAErH;AACA;AACA;AACA;AACA,OAAO,MAAMC,6BAA6B,GAAGd,sBAAsB,CAACS,wBAAwB,EAAEG,wBAAwB,EAAE,CAACG,SAAS,EAAEF,MAAM,KAAKE,SAAS,CAACC,GAAG,CAACC,KAAK,IAAIJ,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;;AAErL;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iCAAiC,GAAGnB,cAAc,CAACO,wBAAwB,EAAEI,YAAY,IAAIA,YAAY,CAACS,qBAAqB,CAAC;;AAE7I;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,wCAAwC,GAAGrB,cAAc,CAACO,wBAAwB,EAAEI,YAAY,IAAIA,YAAY,CAACW,4BAA4B,CAAC;;AAE3J;AACA;AACA;AACA;AACA,OAAO,MAAMC,oCAAoC,GAAGtB,sBAAsB,CAACc,6BAA6B,EAAEI,iCAAiC,EAAEb,oBAAoB,EAAED,sBAAsB,EAAE,CAACI,OAAO,EAAEW,qBAAqB,EAAEI,QAAQ,EAAEC,UAAU,KAAKD,QAAQ,IAAIC,UAAU,GAAG,CAACA,UAAU,CAAC,GAAGhB,OAAO,CAACiB,MAAM,CAACC,MAAM,IAAIP,qBAAqB,CAACO,MAAM,CAACT,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC;;AAErW;AACA;AACA;AACA;AACA,OAAO,MAAMU,+BAA+B,GAAG3B,sBAAsB,CAACsB,oCAAoC,EAAEM,cAAc,IAAIA,cAAc,CAACZ,GAAG,CAACU,MAAM,IAAIA,MAAM,CAACT,KAAK,CAAC,CAAC;;AAEzK;AACA;AACA;AACA;AACA,OAAO,MAAMY,yBAAyB,GAAG5B,kBAAkB,CAACM,KAAK,IAAIA,KAAK,CAACuB,aAAa,CAAC;;AAEzF;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gCAAgC,GAAG/B,sBAAsB,CAAC6B,yBAAyB,EAAEpB,wBAAwB,EAAEN,iBAAiB,EAAE,CAAC6B,KAAK,EAAErB,aAAa,EAAEsB,KAAK,KAAKC,oBAAoB,CAACF,KAAK,EAAErB,aAAa,EAAEsB,KAAK,CAAC,CAAC;;AAElO;AACA;AACA;AACA;AACA,OAAO,MAAME,0CAA0C,GAAGnC,sBAAsB,CAACM,wBAAwB,EAAEuB,yBAAyB,EAAEF,+BAA+B,EAAExB,iBAAiB,EAAEE,oBAAoB,EAAE,CAACK,YAAY,EAAEsB,KAAK,EAAEI,mBAAmB,EAAEH,KAAK,EAAEV,QAAQ,KAAK;EAC7Q,IAAIA,QAAQ,EAAE;IACZ,OAAOrB,0BAA0B;EACnC;EACA,MAAMmC,mBAAmB,GAAGH,oBAAoB,CAACF,KAAK,EAAEI,mBAAmB,EAAEH,KAAK,CAAC;EACnF,MAAMK,oBAAoB,GAAG;IAC3BC,IAAI,EAAEF,mBAAmB,CAACE,IAAI,CAACvB,GAAG,CAACC,KAAK,IAAIP,YAAY,CAACG,MAAM,CAACI,KAAK,CAAC,CAAC;IACvEuB,KAAK,EAAEH,mBAAmB,CAACG,KAAK,CAACxB,GAAG,CAACC,KAAK,IAAIP,YAAY,CAACG,MAAM,CAACI,KAAK,CAAC;EAC1E,CAAC;EACD,OAAOqB,oBAAoB;AAC7B,CAAC,CAAC;AACF,SAASJ,oBAAoBA,CAACJ,aAAa,EAAEtB,OAAO,EAAEiC,MAAM,EAAE;EAC5D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACb,aAAa,CAACS,IAAI,CAAC,IAAI,CAACG,KAAK,CAACC,OAAO,CAACb,aAAa,CAACU,KAAK,CAAC,EAAE;IAC7E,OAAOtC,0BAA0B;EACnC;EACA,IAAI4B,aAAa,CAACS,IAAI,EAAEK,MAAM,KAAK,CAAC,IAAId,aAAa,CAACU,KAAK,EAAEI,MAAM,KAAK,CAAC,EAAE;IACzE,OAAO1C,0BAA0B;EACnC;EACA,MAAMuB,MAAM,GAAGA,CAACoB,gBAAgB,EAAEC,gBAAgB,KAAK;IACrD,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACE,gBAAgB,CAAC,EAAE;MACpC,OAAO,EAAE;IACX;IACA,OAAOA,gBAAgB,CAACpB,MAAM,CAACR,KAAK,IAAI6B,gBAAgB,CAACC,QAAQ,CAAC9B,KAAK,CAAC,CAAC;EAC3E,CAAC;EACD,MAAM+B,iBAAiB,GAAGvB,MAAM,CAACK,aAAa,CAACS,IAAI,EAAE/B,OAAO,CAAC;EAC7D,MAAMyC,+BAA+B,GAAGzC,OAAO,CAACiB,MAAM;EACtD;EACAR,KAAK,IAAI,CAAC+B,iBAAiB,CAACD,QAAQ,CAAC9B,KAAK,CAAC,CAAC;EAC5C,MAAMiC,kBAAkB,GAAGzB,MAAM,CAACK,aAAa,CAACU,KAAK,EAAES,+BAA+B,CAAC;EACvF,IAAIR,MAAM,EAAE;IACV,OAAO;MACLF,IAAI,EAAEW,kBAAkB;MACxBV,KAAK,EAAEQ;IACT,CAAC;EACH;EACA,OAAO;IACLT,IAAI,EAAES,iBAAiB;IACvBR,KAAK,EAAEU;EACT,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,2BAA2B,GAAGnD,sBAAsB,CAACsB,oCAAoC,EAAEM,cAAc,IAAI;EACxH,MAAMwB,SAAS,GAAG,EAAE;EACpB,IAAIC,eAAe,GAAG,CAAC;EACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,cAAc,CAACgB,MAAM,EAAEU,CAAC,IAAI,CAAC,EAAE;IACjDF,SAAS,CAACG,IAAI,CAACF,eAAe,CAAC;IAC/BA,eAAe,IAAIzB,cAAc,CAAC0B,CAAC,CAAC,CAACE,aAAa;EACpD;EACA,OAAOJ,SAAS;AAClB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMK,uCAAuC,GAAGzD,sBAAsB,CAACc,6BAA6B,EAAEN,OAAO,IAAIA,OAAO,CAACiB,MAAM,CAACiC,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,CAAC;;AAE9J;AACA;AACA;AACA;AACA,OAAO,MAAMC,kCAAkC,GAAG5D,sBAAsB,CAACc,6BAA6B,EAAEN,OAAO,IAAIA,OAAO,CAACqD,MAAM,CAAC,CAACC,GAAG,EAAEJ,GAAG,KAAK;EAC9I,IAAIA,GAAG,CAACC,UAAU,EAAE;IAClBG,GAAG,CAACJ,GAAG,CAACzC,KAAK,CAAC,GAAGyC,GAAG;EACtB;EACA,OAAOI,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;AAEP;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAG/D,sBAAsB,CAACc,6BAA6B,EAAEN,OAAO,IAAIA,OAAO,CAACwD,IAAI,CAACtC,MAAM,IAAIA,MAAM,CAACuC,OAAO,KAAKC,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}