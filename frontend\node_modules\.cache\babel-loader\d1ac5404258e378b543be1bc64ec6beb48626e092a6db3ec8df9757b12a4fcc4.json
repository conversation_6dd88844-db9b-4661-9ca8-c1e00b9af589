{"ast": null, "code": "function eq(value, other) {\n  return value === other || Number.isNaN(value) && Number.isNaN(other);\n}\nexport { eq };", "map": {"version": 3, "names": ["eq", "value", "other", "Number", "isNaN"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/compat/util/eq.mjs"], "sourcesContent": ["function eq(value, other) {\n    return value === other || (Number.isNaN(value) && Number.isNaN(other));\n}\n\nexport { eq };\n"], "mappings": "AAAA,SAASA,EAAEA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACtB,OAAOD,KAAK,KAAKC,KAAK,IAAKC,MAAM,CAACC,KAAK,CAACH,KAAK,CAAC,IAAIE,MAAM,CAACC,KAAK,CAACF,KAAK,CAAE;AAC1E;AAEA,SAASF,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}