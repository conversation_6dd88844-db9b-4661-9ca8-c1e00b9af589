{"ast": null, "code": "function rest(func, startIndex = func.length - 1) {\n  return function (...args) {\n    const rest = args.slice(startIndex);\n    const params = args.slice(0, startIndex);\n    while (params.length < startIndex) {\n      params.push(undefined);\n    }\n    return func.apply(this, [...params, rest]);\n  };\n}\nexport { rest };", "map": {"version": 3, "names": ["rest", "func", "startIndex", "length", "args", "slice", "params", "push", "undefined", "apply"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/function/rest.mjs"], "sourcesContent": ["function rest(func, startIndex = func.length - 1) {\n    return function (...args) {\n        const rest = args.slice(startIndex);\n        const params = args.slice(0, startIndex);\n        while (params.length < startIndex) {\n            params.push(undefined);\n        }\n        return func.apply(this, [...params, rest]);\n    };\n}\n\nexport { rest };\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,IAAI,EAAEC,UAAU,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE;EAC9C,OAAO,UAAU,GAAGC,IAAI,EAAE;IACtB,MAAMJ,IAAI,GAAGI,IAAI,CAACC,KAAK,CAACH,UAAU,CAAC;IACnC,MAAMI,MAAM,GAAGF,IAAI,CAACC,KAAK,CAAC,CAAC,EAAEH,UAAU,CAAC;IACxC,OAAOI,MAAM,CAACH,MAAM,GAAGD,UAAU,EAAE;MAC/BI,MAAM,CAACC,IAAI,CAACC,SAAS,CAAC;IAC1B;IACA,OAAOP,IAAI,CAACQ,KAAK,CAAC,IAAI,EAAE,CAAC,GAAGH,MAAM,EAAEN,IAAI,CAAC,CAAC;EAC9C,CAAC;AACL;AAEA,SAASA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}