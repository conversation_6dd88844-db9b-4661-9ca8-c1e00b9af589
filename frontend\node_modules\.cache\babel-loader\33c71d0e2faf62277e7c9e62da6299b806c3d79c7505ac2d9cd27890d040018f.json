{"ast": null, "code": "export function GridPinnedRows(_) {\n  return null;\n}", "map": {"version": 3, "names": ["GridPinnedRows", "_"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/components/GridPinnedRows.js"], "sourcesContent": ["export function GridPinnedRows(_) {\n  return null;\n}"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,CAAC,EAAE;EAChC,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}