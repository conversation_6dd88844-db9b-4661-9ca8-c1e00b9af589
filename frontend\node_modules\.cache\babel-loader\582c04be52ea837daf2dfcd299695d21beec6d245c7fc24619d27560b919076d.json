{"ast": null, "code": "import { GridSignature } from \"../../../constants/signature.js\";\nconst MAX_PAGE_SIZE = 100;\nexport const defaultPageSize = autoPageSize => autoPageSize ? 0 : 100;\nexport const getPageCount = (rowCount, pageSize, page) => {\n  if (pageSize > 0 && rowCount > 0) {\n    return Math.ceil(rowCount / pageSize);\n  }\n  if (rowCount === -1) {\n    // With unknown row-count, we can assume a page after the current one\n    return page + 2;\n  }\n  return 0;\n};\nexport const getDefaultGridPaginationModel = autoPageSize => ({\n  page: 0,\n  pageSize: autoPageSize ? 0 : 100\n});\nexport const getValidPage = (page, pageCount = 0) => {\n  if (pageCount === 0) {\n    return page;\n  }\n  return Math.max(Math.min(page, pageCount - 1), 0);\n};\nexport const throwIfPageSizeExceedsTheLimit = (pageSize, signatureProp) => {\n  if (signatureProp === GridSignature.DataGrid && pageSize > MAX_PAGE_SIZE) {\n    throw new Error(['MUI X: `pageSize` cannot exceed 100 in the MIT version of the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n  }\n};", "map": {"version": 3, "names": ["GridSignature", "MAX_PAGE_SIZE", "defaultPageSize", "autoPageSize", "getPageCount", "rowCount", "pageSize", "page", "Math", "ceil", "getDefaultGridPaginationModel", "getValidPage", "pageCount", "max", "min", "throwIfPageSizeExceedsTheLimit", "signatureProp", "DataGrid", "Error", "join"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/hooks/features/pagination/gridPaginationUtils.js"], "sourcesContent": ["import { GridSignature } from \"../../../constants/signature.js\";\nconst MAX_PAGE_SIZE = 100;\nexport const defaultPageSize = autoPageSize => autoPageSize ? 0 : 100;\nexport const getPageCount = (rowCount, pageSize, page) => {\n  if (pageSize > 0 && rowCount > 0) {\n    return Math.ceil(rowCount / pageSize);\n  }\n  if (rowCount === -1) {\n    // With unknown row-count, we can assume a page after the current one\n    return page + 2;\n  }\n  return 0;\n};\nexport const getDefaultGridPaginationModel = autoPageSize => ({\n  page: 0,\n  pageSize: autoPageSize ? 0 : 100\n});\nexport const getValidPage = (page, pageCount = 0) => {\n  if (pageCount === 0) {\n    return page;\n  }\n  return Math.max(Math.min(page, pageCount - 1), 0);\n};\nexport const throwIfPageSizeExceedsTheLimit = (pageSize, signatureProp) => {\n  if (signatureProp === GridSignature.DataGrid && pageSize > MAX_PAGE_SIZE) {\n    throw new Error(['MUI X: `pageSize` cannot exceed 100 in the MIT version of the DataGrid.', 'You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature.'].join('\\n'));\n  }\n};"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iCAAiC;AAC/D,MAAMC,aAAa,GAAG,GAAG;AACzB,OAAO,MAAMC,eAAe,GAAGC,YAAY,IAAIA,YAAY,GAAG,CAAC,GAAG,GAAG;AACrE,OAAO,MAAMC,YAAY,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,KAAK;EACxD,IAAID,QAAQ,GAAG,CAAC,IAAID,QAAQ,GAAG,CAAC,EAAE;IAChC,OAAOG,IAAI,CAACC,IAAI,CAACJ,QAAQ,GAAGC,QAAQ,CAAC;EACvC;EACA,IAAID,QAAQ,KAAK,CAAC,CAAC,EAAE;IACnB;IACA,OAAOE,IAAI,GAAG,CAAC;EACjB;EACA,OAAO,CAAC;AACV,CAAC;AACD,OAAO,MAAMG,6BAA6B,GAAGP,YAAY,KAAK;EAC5DI,IAAI,EAAE,CAAC;EACPD,QAAQ,EAAEH,YAAY,GAAG,CAAC,GAAG;AAC/B,CAAC,CAAC;AACF,OAAO,MAAMQ,YAAY,GAAGA,CAACJ,IAAI,EAAEK,SAAS,GAAG,CAAC,KAAK;EACnD,IAAIA,SAAS,KAAK,CAAC,EAAE;IACnB,OAAOL,IAAI;EACb;EACA,OAAOC,IAAI,CAACK,GAAG,CAACL,IAAI,CAACM,GAAG,CAACP,IAAI,EAAEK,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACnD,CAAC;AACD,OAAO,MAAMG,8BAA8B,GAAGA,CAACT,QAAQ,EAAEU,aAAa,KAAK;EACzE,IAAIA,aAAa,KAAKhB,aAAa,CAACiB,QAAQ,IAAIX,QAAQ,GAAGL,aAAa,EAAE;IACxE,MAAM,IAAIiB,KAAK,CAAC,CAAC,yEAAyE,EAAE,yFAAyF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACpM;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}