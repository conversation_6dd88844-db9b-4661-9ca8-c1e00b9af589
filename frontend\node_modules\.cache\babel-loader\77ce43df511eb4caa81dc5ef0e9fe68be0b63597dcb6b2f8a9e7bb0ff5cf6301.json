{"ast": null, "code": "function invariant(condition, message) {\n  if (condition) {\n    return;\n  }\n  if (typeof message === 'string') {\n    throw new Error(message);\n  }\n  throw message;\n}\nexport { invariant };", "map": {"version": 3, "names": ["invariant", "condition", "message", "Error"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/util/invariant.mjs"], "sourcesContent": ["function invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (typeof message === 'string') {\n        throw new Error(message);\n    }\n    throw message;\n}\n\nexport { invariant };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACnC,IAAID,SAAS,EAAE;IACX;EACJ;EACA,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIC,KAAK,CAACD,OAAO,CAAC;EAC5B;EACA,MAAMA,OAAO;AACjB;AAEA,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}