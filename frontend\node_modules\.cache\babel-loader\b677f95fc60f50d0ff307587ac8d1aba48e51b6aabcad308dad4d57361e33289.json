{"ast": null, "code": "import { PinnedColumnPosition } from \"../internals/constants.js\";\nexport const shouldCellShowRightBorder = (pinnedPosition, indexInSection, sectionLength, showCellVerticalBorderRootProp, gridHasFiller) => {\n  const isSectionLastCell = indexInSection === sectionLength - 1;\n  if (pinnedPosition === PinnedColumnPosition.LEFT && isSectionLastCell) {\n    return true;\n  }\n  if (showCellVerticalBorderRootProp) {\n    if (pinnedPosition === PinnedColumnPosition.LEFT) {\n      return true;\n    }\n    if (pinnedPosition === PinnedColumnPosition.RIGHT) {\n      return !isSectionLastCell;\n    }\n    // pinnedPosition === undefined, middle section\n    return !isSectionLastCell || gridHasFiller;\n  }\n  return false;\n};\nexport const shouldCellShowLeftBorder = (pinnedPosition, indexInSection) => {\n  return pinnedPosition === PinnedColumnPosition.RIGHT && indexInSection === 0;\n};", "map": {"version": 3, "names": ["PinnedColumnPosition", "shouldCellShowRightBorder", "pinnedPosition", "indexInSection", "sectionLength", "showCellVerticalBorderRootProp", "gridHasFiller", "isSectionLastCell", "LEFT", "RIGHT", "shouldCellShowLeftBorder"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/@mui/x-data-grid/esm/utils/cellBorderUtils.js"], "sourcesContent": ["import { PinnedColumnPosition } from \"../internals/constants.js\";\nexport const shouldCellShowRightBorder = (pinnedPosition, indexInSection, sectionLength, showCellVerticalBorderRootProp, gridHasFiller) => {\n  const isSectionLastCell = indexInSection === sectionLength - 1;\n  if (pinnedPosition === PinnedColumnPosition.LEFT && isSectionLastCell) {\n    return true;\n  }\n  if (showCellVerticalBorderRootProp) {\n    if (pinnedPosition === PinnedColumnPosition.LEFT) {\n      return true;\n    }\n    if (pinnedPosition === PinnedColumnPosition.RIGHT) {\n      return !isSectionLastCell;\n    }\n    // pinnedPosition === undefined, middle section\n    return !isSectionLastCell || gridHasFiller;\n  }\n  return false;\n};\nexport const shouldCellShowLeftBorder = (pinnedPosition, indexInSection) => {\n  return pinnedPosition === PinnedColumnPosition.RIGHT && indexInSection === 0;\n};"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,OAAO,MAAMC,yBAAyB,GAAGA,CAACC,cAAc,EAAEC,cAAc,EAAEC,aAAa,EAAEC,8BAA8B,EAAEC,aAAa,KAAK;EACzI,MAAMC,iBAAiB,GAAGJ,cAAc,KAAKC,aAAa,GAAG,CAAC;EAC9D,IAAIF,cAAc,KAAKF,oBAAoB,CAACQ,IAAI,IAAID,iBAAiB,EAAE;IACrE,OAAO,IAAI;EACb;EACA,IAAIF,8BAA8B,EAAE;IAClC,IAAIH,cAAc,KAAKF,oBAAoB,CAACQ,IAAI,EAAE;MAChD,OAAO,IAAI;IACb;IACA,IAAIN,cAAc,KAAKF,oBAAoB,CAACS,KAAK,EAAE;MACjD,OAAO,CAACF,iBAAiB;IAC3B;IACA;IACA,OAAO,CAACA,iBAAiB,IAAID,aAAa;EAC5C;EACA,OAAO,KAAK;AACd,CAAC;AACD,OAAO,MAAMI,wBAAwB,GAAGA,CAACR,cAAc,EAAEC,cAAc,KAAK;EAC1E,OAAOD,cAAc,KAAKF,oBAAoB,CAACS,KAAK,IAAIN,cAAc,KAAK,CAAC;AAC9E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}