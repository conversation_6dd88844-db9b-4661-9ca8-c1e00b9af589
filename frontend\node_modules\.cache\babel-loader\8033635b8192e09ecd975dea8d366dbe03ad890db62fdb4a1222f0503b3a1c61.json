{"ast": null, "code": "import { Semaphore } from './semaphore.mjs';\nclass Mutex {\n  semaphore = new Semaphore(1);\n  get isLocked() {\n    return this.semaphore.available === 0;\n  }\n  async acquire() {\n    return this.semaphore.acquire();\n  }\n  release() {\n    this.semaphore.release();\n  }\n}\nexport { Mutex };", "map": {"version": 3, "names": ["Semaphore", "Mutex", "semaphore", "isLocked", "available", "acquire", "release"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/promise/mutex.mjs"], "sourcesContent": ["import { Semaphore } from './semaphore.mjs';\n\nclass Mutex {\n    semaphore = new Semaphore(1);\n    get isLocked() {\n        return this.semaphore.available === 0;\n    }\n    async acquire() {\n        return this.semaphore.acquire();\n    }\n    release() {\n        this.semaphore.release();\n    }\n}\n\nexport { Mutex };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAE3C,MAAMC,KAAK,CAAC;EACRC,SAAS,GAAG,IAAIF,SAAS,CAAC,CAAC,CAAC;EAC5B,IAAIG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,SAAS,CAACE,SAAS,KAAK,CAAC;EACzC;EACA,MAAMC,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACH,SAAS,CAACG,OAAO,CAAC,CAAC;EACnC;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACJ,SAAS,CAACI,OAAO,CAAC,CAAC;EAC5B;AACJ;AAEA,SAASL,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}