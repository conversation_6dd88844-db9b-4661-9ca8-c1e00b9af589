{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isMatch = require('./isMatch.js');\nconst toKey = require('../_internal/toKey.js');\nconst cloneDeep = require('../object/cloneDeep.js');\nconst get = require('../object/get.js');\nconst has = require('../object/has.js');\nfunction matchesProperty(property, source) {\n  switch (typeof property) {\n    case 'object':\n      {\n        if (Object.is(property?.valueOf(), -0)) {\n          property = '-0';\n        }\n        break;\n      }\n    case 'number':\n      {\n        property = toKey.toKey(property);\n        break;\n      }\n  }\n  source = cloneDeep.cloneDeep(source);\n  return function (target) {\n    const result = get.get(target, property);\n    if (result === undefined) {\n      return has.has(target, property);\n    }\n    if (source === undefined) {\n      return result === undefined;\n    }\n    return isMatch.isMatch(result, source);\n  };\n}\nexports.matchesProperty = matchesProperty;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isMatch", "require", "to<PERSON><PERSON>", "cloneDeep", "get", "has", "matchesProperty", "property", "source", "is", "valueOf", "target", "result", "undefined"], "sources": ["C:/Users/<USER>/LMS/frontend/node_modules/es-toolkit/dist/compat/predicate/matchesProperty.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isMatch = require('./isMatch.js');\nconst toKey = require('../_internal/toKey.js');\nconst cloneDeep = require('../object/cloneDeep.js');\nconst get = require('../object/get.js');\nconst has = require('../object/has.js');\n\nfunction matchesProperty(property, source) {\n    switch (typeof property) {\n        case 'object': {\n            if (Object.is(property?.valueOf(), -0)) {\n                property = '-0';\n            }\n            break;\n        }\n        case 'number': {\n            property = toKey.toKey(property);\n            break;\n        }\n    }\n    source = cloneDeep.cloneDeep(source);\n    return function (target) {\n        const result = get.get(target, property);\n        if (result === undefined) {\n            return has.has(target, property);\n        }\n        if (source === undefined) {\n            return result === undefined;\n        }\n        return isMatch.isMatch(result, source);\n    };\n}\n\nexports.matchesProperty = matchesProperty;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,MAAMC,KAAK,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAC9C,MAAME,SAAS,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AACnD,MAAMG,GAAG,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AACvC,MAAMI,GAAG,GAAGJ,OAAO,CAAC,kBAAkB,CAAC;AAEvC,SAASK,eAAeA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACvC,QAAQ,OAAOD,QAAQ;IACnB,KAAK,QAAQ;MAAE;QACX,IAAIb,MAAM,CAACe,EAAE,CAACF,QAAQ,EAAEG,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACpCH,QAAQ,GAAG,IAAI;QACnB;QACA;MACJ;IACA,KAAK,QAAQ;MAAE;QACXA,QAAQ,GAAGL,KAAK,CAACA,KAAK,CAACK,QAAQ,CAAC;QAChC;MACJ;EACJ;EACAC,MAAM,GAAGL,SAAS,CAACA,SAAS,CAACK,MAAM,CAAC;EACpC,OAAO,UAAUG,MAAM,EAAE;IACrB,MAAMC,MAAM,GAAGR,GAAG,CAACA,GAAG,CAACO,MAAM,EAAEJ,QAAQ,CAAC;IACxC,IAAIK,MAAM,KAAKC,SAAS,EAAE;MACtB,OAAOR,GAAG,CAACA,GAAG,CAACM,MAAM,EAAEJ,QAAQ,CAAC;IACpC;IACA,IAAIC,MAAM,KAAKK,SAAS,EAAE;MACtB,OAAOD,MAAM,KAAKC,SAAS;IAC/B;IACA,OAAOb,OAAO,CAACA,OAAO,CAACY,MAAM,EAAEJ,MAAM,CAAC;EAC1C,CAAC;AACL;AAEAZ,OAAO,CAACU,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}